{"version": 3, "sources": ["../../.pnpm/@vueuse+shared@9.13.0_vue@3.5.12_typescript@5.3.3_/node_modules/@vueuse/shared/index.mjs", "../../.pnpm/@vueuse+core@9.13.0_vue@3.5.12_typescript@5.3.3_/node_modules/@vueuse/core/index.mjs", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/scrollbar/src/util.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/scrollbar/src/constants.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/scrollbar/src/thumb.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/internal/build/plugin-vue:export-helper", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/hooks/use-namespace/index.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/scrollbar/src/thumb.vue", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/scrollbar/src/bar.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/scrollbar/src/bar.vue", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/hooks/use-aria/index.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/utils/types.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/scrollbar/src/scrollbar.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/utils/objects.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/utils/dom/style.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/scrollbar/src/scrollbar.vue", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/utils/vue/install.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/scrollbar/index.ts"], "sourcesContent": ["import { shallowRef, watchEffect, readonly, unref, ref, isVue3, version, watch, customRef, getCurrentScope, onScopeDispose, effectScope, provide, inject, isRef, computed, reactive, toRefs as toRefs$1, toRef, isVue2, set as set$1, getCurrentInstance, onBeforeMount, nextTick, onBeforeUnmount, onMounted, onUnmounted, isReactive } from 'vue-demi';\n\nvar __defProp$9 = Object.defineProperty;\nvar __defProps$6 = Object.defineProperties;\nvar __getOwnPropDescs$6 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$b = Object.getOwnPropertySymbols;\nvar __hasOwnProp$b = Object.prototype.hasOwnProperty;\nvar __propIsEnum$b = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$9 = (obj, key, value) => key in obj ? __defProp$9(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$9 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$b.call(b, prop))\n      __defNormalProp$9(a, prop, b[prop]);\n  if (__getOwnPropSymbols$b)\n    for (var prop of __getOwnPropSymbols$b(b)) {\n      if (__propIsEnum$b.call(b, prop))\n        __defNormalProp$9(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$6 = (a, b) => __defProps$6(a, __getOwnPropDescs$6(b));\nfunction computedEager(fn, options) {\n  var _a;\n  const result = shallowRef();\n  watchEffect(() => {\n    result.value = fn();\n  }, __spreadProps$6(__spreadValues$9({}, options), {\n    flush: (_a = options == null ? void 0 : options.flush) != null ? _a : \"sync\"\n  }));\n  return readonly(result);\n}\n\nvar _a;\nconst isClient = typeof window !== \"undefined\";\nconst isDef = (val) => typeof val !== \"undefined\";\nconst assert = (condition, ...infos) => {\n  if (!condition)\n    console.warn(...infos);\n};\nconst toString = Object.prototype.toString;\nconst isBoolean = (val) => typeof val === \"boolean\";\nconst isFunction = (val) => typeof val === \"function\";\nconst isNumber = (val) => typeof val === \"number\";\nconst isString = (val) => typeof val === \"string\";\nconst isObject = (val) => toString.call(val) === \"[object Object]\";\nconst isWindow = (val) => typeof window !== \"undefined\" && toString.call(val) === \"[object Window]\";\nconst now = () => Date.now();\nconst timestamp = () => +Date.now();\nconst clamp = (n, min, max) => Math.min(max, Math.max(min, n));\nconst noop = () => {\n};\nconst rand = (min, max) => {\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n};\nconst isIOS = isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && /iP(ad|hone|od)/.test(window.navigator.userAgent);\nconst hasOwn = (val, key) => Object.prototype.hasOwnProperty.call(val, key);\n\nfunction resolveUnref(r) {\n  return typeof r === \"function\" ? r() : unref(r);\n}\n\nfunction createFilterWrapper(filter, fn) {\n  function wrapper(...args) {\n    return new Promise((resolve, reject) => {\n      Promise.resolve(filter(() => fn.apply(this, args), { fn, thisArg: this, args })).then(resolve).catch(reject);\n    });\n  }\n  return wrapper;\n}\nconst bypassFilter = (invoke) => {\n  return invoke();\n};\nfunction debounceFilter(ms, options = {}) {\n  let timer;\n  let maxTimer;\n  let lastRejector = noop;\n  const _clearTimeout = (timer2) => {\n    clearTimeout(timer2);\n    lastRejector();\n    lastRejector = noop;\n  };\n  const filter = (invoke) => {\n    const duration = resolveUnref(ms);\n    const maxDuration = resolveUnref(options.maxWait);\n    if (timer)\n      _clearTimeout(timer);\n    if (duration <= 0 || maxDuration !== void 0 && maxDuration <= 0) {\n      if (maxTimer) {\n        _clearTimeout(maxTimer);\n        maxTimer = null;\n      }\n      return Promise.resolve(invoke());\n    }\n    return new Promise((resolve, reject) => {\n      lastRejector = options.rejectOnCancel ? reject : resolve;\n      if (maxDuration && !maxTimer) {\n        maxTimer = setTimeout(() => {\n          if (timer)\n            _clearTimeout(timer);\n          maxTimer = null;\n          resolve(invoke());\n        }, maxDuration);\n      }\n      timer = setTimeout(() => {\n        if (maxTimer)\n          _clearTimeout(maxTimer);\n        maxTimer = null;\n        resolve(invoke());\n      }, duration);\n    });\n  };\n  return filter;\n}\nfunction throttleFilter(ms, trailing = true, leading = true, rejectOnCancel = false) {\n  let lastExec = 0;\n  let timer;\n  let isLeading = true;\n  let lastRejector = noop;\n  let lastValue;\n  const clear = () => {\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n      lastRejector();\n      lastRejector = noop;\n    }\n  };\n  const filter = (_invoke) => {\n    const duration = resolveUnref(ms);\n    const elapsed = Date.now() - lastExec;\n    const invoke = () => {\n      return lastValue = _invoke();\n    };\n    clear();\n    if (duration <= 0) {\n      lastExec = Date.now();\n      return invoke();\n    }\n    if (elapsed > duration && (leading || !isLeading)) {\n      lastExec = Date.now();\n      invoke();\n    } else if (trailing) {\n      lastValue = new Promise((resolve, reject) => {\n        lastRejector = rejectOnCancel ? reject : resolve;\n        timer = setTimeout(() => {\n          lastExec = Date.now();\n          isLeading = true;\n          resolve(invoke());\n          clear();\n        }, Math.max(0, duration - elapsed));\n      });\n    }\n    if (!leading && !timer)\n      timer = setTimeout(() => isLeading = true, duration);\n    isLeading = false;\n    return lastValue;\n  };\n  return filter;\n}\nfunction pausableFilter(extendFilter = bypassFilter) {\n  const isActive = ref(true);\n  function pause() {\n    isActive.value = false;\n  }\n  function resume() {\n    isActive.value = true;\n  }\n  const eventFilter = (...args) => {\n    if (isActive.value)\n      extendFilter(...args);\n  };\n  return { isActive: readonly(isActive), pause, resume, eventFilter };\n}\n\nfunction __onlyVue3(name = \"this function\") {\n  if (isVue3)\n    return;\n  throw new Error(`[VueUse] ${name} is only works on Vue 3.`);\n}\nfunction __onlyVue27Plus(name = \"this function\") {\n  if (isVue3 || version.startsWith(\"2.7.\"))\n    return;\n  throw new Error(`[VueUse] ${name} is only works on Vue 2.7 or above.`);\n}\nconst directiveHooks = {\n  mounted: isVue3 ? \"mounted\" : \"inserted\",\n  updated: isVue3 ? \"updated\" : \"componentUpdated\",\n  unmounted: isVue3 ? \"unmounted\" : \"unbind\"\n};\n\nfunction promiseTimeout(ms, throwOnTimeout = false, reason = \"Timeout\") {\n  return new Promise((resolve, reject) => {\n    if (throwOnTimeout)\n      setTimeout(() => reject(reason), ms);\n    else\n      setTimeout(resolve, ms);\n  });\n}\nfunction identity(arg) {\n  return arg;\n}\nfunction createSingletonPromise(fn) {\n  let _promise;\n  function wrapper() {\n    if (!_promise)\n      _promise = fn();\n    return _promise;\n  }\n  wrapper.reset = async () => {\n    const _prev = _promise;\n    _promise = void 0;\n    if (_prev)\n      await _prev;\n  };\n  return wrapper;\n}\nfunction invoke(fn) {\n  return fn();\n}\nfunction containsProp(obj, ...props) {\n  return props.some((k) => k in obj);\n}\nfunction increaseWithUnit(target, delta) {\n  var _a;\n  if (typeof target === \"number\")\n    return target + delta;\n  const value = ((_a = target.match(/^-?[0-9]+\\.?[0-9]*/)) == null ? void 0 : _a[0]) || \"\";\n  const unit = target.slice(value.length);\n  const result = parseFloat(value) + delta;\n  if (Number.isNaN(result))\n    return target;\n  return result + unit;\n}\nfunction objectPick(obj, keys, omitUndefined = false) {\n  return keys.reduce((n, k) => {\n    if (k in obj) {\n      if (!omitUndefined || obj[k] !== void 0)\n        n[k] = obj[k];\n    }\n    return n;\n  }, {});\n}\n\nfunction computedWithControl(source, fn) {\n  let v = void 0;\n  let track;\n  let trigger;\n  const dirty = ref(true);\n  const update = () => {\n    dirty.value = true;\n    trigger();\n  };\n  watch(source, update, { flush: \"sync\" });\n  const get = isFunction(fn) ? fn : fn.get;\n  const set = isFunction(fn) ? void 0 : fn.set;\n  const result = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        if (dirty.value) {\n          v = get();\n          dirty.value = false;\n        }\n        track();\n        return v;\n      },\n      set(v2) {\n        set == null ? void 0 : set(v2);\n      }\n    };\n  });\n  if (Object.isExtensible(result))\n    result.trigger = update;\n  return result;\n}\n\nfunction tryOnScopeDispose(fn) {\n  if (getCurrentScope()) {\n    onScopeDispose(fn);\n    return true;\n  }\n  return false;\n}\n\nfunction createEventHook() {\n  const fns = [];\n  const off = (fn) => {\n    const index = fns.indexOf(fn);\n    if (index !== -1)\n      fns.splice(index, 1);\n  };\n  const on = (fn) => {\n    fns.push(fn);\n    const offFn = () => off(fn);\n    tryOnScopeDispose(offFn);\n    return {\n      off: offFn\n    };\n  };\n  const trigger = (param) => {\n    fns.forEach((fn) => fn(param));\n  };\n  return {\n    on,\n    off,\n    trigger\n  };\n}\n\nfunction createGlobalState(stateFactory) {\n  let initialized = false;\n  let state;\n  const scope = effectScope(true);\n  return () => {\n    if (!initialized) {\n      state = scope.run(stateFactory);\n      initialized = true;\n    }\n    return state;\n  };\n}\n\nfunction createInjectionState(composable) {\n  const key = Symbol(\"InjectionState\");\n  const useProvidingState = (...args) => {\n    const state = composable(...args);\n    provide(key, state);\n    return state;\n  };\n  const useInjectedState = () => inject(key);\n  return [useProvidingState, useInjectedState];\n}\n\nfunction createSharedComposable(composable) {\n  let subscribers = 0;\n  let state;\n  let scope;\n  const dispose = () => {\n    subscribers -= 1;\n    if (scope && subscribers <= 0) {\n      scope.stop();\n      state = void 0;\n      scope = void 0;\n    }\n  };\n  return (...args) => {\n    subscribers += 1;\n    if (!state) {\n      scope = effectScope(true);\n      state = scope.run(() => composable(...args));\n    }\n    tryOnScopeDispose(dispose);\n    return state;\n  };\n}\n\nfunction extendRef(ref, extend, { enumerable = false, unwrap = true } = {}) {\n  __onlyVue27Plus();\n  for (const [key, value] of Object.entries(extend)) {\n    if (key === \"value\")\n      continue;\n    if (isRef(value) && unwrap) {\n      Object.defineProperty(ref, key, {\n        get() {\n          return value.value;\n        },\n        set(v) {\n          value.value = v;\n        },\n        enumerable\n      });\n    } else {\n      Object.defineProperty(ref, key, { value, enumerable });\n    }\n  }\n  return ref;\n}\n\nfunction get(obj, key) {\n  if (key == null)\n    return unref(obj);\n  return unref(obj)[key];\n}\n\nfunction isDefined(v) {\n  return unref(v) != null;\n}\n\nvar __defProp$8 = Object.defineProperty;\nvar __getOwnPropSymbols$a = Object.getOwnPropertySymbols;\nvar __hasOwnProp$a = Object.prototype.hasOwnProperty;\nvar __propIsEnum$a = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$8 = (obj, key, value) => key in obj ? __defProp$8(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$8 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$a.call(b, prop))\n      __defNormalProp$8(a, prop, b[prop]);\n  if (__getOwnPropSymbols$a)\n    for (var prop of __getOwnPropSymbols$a(b)) {\n      if (__propIsEnum$a.call(b, prop))\n        __defNormalProp$8(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction makeDestructurable(obj, arr) {\n  if (typeof Symbol !== \"undefined\") {\n    const clone = __spreadValues$8({}, obj);\n    Object.defineProperty(clone, Symbol.iterator, {\n      enumerable: false,\n      value() {\n        let index = 0;\n        return {\n          next: () => ({\n            value: arr[index++],\n            done: index > arr.length\n          })\n        };\n      }\n    });\n    return clone;\n  } else {\n    return Object.assign([...arr], obj);\n  }\n}\n\nfunction reactify(fn, options) {\n  const unrefFn = (options == null ? void 0 : options.computedGetter) === false ? unref : resolveUnref;\n  return function(...args) {\n    return computed(() => fn.apply(this, args.map((i) => unrefFn(i))));\n  };\n}\n\nfunction reactifyObject(obj, optionsOrKeys = {}) {\n  let keys = [];\n  let options;\n  if (Array.isArray(optionsOrKeys)) {\n    keys = optionsOrKeys;\n  } else {\n    options = optionsOrKeys;\n    const { includeOwnProperties = true } = optionsOrKeys;\n    keys.push(...Object.keys(obj));\n    if (includeOwnProperties)\n      keys.push(...Object.getOwnPropertyNames(obj));\n  }\n  return Object.fromEntries(keys.map((key) => {\n    const value = obj[key];\n    return [\n      key,\n      typeof value === \"function\" ? reactify(value.bind(obj), options) : value\n    ];\n  }));\n}\n\nfunction toReactive(objectRef) {\n  if (!isRef(objectRef))\n    return reactive(objectRef);\n  const proxy = new Proxy({}, {\n    get(_, p, receiver) {\n      return unref(Reflect.get(objectRef.value, p, receiver));\n    },\n    set(_, p, value) {\n      if (isRef(objectRef.value[p]) && !isRef(value))\n        objectRef.value[p].value = value;\n      else\n        objectRef.value[p] = value;\n      return true;\n    },\n    deleteProperty(_, p) {\n      return Reflect.deleteProperty(objectRef.value, p);\n    },\n    has(_, p) {\n      return Reflect.has(objectRef.value, p);\n    },\n    ownKeys() {\n      return Object.keys(objectRef.value);\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  });\n  return reactive(proxy);\n}\n\nfunction reactiveComputed(fn) {\n  return toReactive(computed(fn));\n}\n\nfunction reactiveOmit(obj, ...keys) {\n  const flatKeys = keys.flat();\n  return reactiveComputed(() => Object.fromEntries(Object.entries(toRefs$1(obj)).filter((e) => !flatKeys.includes(e[0]))));\n}\n\nfunction reactivePick(obj, ...keys) {\n  const flatKeys = keys.flat();\n  return reactive(Object.fromEntries(flatKeys.map((k) => [k, toRef(obj, k)])));\n}\n\nfunction refAutoReset(defaultValue, afterMs = 1e4) {\n  return customRef((track, trigger) => {\n    let value = defaultValue;\n    let timer;\n    const resetAfter = () => setTimeout(() => {\n      value = defaultValue;\n      trigger();\n    }, resolveUnref(afterMs));\n    tryOnScopeDispose(() => {\n      clearTimeout(timer);\n    });\n    return {\n      get() {\n        track();\n        return value;\n      },\n      set(newValue) {\n        value = newValue;\n        trigger();\n        clearTimeout(timer);\n        timer = resetAfter();\n      }\n    };\n  });\n}\n\nfunction useDebounceFn(fn, ms = 200, options = {}) {\n  return createFilterWrapper(debounceFilter(ms, options), fn);\n}\n\nfunction refDebounced(value, ms = 200, options = {}) {\n  const debounced = ref(value.value);\n  const updater = useDebounceFn(() => {\n    debounced.value = value.value;\n  }, ms, options);\n  watch(value, () => updater());\n  return debounced;\n}\n\nfunction refDefault(source, defaultValue) {\n  return computed({\n    get() {\n      var _a;\n      return (_a = source.value) != null ? _a : defaultValue;\n    },\n    set(value) {\n      source.value = value;\n    }\n  });\n}\n\nfunction useThrottleFn(fn, ms = 200, trailing = false, leading = true, rejectOnCancel = false) {\n  return createFilterWrapper(throttleFilter(ms, trailing, leading, rejectOnCancel), fn);\n}\n\nfunction refThrottled(value, delay = 200, trailing = true, leading = true) {\n  if (delay <= 0)\n    return value;\n  const throttled = ref(value.value);\n  const updater = useThrottleFn(() => {\n    throttled.value = value.value;\n  }, delay, trailing, leading);\n  watch(value, () => updater());\n  return throttled;\n}\n\nfunction refWithControl(initial, options = {}) {\n  let source = initial;\n  let track;\n  let trigger;\n  const ref = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        return get();\n      },\n      set(v) {\n        set(v);\n      }\n    };\n  });\n  function get(tracking = true) {\n    if (tracking)\n      track();\n    return source;\n  }\n  function set(value, triggering = true) {\n    var _a, _b;\n    if (value === source)\n      return;\n    const old = source;\n    if (((_a = options.onBeforeChange) == null ? void 0 : _a.call(options, value, old)) === false)\n      return;\n    source = value;\n    (_b = options.onChanged) == null ? void 0 : _b.call(options, value, old);\n    if (triggering)\n      trigger();\n  }\n  const untrackedGet = () => get(false);\n  const silentSet = (v) => set(v, false);\n  const peek = () => get(false);\n  const lay = (v) => set(v, false);\n  return extendRef(ref, {\n    get,\n    set,\n    untrackedGet,\n    silentSet,\n    peek,\n    lay\n  }, { enumerable: true });\n}\nconst controlledRef = refWithControl;\n\nfunction resolveRef(r) {\n  return typeof r === \"function\" ? computed(r) : ref(r);\n}\n\nfunction set(...args) {\n  if (args.length === 2) {\n    const [ref, value] = args;\n    ref.value = value;\n  }\n  if (args.length === 3) {\n    if (isVue2) {\n      set$1(...args);\n    } else {\n      const [target, key, value] = args;\n      target[key] = value;\n    }\n  }\n}\n\nfunction syncRef(left, right, options = {}) {\n  var _a, _b;\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true,\n    direction = \"both\",\n    transform = {}\n  } = options;\n  let watchLeft;\n  let watchRight;\n  const transformLTR = (_a = transform.ltr) != null ? _a : (v) => v;\n  const transformRTL = (_b = transform.rtl) != null ? _b : (v) => v;\n  if (direction === \"both\" || direction === \"ltr\") {\n    watchLeft = watch(left, (newValue) => right.value = transformLTR(newValue), { flush, deep, immediate });\n  }\n  if (direction === \"both\" || direction === \"rtl\") {\n    watchRight = watch(right, (newValue) => left.value = transformRTL(newValue), { flush, deep, immediate });\n  }\n  return () => {\n    watchLeft == null ? void 0 : watchLeft();\n    watchRight == null ? void 0 : watchRight();\n  };\n}\n\nfunction syncRefs(source, targets, options = {}) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true\n  } = options;\n  if (!Array.isArray(targets))\n    targets = [targets];\n  return watch(source, (newValue) => targets.forEach((target) => target.value = newValue), { flush, deep, immediate });\n}\n\nvar __defProp$7 = Object.defineProperty;\nvar __defProps$5 = Object.defineProperties;\nvar __getOwnPropDescs$5 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$9 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$9 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$9 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$7 = (obj, key, value) => key in obj ? __defProp$7(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$7 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$9.call(b, prop))\n      __defNormalProp$7(a, prop, b[prop]);\n  if (__getOwnPropSymbols$9)\n    for (var prop of __getOwnPropSymbols$9(b)) {\n      if (__propIsEnum$9.call(b, prop))\n        __defNormalProp$7(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$5 = (a, b) => __defProps$5(a, __getOwnPropDescs$5(b));\nfunction toRefs(objectRef) {\n  if (!isRef(objectRef))\n    return toRefs$1(objectRef);\n  const result = Array.isArray(objectRef.value) ? new Array(objectRef.value.length) : {};\n  for (const key in objectRef.value) {\n    result[key] = customRef(() => ({\n      get() {\n        return objectRef.value[key];\n      },\n      set(v) {\n        if (Array.isArray(objectRef.value)) {\n          const copy = [...objectRef.value];\n          copy[key] = v;\n          objectRef.value = copy;\n        } else {\n          const newObject = __spreadProps$5(__spreadValues$7({}, objectRef.value), { [key]: v });\n          Object.setPrototypeOf(newObject, objectRef.value);\n          objectRef.value = newObject;\n        }\n      }\n    }));\n  }\n  return result;\n}\n\nfunction tryOnBeforeMount(fn, sync = true) {\n  if (getCurrentInstance())\n    onBeforeMount(fn);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnBeforeUnmount(fn) {\n  if (getCurrentInstance())\n    onBeforeUnmount(fn);\n}\n\nfunction tryOnMounted(fn, sync = true) {\n  if (getCurrentInstance())\n    onMounted(fn);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnUnmounted(fn) {\n  if (getCurrentInstance())\n    onUnmounted(fn);\n}\n\nfunction createUntil(r, isNot = false) {\n  function toMatch(condition, { flush = \"sync\", deep = false, timeout, throwOnTimeout } = {}) {\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(r, (v) => {\n        if (condition(v) !== isNot) {\n          stop == null ? void 0 : stop();\n          resolve(v);\n        }\n      }, {\n        flush,\n        deep,\n        immediate: true\n      });\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(promiseTimeout(timeout, throwOnTimeout).then(() => resolveUnref(r)).finally(() => stop == null ? void 0 : stop()));\n    }\n    return Promise.race(promises);\n  }\n  function toBe(value, options) {\n    if (!isRef(value))\n      return toMatch((v) => v === value, options);\n    const { flush = \"sync\", deep = false, timeout, throwOnTimeout } = options != null ? options : {};\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch([r, value], ([v1, v2]) => {\n        if (isNot !== (v1 === v2)) {\n          stop == null ? void 0 : stop();\n          resolve(v1);\n        }\n      }, {\n        flush,\n        deep,\n        immediate: true\n      });\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(promiseTimeout(timeout, throwOnTimeout).then(() => resolveUnref(r)).finally(() => {\n        stop == null ? void 0 : stop();\n        return resolveUnref(r);\n      }));\n    }\n    return Promise.race(promises);\n  }\n  function toBeTruthy(options) {\n    return toMatch((v) => Boolean(v), options);\n  }\n  function toBeNull(options) {\n    return toBe(null, options);\n  }\n  function toBeUndefined(options) {\n    return toBe(void 0, options);\n  }\n  function toBeNaN(options) {\n    return toMatch(Number.isNaN, options);\n  }\n  function toContains(value, options) {\n    return toMatch((v) => {\n      const array = Array.from(v);\n      return array.includes(value) || array.includes(resolveUnref(value));\n    }, options);\n  }\n  function changed(options) {\n    return changedTimes(1, options);\n  }\n  function changedTimes(n = 1, options) {\n    let count = -1;\n    return toMatch(() => {\n      count += 1;\n      return count >= n;\n    }, options);\n  }\n  if (Array.isArray(resolveUnref(r))) {\n    const instance = {\n      toMatch,\n      toContains,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  } else {\n    const instance = {\n      toMatch,\n      toBe,\n      toBeTruthy,\n      toBeNull,\n      toBeNaN,\n      toBeUndefined,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  }\n}\nfunction until(r) {\n  return createUntil(r);\n}\n\nfunction useArrayEvery(list, fn) {\n  return computed(() => resolveUnref(list).every((element, index, array) => fn(resolveUnref(element), index, array)));\n}\n\nfunction useArrayFilter(list, fn) {\n  return computed(() => resolveUnref(list).map((i) => resolveUnref(i)).filter(fn));\n}\n\nfunction useArrayFind(list, fn) {\n  return computed(() => resolveUnref(resolveUnref(list).find((element, index, array) => fn(resolveUnref(element), index, array))));\n}\n\nfunction useArrayFindIndex(list, fn) {\n  return computed(() => resolveUnref(list).findIndex((element, index, array) => fn(resolveUnref(element), index, array)));\n}\n\nfunction findLast(arr, cb) {\n  let index = arr.length;\n  while (index-- > 0) {\n    if (cb(arr[index], index, arr))\n      return arr[index];\n  }\n  return void 0;\n}\nfunction useArrayFindLast(list, fn) {\n  return computed(() => resolveUnref(!Array.prototype.findLast ? findLast(resolveUnref(list), (element, index, array) => fn(resolveUnref(element), index, array)) : resolveUnref(list).findLast((element, index, array) => fn(resolveUnref(element), index, array))));\n}\n\nfunction useArrayJoin(list, separator) {\n  return computed(() => resolveUnref(list).map((i) => resolveUnref(i)).join(resolveUnref(separator)));\n}\n\nfunction useArrayMap(list, fn) {\n  return computed(() => resolveUnref(list).map((i) => resolveUnref(i)).map(fn));\n}\n\nfunction useArrayReduce(list, reducer, ...args) {\n  const reduceCallback = (sum, value, index) => reducer(resolveUnref(sum), resolveUnref(value), index);\n  return computed(() => {\n    const resolved = resolveUnref(list);\n    return args.length ? resolved.reduce(reduceCallback, resolveUnref(args[0])) : resolved.reduce(reduceCallback);\n  });\n}\n\nfunction useArraySome(list, fn) {\n  return computed(() => resolveUnref(list).some((element, index, array) => fn(resolveUnref(element), index, array)));\n}\n\nfunction useArrayUnique(list) {\n  return computed(() => [...new Set(resolveUnref(list).map((element) => resolveUnref(element)))]);\n}\n\nfunction useCounter(initialValue = 0, options = {}) {\n  const count = ref(initialValue);\n  const {\n    max = Infinity,\n    min = -Infinity\n  } = options;\n  const inc = (delta = 1) => count.value = Math.min(max, count.value + delta);\n  const dec = (delta = 1) => count.value = Math.max(min, count.value - delta);\n  const get = () => count.value;\n  const set = (val) => count.value = Math.max(min, Math.min(max, val));\n  const reset = (val = initialValue) => {\n    initialValue = val;\n    return set(val);\n  };\n  return { count, inc, dec, get, set, reset };\n}\n\nconst REGEX_PARSE = /^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[Tt\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/;\nconst REGEX_FORMAT = /\\[([^\\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|SSS/g;\nconst defaultMeridiem = (hours, minutes, isLowercase, hasPeriod) => {\n  let m = hours < 12 ? \"AM\" : \"PM\";\n  if (hasPeriod)\n    m = m.split(\"\").reduce((acc, curr) => acc += `${curr}.`, \"\");\n  return isLowercase ? m.toLowerCase() : m;\n};\nconst formatDate = (date, formatStr, options = {}) => {\n  var _a;\n  const years = date.getFullYear();\n  const month = date.getMonth();\n  const days = date.getDate();\n  const hours = date.getHours();\n  const minutes = date.getMinutes();\n  const seconds = date.getSeconds();\n  const milliseconds = date.getMilliseconds();\n  const day = date.getDay();\n  const meridiem = (_a = options.customMeridiem) != null ? _a : defaultMeridiem;\n  const matches = {\n    YY: () => String(years).slice(-2),\n    YYYY: () => years,\n    M: () => month + 1,\n    MM: () => `${month + 1}`.padStart(2, \"0\"),\n    MMM: () => date.toLocaleDateString(options.locales, { month: \"short\" }),\n    MMMM: () => date.toLocaleDateString(options.locales, { month: \"long\" }),\n    D: () => String(days),\n    DD: () => `${days}`.padStart(2, \"0\"),\n    H: () => String(hours),\n    HH: () => `${hours}`.padStart(2, \"0\"),\n    h: () => `${hours % 12 || 12}`.padStart(1, \"0\"),\n    hh: () => `${hours % 12 || 12}`.padStart(2, \"0\"),\n    m: () => String(minutes),\n    mm: () => `${minutes}`.padStart(2, \"0\"),\n    s: () => String(seconds),\n    ss: () => `${seconds}`.padStart(2, \"0\"),\n    SSS: () => `${milliseconds}`.padStart(3, \"0\"),\n    d: () => day,\n    dd: () => date.toLocaleDateString(options.locales, { weekday: \"narrow\" }),\n    ddd: () => date.toLocaleDateString(options.locales, { weekday: \"short\" }),\n    dddd: () => date.toLocaleDateString(options.locales, { weekday: \"long\" }),\n    A: () => meridiem(hours, minutes),\n    AA: () => meridiem(hours, minutes, false, true),\n    a: () => meridiem(hours, minutes, true),\n    aa: () => meridiem(hours, minutes, true, true)\n  };\n  return formatStr.replace(REGEX_FORMAT, (match, $1) => $1 || matches[match]());\n};\nconst normalizeDate = (date) => {\n  if (date === null)\n    return new Date(NaN);\n  if (date === void 0)\n    return new Date();\n  if (date instanceof Date)\n    return new Date(date);\n  if (typeof date === \"string\" && !/Z$/i.test(date)) {\n    const d = date.match(REGEX_PARSE);\n    if (d) {\n      const m = d[2] - 1 || 0;\n      const ms = (d[7] || \"0\").substring(0, 3);\n      return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);\n    }\n  }\n  return new Date(date);\n};\nfunction useDateFormat(date, formatStr = \"HH:mm:ss\", options = {}) {\n  return computed(() => formatDate(normalizeDate(resolveUnref(date)), resolveUnref(formatStr), options));\n}\n\nfunction useIntervalFn(cb, interval = 1e3, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  let timer = null;\n  const isActive = ref(false);\n  function clean() {\n    if (timer) {\n      clearInterval(timer);\n      timer = null;\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    clean();\n  }\n  function resume() {\n    const intervalValue = resolveUnref(interval);\n    if (intervalValue <= 0)\n      return;\n    isActive.value = true;\n    if (immediateCallback)\n      cb();\n    clean();\n    timer = setInterval(cb, intervalValue);\n  }\n  if (immediate && isClient)\n    resume();\n  if (isRef(interval) || isFunction(interval)) {\n    const stopWatch = watch(interval, () => {\n      if (isActive.value && isClient)\n        resume();\n    });\n    tryOnScopeDispose(stopWatch);\n  }\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nvar __defProp$6 = Object.defineProperty;\nvar __getOwnPropSymbols$8 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$8 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$8 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$6 = (obj, key, value) => key in obj ? __defProp$6(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$6 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$8.call(b, prop))\n      __defNormalProp$6(a, prop, b[prop]);\n  if (__getOwnPropSymbols$8)\n    for (var prop of __getOwnPropSymbols$8(b)) {\n      if (__propIsEnum$8.call(b, prop))\n        __defNormalProp$6(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useInterval(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    immediate = true,\n    callback\n  } = options;\n  const counter = ref(0);\n  const update = () => counter.value += 1;\n  const reset = () => {\n    counter.value = 0;\n  };\n  const controls = useIntervalFn(callback ? () => {\n    update();\n    callback(counter.value);\n  } : update, interval, { immediate });\n  if (exposeControls) {\n    return __spreadValues$6({\n      counter,\n      reset\n    }, controls);\n  } else {\n    return counter;\n  }\n}\n\nfunction useLastChanged(source, options = {}) {\n  var _a;\n  const ms = ref((_a = options.initialValue) != null ? _a : null);\n  watch(source, () => ms.value = timestamp(), options);\n  return ms;\n}\n\nfunction useTimeoutFn(cb, interval, options = {}) {\n  const {\n    immediate = true\n  } = options;\n  const isPending = ref(false);\n  let timer = null;\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function stop() {\n    isPending.value = false;\n    clear();\n  }\n  function start(...args) {\n    clear();\n    isPending.value = true;\n    timer = setTimeout(() => {\n      isPending.value = false;\n      timer = null;\n      cb(...args);\n    }, resolveUnref(interval));\n  }\n  if (immediate) {\n    isPending.value = true;\n    if (isClient)\n      start();\n  }\n  tryOnScopeDispose(stop);\n  return {\n    isPending: readonly(isPending),\n    start,\n    stop\n  };\n}\n\nvar __defProp$5 = Object.defineProperty;\nvar __getOwnPropSymbols$7 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$7 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$7 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$5 = (obj, key, value) => key in obj ? __defProp$5(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$5 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$7.call(b, prop))\n      __defNormalProp$5(a, prop, b[prop]);\n  if (__getOwnPropSymbols$7)\n    for (var prop of __getOwnPropSymbols$7(b)) {\n      if (__propIsEnum$7.call(b, prop))\n        __defNormalProp$5(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useTimeout(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    callback\n  } = options;\n  const controls = useTimeoutFn(callback != null ? callback : noop, interval, options);\n  const ready = computed(() => !controls.isPending.value);\n  if (exposeControls) {\n    return __spreadValues$5({\n      ready\n    }, controls);\n  } else {\n    return ready;\n  }\n}\n\nfunction useToNumber(value, options = {}) {\n  const {\n    method = \"parseFloat\",\n    radix,\n    nanToZero\n  } = options;\n  return computed(() => {\n    let resolved = resolveUnref(value);\n    if (typeof resolved === \"string\")\n      resolved = Number[method](resolved, radix);\n    if (nanToZero && isNaN(resolved))\n      resolved = 0;\n    return resolved;\n  });\n}\n\nfunction useToString(value) {\n  return computed(() => `${resolveUnref(value)}`);\n}\n\nfunction useToggle(initialValue = false, options = {}) {\n  const {\n    truthyValue = true,\n    falsyValue = false\n  } = options;\n  const valueIsRef = isRef(initialValue);\n  const _value = ref(initialValue);\n  function toggle(value) {\n    if (arguments.length) {\n      _value.value = value;\n      return _value.value;\n    } else {\n      const truthy = resolveUnref(truthyValue);\n      _value.value = _value.value === truthy ? resolveUnref(falsyValue) : truthy;\n      return _value.value;\n    }\n  }\n  if (valueIsRef)\n    return toggle;\n  else\n    return [_value, toggle];\n}\n\nfunction watchArray(source, cb, options) {\n  let oldList = (options == null ? void 0 : options.immediate) ? [] : [\n    ...source instanceof Function ? source() : Array.isArray(source) ? source : unref(source)\n  ];\n  return watch(source, (newList, _, onCleanup) => {\n    const oldListRemains = new Array(oldList.length);\n    const added = [];\n    for (const obj of newList) {\n      let found = false;\n      for (let i = 0; i < oldList.length; i++) {\n        if (!oldListRemains[i] && obj === oldList[i]) {\n          oldListRemains[i] = true;\n          found = true;\n          break;\n        }\n      }\n      if (!found)\n        added.push(obj);\n    }\n    const removed = oldList.filter((_2, i) => !oldListRemains[i]);\n    cb(newList, oldList, added, removed, onCleanup);\n    oldList = [...newList];\n  }, options);\n}\n\nvar __getOwnPropSymbols$6 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$6 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$6 = Object.prototype.propertyIsEnumerable;\nvar __objRest$5 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$6.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$6)\n    for (var prop of __getOwnPropSymbols$6(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$6.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchWithFilter(source, cb, options = {}) {\n  const _a = options, {\n    eventFilter = bypassFilter\n  } = _a, watchOptions = __objRest$5(_a, [\n    \"eventFilter\"\n  ]);\n  return watch(source, createFilterWrapper(eventFilter, cb), watchOptions);\n}\n\nvar __getOwnPropSymbols$5 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$5 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$5 = Object.prototype.propertyIsEnumerable;\nvar __objRest$4 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$5.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$5)\n    for (var prop of __getOwnPropSymbols$5(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$5.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchAtMost(source, cb, options) {\n  const _a = options, {\n    count\n  } = _a, watchOptions = __objRest$4(_a, [\n    \"count\"\n  ]);\n  const current = ref(0);\n  const stop = watchWithFilter(source, (...args) => {\n    current.value += 1;\n    if (current.value >= resolveUnref(count))\n      nextTick(() => stop());\n    cb(...args);\n  }, watchOptions);\n  return { count: current, stop };\n}\n\nvar __defProp$4 = Object.defineProperty;\nvar __defProps$4 = Object.defineProperties;\nvar __getOwnPropDescs$4 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$4 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$4 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$4 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$4 = (obj, key, value) => key in obj ? __defProp$4(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$4 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$4.call(b, prop))\n      __defNormalProp$4(a, prop, b[prop]);\n  if (__getOwnPropSymbols$4)\n    for (var prop of __getOwnPropSymbols$4(b)) {\n      if (__propIsEnum$4.call(b, prop))\n        __defNormalProp$4(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$4 = (a, b) => __defProps$4(a, __getOwnPropDescs$4(b));\nvar __objRest$3 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$4.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$4)\n    for (var prop of __getOwnPropSymbols$4(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$4.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchDebounced(source, cb, options = {}) {\n  const _a = options, {\n    debounce = 0,\n    maxWait = void 0\n  } = _a, watchOptions = __objRest$3(_a, [\n    \"debounce\",\n    \"maxWait\"\n  ]);\n  return watchWithFilter(source, cb, __spreadProps$4(__spreadValues$4({}, watchOptions), {\n    eventFilter: debounceFilter(debounce, { maxWait })\n  }));\n}\n\nvar __defProp$3 = Object.defineProperty;\nvar __defProps$3 = Object.defineProperties;\nvar __getOwnPropDescs$3 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$3 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$3 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$3 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$3 = (obj, key, value) => key in obj ? __defProp$3(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$3 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$3.call(b, prop))\n      __defNormalProp$3(a, prop, b[prop]);\n  if (__getOwnPropSymbols$3)\n    for (var prop of __getOwnPropSymbols$3(b)) {\n      if (__propIsEnum$3.call(b, prop))\n        __defNormalProp$3(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$3 = (a, b) => __defProps$3(a, __getOwnPropDescs$3(b));\nvar __objRest$2 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$3.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$3)\n    for (var prop of __getOwnPropSymbols$3(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$3.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchIgnorable(source, cb, options = {}) {\n  const _a = options, {\n    eventFilter = bypassFilter\n  } = _a, watchOptions = __objRest$2(_a, [\n    \"eventFilter\"\n  ]);\n  const filteredCb = createFilterWrapper(eventFilter, cb);\n  let ignoreUpdates;\n  let ignorePrevAsyncUpdates;\n  let stop;\n  if (watchOptions.flush === \"sync\") {\n    const ignore = ref(false);\n    ignorePrevAsyncUpdates = () => {\n    };\n    ignoreUpdates = (updater) => {\n      ignore.value = true;\n      updater();\n      ignore.value = false;\n    };\n    stop = watch(source, (...args) => {\n      if (!ignore.value)\n        filteredCb(...args);\n    }, watchOptions);\n  } else {\n    const disposables = [];\n    const ignoreCounter = ref(0);\n    const syncCounter = ref(0);\n    ignorePrevAsyncUpdates = () => {\n      ignoreCounter.value = syncCounter.value;\n    };\n    disposables.push(watch(source, () => {\n      syncCounter.value++;\n    }, __spreadProps$3(__spreadValues$3({}, watchOptions), { flush: \"sync\" })));\n    ignoreUpdates = (updater) => {\n      const syncCounterPrev = syncCounter.value;\n      updater();\n      ignoreCounter.value += syncCounter.value - syncCounterPrev;\n    };\n    disposables.push(watch(source, (...args) => {\n      const ignore = ignoreCounter.value > 0 && ignoreCounter.value === syncCounter.value;\n      ignoreCounter.value = 0;\n      syncCounter.value = 0;\n      if (ignore)\n        return;\n      filteredCb(...args);\n    }, watchOptions));\n    stop = () => {\n      disposables.forEach((fn) => fn());\n    };\n  }\n  return { stop, ignoreUpdates, ignorePrevAsyncUpdates };\n}\n\nfunction watchOnce(source, cb, options) {\n  const stop = watch(source, (...args) => {\n    nextTick(() => stop());\n    return cb(...args);\n  }, options);\n}\n\nvar __defProp$2 = Object.defineProperty;\nvar __defProps$2 = Object.defineProperties;\nvar __getOwnPropDescs$2 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$2 = (obj, key, value) => key in obj ? __defProp$2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$2 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$2.call(b, prop))\n      __defNormalProp$2(a, prop, b[prop]);\n  if (__getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(b)) {\n      if (__propIsEnum$2.call(b, prop))\n        __defNormalProp$2(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$2 = (a, b) => __defProps$2(a, __getOwnPropDescs$2(b));\nvar __objRest$1 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$2.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$2.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchPausable(source, cb, options = {}) {\n  const _a = options, {\n    eventFilter: filter\n  } = _a, watchOptions = __objRest$1(_a, [\n    \"eventFilter\"\n  ]);\n  const { eventFilter, pause, resume, isActive } = pausableFilter(filter);\n  const stop = watchWithFilter(source, cb, __spreadProps$2(__spreadValues$2({}, watchOptions), {\n    eventFilter\n  }));\n  return { stop, pause, resume, isActive };\n}\n\nvar __defProp$1 = Object.defineProperty;\nvar __defProps$1 = Object.defineProperties;\nvar __getOwnPropDescs$1 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$1 = (a, b) => __defProps$1(a, __getOwnPropDescs$1(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$1.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$1.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchThrottled(source, cb, options = {}) {\n  const _a = options, {\n    throttle = 0,\n    trailing = true,\n    leading = true\n  } = _a, watchOptions = __objRest(_a, [\n    \"throttle\",\n    \"trailing\",\n    \"leading\"\n  ]);\n  return watchWithFilter(source, cb, __spreadProps$1(__spreadValues$1({}, watchOptions), {\n    eventFilter: throttleFilter(throttle, trailing, leading)\n  }));\n}\n\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nfunction watchTriggerable(source, cb, options = {}) {\n  let cleanupFn;\n  function onEffect() {\n    if (!cleanupFn)\n      return;\n    const fn = cleanupFn;\n    cleanupFn = void 0;\n    fn();\n  }\n  function onCleanup(callback) {\n    cleanupFn = callback;\n  }\n  const _cb = (value, oldValue) => {\n    onEffect();\n    return cb(value, oldValue, onCleanup);\n  };\n  const res = watchIgnorable(source, _cb, options);\n  const { ignoreUpdates } = res;\n  const trigger = () => {\n    let res2;\n    ignoreUpdates(() => {\n      res2 = _cb(getWatchSources(source), getOldValue(source));\n    });\n    return res2;\n  };\n  return __spreadProps(__spreadValues({}, res), {\n    trigger\n  });\n}\nfunction getWatchSources(sources) {\n  if (isReactive(sources))\n    return sources;\n  if (Array.isArray(sources))\n    return sources.map((item) => getOneWatchSource(item));\n  return getOneWatchSource(sources);\n}\nfunction getOneWatchSource(source) {\n  return typeof source === \"function\" ? source() : unref(source);\n}\nfunction getOldValue(source) {\n  return Array.isArray(source) ? source.map(() => void 0) : void 0;\n}\n\nfunction whenever(source, cb, options) {\n  return watch(source, (v, ov, onInvalidate) => {\n    if (v)\n      cb(v, ov, onInvalidate);\n  }, options);\n}\n\nexport { __onlyVue27Plus, __onlyVue3, assert, refAutoReset as autoResetRef, bypassFilter, clamp, computedEager, computedWithControl, containsProp, computedWithControl as controlledComputed, controlledRef, createEventHook, createFilterWrapper, createGlobalState, createInjectionState, reactify as createReactiveFn, createSharedComposable, createSingletonPromise, debounceFilter, refDebounced as debouncedRef, watchDebounced as debouncedWatch, directiveHooks, computedEager as eagerComputed, extendRef, formatDate, get, hasOwn, identity, watchIgnorable as ignorableWatch, increaseWithUnit, invoke, isBoolean, isClient, isDef, isDefined, isFunction, isIOS, isNumber, isObject, isString, isWindow, makeDestructurable, noop, normalizeDate, now, objectPick, pausableFilter, watchPausable as pausableWatch, promiseTimeout, rand, reactify, reactifyObject, reactiveComputed, reactiveOmit, reactivePick, refAutoReset, refDebounced, refDefault, refThrottled, refWithControl, resolveRef, resolveUnref, set, syncRef, syncRefs, throttleFilter, refThrottled as throttledRef, watchThrottled as throttledWatch, timestamp, toReactive, toRefs, tryOnBeforeMount, tryOnBeforeUnmount, tryOnMounted, tryOnScopeDispose, tryOnUnmounted, until, useArrayEvery, useArrayFilter, useArrayFind, useArrayFindIndex, useArrayFindLast, useArrayJoin, useArrayMap, useArrayReduce, useArraySome, useArrayUnique, useCounter, useDateFormat, refDebounced as useDebounce, useDebounceFn, useInterval, useIntervalFn, useLastChanged, refThrottled as useThrottle, useThrottleFn, useTimeout, useTimeoutFn, useToNumber, useToString, useToggle, watchArray, watchAtMost, watchDebounced, watchIgnorable, watchOnce, watchPausable, watchThrottled, watchTriggerable, watchWithFilter, whenever };\n", "import { noop, resolveUnref, isClient, isString, tryOnScopeDispose, isIOS, tryOnMounted, computedWithControl, promiseTimeout, isFunction, resolveRef, increaseWithUnit, useTimeoutFn, pausableWatch, createEventHook, timestamp, pausableFilter, watchIgnorable, debounceFilter, createFilterWrapper, bypassFilter, createSingletonPromise, toRefs, containsProp, until, hasOwn, throttleFilter, useDebounceFn, useThrottleFn, isObject, isNumber, useIntervalFn, clamp, syncRef, objectPick, tryOnUnmounted, watchWithFilter, identity, isDef } from '@vueuse/shared';\nexport * from '@vueuse/shared';\nimport { isRef, ref, shallowRef, watchEffect, computed, inject, unref, watch, getCurrentInstance, customRef, onUpdated, reactive, nextTick, onMounted, markRaw, readonly, getCurrentScope, isVue2, set, del, isReadonly, onBeforeUpdate } from 'vue-demi';\n\nfunction computedAsync(evaluationCallback, initialState, optionsOrRef) {\n  let options;\n  if (isRef(optionsOrRef)) {\n    options = {\n      evaluating: optionsOrRef\n    };\n  } else {\n    options = optionsOrRef || {};\n  }\n  const {\n    lazy = false,\n    evaluating = void 0,\n    shallow = false,\n    onError = noop\n  } = options;\n  const started = ref(!lazy);\n  const current = shallow ? shallowRef(initialState) : ref(initialState);\n  let counter = 0;\n  watchEffect(async (onInvalidate) => {\n    if (!started.value)\n      return;\n    counter++;\n    const counterAtBeginning = counter;\n    let hasFinished = false;\n    if (evaluating) {\n      Promise.resolve().then(() => {\n        evaluating.value = true;\n      });\n    }\n    try {\n      const result = await evaluationCallback((cancelCallback) => {\n        onInvalidate(() => {\n          if (evaluating)\n            evaluating.value = false;\n          if (!hasFinished)\n            cancelCallback();\n        });\n      });\n      if (counterAtBeginning === counter)\n        current.value = result;\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (evaluating && counterAtBeginning === counter)\n        evaluating.value = false;\n      hasFinished = true;\n    }\n  });\n  if (lazy) {\n    return computed(() => {\n      started.value = true;\n      return current.value;\n    });\n  } else {\n    return current;\n  }\n}\n\nfunction computedInject(key, options, defaultSource, treatDefaultAsFactory) {\n  let source = inject(key);\n  if (defaultSource)\n    source = inject(key, defaultSource);\n  if (treatDefaultAsFactory)\n    source = inject(key, defaultSource, treatDefaultAsFactory);\n  if (typeof options === \"function\") {\n    return computed((ctx) => options(source, ctx));\n  } else {\n    return computed({\n      get: (ctx) => options.get(source, ctx),\n      set: options.set\n    });\n  }\n}\n\nconst createUnrefFn = (fn) => {\n  return function(...args) {\n    return fn.apply(this, args.map((i) => unref(i)));\n  };\n};\n\nfunction unrefElement(elRef) {\n  var _a;\n  const plain = resolveUnref(elRef);\n  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;\n}\n\nconst defaultWindow = isClient ? window : void 0;\nconst defaultDocument = isClient ? window.document : void 0;\nconst defaultNavigator = isClient ? window.navigator : void 0;\nconst defaultLocation = isClient ? window.location : void 0;\n\nfunction useEventListener(...args) {\n  let target;\n  let events;\n  let listeners;\n  let options;\n  if (isString(args[0]) || Array.isArray(args[0])) {\n    [events, listeners, options] = args;\n    target = defaultWindow;\n  } else {\n    [target, events, listeners, options] = args;\n  }\n  if (!target)\n    return noop;\n  if (!Array.isArray(events))\n    events = [events];\n  if (!Array.isArray(listeners))\n    listeners = [listeners];\n  const cleanups = [];\n  const cleanup = () => {\n    cleanups.forEach((fn) => fn());\n    cleanups.length = 0;\n  };\n  const register = (el, event, listener, options2) => {\n    el.addEventListener(event, listener, options2);\n    return () => el.removeEventListener(event, listener, options2);\n  };\n  const stopWatch = watch(() => [unrefElement(target), resolveUnref(options)], ([el, options2]) => {\n    cleanup();\n    if (!el)\n      return;\n    cleanups.push(...events.flatMap((event) => {\n      return listeners.map((listener) => register(el, event, listener, options2));\n    }));\n  }, { immediate: true, flush: \"post\" });\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(stop);\n  return stop;\n}\n\nlet _iOSWorkaround = false;\nfunction onClickOutside(target, handler, options = {}) {\n  const { window = defaultWindow, ignore = [], capture = true, detectIframe = false } = options;\n  if (!window)\n    return;\n  if (isIOS && !_iOSWorkaround) {\n    _iOSWorkaround = true;\n    Array.from(window.document.body.children).forEach((el) => el.addEventListener(\"click\", noop));\n  }\n  let shouldListen = true;\n  const shouldIgnore = (event) => {\n    return ignore.some((target2) => {\n      if (typeof target2 === \"string\") {\n        return Array.from(window.document.querySelectorAll(target2)).some((el) => el === event.target || event.composedPath().includes(el));\n      } else {\n        const el = unrefElement(target2);\n        return el && (event.target === el || event.composedPath().includes(el));\n      }\n    });\n  };\n  const listener = (event) => {\n    const el = unrefElement(target);\n    if (!el || el === event.target || event.composedPath().includes(el))\n      return;\n    if (event.detail === 0)\n      shouldListen = !shouldIgnore(event);\n    if (!shouldListen) {\n      shouldListen = true;\n      return;\n    }\n    handler(event);\n  };\n  const cleanup = [\n    useEventListener(window, \"click\", listener, { passive: true, capture }),\n    useEventListener(window, \"pointerdown\", (e) => {\n      const el = unrefElement(target);\n      if (el)\n        shouldListen = !e.composedPath().includes(el) && !shouldIgnore(e);\n    }, { passive: true }),\n    detectIframe && useEventListener(window, \"blur\", (event) => {\n      var _a;\n      const el = unrefElement(target);\n      if (((_a = window.document.activeElement) == null ? void 0 : _a.tagName) === \"IFRAME\" && !(el == null ? void 0 : el.contains(window.document.activeElement)))\n        handler(event);\n    })\n  ].filter(Boolean);\n  const stop = () => cleanup.forEach((fn) => fn());\n  return stop;\n}\n\nvar __defProp$n = Object.defineProperty;\nvar __defProps$9 = Object.defineProperties;\nvar __getOwnPropDescs$9 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$p = Object.getOwnPropertySymbols;\nvar __hasOwnProp$p = Object.prototype.hasOwnProperty;\nvar __propIsEnum$p = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$n = (obj, key, value) => key in obj ? __defProp$n(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$n = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$p.call(b, prop))\n      __defNormalProp$n(a, prop, b[prop]);\n  if (__getOwnPropSymbols$p)\n    for (var prop of __getOwnPropSymbols$p(b)) {\n      if (__propIsEnum$p.call(b, prop))\n        __defNormalProp$n(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$9 = (a, b) => __defProps$9(a, __getOwnPropDescs$9(b));\nconst createKeyPredicate = (keyFilter) => {\n  if (typeof keyFilter === \"function\")\n    return keyFilter;\n  else if (typeof keyFilter === \"string\")\n    return (event) => event.key === keyFilter;\n  else if (Array.isArray(keyFilter))\n    return (event) => keyFilter.includes(event.key);\n  return () => true;\n};\nfunction onKeyStroke(...args) {\n  let key;\n  let handler;\n  let options = {};\n  if (args.length === 3) {\n    key = args[0];\n    handler = args[1];\n    options = args[2];\n  } else if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      key = true;\n      handler = args[0];\n      options = args[1];\n    } else {\n      key = args[0];\n      handler = args[1];\n    }\n  } else {\n    key = true;\n    handler = args[0];\n  }\n  const { target = defaultWindow, eventName = \"keydown\", passive = false } = options;\n  const predicate = createKeyPredicate(key);\n  const listener = (e) => {\n    if (predicate(e))\n      handler(e);\n  };\n  return useEventListener(target, eventName, listener, passive);\n}\nfunction onKeyDown(key, handler, options = {}) {\n  return onKeyStroke(key, handler, __spreadProps$9(__spreadValues$n({}, options), { eventName: \"keydown\" }));\n}\nfunction onKeyPressed(key, handler, options = {}) {\n  return onKeyStroke(key, handler, __spreadProps$9(__spreadValues$n({}, options), { eventName: \"keypress\" }));\n}\nfunction onKeyUp(key, handler, options = {}) {\n  return onKeyStroke(key, handler, __spreadProps$9(__spreadValues$n({}, options), { eventName: \"keyup\" }));\n}\n\nconst DEFAULT_DELAY = 500;\nfunction onLongPress(target, handler, options) {\n  var _a, _b;\n  const elementRef = computed(() => unrefElement(target));\n  let timeout;\n  function clear() {\n    if (timeout) {\n      clearTimeout(timeout);\n      timeout = void 0;\n    }\n  }\n  function onDown(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    clear();\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    timeout = setTimeout(() => handler(ev), (_d = options == null ? void 0 : options.delay) != null ? _d : DEFAULT_DELAY);\n  }\n  const listenerOptions = {\n    capture: (_a = options == null ? void 0 : options.modifiers) == null ? void 0 : _a.capture,\n    once: (_b = options == null ? void 0 : options.modifiers) == null ? void 0 : _b.once\n  };\n  useEventListener(elementRef, \"pointerdown\", onDown, listenerOptions);\n  useEventListener(elementRef, \"pointerup\", clear, listenerOptions);\n  useEventListener(elementRef, \"pointerleave\", clear, listenerOptions);\n}\n\nconst isFocusedElementEditable = () => {\n  const { activeElement, body } = document;\n  if (!activeElement)\n    return false;\n  if (activeElement === body)\n    return false;\n  switch (activeElement.tagName) {\n    case \"INPUT\":\n    case \"TEXTAREA\":\n      return true;\n  }\n  return activeElement.hasAttribute(\"contenteditable\");\n};\nconst isTypedCharValid = ({\n  keyCode,\n  metaKey,\n  ctrlKey,\n  altKey\n}) => {\n  if (metaKey || ctrlKey || altKey)\n    return false;\n  if (keyCode >= 48 && keyCode <= 57 || keyCode >= 96 && keyCode <= 105)\n    return true;\n  if (keyCode >= 65 && keyCode <= 90)\n    return true;\n  return false;\n};\nfunction onStartTyping(callback, options = {}) {\n  const { document: document2 = defaultDocument } = options;\n  const keydown = (event) => {\n    !isFocusedElementEditable() && isTypedCharValid(event) && callback(event);\n  };\n  if (document2)\n    useEventListener(document2, \"keydown\", keydown, { passive: true });\n}\n\nfunction templateRef(key, initialValue = null) {\n  const instance = getCurrentInstance();\n  let _trigger = () => {\n  };\n  const element = customRef((track, trigger) => {\n    _trigger = trigger;\n    return {\n      get() {\n        var _a, _b;\n        track();\n        return (_b = (_a = instance == null ? void 0 : instance.proxy) == null ? void 0 : _a.$refs[key]) != null ? _b : initialValue;\n      },\n      set() {\n      }\n    };\n  });\n  tryOnMounted(_trigger);\n  onUpdated(_trigger);\n  return element;\n}\n\nfunction useActiveElement(options = {}) {\n  var _a;\n  const { window = defaultWindow } = options;\n  const document = (_a = options.document) != null ? _a : window == null ? void 0 : window.document;\n  const activeElement = computedWithControl(() => null, () => document == null ? void 0 : document.activeElement);\n  if (window) {\n    useEventListener(window, \"blur\", (event) => {\n      if (event.relatedTarget !== null)\n        return;\n      activeElement.trigger();\n    }, true);\n    useEventListener(window, \"focus\", activeElement.trigger, true);\n  }\n  return activeElement;\n}\n\nfunction useAsyncQueue(tasks, options = {}) {\n  const {\n    interrupt = true,\n    onError = noop,\n    onFinished = noop\n  } = options;\n  const promiseState = {\n    pending: \"pending\",\n    rejected: \"rejected\",\n    fulfilled: \"fulfilled\"\n  };\n  const initialResult = Array.from(new Array(tasks.length), () => ({ state: promiseState.pending, data: null }));\n  const result = reactive(initialResult);\n  const activeIndex = ref(-1);\n  if (!tasks || tasks.length === 0) {\n    onFinished();\n    return {\n      activeIndex,\n      result\n    };\n  }\n  function updateResult(state, res) {\n    activeIndex.value++;\n    result[activeIndex.value].data = res;\n    result[activeIndex.value].state = state;\n  }\n  tasks.reduce((prev, curr) => {\n    return prev.then((prevRes) => {\n      var _a;\n      if (((_a = result[activeIndex.value]) == null ? void 0 : _a.state) === promiseState.rejected && interrupt) {\n        onFinished();\n        return;\n      }\n      return curr(prevRes).then((currentRes) => {\n        updateResult(promiseState.fulfilled, currentRes);\n        activeIndex.value === tasks.length - 1 && onFinished();\n        return currentRes;\n      });\n    }).catch((e) => {\n      updateResult(promiseState.rejected, e);\n      onError();\n      return e;\n    });\n  }, Promise.resolve());\n  return {\n    activeIndex,\n    result\n  };\n}\n\nfunction useAsyncState(promise, initialState, options) {\n  const {\n    immediate = true,\n    delay = 0,\n    onError = noop,\n    onSuccess = noop,\n    resetOnExecute = true,\n    shallow = true,\n    throwError\n  } = options != null ? options : {};\n  const state = shallow ? shallowRef(initialState) : ref(initialState);\n  const isReady = ref(false);\n  const isLoading = ref(false);\n  const error = ref(void 0);\n  async function execute(delay2 = 0, ...args) {\n    if (resetOnExecute)\n      state.value = initialState;\n    error.value = void 0;\n    isReady.value = false;\n    isLoading.value = true;\n    if (delay2 > 0)\n      await promiseTimeout(delay2);\n    const _promise = typeof promise === \"function\" ? promise(...args) : promise;\n    try {\n      const data = await _promise;\n      state.value = data;\n      isReady.value = true;\n      onSuccess(data);\n    } catch (e) {\n      error.value = e;\n      onError(e);\n      if (throwError)\n        throw error;\n    } finally {\n      isLoading.value = false;\n    }\n    return state.value;\n  }\n  if (immediate)\n    execute(delay);\n  return {\n    state,\n    isReady,\n    isLoading,\n    error,\n    execute\n  };\n}\n\nconst defaults = {\n  array: (v) => JSON.stringify(v),\n  object: (v) => JSON.stringify(v),\n  set: (v) => JSON.stringify(Array.from(v)),\n  map: (v) => JSON.stringify(Object.fromEntries(v)),\n  null: () => \"\"\n};\nfunction getDefaultSerialization(target) {\n  if (!target)\n    return defaults.null;\n  if (target instanceof Map)\n    return defaults.map;\n  else if (target instanceof Set)\n    return defaults.set;\n  else if (Array.isArray(target))\n    return defaults.array;\n  else\n    return defaults.object;\n}\n\nfunction useBase64(target, options) {\n  const base64 = ref(\"\");\n  const promise = ref();\n  function execute() {\n    if (!isClient)\n      return;\n    promise.value = new Promise((resolve, reject) => {\n      try {\n        const _target = resolveUnref(target);\n        if (_target == null) {\n          resolve(\"\");\n        } else if (typeof _target === \"string\") {\n          resolve(blobToBase64(new Blob([_target], { type: \"text/plain\" })));\n        } else if (_target instanceof Blob) {\n          resolve(blobToBase64(_target));\n        } else if (_target instanceof ArrayBuffer) {\n          resolve(window.btoa(String.fromCharCode(...new Uint8Array(_target))));\n        } else if (_target instanceof HTMLCanvasElement) {\n          resolve(_target.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n        } else if (_target instanceof HTMLImageElement) {\n          const img = _target.cloneNode(false);\n          img.crossOrigin = \"Anonymous\";\n          imgLoaded(img).then(() => {\n            const canvas = document.createElement(\"canvas\");\n            const ctx = canvas.getContext(\"2d\");\n            canvas.width = img.width;\n            canvas.height = img.height;\n            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n            resolve(canvas.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n          }).catch(reject);\n        } else if (typeof _target === \"object\") {\n          const _serializeFn = (options == null ? void 0 : options.serializer) || getDefaultSerialization(_target);\n          const serialized = _serializeFn(_target);\n          return resolve(blobToBase64(new Blob([serialized], { type: \"application/json\" })));\n        } else {\n          reject(new Error(\"target is unsupported types\"));\n        }\n      } catch (error) {\n        reject(error);\n      }\n    });\n    promise.value.then((res) => base64.value = res);\n    return promise.value;\n  }\n  if (isRef(target) || isFunction(target))\n    watch(target, execute, { immediate: true });\n  else\n    execute();\n  return {\n    base64,\n    promise,\n    execute\n  };\n}\nfunction imgLoaded(img) {\n  return new Promise((resolve, reject) => {\n    if (!img.complete) {\n      img.onload = () => {\n        resolve();\n      };\n      img.onerror = reject;\n    } else {\n      resolve();\n    }\n  });\n}\nfunction blobToBase64(blob) {\n  return new Promise((resolve, reject) => {\n    const fr = new FileReader();\n    fr.onload = (e) => {\n      resolve(e.target.result);\n    };\n    fr.onerror = reject;\n    fr.readAsDataURL(blob);\n  });\n}\n\nfunction useSupported(callback, sync = false) {\n  const isSupported = ref();\n  const update = () => isSupported.value = Boolean(callback());\n  update();\n  tryOnMounted(update, sync);\n  return isSupported;\n}\n\nfunction useBattery({ navigator = defaultNavigator } = {}) {\n  const events = [\"chargingchange\", \"chargingtimechange\", \"dischargingtimechange\", \"levelchange\"];\n  const isSupported = useSupported(() => navigator && \"getBattery\" in navigator);\n  const charging = ref(false);\n  const chargingTime = ref(0);\n  const dischargingTime = ref(0);\n  const level = ref(1);\n  let battery;\n  function updateBatteryInfo() {\n    charging.value = this.charging;\n    chargingTime.value = this.chargingTime || 0;\n    dischargingTime.value = this.dischargingTime || 0;\n    level.value = this.level;\n  }\n  if (isSupported.value) {\n    navigator.getBattery().then((_battery) => {\n      battery = _battery;\n      updateBatteryInfo.call(battery);\n      for (const event of events)\n        useEventListener(battery, event, updateBatteryInfo, { passive: true });\n    });\n  }\n  return {\n    isSupported,\n    charging,\n    chargingTime,\n    dischargingTime,\n    level\n  };\n}\n\nfunction useBluetooth(options) {\n  let {\n    acceptAllDevices = false\n  } = options || {};\n  const {\n    filters = void 0,\n    optionalServices = void 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => navigator && \"bluetooth\" in navigator);\n  const device = shallowRef(void 0);\n  const error = shallowRef(null);\n  watch(device, () => {\n    connectToBluetoothGATTServer();\n  });\n  async function requestDevice() {\n    if (!isSupported.value)\n      return;\n    error.value = null;\n    if (filters && filters.length > 0)\n      acceptAllDevices = false;\n    try {\n      device.value = await (navigator == null ? void 0 : navigator.bluetooth.requestDevice({\n        acceptAllDevices,\n        filters,\n        optionalServices\n      }));\n    } catch (err) {\n      error.value = err;\n    }\n  }\n  const server = ref();\n  const isConnected = computed(() => {\n    var _a;\n    return ((_a = server.value) == null ? void 0 : _a.connected) || false;\n  });\n  async function connectToBluetoothGATTServer() {\n    error.value = null;\n    if (device.value && device.value.gatt) {\n      device.value.addEventListener(\"gattserverdisconnected\", () => {\n      });\n      try {\n        server.value = await device.value.gatt.connect();\n      } catch (err) {\n        error.value = err;\n      }\n    }\n  }\n  tryOnMounted(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.connect();\n  });\n  tryOnScopeDispose(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.disconnect();\n  });\n  return {\n    isSupported,\n    isConnected,\n    device,\n    requestDevice,\n    server,\n    error\n  };\n}\n\nfunction useMediaQuery(query, options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = useSupported(() => window && \"matchMedia\" in window && typeof window.matchMedia === \"function\");\n  let mediaQuery;\n  const matches = ref(false);\n  const cleanup = () => {\n    if (!mediaQuery)\n      return;\n    if (\"removeEventListener\" in mediaQuery)\n      mediaQuery.removeEventListener(\"change\", update);\n    else\n      mediaQuery.removeListener(update);\n  };\n  const update = () => {\n    if (!isSupported.value)\n      return;\n    cleanup();\n    mediaQuery = window.matchMedia(resolveRef(query).value);\n    matches.value = mediaQuery.matches;\n    if (\"addEventListener\" in mediaQuery)\n      mediaQuery.addEventListener(\"change\", update);\n    else\n      mediaQuery.addListener(update);\n  };\n  watchEffect(update);\n  tryOnScopeDispose(() => cleanup());\n  return matches;\n}\n\nconst breakpointsTailwind = {\n  \"sm\": 640,\n  \"md\": 768,\n  \"lg\": 1024,\n  \"xl\": 1280,\n  \"2xl\": 1536\n};\nconst breakpointsBootstrapV5 = {\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n};\nconst breakpointsVuetify = {\n  xs: 600,\n  sm: 960,\n  md: 1264,\n  lg: 1904\n};\nconst breakpointsAntDesign = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600\n};\nconst breakpointsQuasar = {\n  xs: 600,\n  sm: 1024,\n  md: 1440,\n  lg: 1920\n};\nconst breakpointsSematic = {\n  mobileS: 320,\n  mobileM: 375,\n  mobileL: 425,\n  tablet: 768,\n  laptop: 1024,\n  laptopL: 1440,\n  desktop4K: 2560\n};\nconst breakpointsMasterCss = {\n  \"3xs\": 360,\n  \"2xs\": 480,\n  \"xs\": 600,\n  \"sm\": 768,\n  \"md\": 1024,\n  \"lg\": 1280,\n  \"xl\": 1440,\n  \"2xl\": 1600,\n  \"3xl\": 1920,\n  \"4xl\": 2560\n};\n\nvar __defProp$m = Object.defineProperty;\nvar __getOwnPropSymbols$o = Object.getOwnPropertySymbols;\nvar __hasOwnProp$o = Object.prototype.hasOwnProperty;\nvar __propIsEnum$o = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$m = (obj, key, value) => key in obj ? __defProp$m(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$m = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$o.call(b, prop))\n      __defNormalProp$m(a, prop, b[prop]);\n  if (__getOwnPropSymbols$o)\n    for (var prop of __getOwnPropSymbols$o(b)) {\n      if (__propIsEnum$o.call(b, prop))\n        __defNormalProp$m(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useBreakpoints(breakpoints, options = {}) {\n  function getValue(k, delta) {\n    let v = breakpoints[k];\n    if (delta != null)\n      v = increaseWithUnit(v, delta);\n    if (typeof v === \"number\")\n      v = `${v}px`;\n    return v;\n  }\n  const { window = defaultWindow } = options;\n  function match(query) {\n    if (!window)\n      return false;\n    return window.matchMedia(query).matches;\n  }\n  const greaterOrEqual = (k) => {\n    return useMediaQuery(`(min-width: ${getValue(k)})`, options);\n  };\n  const shortcutMethods = Object.keys(breakpoints).reduce((shortcuts, k) => {\n    Object.defineProperty(shortcuts, k, {\n      get: () => greaterOrEqual(k),\n      enumerable: true,\n      configurable: true\n    });\n    return shortcuts;\n  }, {});\n  return __spreadValues$m({\n    greater(k) {\n      return useMediaQuery(`(min-width: ${getValue(k, 0.1)})`, options);\n    },\n    greaterOrEqual,\n    smaller(k) {\n      return useMediaQuery(`(max-width: ${getValue(k, -0.1)})`, options);\n    },\n    smallerOrEqual(k) {\n      return useMediaQuery(`(max-width: ${getValue(k)})`, options);\n    },\n    between(a, b) {\n      return useMediaQuery(`(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`, options);\n    },\n    isGreater(k) {\n      return match(`(min-width: ${getValue(k, 0.1)})`);\n    },\n    isGreaterOrEqual(k) {\n      return match(`(min-width: ${getValue(k)})`);\n    },\n    isSmaller(k) {\n      return match(`(max-width: ${getValue(k, -0.1)})`);\n    },\n    isSmallerOrEqual(k) {\n      return match(`(max-width: ${getValue(k)})`);\n    },\n    isInBetween(a, b) {\n      return match(`(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`);\n    }\n  }, shortcutMethods);\n}\n\nconst useBroadcastChannel = (options) => {\n  const {\n    name,\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"BroadcastChannel\" in window);\n  const isClosed = ref(false);\n  const channel = ref();\n  const data = ref();\n  const error = ref(null);\n  const post = (data2) => {\n    if (channel.value)\n      channel.value.postMessage(data2);\n  };\n  const close = () => {\n    if (channel.value)\n      channel.value.close();\n    isClosed.value = true;\n  };\n  if (isSupported.value) {\n    tryOnMounted(() => {\n      error.value = null;\n      channel.value = new BroadcastChannel(name);\n      channel.value.addEventListener(\"message\", (e) => {\n        data.value = e.data;\n      }, { passive: true });\n      channel.value.addEventListener(\"messageerror\", (e) => {\n        error.value = e;\n      }, { passive: true });\n      channel.value.addEventListener(\"close\", () => {\n        isClosed.value = true;\n      });\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    isSupported,\n    channel,\n    data,\n    post,\n    close,\n    error,\n    isClosed\n  };\n};\n\nfunction useBrowserLocation({ window = defaultWindow } = {}) {\n  const buildState = (trigger) => {\n    const { state: state2, length } = (window == null ? void 0 : window.history) || {};\n    const { hash, host, hostname, href, origin, pathname, port, protocol, search } = (window == null ? void 0 : window.location) || {};\n    return {\n      trigger,\n      state: state2,\n      length,\n      hash,\n      host,\n      hostname,\n      href,\n      origin,\n      pathname,\n      port,\n      protocol,\n      search\n    };\n  };\n  const state = ref(buildState(\"load\"));\n  if (window) {\n    useEventListener(window, \"popstate\", () => state.value = buildState(\"popstate\"), { passive: true });\n    useEventListener(window, \"hashchange\", () => state.value = buildState(\"hashchange\"), { passive: true });\n  }\n  return state;\n}\n\nfunction useCached(refValue, comparator = (a, b) => a === b, watchOptions) {\n  const cachedValue = ref(refValue.value);\n  watch(() => refValue.value, (value) => {\n    if (!comparator(value, cachedValue.value))\n      cachedValue.value = value;\n  }, watchOptions);\n  return cachedValue;\n}\n\nfunction useClipboard(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500,\n    legacy = false\n  } = options;\n  const events = [\"copy\", \"cut\"];\n  const isClipboardApiSupported = useSupported(() => navigator && \"clipboard\" in navigator);\n  const isSupported = computed(() => isClipboardApiSupported.value || legacy);\n  const text = ref(\"\");\n  const copied = ref(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring);\n  function updateText() {\n    if (isClipboardApiSupported.value) {\n      navigator.clipboard.readText().then((value) => {\n        text.value = value;\n      });\n    } else {\n      text.value = legacyRead();\n    }\n  }\n  if (isSupported.value && read) {\n    for (const event of events)\n      useEventListener(event, updateText);\n  }\n  async function copy(value = resolveUnref(source)) {\n    if (isSupported.value && value != null) {\n      if (isClipboardApiSupported.value)\n        await navigator.clipboard.writeText(value);\n      else\n        legacyCopy(value);\n      text.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  function legacyCopy(value) {\n    const ta = document.createElement(\"textarea\");\n    ta.value = value != null ? value : \"\";\n    ta.style.position = \"absolute\";\n    ta.style.opacity = \"0\";\n    document.body.appendChild(ta);\n    ta.select();\n    document.execCommand(\"copy\");\n    ta.remove();\n  }\n  function legacyRead() {\n    var _a, _b, _c;\n    return (_c = (_b = (_a = document == null ? void 0 : document.getSelection) == null ? void 0 : _a.call(document)) == null ? void 0 : _b.toString()) != null ? _c : \"\";\n  }\n  return {\n    isSupported,\n    text,\n    copied,\n    copy\n  };\n}\n\nvar __defProp$l = Object.defineProperty;\nvar __defProps$8 = Object.defineProperties;\nvar __getOwnPropDescs$8 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$n = Object.getOwnPropertySymbols;\nvar __hasOwnProp$n = Object.prototype.hasOwnProperty;\nvar __propIsEnum$n = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$l = (obj, key, value) => key in obj ? __defProp$l(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$l = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$n.call(b, prop))\n      __defNormalProp$l(a, prop, b[prop]);\n  if (__getOwnPropSymbols$n)\n    for (var prop of __getOwnPropSymbols$n(b)) {\n      if (__propIsEnum$n.call(b, prop))\n        __defNormalProp$l(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$8 = (a, b) => __defProps$8(a, __getOwnPropDescs$8(b));\nfunction cloneFnJSON(source) {\n  return JSON.parse(JSON.stringify(source));\n}\nfunction useCloned(source, options = {}) {\n  const cloned = ref({});\n  const {\n    manual,\n    clone = cloneFnJSON,\n    deep = true,\n    immediate = true\n  } = options;\n  function sync() {\n    cloned.value = clone(unref(source));\n  }\n  if (!manual && isRef(source)) {\n    watch(source, sync, __spreadProps$8(__spreadValues$l({}, options), {\n      deep,\n      immediate\n    }));\n  } else {\n    sync();\n  }\n  return { cloned, sync };\n}\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__vueuse_ssr_handlers__\";\n_global[globalKey] = _global[globalKey] || {};\nconst handlers = _global[globalKey];\nfunction getSSRHandler(key, fallback) {\n  return handlers[key] || fallback;\n}\nfunction setSSRHandler(key, fn) {\n  handlers[key] = fn;\n}\n\nfunction guessSerializerType(rawInit) {\n  return rawInit == null ? \"any\" : rawInit instanceof Set ? \"set\" : rawInit instanceof Map ? \"map\" : rawInit instanceof Date ? \"date\" : typeof rawInit === \"boolean\" ? \"boolean\" : typeof rawInit === \"string\" ? \"string\" : typeof rawInit === \"object\" ? \"object\" : !Number.isNaN(rawInit) ? \"number\" : \"any\";\n}\n\nvar __defProp$k = Object.defineProperty;\nvar __getOwnPropSymbols$m = Object.getOwnPropertySymbols;\nvar __hasOwnProp$m = Object.prototype.hasOwnProperty;\nvar __propIsEnum$m = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$k = (obj, key, value) => key in obj ? __defProp$k(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$k = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$m.call(b, prop))\n      __defNormalProp$k(a, prop, b[prop]);\n  if (__getOwnPropSymbols$m)\n    for (var prop of __getOwnPropSymbols$m(b)) {\n      if (__propIsEnum$m.call(b, prop))\n        __defNormalProp$k(a, prop, b[prop]);\n    }\n  return a;\n};\nconst StorageSerializers = {\n  boolean: {\n    read: (v) => v === \"true\",\n    write: (v) => String(v)\n  },\n  object: {\n    read: (v) => JSON.parse(v),\n    write: (v) => JSON.stringify(v)\n  },\n  number: {\n    read: (v) => Number.parseFloat(v),\n    write: (v) => String(v)\n  },\n  any: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  string: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  map: {\n    read: (v) => new Map(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v.entries()))\n  },\n  set: {\n    read: (v) => new Set(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v))\n  },\n  date: {\n    read: (v) => new Date(v),\n    write: (v) => v.toISOString()\n  }\n};\nconst customStorageEventName = \"vueuse-storage\";\nfunction useStorage(key, defaults, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const data = (shallow ? shallowRef : ref)(defaults);\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  if (!storage)\n    return data;\n  const rawInit = resolveUnref(defaults);\n  const type = guessSerializerType(rawInit);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  const { pause: pauseWatch, resume: resumeWatch } = pausableWatch(data, () => write(data.value), { flush, deep, eventFilter });\n  if (window && listenToStorageChanges) {\n    useEventListener(window, \"storage\", update);\n    useEventListener(window, customStorageEventName, updateFromCustomEvent);\n  }\n  update();\n  return data;\n  function write(v) {\n    try {\n      if (v == null) {\n        storage.removeItem(key);\n      } else {\n        const serialized = serializer.write(v);\n        const oldValue = storage.getItem(key);\n        if (oldValue !== serialized) {\n          storage.setItem(key, serialized);\n          if (window) {\n            window.dispatchEvent(new CustomEvent(customStorageEventName, {\n              detail: {\n                key,\n                oldValue,\n                newValue: serialized,\n                storageArea: storage\n              }\n            }));\n          }\n        }\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  function read(event) {\n    const rawValue = event ? event.newValue : storage.getItem(key);\n    if (rawValue == null) {\n      if (writeDefaults && rawInit !== null)\n        storage.setItem(key, serializer.write(rawInit));\n      return rawInit;\n    } else if (!event && mergeDefaults) {\n      const value = serializer.read(rawValue);\n      if (isFunction(mergeDefaults))\n        return mergeDefaults(value, rawInit);\n      else if (type === \"object\" && !Array.isArray(value))\n        return __spreadValues$k(__spreadValues$k({}, rawInit), value);\n      return value;\n    } else if (typeof rawValue !== \"string\") {\n      return rawValue;\n    } else {\n      return serializer.read(rawValue);\n    }\n  }\n  function updateFromCustomEvent(event) {\n    update(event.detail);\n  }\n  function update(event) {\n    if (event && event.storageArea !== storage)\n      return;\n    if (event && event.key == null) {\n      data.value = rawInit;\n      return;\n    }\n    if (event && event.key !== key)\n      return;\n    pauseWatch();\n    try {\n      data.value = read(event);\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (event)\n        nextTick(resumeWatch);\n      else\n        resumeWatch();\n    }\n  }\n}\n\nfunction usePreferredDark(options) {\n  return useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n}\n\nvar __defProp$j = Object.defineProperty;\nvar __getOwnPropSymbols$l = Object.getOwnPropertySymbols;\nvar __hasOwnProp$l = Object.prototype.hasOwnProperty;\nvar __propIsEnum$l = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$j = (obj, key, value) => key in obj ? __defProp$j(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$j = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$l.call(b, prop))\n      __defNormalProp$j(a, prop, b[prop]);\n  if (__getOwnPropSymbols$l)\n    for (var prop of __getOwnPropSymbols$l(b)) {\n      if (__propIsEnum$l.call(b, prop))\n        __defNormalProp$j(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useColorMode(options = {}) {\n  const {\n    selector = \"html\",\n    attribute = \"class\",\n    initialValue = \"auto\",\n    window = defaultWindow,\n    storage,\n    storageKey = \"vueuse-color-scheme\",\n    listenToStorageChanges = true,\n    storageRef,\n    emitAuto\n  } = options;\n  const modes = __spreadValues$j({\n    auto: \"\",\n    light: \"light\",\n    dark: \"dark\"\n  }, options.modes || {});\n  const preferredDark = usePreferredDark({ window });\n  const preferredMode = computed(() => preferredDark.value ? \"dark\" : \"light\");\n  const store = storageRef || (storageKey == null ? ref(initialValue) : useStorage(storageKey, initialValue, storage, { window, listenToStorageChanges }));\n  const state = computed({\n    get() {\n      return store.value === \"auto\" && !emitAuto ? preferredMode.value : store.value;\n    },\n    set(v) {\n      store.value = v;\n    }\n  });\n  const updateHTMLAttrs = getSSRHandler(\"updateHTMLAttrs\", (selector2, attribute2, value) => {\n    const el = window == null ? void 0 : window.document.querySelector(selector2);\n    if (!el)\n      return;\n    if (attribute2 === \"class\") {\n      const current = value.split(/\\s/g);\n      Object.values(modes).flatMap((i) => (i || \"\").split(/\\s/g)).filter(Boolean).forEach((v) => {\n        if (current.includes(v))\n          el.classList.add(v);\n        else\n          el.classList.remove(v);\n      });\n    } else {\n      el.setAttribute(attribute2, value);\n    }\n  });\n  function defaultOnChanged(mode) {\n    var _a;\n    const resolvedMode = mode === \"auto\" ? preferredMode.value : mode;\n    updateHTMLAttrs(selector, attribute, (_a = modes[resolvedMode]) != null ? _a : resolvedMode);\n  }\n  function onChanged(mode) {\n    if (options.onChanged)\n      options.onChanged(mode, defaultOnChanged);\n    else\n      defaultOnChanged(mode);\n  }\n  watch(state, onChanged, { flush: \"post\", immediate: true });\n  if (emitAuto)\n    watch(preferredMode, () => onChanged(state.value), { flush: \"post\" });\n  tryOnMounted(() => onChanged(state.value));\n  return state;\n}\n\nfunction useConfirmDialog(revealed = ref(false)) {\n  const confirmHook = createEventHook();\n  const cancelHook = createEventHook();\n  const revealHook = createEventHook();\n  let _resolve = noop;\n  const reveal = (data) => {\n    revealHook.trigger(data);\n    revealed.value = true;\n    return new Promise((resolve) => {\n      _resolve = resolve;\n    });\n  };\n  const confirm = (data) => {\n    revealed.value = false;\n    confirmHook.trigger(data);\n    _resolve({ data, isCanceled: false });\n  };\n  const cancel = (data) => {\n    revealed.value = false;\n    cancelHook.trigger(data);\n    _resolve({ data, isCanceled: true });\n  };\n  return {\n    isRevealed: computed(() => revealed.value),\n    reveal,\n    confirm,\n    cancel,\n    onReveal: revealHook.on,\n    onConfirm: confirmHook.on,\n    onCancel: cancelHook.on\n  };\n}\n\nfunction useCssVar(prop, target, { window = defaultWindow, initialValue = \"\" } = {}) {\n  const variable = ref(initialValue);\n  const elRef = computed(() => {\n    var _a;\n    return unrefElement(target) || ((_a = window == null ? void 0 : window.document) == null ? void 0 : _a.documentElement);\n  });\n  watch([elRef, () => resolveUnref(prop)], ([el, prop2]) => {\n    var _a;\n    if (el && window) {\n      const value = (_a = window.getComputedStyle(el).getPropertyValue(prop2)) == null ? void 0 : _a.trim();\n      variable.value = value || initialValue;\n    }\n  }, { immediate: true });\n  watch(variable, (val) => {\n    var _a;\n    if ((_a = elRef.value) == null ? void 0 : _a.style)\n      elRef.value.style.setProperty(resolveUnref(prop), val);\n  });\n  return variable;\n}\n\nfunction useCurrentElement() {\n  const vm = getCurrentInstance();\n  const currentElement = computedWithControl(() => null, () => vm.proxy.$el);\n  onUpdated(currentElement.trigger);\n  onMounted(currentElement.trigger);\n  return currentElement;\n}\n\nfunction useCycleList(list, options) {\n  var _a;\n  const state = shallowRef((_a = options == null ? void 0 : options.initialValue) != null ? _a : list[0]);\n  const index = computed({\n    get() {\n      var _a2;\n      let index2 = (options == null ? void 0 : options.getIndexOf) ? options.getIndexOf(state.value, list) : list.indexOf(state.value);\n      if (index2 < 0)\n        index2 = (_a2 = options == null ? void 0 : options.fallbackIndex) != null ? _a2 : 0;\n      return index2;\n    },\n    set(v) {\n      set(v);\n    }\n  });\n  function set(i) {\n    const length = list.length;\n    const index2 = (i % length + length) % length;\n    const value = list[index2];\n    state.value = value;\n    return value;\n  }\n  function shift(delta = 1) {\n    return set(index.value + delta);\n  }\n  function next(n = 1) {\n    return shift(n);\n  }\n  function prev(n = 1) {\n    return shift(-n);\n  }\n  return {\n    state,\n    index,\n    next,\n    prev\n  };\n}\n\nvar __defProp$i = Object.defineProperty;\nvar __defProps$7 = Object.defineProperties;\nvar __getOwnPropDescs$7 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$k = Object.getOwnPropertySymbols;\nvar __hasOwnProp$k = Object.prototype.hasOwnProperty;\nvar __propIsEnum$k = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$i = (obj, key, value) => key in obj ? __defProp$i(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$i = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$k.call(b, prop))\n      __defNormalProp$i(a, prop, b[prop]);\n  if (__getOwnPropSymbols$k)\n    for (var prop of __getOwnPropSymbols$k(b)) {\n      if (__propIsEnum$k.call(b, prop))\n        __defNormalProp$i(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$7 = (a, b) => __defProps$7(a, __getOwnPropDescs$7(b));\nfunction useDark(options = {}) {\n  const {\n    valueDark = \"dark\",\n    valueLight = \"\",\n    window = defaultWindow\n  } = options;\n  const mode = useColorMode(__spreadProps$7(__spreadValues$i({}, options), {\n    onChanged: (mode2, defaultHandler) => {\n      var _a;\n      if (options.onChanged)\n        (_a = options.onChanged) == null ? void 0 : _a.call(options, mode2 === \"dark\");\n      else\n        defaultHandler(mode2);\n    },\n    modes: {\n      dark: valueDark,\n      light: valueLight\n    }\n  }));\n  const preferredDark = usePreferredDark({ window });\n  const isDark = computed({\n    get() {\n      return mode.value === \"dark\";\n    },\n    set(v) {\n      if (v === preferredDark.value)\n        mode.value = \"auto\";\n      else\n        mode.value = v ? \"dark\" : \"light\";\n    }\n  });\n  return isDark;\n}\n\nconst fnBypass = (v) => v;\nconst fnSetSource = (source, value) => source.value = value;\nfunction defaultDump(clone) {\n  return clone ? isFunction(clone) ? clone : cloneFnJSON : fnBypass;\n}\nfunction defaultParse(clone) {\n  return clone ? isFunction(clone) ? clone : cloneFnJSON : fnBypass;\n}\nfunction useManualRefHistory(source, options = {}) {\n  const {\n    clone = false,\n    dump = defaultDump(clone),\n    parse = defaultParse(clone),\n    setSource = fnSetSource\n  } = options;\n  function _createHistoryRecord() {\n    return markRaw({\n      snapshot: dump(source.value),\n      timestamp: timestamp()\n    });\n  }\n  const last = ref(_createHistoryRecord());\n  const undoStack = ref([]);\n  const redoStack = ref([]);\n  const _setSource = (record) => {\n    setSource(source, parse(record.snapshot));\n    last.value = record;\n  };\n  const commit = () => {\n    undoStack.value.unshift(last.value);\n    last.value = _createHistoryRecord();\n    if (options.capacity && undoStack.value.length > options.capacity)\n      undoStack.value.splice(options.capacity, Infinity);\n    if (redoStack.value.length)\n      redoStack.value.splice(0, redoStack.value.length);\n  };\n  const clear = () => {\n    undoStack.value.splice(0, undoStack.value.length);\n    redoStack.value.splice(0, redoStack.value.length);\n  };\n  const undo = () => {\n    const state = undoStack.value.shift();\n    if (state) {\n      redoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const redo = () => {\n    const state = redoStack.value.shift();\n    if (state) {\n      undoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const reset = () => {\n    _setSource(last.value);\n  };\n  const history = computed(() => [last.value, ...undoStack.value]);\n  const canUndo = computed(() => undoStack.value.length > 0);\n  const canRedo = computed(() => redoStack.value.length > 0);\n  return {\n    source,\n    undoStack,\n    redoStack,\n    last,\n    history,\n    canUndo,\n    canRedo,\n    clear,\n    commit,\n    reset,\n    undo,\n    redo\n  };\n}\n\nvar __defProp$h = Object.defineProperty;\nvar __defProps$6 = Object.defineProperties;\nvar __getOwnPropDescs$6 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$j = Object.getOwnPropertySymbols;\nvar __hasOwnProp$j = Object.prototype.hasOwnProperty;\nvar __propIsEnum$j = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$h = (obj, key, value) => key in obj ? __defProp$h(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$h = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$j.call(b, prop))\n      __defNormalProp$h(a, prop, b[prop]);\n  if (__getOwnPropSymbols$j)\n    for (var prop of __getOwnPropSymbols$j(b)) {\n      if (__propIsEnum$j.call(b, prop))\n        __defNormalProp$h(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$6 = (a, b) => __defProps$6(a, __getOwnPropDescs$6(b));\nfunction useRefHistory(source, options = {}) {\n  const {\n    deep = false,\n    flush = \"pre\",\n    eventFilter\n  } = options;\n  const {\n    eventFilter: composedFilter,\n    pause,\n    resume: resumeTracking,\n    isActive: isTracking\n  } = pausableFilter(eventFilter);\n  const {\n    ignoreUpdates,\n    ignorePrevAsyncUpdates,\n    stop\n  } = watchIgnorable(source, commit, { deep, flush, eventFilter: composedFilter });\n  function setSource(source2, value) {\n    ignorePrevAsyncUpdates();\n    ignoreUpdates(() => {\n      source2.value = value;\n    });\n  }\n  const manualHistory = useManualRefHistory(source, __spreadProps$6(__spreadValues$h({}, options), { clone: options.clone || deep, setSource }));\n  const { clear, commit: manualCommit } = manualHistory;\n  function commit() {\n    ignorePrevAsyncUpdates();\n    manualCommit();\n  }\n  function resume(commitNow) {\n    resumeTracking();\n    if (commitNow)\n      commit();\n  }\n  function batch(fn) {\n    let canceled = false;\n    const cancel = () => canceled = true;\n    ignoreUpdates(() => {\n      fn(cancel);\n    });\n    if (!canceled)\n      commit();\n  }\n  function dispose() {\n    stop();\n    clear();\n  }\n  return __spreadProps$6(__spreadValues$h({}, manualHistory), {\n    isTracking,\n    pause,\n    resume,\n    commit,\n    batch,\n    dispose\n  });\n}\n\nvar __defProp$g = Object.defineProperty;\nvar __defProps$5 = Object.defineProperties;\nvar __getOwnPropDescs$5 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$i = Object.getOwnPropertySymbols;\nvar __hasOwnProp$i = Object.prototype.hasOwnProperty;\nvar __propIsEnum$i = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$g = (obj, key, value) => key in obj ? __defProp$g(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$g = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$i.call(b, prop))\n      __defNormalProp$g(a, prop, b[prop]);\n  if (__getOwnPropSymbols$i)\n    for (var prop of __getOwnPropSymbols$i(b)) {\n      if (__propIsEnum$i.call(b, prop))\n        __defNormalProp$g(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$5 = (a, b) => __defProps$5(a, __getOwnPropDescs$5(b));\nfunction useDebouncedRefHistory(source, options = {}) {\n  const filter = options.debounce ? debounceFilter(options.debounce) : void 0;\n  const history = useRefHistory(source, __spreadProps$5(__spreadValues$g({}, options), { eventFilter: filter }));\n  return __spreadValues$g({}, history);\n}\n\nfunction useDeviceMotion(options = {}) {\n  const {\n    window = defaultWindow,\n    eventFilter = bypassFilter\n  } = options;\n  const acceleration = ref({ x: null, y: null, z: null });\n  const rotationRate = ref({ alpha: null, beta: null, gamma: null });\n  const interval = ref(0);\n  const accelerationIncludingGravity = ref({\n    x: null,\n    y: null,\n    z: null\n  });\n  if (window) {\n    const onDeviceMotion = createFilterWrapper(eventFilter, (event) => {\n      acceleration.value = event.acceleration;\n      accelerationIncludingGravity.value = event.accelerationIncludingGravity;\n      rotationRate.value = event.rotationRate;\n      interval.value = event.interval;\n    });\n    useEventListener(window, \"devicemotion\", onDeviceMotion);\n  }\n  return {\n    acceleration,\n    accelerationIncludingGravity,\n    rotationRate,\n    interval\n  };\n}\n\nfunction useDeviceOrientation(options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = useSupported(() => window && \"DeviceOrientationEvent\" in window);\n  const isAbsolute = ref(false);\n  const alpha = ref(null);\n  const beta = ref(null);\n  const gamma = ref(null);\n  if (window && isSupported.value) {\n    useEventListener(window, \"deviceorientation\", (event) => {\n      isAbsolute.value = event.absolute;\n      alpha.value = event.alpha;\n      beta.value = event.beta;\n      gamma.value = event.gamma;\n    });\n  }\n  return {\n    isSupported,\n    isAbsolute,\n    alpha,\n    beta,\n    gamma\n  };\n}\n\nfunction useDevicePixelRatio({\n  window = defaultWindow\n} = {}) {\n  const pixelRatio = ref(1);\n  if (window) {\n    let observe = function() {\n      pixelRatio.value = window.devicePixelRatio;\n      cleanup();\n      media = window.matchMedia(`(resolution: ${pixelRatio.value}dppx)`);\n      media.addEventListener(\"change\", observe, { once: true });\n    }, cleanup = function() {\n      media == null ? void 0 : media.removeEventListener(\"change\", observe);\n    };\n    let media;\n    observe();\n    tryOnScopeDispose(cleanup);\n  }\n  return { pixelRatio };\n}\n\nfunction usePermission(permissionDesc, options = {}) {\n  const {\n    controls = false,\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"permissions\" in navigator);\n  let permissionStatus;\n  const desc = typeof permissionDesc === \"string\" ? { name: permissionDesc } : permissionDesc;\n  const state = ref();\n  const onChange = () => {\n    if (permissionStatus)\n      state.value = permissionStatus.state;\n  };\n  const query = createSingletonPromise(async () => {\n    if (!isSupported.value)\n      return;\n    if (!permissionStatus) {\n      try {\n        permissionStatus = await navigator.permissions.query(desc);\n        useEventListener(permissionStatus, \"change\", onChange);\n        onChange();\n      } catch (e) {\n        state.value = \"prompt\";\n      }\n    }\n    return permissionStatus;\n  });\n  query();\n  if (controls) {\n    return {\n      state,\n      isSupported,\n      query\n    };\n  } else {\n    return state;\n  }\n}\n\nfunction useDevicesList(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    requestPermissions = false,\n    constraints = { audio: true, video: true },\n    onUpdated\n  } = options;\n  const devices = ref([]);\n  const videoInputs = computed(() => devices.value.filter((i) => i.kind === \"videoinput\"));\n  const audioInputs = computed(() => devices.value.filter((i) => i.kind === \"audioinput\"));\n  const audioOutputs = computed(() => devices.value.filter((i) => i.kind === \"audiooutput\"));\n  const isSupported = useSupported(() => navigator && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices);\n  const permissionGranted = ref(false);\n  async function update() {\n    if (!isSupported.value)\n      return;\n    devices.value = await navigator.mediaDevices.enumerateDevices();\n    onUpdated == null ? void 0 : onUpdated(devices.value);\n  }\n  async function ensurePermissions() {\n    if (!isSupported.value)\n      return false;\n    if (permissionGranted.value)\n      return true;\n    const { state, query } = usePermission(\"camera\", { controls: true });\n    await query();\n    if (state.value !== \"granted\") {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      stream.getTracks().forEach((t) => t.stop());\n      update();\n      permissionGranted.value = true;\n    } else {\n      permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  }\n  if (isSupported.value) {\n    if (requestPermissions)\n      ensurePermissions();\n    useEventListener(navigator.mediaDevices, \"devicechange\", update);\n    update();\n  }\n  return {\n    devices,\n    ensurePermissions,\n    permissionGranted,\n    videoInputs,\n    audioInputs,\n    audioOutputs,\n    isSupported\n  };\n}\n\nfunction useDisplayMedia(options = {}) {\n  var _a;\n  const enabled = ref((_a = options.enabled) != null ? _a : false);\n  const video = options.video;\n  const audio = options.audio;\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getDisplayMedia;\n  });\n  const constraint = { audio, video };\n  const stream = shallowRef();\n  async function _start() {\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getDisplayMedia(constraint);\n    return stream.value;\n  }\n  async function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  watch(enabled, (v) => {\n    if (v)\n      _start();\n    else\n      _stop();\n  }, { immediate: true });\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    enabled\n  };\n}\n\nfunction useDocumentVisibility({ document = defaultDocument } = {}) {\n  if (!document)\n    return ref(\"visible\");\n  const visibility = ref(document.visibilityState);\n  useEventListener(document, \"visibilitychange\", () => {\n    visibility.value = document.visibilityState;\n  });\n  return visibility;\n}\n\nvar __defProp$f = Object.defineProperty;\nvar __defProps$4 = Object.defineProperties;\nvar __getOwnPropDescs$4 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$h = Object.getOwnPropertySymbols;\nvar __hasOwnProp$h = Object.prototype.hasOwnProperty;\nvar __propIsEnum$h = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$f = (obj, key, value) => key in obj ? __defProp$f(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$f = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$h.call(b, prop))\n      __defNormalProp$f(a, prop, b[prop]);\n  if (__getOwnPropSymbols$h)\n    for (var prop of __getOwnPropSymbols$h(b)) {\n      if (__propIsEnum$h.call(b, prop))\n        __defNormalProp$f(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$4 = (a, b) => __defProps$4(a, __getOwnPropDescs$4(b));\nfunction useDraggable(target, options = {}) {\n  var _a, _b, _c;\n  const draggingElement = (_a = options.draggingElement) != null ? _a : defaultWindow;\n  const draggingHandle = (_b = options.handle) != null ? _b : target;\n  const position = ref((_c = resolveUnref(options.initialValue)) != null ? _c : { x: 0, y: 0 });\n  const pressedDelta = ref();\n  const filterEvent = (e) => {\n    if (options.pointerTypes)\n      return options.pointerTypes.includes(e.pointerType);\n    return true;\n  };\n  const handleEvent = (e) => {\n    if (resolveUnref(options.preventDefault))\n      e.preventDefault();\n    if (resolveUnref(options.stopPropagation))\n      e.stopPropagation();\n  };\n  const start = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (resolveUnref(options.exact) && e.target !== resolveUnref(target))\n      return;\n    const rect = resolveUnref(target).getBoundingClientRect();\n    const pos = {\n      x: e.clientX - rect.left,\n      y: e.clientY - rect.top\n    };\n    if (((_a2 = options.onStart) == null ? void 0 : _a2.call(options, pos, e)) === false)\n      return;\n    pressedDelta.value = pos;\n    handleEvent(e);\n  };\n  const move = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    position.value = {\n      x: e.clientX - pressedDelta.value.x,\n      y: e.clientY - pressedDelta.value.y\n    };\n    (_a2 = options.onMove) == null ? void 0 : _a2.call(options, position.value, e);\n    handleEvent(e);\n  };\n  const end = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    pressedDelta.value = void 0;\n    (_a2 = options.onEnd) == null ? void 0 : _a2.call(options, position.value, e);\n    handleEvent(e);\n  };\n  if (isClient) {\n    useEventListener(draggingHandle, \"pointerdown\", start, true);\n    useEventListener(draggingElement, \"pointermove\", move, true);\n    useEventListener(draggingElement, \"pointerup\", end, true);\n  }\n  return __spreadProps$4(__spreadValues$f({}, toRefs(position)), {\n    position,\n    isDragging: computed(() => !!pressedDelta.value),\n    style: computed(() => `left:${position.value.x}px;top:${position.value.y}px;`)\n  });\n}\n\nfunction useDropZone(target, onDrop) {\n  const isOverDropZone = ref(false);\n  let counter = 0;\n  if (isClient) {\n    useEventListener(target, \"dragenter\", (event) => {\n      event.preventDefault();\n      counter += 1;\n      isOverDropZone.value = true;\n    });\n    useEventListener(target, \"dragover\", (event) => {\n      event.preventDefault();\n    });\n    useEventListener(target, \"dragleave\", (event) => {\n      event.preventDefault();\n      counter -= 1;\n      if (counter === 0)\n        isOverDropZone.value = false;\n    });\n    useEventListener(target, \"drop\", (event) => {\n      var _a, _b;\n      event.preventDefault();\n      counter = 0;\n      isOverDropZone.value = false;\n      const files = Array.from((_b = (_a = event.dataTransfer) == null ? void 0 : _a.files) != null ? _b : []);\n      onDrop == null ? void 0 : onDrop(files.length === 0 ? null : files);\n    });\n  }\n  return {\n    isOverDropZone\n  };\n}\n\nvar __getOwnPropSymbols$g = Object.getOwnPropertySymbols;\nvar __hasOwnProp$g = Object.prototype.hasOwnProperty;\nvar __propIsEnum$g = Object.prototype.propertyIsEnumerable;\nvar __objRest$2 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$g.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$g)\n    for (var prop of __getOwnPropSymbols$g(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$g.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction useResizeObserver(target, callback, options = {}) {\n  const _a = options, { window = defaultWindow } = _a, observerOptions = __objRest$2(_a, [\"window\"]);\n  let observer;\n  const isSupported = useSupported(() => window && \"ResizeObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const stopWatch = watch(() => unrefElement(target), (el) => {\n    cleanup();\n    if (isSupported.value && window && el) {\n      observer = new ResizeObserver(callback);\n      observer.observe(el, observerOptions);\n    }\n  }, { immediate: true, flush: \"post\" });\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nfunction useElementBounding(target, options = {}) {\n  const {\n    reset = true,\n    windowResize = true,\n    windowScroll = true,\n    immediate = true\n  } = options;\n  const height = ref(0);\n  const bottom = ref(0);\n  const left = ref(0);\n  const right = ref(0);\n  const top = ref(0);\n  const width = ref(0);\n  const x = ref(0);\n  const y = ref(0);\n  function update() {\n    const el = unrefElement(target);\n    if (!el) {\n      if (reset) {\n        height.value = 0;\n        bottom.value = 0;\n        left.value = 0;\n        right.value = 0;\n        top.value = 0;\n        width.value = 0;\n        x.value = 0;\n        y.value = 0;\n      }\n      return;\n    }\n    const rect = el.getBoundingClientRect();\n    height.value = rect.height;\n    bottom.value = rect.bottom;\n    left.value = rect.left;\n    right.value = rect.right;\n    top.value = rect.top;\n    width.value = rect.width;\n    x.value = rect.x;\n    y.value = rect.y;\n  }\n  useResizeObserver(target, update);\n  watch(() => unrefElement(target), (ele) => !ele && update());\n  if (windowScroll)\n    useEventListener(\"scroll\", update, { capture: true, passive: true });\n  if (windowResize)\n    useEventListener(\"resize\", update, { passive: true });\n  tryOnMounted(() => {\n    if (immediate)\n      update();\n  });\n  return {\n    height,\n    bottom,\n    left,\n    right,\n    top,\n    width,\n    x,\n    y,\n    update\n  };\n}\n\nfunction useRafFn(fn, options = {}) {\n  const {\n    immediate = true,\n    window = defaultWindow\n  } = options;\n  const isActive = ref(false);\n  let previousFrameTimestamp = 0;\n  let rafId = null;\n  function loop(timestamp) {\n    if (!isActive.value || !window)\n      return;\n    const delta = timestamp - previousFrameTimestamp;\n    fn({ delta, timestamp });\n    previousFrameTimestamp = timestamp;\n    rafId = window.requestAnimationFrame(loop);\n  }\n  function resume() {\n    if (!isActive.value && window) {\n      isActive.value = true;\n      rafId = window.requestAnimationFrame(loop);\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    if (rafId != null && window) {\n      window.cancelAnimationFrame(rafId);\n      rafId = null;\n    }\n  }\n  if (immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive: readonly(isActive),\n    pause,\n    resume\n  };\n}\n\nvar __defProp$e = Object.defineProperty;\nvar __getOwnPropSymbols$f = Object.getOwnPropertySymbols;\nvar __hasOwnProp$f = Object.prototype.hasOwnProperty;\nvar __propIsEnum$f = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$e = (obj, key, value) => key in obj ? __defProp$e(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$e = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$f.call(b, prop))\n      __defNormalProp$e(a, prop, b[prop]);\n  if (__getOwnPropSymbols$f)\n    for (var prop of __getOwnPropSymbols$f(b)) {\n      if (__propIsEnum$f.call(b, prop))\n        __defNormalProp$e(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useElementByPoint(options) {\n  const element = ref(null);\n  const { x, y, document = defaultDocument } = options;\n  const controls = useRafFn(() => {\n    element.value = (document == null ? void 0 : document.elementFromPoint(resolveUnref(x), resolveUnref(y))) || null;\n  });\n  return __spreadValues$e({\n    element\n  }, controls);\n}\n\nfunction useElementHover(el, options = {}) {\n  const delayEnter = options ? options.delayEnter : 0;\n  const delayLeave = options ? options.delayLeave : 0;\n  const isHovered = ref(false);\n  let timer;\n  const toggle = (entering) => {\n    const delay = entering ? delayEnter : delayLeave;\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n    }\n    if (delay)\n      timer = setTimeout(() => isHovered.value = entering, delay);\n    else\n      isHovered.value = entering;\n  };\n  if (!window)\n    return isHovered;\n  useEventListener(el, \"mouseenter\", () => toggle(true), { passive: true });\n  useEventListener(el, \"mouseleave\", () => toggle(false), { passive: true });\n  return isHovered;\n}\n\nfunction useElementSize(target, initialSize = { width: 0, height: 0 }, options = {}) {\n  const { window = defaultWindow, box = \"content-box\" } = options;\n  const isSVG = computed(() => {\n    var _a, _b;\n    return (_b = (_a = unrefElement(target)) == null ? void 0 : _a.namespaceURI) == null ? void 0 : _b.includes(\"svg\");\n  });\n  const width = ref(initialSize.width);\n  const height = ref(initialSize.height);\n  useResizeObserver(target, ([entry]) => {\n    const boxSize = box === \"border-box\" ? entry.borderBoxSize : box === \"content-box\" ? entry.contentBoxSize : entry.devicePixelContentBoxSize;\n    if (window && isSVG.value) {\n      const $elem = unrefElement(target);\n      if ($elem) {\n        const styles = window.getComputedStyle($elem);\n        width.value = parseFloat(styles.width);\n        height.value = parseFloat(styles.height);\n      }\n    } else {\n      if (boxSize) {\n        const formatBoxSize = Array.isArray(boxSize) ? boxSize : [boxSize];\n        width.value = formatBoxSize.reduce((acc, { inlineSize }) => acc + inlineSize, 0);\n        height.value = formatBoxSize.reduce((acc, { blockSize }) => acc + blockSize, 0);\n      } else {\n        width.value = entry.contentRect.width;\n        height.value = entry.contentRect.height;\n      }\n    }\n  }, options);\n  watch(() => unrefElement(target), (ele) => {\n    width.value = ele ? initialSize.width : 0;\n    height.value = ele ? initialSize.height : 0;\n  });\n  return {\n    width,\n    height\n  };\n}\n\nfunction useElementVisibility(element, { window = defaultWindow, scrollTarget } = {}) {\n  const elementIsVisible = ref(false);\n  const testBounding = () => {\n    if (!window)\n      return;\n    const document = window.document;\n    const el = unrefElement(element);\n    if (!el) {\n      elementIsVisible.value = false;\n    } else {\n      const rect = el.getBoundingClientRect();\n      elementIsVisible.value = rect.top <= (window.innerHeight || document.documentElement.clientHeight) && rect.left <= (window.innerWidth || document.documentElement.clientWidth) && rect.bottom >= 0 && rect.right >= 0;\n    }\n  };\n  watch(() => unrefElement(element), () => testBounding(), { immediate: true, flush: \"post\" });\n  if (window) {\n    useEventListener(scrollTarget || window, \"scroll\", testBounding, {\n      capture: false,\n      passive: true\n    });\n  }\n  return elementIsVisible;\n}\n\nconst events = new Map();\n\nfunction useEventBus(key) {\n  const scope = getCurrentScope();\n  function on(listener) {\n    var _a;\n    const listeners = events.get(key) || [];\n    listeners.push(listener);\n    events.set(key, listeners);\n    const _off = () => off(listener);\n    (_a = scope == null ? void 0 : scope.cleanups) == null ? void 0 : _a.push(_off);\n    return _off;\n  }\n  function once(listener) {\n    function _listener(...args) {\n      off(_listener);\n      listener(...args);\n    }\n    return on(_listener);\n  }\n  function off(listener) {\n    const listeners = events.get(key);\n    if (!listeners)\n      return;\n    const index = listeners.indexOf(listener);\n    if (index > -1)\n      listeners.splice(index, 1);\n    if (!listeners.length)\n      events.delete(key);\n  }\n  function reset() {\n    events.delete(key);\n  }\n  function emit(event, payload) {\n    var _a;\n    (_a = events.get(key)) == null ? void 0 : _a.forEach((v) => v(event, payload));\n  }\n  return { on, once, off, emit, reset };\n}\n\nfunction useEventSource(url, events = [], options = {}) {\n  const event = ref(null);\n  const data = ref(null);\n  const status = ref(\"CONNECTING\");\n  const eventSource = ref(null);\n  const error = ref(null);\n  const {\n    withCredentials = false\n  } = options;\n  const close = () => {\n    if (eventSource.value) {\n      eventSource.value.close();\n      eventSource.value = null;\n      status.value = \"CLOSED\";\n    }\n  };\n  const es = new EventSource(url, { withCredentials });\n  eventSource.value = es;\n  es.onopen = () => {\n    status.value = \"OPEN\";\n    error.value = null;\n  };\n  es.onerror = (e) => {\n    status.value = \"CLOSED\";\n    error.value = e;\n  };\n  es.onmessage = (e) => {\n    event.value = null;\n    data.value = e.data;\n  };\n  for (const event_name of events) {\n    useEventListener(es, event_name, (e) => {\n      event.value = event_name;\n      data.value = e.data || null;\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    eventSource,\n    event,\n    data,\n    status,\n    error,\n    close\n  };\n}\n\nfunction useEyeDropper(options = {}) {\n  const { initialValue = \"\" } = options;\n  const isSupported = useSupported(() => typeof window !== \"undefined\" && \"EyeDropper\" in window);\n  const sRGBHex = ref(initialValue);\n  async function open(openOptions) {\n    if (!isSupported.value)\n      return;\n    const eyeDropper = new window.EyeDropper();\n    const result = await eyeDropper.open(openOptions);\n    sRGBHex.value = result.sRGBHex;\n    return result;\n  }\n  return { isSupported, sRGBHex, open };\n}\n\nfunction useFavicon(newIcon = null, options = {}) {\n  const {\n    baseUrl = \"\",\n    rel = \"icon\",\n    document = defaultDocument\n  } = options;\n  const favicon = resolveRef(newIcon);\n  const applyIcon = (icon) => {\n    document == null ? void 0 : document.head.querySelectorAll(`link[rel*=\"${rel}\"]`).forEach((el) => el.href = `${baseUrl}${icon}`);\n  };\n  watch(favicon, (i, o) => {\n    if (isString(i) && i !== o)\n      applyIcon(i);\n  }, { immediate: true });\n  return favicon;\n}\n\nvar __defProp$d = Object.defineProperty;\nvar __defProps$3 = Object.defineProperties;\nvar __getOwnPropDescs$3 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$e = Object.getOwnPropertySymbols;\nvar __hasOwnProp$e = Object.prototype.hasOwnProperty;\nvar __propIsEnum$e = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$d = (obj, key, value) => key in obj ? __defProp$d(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$d = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$e.call(b, prop))\n      __defNormalProp$d(a, prop, b[prop]);\n  if (__getOwnPropSymbols$e)\n    for (var prop of __getOwnPropSymbols$e(b)) {\n      if (__propIsEnum$e.call(b, prop))\n        __defNormalProp$d(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$3 = (a, b) => __defProps$3(a, __getOwnPropDescs$3(b));\nconst payloadMapping = {\n  json: \"application/json\",\n  text: \"text/plain\"\n};\nfunction isFetchOptions(obj) {\n  return obj && containsProp(obj, \"immediate\", \"refetch\", \"initialData\", \"timeout\", \"beforeFetch\", \"afterFetch\", \"onFetchError\", \"fetch\");\n}\nfunction isAbsoluteURL(url) {\n  return /^([a-z][a-z\\d+\\-.]*:)?\\/\\//i.test(url);\n}\nfunction headersToObject(headers) {\n  if (typeof Headers !== \"undefined\" && headers instanceof Headers)\n    return Object.fromEntries([...headers.entries()]);\n  return headers;\n}\nfunction combineCallbacks(combination, ...callbacks) {\n  if (combination === \"overwrite\") {\n    return async (ctx) => {\n      const callback = callbacks[callbacks.length - 1];\n      if (callback !== void 0)\n        await callback(ctx);\n      return ctx;\n    };\n  } else {\n    return async (ctx) => {\n      await callbacks.reduce((prevCallback, callback) => prevCallback.then(async () => {\n        if (callback)\n          ctx = __spreadValues$d(__spreadValues$d({}, ctx), await callback(ctx));\n      }), Promise.resolve());\n      return ctx;\n    };\n  }\n}\nfunction createFetch(config = {}) {\n  const _combination = config.combination || \"chain\";\n  const _options = config.options || {};\n  const _fetchOptions = config.fetchOptions || {};\n  function useFactoryFetch(url, ...args) {\n    const computedUrl = computed(() => {\n      const baseUrl = resolveUnref(config.baseUrl);\n      const targetUrl = resolveUnref(url);\n      return baseUrl && !isAbsoluteURL(targetUrl) ? joinPaths(baseUrl, targetUrl) : targetUrl;\n    });\n    let options = _options;\n    let fetchOptions = _fetchOptions;\n    if (args.length > 0) {\n      if (isFetchOptions(args[0])) {\n        options = __spreadProps$3(__spreadValues$d(__spreadValues$d({}, options), args[0]), {\n          beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[0].beforeFetch),\n          afterFetch: combineCallbacks(_combination, _options.afterFetch, args[0].afterFetch),\n          onFetchError: combineCallbacks(_combination, _options.onFetchError, args[0].onFetchError)\n        });\n      } else {\n        fetchOptions = __spreadProps$3(__spreadValues$d(__spreadValues$d({}, fetchOptions), args[0]), {\n          headers: __spreadValues$d(__spreadValues$d({}, headersToObject(fetchOptions.headers) || {}), headersToObject(args[0].headers) || {})\n        });\n      }\n    }\n    if (args.length > 1 && isFetchOptions(args[1])) {\n      options = __spreadProps$3(__spreadValues$d(__spreadValues$d({}, options), args[1]), {\n        beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[1].beforeFetch),\n        afterFetch: combineCallbacks(_combination, _options.afterFetch, args[1].afterFetch),\n        onFetchError: combineCallbacks(_combination, _options.onFetchError, args[1].onFetchError)\n      });\n    }\n    return useFetch(computedUrl, fetchOptions, options);\n  }\n  return useFactoryFetch;\n}\nfunction useFetch(url, ...args) {\n  var _a;\n  const supportsAbort = typeof AbortController === \"function\";\n  let fetchOptions = {};\n  let options = { immediate: true, refetch: false, timeout: 0 };\n  const config = {\n    method: \"GET\",\n    type: \"text\",\n    payload: void 0\n  };\n  if (args.length > 0) {\n    if (isFetchOptions(args[0]))\n      options = __spreadValues$d(__spreadValues$d({}, options), args[0]);\n    else\n      fetchOptions = args[0];\n  }\n  if (args.length > 1) {\n    if (isFetchOptions(args[1]))\n      options = __spreadValues$d(__spreadValues$d({}, options), args[1]);\n  }\n  const {\n    fetch = (_a = defaultWindow) == null ? void 0 : _a.fetch,\n    initialData,\n    timeout\n  } = options;\n  const responseEvent = createEventHook();\n  const errorEvent = createEventHook();\n  const finallyEvent = createEventHook();\n  const isFinished = ref(false);\n  const isFetching = ref(false);\n  const aborted = ref(false);\n  const statusCode = ref(null);\n  const response = shallowRef(null);\n  const error = shallowRef(null);\n  const data = shallowRef(initialData);\n  const canAbort = computed(() => supportsAbort && isFetching.value);\n  let controller;\n  let timer;\n  const abort = () => {\n    if (supportsAbort && controller) {\n      controller.abort();\n      controller = void 0;\n    }\n  };\n  const loading = (isLoading) => {\n    isFetching.value = isLoading;\n    isFinished.value = !isLoading;\n  };\n  if (timeout)\n    timer = useTimeoutFn(abort, timeout, { immediate: false });\n  const execute = async (throwOnFailed = false) => {\n    var _a2;\n    loading(true);\n    error.value = null;\n    statusCode.value = null;\n    aborted.value = false;\n    if (supportsAbort) {\n      abort();\n      controller = new AbortController();\n      controller.signal.onabort = () => aborted.value = true;\n      fetchOptions = __spreadProps$3(__spreadValues$d({}, fetchOptions), {\n        signal: controller.signal\n      });\n    }\n    const defaultFetchOptions = {\n      method: config.method,\n      headers: {}\n    };\n    if (config.payload) {\n      const headers = headersToObject(defaultFetchOptions.headers);\n      if (config.payloadType)\n        headers[\"Content-Type\"] = (_a2 = payloadMapping[config.payloadType]) != null ? _a2 : config.payloadType;\n      const payload = resolveUnref(config.payload);\n      defaultFetchOptions.body = config.payloadType === \"json\" ? JSON.stringify(payload) : payload;\n    }\n    let isCanceled = false;\n    const context = {\n      url: resolveUnref(url),\n      options: __spreadValues$d(__spreadValues$d({}, defaultFetchOptions), fetchOptions),\n      cancel: () => {\n        isCanceled = true;\n      }\n    };\n    if (options.beforeFetch)\n      Object.assign(context, await options.beforeFetch(context));\n    if (isCanceled || !fetch) {\n      loading(false);\n      return Promise.resolve(null);\n    }\n    let responseData = null;\n    if (timer)\n      timer.start();\n    return new Promise((resolve, reject) => {\n      var _a3;\n      fetch(context.url, __spreadProps$3(__spreadValues$d(__spreadValues$d({}, defaultFetchOptions), context.options), {\n        headers: __spreadValues$d(__spreadValues$d({}, headersToObject(defaultFetchOptions.headers)), headersToObject((_a3 = context.options) == null ? void 0 : _a3.headers))\n      })).then(async (fetchResponse) => {\n        response.value = fetchResponse;\n        statusCode.value = fetchResponse.status;\n        responseData = await fetchResponse[config.type]();\n        if (options.afterFetch && statusCode.value >= 200 && statusCode.value < 300)\n          ({ data: responseData } = await options.afterFetch({ data: responseData, response: fetchResponse }));\n        data.value = responseData;\n        if (!fetchResponse.ok)\n          throw new Error(fetchResponse.statusText);\n        responseEvent.trigger(fetchResponse);\n        return resolve(fetchResponse);\n      }).catch(async (fetchError) => {\n        let errorData = fetchError.message || fetchError.name;\n        if (options.onFetchError)\n          ({ data: responseData, error: errorData } = await options.onFetchError({ data: responseData, error: fetchError, response: response.value }));\n        data.value = responseData;\n        error.value = errorData;\n        errorEvent.trigger(fetchError);\n        if (throwOnFailed)\n          return reject(fetchError);\n        return resolve(null);\n      }).finally(() => {\n        loading(false);\n        if (timer)\n          timer.stop();\n        finallyEvent.trigger(null);\n      });\n    });\n  };\n  const refetch = resolveRef(options.refetch);\n  watch([\n    refetch,\n    resolveRef(url)\n  ], ([refetch2]) => refetch2 && execute(), { deep: true });\n  const shell = {\n    isFinished,\n    statusCode,\n    response,\n    error,\n    data,\n    isFetching,\n    canAbort,\n    aborted,\n    abort,\n    execute,\n    onFetchResponse: responseEvent.on,\n    onFetchError: errorEvent.on,\n    onFetchFinally: finallyEvent.on,\n    get: setMethod(\"GET\"),\n    put: setMethod(\"PUT\"),\n    post: setMethod(\"POST\"),\n    delete: setMethod(\"DELETE\"),\n    patch: setMethod(\"PATCH\"),\n    head: setMethod(\"HEAD\"),\n    options: setMethod(\"OPTIONS\"),\n    json: setType(\"json\"),\n    text: setType(\"text\"),\n    blob: setType(\"blob\"),\n    arrayBuffer: setType(\"arrayBuffer\"),\n    formData: setType(\"formData\")\n  };\n  function setMethod(method) {\n    return (payload, payloadType) => {\n      if (!isFetching.value) {\n        config.method = method;\n        config.payload = payload;\n        config.payloadType = payloadType;\n        if (isRef(config.payload)) {\n          watch([\n            refetch,\n            resolveRef(config.payload)\n          ], ([refetch2]) => refetch2 && execute(), { deep: true });\n        }\n        const rawPayload = resolveUnref(config.payload);\n        if (!payloadType && rawPayload && Object.getPrototypeOf(rawPayload) === Object.prototype && !(rawPayload instanceof FormData))\n          config.payloadType = \"json\";\n        return __spreadProps$3(__spreadValues$d({}, shell), {\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        });\n      }\n      return void 0;\n    };\n  }\n  function waitUntilFinished() {\n    return new Promise((resolve, reject) => {\n      until(isFinished).toBe(true).then(() => resolve(shell)).catch((error2) => reject(error2));\n    });\n  }\n  function setType(type) {\n    return () => {\n      if (!isFetching.value) {\n        config.type = type;\n        return __spreadProps$3(__spreadValues$d({}, shell), {\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        });\n      }\n      return void 0;\n    };\n  }\n  if (options.immediate)\n    setTimeout(execute, 0);\n  return __spreadProps$3(__spreadValues$d({}, shell), {\n    then(onFulfilled, onRejected) {\n      return waitUntilFinished().then(onFulfilled, onRejected);\n    }\n  });\n}\nfunction joinPaths(start, end) {\n  if (!start.endsWith(\"/\") && !end.startsWith(\"/\"))\n    return `${start}/${end}`;\n  return `${start}${end}`;\n}\n\nvar __defProp$c = Object.defineProperty;\nvar __getOwnPropSymbols$d = Object.getOwnPropertySymbols;\nvar __hasOwnProp$d = Object.prototype.hasOwnProperty;\nvar __propIsEnum$d = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$c = (obj, key, value) => key in obj ? __defProp$c(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$c = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$d.call(b, prop))\n      __defNormalProp$c(a, prop, b[prop]);\n  if (__getOwnPropSymbols$d)\n    for (var prop of __getOwnPropSymbols$d(b)) {\n      if (__propIsEnum$d.call(b, prop))\n        __defNormalProp$c(a, prop, b[prop]);\n    }\n  return a;\n};\nconst DEFAULT_OPTIONS = {\n  multiple: true,\n  accept: \"*\"\n};\nfunction useFileDialog(options = {}) {\n  const {\n    document = defaultDocument\n  } = options;\n  const files = ref(null);\n  let input;\n  if (document) {\n    input = document.createElement(\"input\");\n    input.type = \"file\";\n    input.onchange = (event) => {\n      const result = event.target;\n      files.value = result.files;\n    };\n  }\n  const open = (localOptions) => {\n    if (!input)\n      return;\n    const _options = __spreadValues$c(__spreadValues$c(__spreadValues$c({}, DEFAULT_OPTIONS), options), localOptions);\n    input.multiple = _options.multiple;\n    input.accept = _options.accept;\n    if (hasOwn(_options, \"capture\"))\n      input.capture = _options.capture;\n    input.click();\n  };\n  const reset = () => {\n    files.value = null;\n    if (input)\n      input.value = \"\";\n  };\n  return {\n    files: readonly(files),\n    open,\n    reset\n  };\n}\n\nvar __defProp$b = Object.defineProperty;\nvar __getOwnPropSymbols$c = Object.getOwnPropertySymbols;\nvar __hasOwnProp$c = Object.prototype.hasOwnProperty;\nvar __propIsEnum$c = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$b = (obj, key, value) => key in obj ? __defProp$b(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$b = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$c.call(b, prop))\n      __defNormalProp$b(a, prop, b[prop]);\n  if (__getOwnPropSymbols$c)\n    for (var prop of __getOwnPropSymbols$c(b)) {\n      if (__propIsEnum$c.call(b, prop))\n        __defNormalProp$b(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useFileSystemAccess(options = {}) {\n  const {\n    window: _window = defaultWindow,\n    dataType = \"Text\"\n  } = unref(options);\n  const window = _window;\n  const isSupported = useSupported(() => window && \"showSaveFilePicker\" in window && \"showOpenFilePicker\" in window);\n  const fileHandle = ref();\n  const data = ref();\n  const file = ref();\n  const fileName = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.name) != null ? _b : \"\";\n  });\n  const fileMIME = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.type) != null ? _b : \"\";\n  });\n  const fileSize = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.size) != null ? _b : 0;\n  });\n  const fileLastModified = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.lastModified) != null ? _b : 0;\n  });\n  async function open(_options = {}) {\n    if (!isSupported.value)\n      return;\n    const [handle] = await window.showOpenFilePicker(__spreadValues$b(__spreadValues$b({}, unref(options)), _options));\n    fileHandle.value = handle;\n    await updateFile();\n    await updateData();\n  }\n  async function create(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker(__spreadValues$b(__spreadValues$b({}, unref(options)), _options));\n    data.value = void 0;\n    await updateFile();\n    await updateData();\n  }\n  async function save(_options = {}) {\n    if (!isSupported.value)\n      return;\n    if (!fileHandle.value)\n      return saveAs(_options);\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function saveAs(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker(__spreadValues$b(__spreadValues$b({}, unref(options)), _options));\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function updateFile() {\n    var _a;\n    file.value = await ((_a = fileHandle.value) == null ? void 0 : _a.getFile());\n  }\n  async function updateData() {\n    var _a, _b;\n    if (unref(dataType) === \"Text\")\n      data.value = await ((_a = file.value) == null ? void 0 : _a.text());\n    if (unref(dataType) === \"ArrayBuffer\")\n      data.value = await ((_b = file.value) == null ? void 0 : _b.arrayBuffer());\n    if (unref(dataType) === \"Blob\")\n      data.value = file.value;\n  }\n  watch(() => unref(dataType), updateData);\n  return {\n    isSupported,\n    data,\n    file,\n    fileName,\n    fileMIME,\n    fileSize,\n    fileLastModified,\n    open,\n    create,\n    save,\n    saveAs,\n    updateData\n  };\n}\n\nfunction useFocus(target, options = {}) {\n  const { initialValue = false } = options;\n  const innerFocused = ref(false);\n  const targetElement = computed(() => unrefElement(target));\n  useEventListener(targetElement, \"focus\", () => innerFocused.value = true);\n  useEventListener(targetElement, \"blur\", () => innerFocused.value = false);\n  const focused = computed({\n    get: () => innerFocused.value,\n    set(value) {\n      var _a, _b;\n      if (!value && innerFocused.value)\n        (_a = targetElement.value) == null ? void 0 : _a.blur();\n      else if (value && !innerFocused.value)\n        (_b = targetElement.value) == null ? void 0 : _b.focus();\n    }\n  });\n  watch(targetElement, () => {\n    focused.value = initialValue;\n  }, { immediate: true, flush: \"post\" });\n  return { focused };\n}\n\nfunction useFocusWithin(target, options = {}) {\n  const activeElement = useActiveElement(options);\n  const targetElement = computed(() => unrefElement(target));\n  const focused = computed(() => targetElement.value && activeElement.value ? targetElement.value.contains(activeElement.value) : false);\n  return { focused };\n}\n\nfunction useFps(options) {\n  var _a;\n  const fps = ref(0);\n  if (typeof performance === \"undefined\")\n    return fps;\n  const every = (_a = options == null ? void 0 : options.every) != null ? _a : 10;\n  let last = performance.now();\n  let ticks = 0;\n  useRafFn(() => {\n    ticks += 1;\n    if (ticks >= every) {\n      const now = performance.now();\n      const diff = now - last;\n      fps.value = Math.round(1e3 / (diff / ticks));\n      last = now;\n      ticks = 0;\n    }\n  });\n  return fps;\n}\n\nconst functionsMap = [\n  [\n    \"requestFullscreen\",\n    \"exitFullscreen\",\n    \"fullscreenElement\",\n    \"fullscreenEnabled\",\n    \"fullscreenchange\",\n    \"fullscreenerror\"\n  ],\n  [\n    \"webkitRequestFullscreen\",\n    \"webkitExitFullscreen\",\n    \"webkitFullscreenElement\",\n    \"webkitFullscreenEnabled\",\n    \"webkitfullscreenchange\",\n    \"webkitfullscreenerror\"\n  ],\n  [\n    \"webkitRequestFullScreen\",\n    \"webkitCancelFullScreen\",\n    \"webkitCurrentFullScreenElement\",\n    \"webkitCancelFullScreen\",\n    \"webkitfullscreenchange\",\n    \"webkitfullscreenerror\"\n  ],\n  [\n    \"mozRequestFullScreen\",\n    \"mozCancelFullScreen\",\n    \"mozFullScreenElement\",\n    \"mozFullScreenEnabled\",\n    \"mozfullscreenchange\",\n    \"mozfullscreenerror\"\n  ],\n  [\n    \"msRequestFullscreen\",\n    \"msExitFullscreen\",\n    \"msFullscreenElement\",\n    \"msFullscreenEnabled\",\n    \"MSFullscreenChange\",\n    \"MSFullscreenError\"\n  ]\n];\nfunction useFullscreen(target, options = {}) {\n  const { document = defaultDocument, autoExit = false } = options;\n  const targetRef = target || (document == null ? void 0 : document.querySelector(\"html\"));\n  const isFullscreen = ref(false);\n  let map = functionsMap[0];\n  const isSupported = useSupported(() => {\n    if (!document) {\n      return false;\n    } else {\n      for (const m of functionsMap) {\n        if (m[1] in document) {\n          map = m;\n          return true;\n        }\n      }\n    }\n    return false;\n  });\n  const [REQUEST, EXIT, ELEMENT, , EVENT] = map;\n  async function exit() {\n    if (!isSupported.value)\n      return;\n    if (document == null ? void 0 : document[ELEMENT])\n      await document[EXIT]();\n    isFullscreen.value = false;\n  }\n  async function enter() {\n    if (!isSupported.value)\n      return;\n    await exit();\n    const target2 = unrefElement(targetRef);\n    if (target2) {\n      await target2[REQUEST]();\n      isFullscreen.value = true;\n    }\n  }\n  async function toggle() {\n    if (isFullscreen.value)\n      await exit();\n    else\n      await enter();\n  }\n  if (document) {\n    useEventListener(document, EVENT, () => {\n      isFullscreen.value = !!(document == null ? void 0 : document[ELEMENT]);\n    }, false);\n  }\n  if (autoExit)\n    tryOnScopeDispose(exit);\n  return {\n    isSupported,\n    isFullscreen,\n    enter,\n    exit,\n    toggle\n  };\n}\n\nfunction mapGamepadToXbox360Controller(gamepad) {\n  return computed(() => {\n    if (gamepad.value) {\n      return {\n        buttons: {\n          a: gamepad.value.buttons[0],\n          b: gamepad.value.buttons[1],\n          x: gamepad.value.buttons[2],\n          y: gamepad.value.buttons[3]\n        },\n        bumper: {\n          left: gamepad.value.buttons[4],\n          right: gamepad.value.buttons[5]\n        },\n        triggers: {\n          left: gamepad.value.buttons[6],\n          right: gamepad.value.buttons[7]\n        },\n        stick: {\n          left: {\n            horizontal: gamepad.value.axes[0],\n            vertical: gamepad.value.axes[1],\n            button: gamepad.value.buttons[10]\n          },\n          right: {\n            horizontal: gamepad.value.axes[2],\n            vertical: gamepad.value.axes[3],\n            button: gamepad.value.buttons[11]\n          }\n        },\n        dpad: {\n          up: gamepad.value.buttons[12],\n          down: gamepad.value.buttons[13],\n          left: gamepad.value.buttons[14],\n          right: gamepad.value.buttons[15]\n        },\n        back: gamepad.value.buttons[8],\n        start: gamepad.value.buttons[9]\n      };\n    }\n    return null;\n  });\n}\nfunction useGamepad(options = {}) {\n  const {\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"getGamepads\" in navigator);\n  const gamepads = ref([]);\n  const onConnectedHook = createEventHook();\n  const onDisconnectedHook = createEventHook();\n  const stateFromGamepad = (gamepad) => {\n    const hapticActuators = [];\n    const vibrationActuator = \"vibrationActuator\" in gamepad ? gamepad.vibrationActuator : null;\n    if (vibrationActuator)\n      hapticActuators.push(vibrationActuator);\n    if (gamepad.hapticActuators)\n      hapticActuators.push(...gamepad.hapticActuators);\n    return {\n      id: gamepad.id,\n      hapticActuators,\n      index: gamepad.index,\n      mapping: gamepad.mapping,\n      connected: gamepad.connected,\n      timestamp: gamepad.timestamp,\n      axes: gamepad.axes.map((axes) => axes),\n      buttons: gamepad.buttons.map((button) => ({ pressed: button.pressed, touched: button.touched, value: button.value }))\n    };\n  };\n  const updateGamepadState = () => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    for (let i = 0; i < _gamepads.length; ++i) {\n      const gamepad = _gamepads[i];\n      if (gamepad) {\n        const index = gamepads.value.findIndex(({ index: index2 }) => index2 === gamepad.index);\n        if (index > -1)\n          gamepads.value[index] = stateFromGamepad(gamepad);\n      }\n    }\n  };\n  const { isActive, pause, resume } = useRafFn(updateGamepadState);\n  const onGamepadConnected = (gamepad) => {\n    if (!gamepads.value.some(({ index }) => index === gamepad.index)) {\n      gamepads.value.push(stateFromGamepad(gamepad));\n      onConnectedHook.trigger(gamepad.index);\n    }\n    resume();\n  };\n  const onGamepadDisconnected = (gamepad) => {\n    gamepads.value = gamepads.value.filter((x) => x.index !== gamepad.index);\n    onDisconnectedHook.trigger(gamepad.index);\n  };\n  useEventListener(\"gamepadconnected\", (e) => onGamepadConnected(e.gamepad));\n  useEventListener(\"gamepaddisconnected\", (e) => onGamepadDisconnected(e.gamepad));\n  tryOnMounted(() => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    if (_gamepads) {\n      for (let i = 0; i < _gamepads.length; ++i) {\n        const gamepad = _gamepads[i];\n        if (gamepad)\n          onGamepadConnected(gamepad);\n      }\n    }\n  });\n  pause();\n  return {\n    isSupported,\n    onConnected: onConnectedHook.on,\n    onDisconnected: onDisconnectedHook.on,\n    gamepads,\n    pause,\n    resume,\n    isActive\n  };\n}\n\nfunction useGeolocation(options = {}) {\n  const {\n    enableHighAccuracy = true,\n    maximumAge = 3e4,\n    timeout = 27e3,\n    navigator = defaultNavigator,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => navigator && \"geolocation\" in navigator);\n  const locatedAt = ref(null);\n  const error = ref(null);\n  const coords = ref({\n    accuracy: 0,\n    latitude: Infinity,\n    longitude: Infinity,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    speed: null\n  });\n  function updatePosition(position) {\n    locatedAt.value = position.timestamp;\n    coords.value = position.coords;\n    error.value = null;\n  }\n  let watcher;\n  function resume() {\n    if (isSupported.value) {\n      watcher = navigator.geolocation.watchPosition(updatePosition, (err) => error.value = err, {\n        enableHighAccuracy,\n        maximumAge,\n        timeout\n      });\n    }\n  }\n  if (immediate)\n    resume();\n  function pause() {\n    if (watcher && navigator)\n      navigator.geolocation.clearWatch(watcher);\n  }\n  tryOnScopeDispose(() => {\n    pause();\n  });\n  return {\n    isSupported,\n    coords,\n    locatedAt,\n    error,\n    resume,\n    pause\n  };\n}\n\nconst defaultEvents$1 = [\"mousemove\", \"mousedown\", \"resize\", \"keydown\", \"touchstart\", \"wheel\"];\nconst oneMinute = 6e4;\nfunction useIdle(timeout = oneMinute, options = {}) {\n  const {\n    initialState = false,\n    listenForVisibilityChange = true,\n    events = defaultEvents$1,\n    window = defaultWindow,\n    eventFilter = throttleFilter(50)\n  } = options;\n  const idle = ref(initialState);\n  const lastActive = ref(timestamp());\n  let timer;\n  const onEvent = createFilterWrapper(eventFilter, () => {\n    idle.value = false;\n    lastActive.value = timestamp();\n    clearTimeout(timer);\n    timer = setTimeout(() => idle.value = true, timeout);\n  });\n  if (window) {\n    const document = window.document;\n    for (const event of events)\n      useEventListener(window, event, onEvent, { passive: true });\n    if (listenForVisibilityChange) {\n      useEventListener(document, \"visibilitychange\", () => {\n        if (!document.hidden)\n          onEvent();\n      });\n    }\n  }\n  timer = setTimeout(() => idle.value = true, timeout);\n  return { idle, lastActive };\n}\n\nvar __defProp$a = Object.defineProperty;\nvar __getOwnPropSymbols$b = Object.getOwnPropertySymbols;\nvar __hasOwnProp$b = Object.prototype.hasOwnProperty;\nvar __propIsEnum$b = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$a = (obj, key, value) => key in obj ? __defProp$a(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$a = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$b.call(b, prop))\n      __defNormalProp$a(a, prop, b[prop]);\n  if (__getOwnPropSymbols$b)\n    for (var prop of __getOwnPropSymbols$b(b)) {\n      if (__propIsEnum$b.call(b, prop))\n        __defNormalProp$a(a, prop, b[prop]);\n    }\n  return a;\n};\nasync function loadImage(options) {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const { src, srcset, sizes } = options;\n    img.src = src;\n    if (srcset)\n      img.srcset = srcset;\n    if (sizes)\n      img.sizes = sizes;\n    img.onload = () => resolve(img);\n    img.onerror = reject;\n  });\n}\nconst useImage = (options, asyncStateOptions = {}) => {\n  const state = useAsyncState(() => loadImage(resolveUnref(options)), void 0, __spreadValues$a({\n    resetOnExecute: true\n  }, asyncStateOptions));\n  watch(() => resolveUnref(options), () => state.execute(asyncStateOptions.delay), { deep: true });\n  return state;\n};\n\nconst ARRIVED_STATE_THRESHOLD_PIXELS = 1;\nfunction useScroll(element, options = {}) {\n  const {\n    throttle = 0,\n    idle = 200,\n    onStop = noop,\n    onScroll = noop,\n    offset = {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0\n    },\n    eventListenerOptions = {\n      capture: false,\n      passive: true\n    },\n    behavior = \"auto\"\n  } = options;\n  const internalX = ref(0);\n  const internalY = ref(0);\n  const x = computed({\n    get() {\n      return internalX.value;\n    },\n    set(x2) {\n      scrollTo(x2, void 0);\n    }\n  });\n  const y = computed({\n    get() {\n      return internalY.value;\n    },\n    set(y2) {\n      scrollTo(void 0, y2);\n    }\n  });\n  function scrollTo(_x, _y) {\n    var _a, _b, _c;\n    const _element = resolveUnref(element);\n    if (!_element)\n      return;\n    (_c = _element instanceof Document ? document.body : _element) == null ? void 0 : _c.scrollTo({\n      top: (_a = resolveUnref(_y)) != null ? _a : y.value,\n      left: (_b = resolveUnref(_x)) != null ? _b : x.value,\n      behavior: resolveUnref(behavior)\n    });\n  }\n  const isScrolling = ref(false);\n  const arrivedState = reactive({\n    left: true,\n    right: false,\n    top: true,\n    bottom: false\n  });\n  const directions = reactive({\n    left: false,\n    right: false,\n    top: false,\n    bottom: false\n  });\n  const onScrollEnd = (e) => {\n    if (!isScrolling.value)\n      return;\n    isScrolling.value = false;\n    directions.left = false;\n    directions.right = false;\n    directions.top = false;\n    directions.bottom = false;\n    onStop(e);\n  };\n  const onScrollEndDebounced = useDebounceFn(onScrollEnd, throttle + idle);\n  const onScrollHandler = (e) => {\n    const eventTarget = e.target === document ? e.target.documentElement : e.target;\n    const scrollLeft = eventTarget.scrollLeft;\n    directions.left = scrollLeft < internalX.value;\n    directions.right = scrollLeft > internalY.value;\n    arrivedState.left = scrollLeft <= 0 + (offset.left || 0);\n    arrivedState.right = scrollLeft + eventTarget.clientWidth >= eventTarget.scrollWidth - (offset.right || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    internalX.value = scrollLeft;\n    let scrollTop = eventTarget.scrollTop;\n    if (e.target === document && !scrollTop)\n      scrollTop = document.body.scrollTop;\n    directions.top = scrollTop < internalY.value;\n    directions.bottom = scrollTop > internalY.value;\n    arrivedState.top = scrollTop <= 0 + (offset.top || 0);\n    arrivedState.bottom = scrollTop + eventTarget.clientHeight >= eventTarget.scrollHeight - (offset.bottom || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    internalY.value = scrollTop;\n    isScrolling.value = true;\n    onScrollEndDebounced(e);\n    onScroll(e);\n  };\n  useEventListener(element, \"scroll\", throttle ? useThrottleFn(onScrollHandler, throttle, true, false) : onScrollHandler, eventListenerOptions);\n  useEventListener(element, \"scrollend\", onScrollEnd, eventListenerOptions);\n  return {\n    x,\n    y,\n    isScrolling,\n    arrivedState,\n    directions\n  };\n}\n\nvar __defProp$9 = Object.defineProperty;\nvar __defProps$2 = Object.defineProperties;\nvar __getOwnPropDescs$2 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$a = Object.getOwnPropertySymbols;\nvar __hasOwnProp$a = Object.prototype.hasOwnProperty;\nvar __propIsEnum$a = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$9 = (obj, key, value) => key in obj ? __defProp$9(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$9 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$a.call(b, prop))\n      __defNormalProp$9(a, prop, b[prop]);\n  if (__getOwnPropSymbols$a)\n    for (var prop of __getOwnPropSymbols$a(b)) {\n      if (__propIsEnum$a.call(b, prop))\n        __defNormalProp$9(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$2 = (a, b) => __defProps$2(a, __getOwnPropDescs$2(b));\nfunction useInfiniteScroll(element, onLoadMore, options = {}) {\n  var _a, _b;\n  const direction = (_a = options.direction) != null ? _a : \"bottom\";\n  const state = reactive(useScroll(element, __spreadProps$2(__spreadValues$9({}, options), {\n    offset: __spreadValues$9({\n      [direction]: (_b = options.distance) != null ? _b : 0\n    }, options.offset)\n  })));\n  watch(() => state.arrivedState[direction], async (v) => {\n    var _a2, _b2;\n    if (v) {\n      const elem = resolveUnref(element);\n      const previous = {\n        height: (_a2 = elem == null ? void 0 : elem.scrollHeight) != null ? _a2 : 0,\n        width: (_b2 = elem == null ? void 0 : elem.scrollWidth) != null ? _b2 : 0\n      };\n      await onLoadMore(state);\n      if (options.preserveScrollPosition && elem) {\n        nextTick(() => {\n          elem.scrollTo({\n            top: elem.scrollHeight - previous.height,\n            left: elem.scrollWidth - previous.width\n          });\n        });\n      }\n    }\n  });\n}\n\nfunction useIntersectionObserver(target, callback, options = {}) {\n  const {\n    root,\n    rootMargin = \"0px\",\n    threshold = 0.1,\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"IntersectionObserver\" in window);\n  let cleanup = noop;\n  const stopWatch = isSupported.value ? watch(() => ({\n    el: unrefElement(target),\n    root: unrefElement(root)\n  }), ({ el, root: root2 }) => {\n    cleanup();\n    if (!el)\n      return;\n    const observer = new IntersectionObserver(callback, {\n      root: root2,\n      rootMargin,\n      threshold\n    });\n    observer.observe(el);\n    cleanup = () => {\n      observer.disconnect();\n      cleanup = noop;\n    };\n  }, { immediate: true, flush: \"post\" }) : noop;\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nconst defaultEvents = [\"mousedown\", \"mouseup\", \"keydown\", \"keyup\"];\nfunction useKeyModifier(modifier, options = {}) {\n  const {\n    events = defaultEvents,\n    document = defaultDocument,\n    initial = null\n  } = options;\n  const state = ref(initial);\n  if (document) {\n    events.forEach((listenerEvent) => {\n      useEventListener(document, listenerEvent, (evt) => {\n        if (typeof evt.getModifierState === \"function\")\n          state.value = evt.getModifierState(modifier);\n      });\n    });\n  }\n  return state;\n}\n\nfunction useLocalStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.localStorage, options);\n}\n\nconst DefaultMagicKeysAliasMap = {\n  ctrl: \"control\",\n  command: \"meta\",\n  cmd: \"meta\",\n  option: \"alt\",\n  up: \"arrowup\",\n  down: \"arrowdown\",\n  left: \"arrowleft\",\n  right: \"arrowright\"\n};\n\nfunction useMagicKeys(options = {}) {\n  const {\n    reactive: useReactive = false,\n    target = defaultWindow,\n    aliasMap = DefaultMagicKeysAliasMap,\n    passive = true,\n    onEventFired = noop\n  } = options;\n  const current = reactive(new Set());\n  const obj = {\n    toJSON() {\n      return {};\n    },\n    current\n  };\n  const refs = useReactive ? reactive(obj) : obj;\n  const metaDeps = new Set();\n  const usedKeys = new Set();\n  function setRefs(key, value) {\n    if (key in refs) {\n      if (useReactive)\n        refs[key] = value;\n      else\n        refs[key].value = value;\n    }\n  }\n  function reset() {\n    current.clear();\n    for (const key of usedKeys)\n      setRefs(key, false);\n  }\n  function updateRefs(e, value) {\n    var _a, _b;\n    const key = (_a = e.key) == null ? void 0 : _a.toLowerCase();\n    const code = (_b = e.code) == null ? void 0 : _b.toLowerCase();\n    const values = [code, key].filter(Boolean);\n    if (key) {\n      if (value)\n        current.add(key);\n      else\n        current.delete(key);\n    }\n    for (const key2 of values) {\n      usedKeys.add(key2);\n      setRefs(key2, value);\n    }\n    if (key === \"meta\" && !value) {\n      metaDeps.forEach((key2) => {\n        current.delete(key2);\n        setRefs(key2, false);\n      });\n      metaDeps.clear();\n    } else if (typeof e.getModifierState === \"function\" && e.getModifierState(\"Meta\") && value) {\n      [...current, ...values].forEach((key2) => metaDeps.add(key2));\n    }\n  }\n  useEventListener(target, \"keydown\", (e) => {\n    updateRefs(e, true);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(target, \"keyup\", (e) => {\n    updateRefs(e, false);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(\"blur\", reset, { passive: true });\n  useEventListener(\"focus\", reset, { passive: true });\n  const proxy = new Proxy(refs, {\n    get(target2, prop, rec) {\n      if (typeof prop !== \"string\")\n        return Reflect.get(target2, prop, rec);\n      prop = prop.toLowerCase();\n      if (prop in aliasMap)\n        prop = aliasMap[prop];\n      if (!(prop in refs)) {\n        if (/[+_-]/.test(prop)) {\n          const keys = prop.split(/[+_-]/g).map((i) => i.trim());\n          refs[prop] = computed(() => keys.every((key) => unref(proxy[key])));\n        } else {\n          refs[prop] = ref(false);\n        }\n      }\n      const r = Reflect.get(target2, prop, rec);\n      return useReactive ? unref(r) : r;\n    }\n  });\n  return proxy;\n}\n\nvar __defProp$8 = Object.defineProperty;\nvar __getOwnPropSymbols$9 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$9 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$9 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$8 = (obj, key, value) => key in obj ? __defProp$8(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$8 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$9.call(b, prop))\n      __defNormalProp$8(a, prop, b[prop]);\n  if (__getOwnPropSymbols$9)\n    for (var prop of __getOwnPropSymbols$9(b)) {\n      if (__propIsEnum$9.call(b, prop))\n        __defNormalProp$8(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction usingElRef(source, cb) {\n  if (resolveUnref(source))\n    cb(resolveUnref(source));\n}\nfunction timeRangeToArray(timeRanges) {\n  let ranges = [];\n  for (let i = 0; i < timeRanges.length; ++i)\n    ranges = [...ranges, [timeRanges.start(i), timeRanges.end(i)]];\n  return ranges;\n}\nfunction tracksToArray(tracks) {\n  return Array.from(tracks).map(({ label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }, id) => ({ id, label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }));\n}\nconst defaultOptions = {\n  src: \"\",\n  tracks: []\n};\nfunction useMediaControls(target, options = {}) {\n  options = __spreadValues$8(__spreadValues$8({}, defaultOptions), options);\n  const {\n    document = defaultDocument\n  } = options;\n  const currentTime = ref(0);\n  const duration = ref(0);\n  const seeking = ref(false);\n  const volume = ref(1);\n  const waiting = ref(false);\n  const ended = ref(false);\n  const playing = ref(false);\n  const rate = ref(1);\n  const stalled = ref(false);\n  const buffered = ref([]);\n  const tracks = ref([]);\n  const selectedTrack = ref(-1);\n  const isPictureInPicture = ref(false);\n  const muted = ref(false);\n  const supportsPictureInPicture = document && \"pictureInPictureEnabled\" in document;\n  const sourceErrorEvent = createEventHook();\n  const disableTrack = (track) => {\n    usingElRef(target, (el) => {\n      if (track) {\n        const id = isNumber(track) ? track : track.id;\n        el.textTracks[id].mode = \"disabled\";\n      } else {\n        for (let i = 0; i < el.textTracks.length; ++i)\n          el.textTracks[i].mode = \"disabled\";\n      }\n      selectedTrack.value = -1;\n    });\n  };\n  const enableTrack = (track, disableTracks = true) => {\n    usingElRef(target, (el) => {\n      const id = isNumber(track) ? track : track.id;\n      if (disableTracks)\n        disableTrack();\n      el.textTracks[id].mode = \"showing\";\n      selectedTrack.value = id;\n    });\n  };\n  const togglePictureInPicture = () => {\n    return new Promise((resolve, reject) => {\n      usingElRef(target, async (el) => {\n        if (supportsPictureInPicture) {\n          if (!isPictureInPicture.value) {\n            el.requestPictureInPicture().then(resolve).catch(reject);\n          } else {\n            document.exitPictureInPicture().then(resolve).catch(reject);\n          }\n        }\n      });\n    });\n  };\n  watchEffect(() => {\n    if (!document)\n      return;\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    const src = resolveUnref(options.src);\n    let sources = [];\n    if (!src)\n      return;\n    if (isString(src))\n      sources = [{ src }];\n    else if (Array.isArray(src))\n      sources = src;\n    else if (isObject(src))\n      sources = [src];\n    el.querySelectorAll(\"source\").forEach((e) => {\n      e.removeEventListener(\"error\", sourceErrorEvent.trigger);\n      e.remove();\n    });\n    sources.forEach(({ src: src2, type }) => {\n      const source = document.createElement(\"source\");\n      source.setAttribute(\"src\", src2);\n      source.setAttribute(\"type\", type || \"\");\n      source.addEventListener(\"error\", sourceErrorEvent.trigger);\n      el.appendChild(source);\n    });\n    el.load();\n  });\n  tryOnScopeDispose(() => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    el.querySelectorAll(\"source\").forEach((e) => e.removeEventListener(\"error\", sourceErrorEvent.trigger));\n  });\n  watch(volume, (vol) => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    el.volume = vol;\n  });\n  watch(muted, (mute) => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    el.muted = mute;\n  });\n  watch(rate, (rate2) => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    el.playbackRate = rate2;\n  });\n  watchEffect(() => {\n    if (!document)\n      return;\n    const textTracks = resolveUnref(options.tracks);\n    const el = resolveUnref(target);\n    if (!textTracks || !textTracks.length || !el)\n      return;\n    el.querySelectorAll(\"track\").forEach((e) => e.remove());\n    textTracks.forEach(({ default: isDefault, kind, label, src, srcLang }, i) => {\n      const track = document.createElement(\"track\");\n      track.default = isDefault || false;\n      track.kind = kind;\n      track.label = label;\n      track.src = src;\n      track.srclang = srcLang;\n      if (track.default)\n        selectedTrack.value = i;\n      el.appendChild(track);\n    });\n  });\n  const { ignoreUpdates: ignoreCurrentTimeUpdates } = watchIgnorable(currentTime, (time) => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    el.currentTime = time;\n  });\n  const { ignoreUpdates: ignorePlayingUpdates } = watchIgnorable(playing, (isPlaying) => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    isPlaying ? el.play() : el.pause();\n  });\n  useEventListener(target, \"timeupdate\", () => ignoreCurrentTimeUpdates(() => currentTime.value = resolveUnref(target).currentTime));\n  useEventListener(target, \"durationchange\", () => duration.value = resolveUnref(target).duration);\n  useEventListener(target, \"progress\", () => buffered.value = timeRangeToArray(resolveUnref(target).buffered));\n  useEventListener(target, \"seeking\", () => seeking.value = true);\n  useEventListener(target, \"seeked\", () => seeking.value = false);\n  useEventListener(target, \"waiting\", () => waiting.value = true);\n  useEventListener(target, \"playing\", () => {\n    waiting.value = false;\n    ended.value = false;\n  });\n  useEventListener(target, \"ratechange\", () => rate.value = resolveUnref(target).playbackRate);\n  useEventListener(target, \"stalled\", () => stalled.value = true);\n  useEventListener(target, \"ended\", () => ended.value = true);\n  useEventListener(target, \"pause\", () => ignorePlayingUpdates(() => playing.value = false));\n  useEventListener(target, \"play\", () => ignorePlayingUpdates(() => playing.value = true));\n  useEventListener(target, \"enterpictureinpicture\", () => isPictureInPicture.value = true);\n  useEventListener(target, \"leavepictureinpicture\", () => isPictureInPicture.value = false);\n  useEventListener(target, \"volumechange\", () => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    volume.value = el.volume;\n    muted.value = el.muted;\n  });\n  const listeners = [];\n  const stop = watch([target], () => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    stop();\n    listeners[0] = useEventListener(el.textTracks, \"addtrack\", () => tracks.value = tracksToArray(el.textTracks));\n    listeners[1] = useEventListener(el.textTracks, \"removetrack\", () => tracks.value = tracksToArray(el.textTracks));\n    listeners[2] = useEventListener(el.textTracks, \"change\", () => tracks.value = tracksToArray(el.textTracks));\n  });\n  tryOnScopeDispose(() => listeners.forEach((listener) => listener()));\n  return {\n    currentTime,\n    duration,\n    waiting,\n    seeking,\n    ended,\n    stalled,\n    buffered,\n    playing,\n    rate,\n    volume,\n    muted,\n    tracks,\n    selectedTrack,\n    enableTrack,\n    disableTrack,\n    supportsPictureInPicture,\n    togglePictureInPicture,\n    isPictureInPicture,\n    onSourceError: sourceErrorEvent.on\n  };\n}\n\nconst getMapVue2Compat = () => {\n  const data = reactive({});\n  return {\n    get: (key) => data[key],\n    set: (key, value) => set(data, key, value),\n    has: (key) => hasOwn(data, key),\n    delete: (key) => del(data, key),\n    clear: () => {\n      Object.keys(data).forEach((key) => {\n        del(data, key);\n      });\n    }\n  };\n};\nfunction useMemoize(resolver, options) {\n  const initCache = () => {\n    if (options == null ? void 0 : options.cache)\n      return reactive(options.cache);\n    if (isVue2)\n      return getMapVue2Compat();\n    return reactive(new Map());\n  };\n  const cache = initCache();\n  const generateKey = (...args) => (options == null ? void 0 : options.getKey) ? options.getKey(...args) : JSON.stringify(args);\n  const _loadData = (key, ...args) => {\n    cache.set(key, resolver(...args));\n    return cache.get(key);\n  };\n  const loadData = (...args) => _loadData(generateKey(...args), ...args);\n  const deleteData = (...args) => {\n    cache.delete(generateKey(...args));\n  };\n  const clearData = () => {\n    cache.clear();\n  };\n  const memoized = (...args) => {\n    const key = generateKey(...args);\n    if (cache.has(key))\n      return cache.get(key);\n    return _loadData(key, ...args);\n  };\n  memoized.load = loadData;\n  memoized.delete = deleteData;\n  memoized.clear = clearData;\n  memoized.generateKey = generateKey;\n  memoized.cache = cache;\n  return memoized;\n}\n\nfunction useMemory(options = {}) {\n  const memory = ref();\n  const isSupported = useSupported(() => typeof performance !== \"undefined\" && \"memory\" in performance);\n  if (isSupported.value) {\n    const { interval = 1e3 } = options;\n    useIntervalFn(() => {\n      memory.value = performance.memory;\n    }, interval, { immediate: options.immediate, immediateCallback: options.immediateCallback });\n  }\n  return { isSupported, memory };\n}\n\nfunction useMounted() {\n  const isMounted = ref(false);\n  onMounted(() => {\n    isMounted.value = true;\n  });\n  return isMounted;\n}\n\nfunction useMouse(options = {}) {\n  const {\n    type = \"page\",\n    touch = true,\n    resetOnTouchEnds = false,\n    initialValue = { x: 0, y: 0 },\n    window = defaultWindow,\n    eventFilter\n  } = options;\n  const x = ref(initialValue.x);\n  const y = ref(initialValue.y);\n  const sourceType = ref(null);\n  const mouseHandler = (event) => {\n    if (type === \"page\") {\n      x.value = event.pageX;\n      y.value = event.pageY;\n    } else if (type === \"client\") {\n      x.value = event.clientX;\n      y.value = event.clientY;\n    } else if (type === \"movement\") {\n      x.value = event.movementX;\n      y.value = event.movementY;\n    }\n    sourceType.value = \"mouse\";\n  };\n  const reset = () => {\n    x.value = initialValue.x;\n    y.value = initialValue.y;\n  };\n  const touchHandler = (event) => {\n    if (event.touches.length > 0) {\n      const touch2 = event.touches[0];\n      if (type === \"page\") {\n        x.value = touch2.pageX;\n        y.value = touch2.pageY;\n      } else if (type === \"client\") {\n        x.value = touch2.clientX;\n        y.value = touch2.clientY;\n      }\n      sourceType.value = \"touch\";\n    }\n  };\n  const mouseHandlerWrapper = (event) => {\n    return eventFilter === void 0 ? mouseHandler(event) : eventFilter(() => mouseHandler(event), {});\n  };\n  const touchHandlerWrapper = (event) => {\n    return eventFilter === void 0 ? touchHandler(event) : eventFilter(() => touchHandler(event), {});\n  };\n  if (window) {\n    useEventListener(window, \"mousemove\", mouseHandlerWrapper, { passive: true });\n    useEventListener(window, \"dragover\", mouseHandlerWrapper, { passive: true });\n    if (touch && type !== \"movement\") {\n      useEventListener(window, \"touchstart\", touchHandlerWrapper, { passive: true });\n      useEventListener(window, \"touchmove\", touchHandlerWrapper, { passive: true });\n      if (resetOnTouchEnds)\n        useEventListener(window, \"touchend\", reset, { passive: true });\n    }\n  }\n  return {\n    x,\n    y,\n    sourceType\n  };\n}\n\nfunction useMouseInElement(target, options = {}) {\n  const {\n    handleOutside = true,\n    window = defaultWindow\n  } = options;\n  const { x, y, sourceType } = useMouse(options);\n  const targetRef = ref(target != null ? target : window == null ? void 0 : window.document.body);\n  const elementX = ref(0);\n  const elementY = ref(0);\n  const elementPositionX = ref(0);\n  const elementPositionY = ref(0);\n  const elementHeight = ref(0);\n  const elementWidth = ref(0);\n  const isOutside = ref(true);\n  let stop = () => {\n  };\n  if (window) {\n    stop = watch([targetRef, x, y], () => {\n      const el = unrefElement(targetRef);\n      if (!el)\n        return;\n      const {\n        left,\n        top,\n        width,\n        height\n      } = el.getBoundingClientRect();\n      elementPositionX.value = left + window.pageXOffset;\n      elementPositionY.value = top + window.pageYOffset;\n      elementHeight.value = height;\n      elementWidth.value = width;\n      const elX = x.value - elementPositionX.value;\n      const elY = y.value - elementPositionY.value;\n      isOutside.value = width === 0 || height === 0 || elX < 0 || elY < 0 || elX > width || elY > height;\n      if (handleOutside || !isOutside.value) {\n        elementX.value = elX;\n        elementY.value = elY;\n      }\n    }, { immediate: true });\n    useEventListener(document, \"mouseleave\", () => {\n      isOutside.value = true;\n    });\n  }\n  return {\n    x,\n    y,\n    sourceType,\n    elementX,\n    elementY,\n    elementPositionX,\n    elementPositionY,\n    elementHeight,\n    elementWidth,\n    isOutside,\n    stop\n  };\n}\n\nfunction useMousePressed(options = {}) {\n  const {\n    touch = true,\n    drag = true,\n    initialValue = false,\n    window = defaultWindow\n  } = options;\n  const pressed = ref(initialValue);\n  const sourceType = ref(null);\n  if (!window) {\n    return {\n      pressed,\n      sourceType\n    };\n  }\n  const onPressed = (srcType) => () => {\n    pressed.value = true;\n    sourceType.value = srcType;\n  };\n  const onReleased = () => {\n    pressed.value = false;\n    sourceType.value = null;\n  };\n  const target = computed(() => unrefElement(options.target) || window);\n  useEventListener(target, \"mousedown\", onPressed(\"mouse\"), { passive: true });\n  useEventListener(window, \"mouseleave\", onReleased, { passive: true });\n  useEventListener(window, \"mouseup\", onReleased, { passive: true });\n  if (drag) {\n    useEventListener(target, \"dragstart\", onPressed(\"mouse\"), { passive: true });\n    useEventListener(window, \"drop\", onReleased, { passive: true });\n    useEventListener(window, \"dragend\", onReleased, { passive: true });\n  }\n  if (touch) {\n    useEventListener(target, \"touchstart\", onPressed(\"touch\"), { passive: true });\n    useEventListener(window, \"touchend\", onReleased, { passive: true });\n    useEventListener(window, \"touchcancel\", onReleased, { passive: true });\n  }\n  return {\n    pressed,\n    sourceType\n  };\n}\n\nvar __getOwnPropSymbols$8 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$8 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$8 = Object.prototype.propertyIsEnumerable;\nvar __objRest$1 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$8.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$8)\n    for (var prop of __getOwnPropSymbols$8(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$8.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction useMutationObserver(target, callback, options = {}) {\n  const _a = options, { window = defaultWindow } = _a, mutationOptions = __objRest$1(_a, [\"window\"]);\n  let observer;\n  const isSupported = useSupported(() => window && \"MutationObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const stopWatch = watch(() => unrefElement(target), (el) => {\n    cleanup();\n    if (isSupported.value && window && el) {\n      observer = new MutationObserver(callback);\n      observer.observe(el, mutationOptions);\n    }\n  }, { immediate: true });\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nconst useNavigatorLanguage = (options = {}) => {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"language\" in navigator);\n  const language = ref(navigator == null ? void 0 : navigator.language);\n  useEventListener(window, \"languagechange\", () => {\n    if (navigator)\n      language.value = navigator.language;\n  });\n  return {\n    isSupported,\n    language\n  };\n};\n\nfunction useNetwork(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"connection\" in navigator);\n  const isOnline = ref(true);\n  const saveData = ref(false);\n  const offlineAt = ref(void 0);\n  const onlineAt = ref(void 0);\n  const downlink = ref(void 0);\n  const downlinkMax = ref(void 0);\n  const rtt = ref(void 0);\n  const effectiveType = ref(void 0);\n  const type = ref(\"unknown\");\n  const connection = isSupported.value && navigator.connection;\n  function updateNetworkInformation() {\n    if (!navigator)\n      return;\n    isOnline.value = navigator.onLine;\n    offlineAt.value = isOnline.value ? void 0 : Date.now();\n    onlineAt.value = isOnline.value ? Date.now() : void 0;\n    if (connection) {\n      downlink.value = connection.downlink;\n      downlinkMax.value = connection.downlinkMax;\n      effectiveType.value = connection.effectiveType;\n      rtt.value = connection.rtt;\n      saveData.value = connection.saveData;\n      type.value = connection.type;\n    }\n  }\n  if (window) {\n    useEventListener(window, \"offline\", () => {\n      isOnline.value = false;\n      offlineAt.value = Date.now();\n    });\n    useEventListener(window, \"online\", () => {\n      isOnline.value = true;\n      onlineAt.value = Date.now();\n    });\n  }\n  if (connection)\n    useEventListener(connection, \"change\", updateNetworkInformation, false);\n  updateNetworkInformation();\n  return {\n    isSupported,\n    isOnline,\n    saveData,\n    offlineAt,\n    onlineAt,\n    downlink,\n    downlinkMax,\n    effectiveType,\n    rtt,\n    type\n  };\n}\n\nvar __defProp$7 = Object.defineProperty;\nvar __getOwnPropSymbols$7 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$7 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$7 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$7 = (obj, key, value) => key in obj ? __defProp$7(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$7 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$7.call(b, prop))\n      __defNormalProp$7(a, prop, b[prop]);\n  if (__getOwnPropSymbols$7)\n    for (var prop of __getOwnPropSymbols$7(b)) {\n      if (__propIsEnum$7.call(b, prop))\n        __defNormalProp$7(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useNow(options = {}) {\n  const {\n    controls: exposeControls = false,\n    interval = \"requestAnimationFrame\"\n  } = options;\n  const now = ref(new Date());\n  const update = () => now.value = new Date();\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(update, { immediate: true }) : useIntervalFn(update, interval, { immediate: true });\n  if (exposeControls) {\n    return __spreadValues$7({\n      now\n    }, controls);\n  } else {\n    return now;\n  }\n}\n\nfunction useObjectUrl(object) {\n  const url = ref();\n  const release = () => {\n    if (url.value)\n      URL.revokeObjectURL(url.value);\n    url.value = void 0;\n  };\n  watch(() => unref(object), (newObject) => {\n    release();\n    if (newObject)\n      url.value = URL.createObjectURL(newObject);\n  }, { immediate: true });\n  tryOnScopeDispose(release);\n  return readonly(url);\n}\n\nfunction useClamp(value, min, max) {\n  if (isFunction(value) || isReadonly(value))\n    return computed(() => clamp(resolveUnref(value), resolveUnref(min), resolveUnref(max)));\n  const _value = ref(value);\n  return computed({\n    get() {\n      return _value.value = clamp(_value.value, resolveUnref(min), resolveUnref(max));\n    },\n    set(value2) {\n      _value.value = clamp(value2, resolveUnref(min), resolveUnref(max));\n    }\n  });\n}\n\nfunction useOffsetPagination(options) {\n  const {\n    total = Infinity,\n    pageSize = 10,\n    page = 1,\n    onPageChange = noop,\n    onPageSizeChange = noop,\n    onPageCountChange = noop\n  } = options;\n  const currentPageSize = useClamp(pageSize, 1, Infinity);\n  const pageCount = computed(() => Math.max(1, Math.ceil(unref(total) / unref(currentPageSize))));\n  const currentPage = useClamp(page, 1, pageCount);\n  const isFirstPage = computed(() => currentPage.value === 1);\n  const isLastPage = computed(() => currentPage.value === pageCount.value);\n  if (isRef(page))\n    syncRef(page, currentPage);\n  if (isRef(pageSize))\n    syncRef(pageSize, currentPageSize);\n  function prev() {\n    currentPage.value--;\n  }\n  function next() {\n    currentPage.value++;\n  }\n  const returnValue = {\n    currentPage,\n    currentPageSize,\n    pageCount,\n    isFirstPage,\n    isLastPage,\n    prev,\n    next\n  };\n  watch(currentPage, () => {\n    onPageChange(reactive(returnValue));\n  });\n  watch(currentPageSize, () => {\n    onPageSizeChange(reactive(returnValue));\n  });\n  watch(pageCount, () => {\n    onPageCountChange(reactive(returnValue));\n  });\n  return returnValue;\n}\n\nfunction useOnline(options = {}) {\n  const { isOnline } = useNetwork(options);\n  return isOnline;\n}\n\nfunction usePageLeave(options = {}) {\n  const { window = defaultWindow } = options;\n  const isLeft = ref(false);\n  const handler = (event) => {\n    if (!window)\n      return;\n    event = event || window.event;\n    const from = event.relatedTarget || event.toElement;\n    isLeft.value = !from;\n  };\n  if (window) {\n    useEventListener(window, \"mouseout\", handler, { passive: true });\n    useEventListener(window.document, \"mouseleave\", handler, { passive: true });\n    useEventListener(window.document, \"mouseenter\", handler, { passive: true });\n  }\n  return isLeft;\n}\n\nfunction useParallax(target, options = {}) {\n  const {\n    deviceOrientationTiltAdjust = (i) => i,\n    deviceOrientationRollAdjust = (i) => i,\n    mouseTiltAdjust = (i) => i,\n    mouseRollAdjust = (i) => i,\n    window = defaultWindow\n  } = options;\n  const orientation = reactive(useDeviceOrientation({ window }));\n  const {\n    elementX: x,\n    elementY: y,\n    elementWidth: width,\n    elementHeight: height\n  } = useMouseInElement(target, { handleOutside: false, window });\n  const source = computed(() => {\n    if (orientation.isSupported && (orientation.alpha != null && orientation.alpha !== 0 || orientation.gamma != null && orientation.gamma !== 0))\n      return \"deviceOrientation\";\n    return \"mouse\";\n  });\n  const roll = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      const value = -orientation.beta / 90;\n      return deviceOrientationRollAdjust(value);\n    } else {\n      const value = -(y.value - height.value / 2) / height.value;\n      return mouseRollAdjust(value);\n    }\n  });\n  const tilt = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      const value = orientation.gamma / 90;\n      return deviceOrientationTiltAdjust(value);\n    } else {\n      const value = (x.value - width.value / 2) / width.value;\n      return mouseTiltAdjust(value);\n    }\n  });\n  return { roll, tilt, source };\n}\n\nvar __defProp$6 = Object.defineProperty;\nvar __defProps$1 = Object.defineProperties;\nvar __getOwnPropDescs$1 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$6 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$6 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$6 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$6 = (obj, key, value) => key in obj ? __defProp$6(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$6 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$6.call(b, prop))\n      __defNormalProp$6(a, prop, b[prop]);\n  if (__getOwnPropSymbols$6)\n    for (var prop of __getOwnPropSymbols$6(b)) {\n      if (__propIsEnum$6.call(b, prop))\n        __defNormalProp$6(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$1 = (a, b) => __defProps$1(a, __getOwnPropDescs$1(b));\nconst defaultState = {\n  x: 0,\n  y: 0,\n  pointerId: 0,\n  pressure: 0,\n  tiltX: 0,\n  tiltY: 0,\n  width: 0,\n  height: 0,\n  twist: 0,\n  pointerType: null\n};\nconst keys = /* @__PURE__ */ Object.keys(defaultState);\nfunction usePointer(options = {}) {\n  const {\n    target = defaultWindow\n  } = options;\n  const isInside = ref(false);\n  const state = ref(options.initialValue || {});\n  Object.assign(state.value, defaultState, state.value);\n  const handler = (event) => {\n    isInside.value = true;\n    if (options.pointerTypes && !options.pointerTypes.includes(event.pointerType))\n      return;\n    state.value = objectPick(event, keys, false);\n  };\n  if (target) {\n    useEventListener(target, \"pointerdown\", handler, { passive: true });\n    useEventListener(target, \"pointermove\", handler, { passive: true });\n    useEventListener(target, \"pointerleave\", () => isInside.value = false, { passive: true });\n  }\n  return __spreadProps$1(__spreadValues$6({}, toRefs(state)), {\n    isInside\n  });\n}\n\nfunction usePointerLock(target, options = {}) {\n  const { document = defaultDocument, pointerLockOptions } = options;\n  const isSupported = useSupported(() => document && \"pointerLockElement\" in document);\n  const element = ref();\n  const triggerElement = ref();\n  let targetElement;\n  if (isSupported.value) {\n    useEventListener(document, \"pointerlockchange\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        element.value = document.pointerLockElement;\n        if (!element.value)\n          targetElement = triggerElement.value = null;\n      }\n    });\n    useEventListener(document, \"pointerlockerror\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        const action = document.pointerLockElement ? \"release\" : \"acquire\";\n        throw new Error(`Failed to ${action} pointer lock.`);\n      }\n    });\n  }\n  async function lock(e, options2) {\n    var _a;\n    if (!isSupported.value)\n      throw new Error(\"Pointer Lock API is not supported by your browser.\");\n    triggerElement.value = e instanceof Event ? e.currentTarget : null;\n    targetElement = e instanceof Event ? (_a = unrefElement(target)) != null ? _a : triggerElement.value : unrefElement(e);\n    if (!targetElement)\n      throw new Error(\"Target element undefined.\");\n    targetElement.requestPointerLock(options2 != null ? options2 : pointerLockOptions);\n    return await until(element).toBe(targetElement);\n  }\n  async function unlock() {\n    if (!element.value)\n      return false;\n    document.exitPointerLock();\n    await until(element).toBeNull();\n    return true;\n  }\n  return {\n    isSupported,\n    element,\n    triggerElement,\n    lock,\n    unlock\n  };\n}\n\nvar SwipeDirection;\n(function(SwipeDirection2) {\n  SwipeDirection2[\"UP\"] = \"UP\";\n  SwipeDirection2[\"RIGHT\"] = \"RIGHT\";\n  SwipeDirection2[\"DOWN\"] = \"DOWN\";\n  SwipeDirection2[\"LEFT\"] = \"LEFT\";\n  SwipeDirection2[\"NONE\"] = \"NONE\";\n})(SwipeDirection || (SwipeDirection = {}));\nfunction useSwipe(target, options = {}) {\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    passive = true,\n    window = defaultWindow\n  } = options;\n  const coordsStart = reactive({ x: 0, y: 0 });\n  const coordsEnd = reactive({ x: 0, y: 0 });\n  const diffX = computed(() => coordsStart.x - coordsEnd.x);\n  const diffY = computed(() => coordsStart.y - coordsEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(diffX.value), abs(diffY.value)) >= threshold);\n  const isSwiping = ref(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return SwipeDirection.NONE;\n    if (abs(diffX.value) > abs(diffY.value)) {\n      return diffX.value > 0 ? SwipeDirection.LEFT : SwipeDirection.RIGHT;\n    } else {\n      return diffY.value > 0 ? SwipeDirection.UP : SwipeDirection.DOWN;\n    }\n  });\n  const getTouchEventCoords = (e) => [e.touches[0].clientX, e.touches[0].clientY];\n  const updateCoordsStart = (x, y) => {\n    coordsStart.x = x;\n    coordsStart.y = y;\n  };\n  const updateCoordsEnd = (x, y) => {\n    coordsEnd.x = x;\n    coordsEnd.y = y;\n  };\n  let listenerOptions;\n  const isPassiveEventSupported = checkPassiveEventSupport(window == null ? void 0 : window.document);\n  if (!passive)\n    listenerOptions = isPassiveEventSupported ? { passive: false, capture: true } : { capture: true };\n  else\n    listenerOptions = isPassiveEventSupported ? { passive: true } : { capture: false };\n  const onTouchEnd = (e) => {\n    if (isSwiping.value)\n      onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n    isSwiping.value = false;\n  };\n  const stops = [\n    useEventListener(target, \"touchstart\", (e) => {\n      if (listenerOptions.capture && !listenerOptions.passive)\n        e.preventDefault();\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsStart(x, y);\n      updateCoordsEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }, listenerOptions),\n    useEventListener(target, \"touchmove\", (e) => {\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }, listenerOptions),\n    useEventListener(target, \"touchend\", onTouchEnd, listenerOptions),\n    useEventListener(target, \"touchcancel\", onTouchEnd, listenerOptions)\n  ];\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isPassiveEventSupported,\n    isSwiping,\n    direction,\n    coordsStart,\n    coordsEnd,\n    lengthX: diffX,\n    lengthY: diffY,\n    stop\n  };\n}\nfunction checkPassiveEventSupport(document) {\n  if (!document)\n    return false;\n  let supportsPassive = false;\n  const optionsBlock = {\n    get passive() {\n      supportsPassive = true;\n      return false;\n    }\n  };\n  document.addEventListener(\"x\", noop, optionsBlock);\n  document.removeEventListener(\"x\", noop);\n  return supportsPassive;\n}\n\nfunction usePointerSwipe(target, options = {}) {\n  const targetRef = resolveRef(target);\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart\n  } = options;\n  const posStart = reactive({ x: 0, y: 0 });\n  const updatePosStart = (x, y) => {\n    posStart.x = x;\n    posStart.y = y;\n  };\n  const posEnd = reactive({ x: 0, y: 0 });\n  const updatePosEnd = (x, y) => {\n    posEnd.x = x;\n    posEnd.y = y;\n  };\n  const distanceX = computed(() => posStart.x - posEnd.x);\n  const distanceY = computed(() => posStart.y - posEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(distanceX.value), abs(distanceY.value)) >= threshold);\n  const isSwiping = ref(false);\n  const isPointerDown = ref(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return SwipeDirection.NONE;\n    if (abs(distanceX.value) > abs(distanceY.value)) {\n      return distanceX.value > 0 ? SwipeDirection.LEFT : SwipeDirection.RIGHT;\n    } else {\n      return distanceY.value > 0 ? SwipeDirection.UP : SwipeDirection.DOWN;\n    }\n  });\n  const eventIsAllowed = (e) => {\n    var _a, _b, _c;\n    const isReleasingButton = e.buttons === 0;\n    const isPrimaryButton = e.buttons === 1;\n    return (_c = (_b = (_a = options.pointerTypes) == null ? void 0 : _a.includes(e.pointerType)) != null ? _b : isReleasingButton || isPrimaryButton) != null ? _c : true;\n  };\n  const stops = [\n    useEventListener(target, \"pointerdown\", (e) => {\n      var _a, _b;\n      if (!eventIsAllowed(e))\n        return;\n      isPointerDown.value = true;\n      (_b = (_a = targetRef.value) == null ? void 0 : _a.style) == null ? void 0 : _b.setProperty(\"touch-action\", \"none\");\n      const eventTarget = e.target;\n      eventTarget == null ? void 0 : eventTarget.setPointerCapture(e.pointerId);\n      const { clientX: x, clientY: y } = e;\n      updatePosStart(x, y);\n      updatePosEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }),\n    useEventListener(target, \"pointermove\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      if (!isPointerDown.value)\n        return;\n      const { clientX: x, clientY: y } = e;\n      updatePosEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }),\n    useEventListener(target, \"pointerup\", (e) => {\n      var _a, _b;\n      if (!eventIsAllowed(e))\n        return;\n      if (isSwiping.value)\n        onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n      isPointerDown.value = false;\n      isSwiping.value = false;\n      (_b = (_a = targetRef.value) == null ? void 0 : _a.style) == null ? void 0 : _b.setProperty(\"touch-action\", \"initial\");\n    })\n  ];\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isSwiping: readonly(isSwiping),\n    direction: readonly(direction),\n    posStart: readonly(posStart),\n    posEnd: readonly(posEnd),\n    distanceX,\n    distanceY,\n    stop\n  };\n}\n\nfunction usePreferredColorScheme(options) {\n  const isLight = useMediaQuery(\"(prefers-color-scheme: light)\", options);\n  const isDark = useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n  return computed(() => {\n    if (isDark.value)\n      return \"dark\";\n    if (isLight.value)\n      return \"light\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredContrast(options) {\n  const isMore = useMediaQuery(\"(prefers-contrast: more)\", options);\n  const isLess = useMediaQuery(\"(prefers-contrast: less)\", options);\n  const isCustom = useMediaQuery(\"(prefers-contrast: custom)\", options);\n  return computed(() => {\n    if (isMore.value)\n      return \"more\";\n    if (isLess.value)\n      return \"less\";\n    if (isCustom.value)\n      return \"custom\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredLanguages(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return ref([\"en\"]);\n  const navigator = window.navigator;\n  const value = ref(navigator.languages);\n  useEventListener(window, \"languagechange\", () => {\n    value.value = navigator.languages;\n  });\n  return value;\n}\n\nfunction usePreferredReducedMotion(options) {\n  const isReduced = useMediaQuery(\"(prefers-reduced-motion: reduce)\", options);\n  return computed(() => {\n    if (isReduced.value)\n      return \"reduce\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePrevious(value, initialValue) {\n  const previous = shallowRef(initialValue);\n  watch(resolveRef(value), (_, oldValue) => {\n    previous.value = oldValue;\n  }, { flush: \"sync\" });\n  return readonly(previous);\n}\n\nconst useScreenOrientation = (options = {}) => {\n  const {\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"screen\" in window && \"orientation\" in window.screen);\n  const screenOrientation = isSupported.value ? window.screen.orientation : {};\n  const orientation = ref(screenOrientation.type);\n  const angle = ref(screenOrientation.angle || 0);\n  if (isSupported.value) {\n    useEventListener(window, \"orientationchange\", () => {\n      orientation.value = screenOrientation.type;\n      angle.value = screenOrientation.angle;\n    });\n  }\n  const lockOrientation = (type) => {\n    if (!isSupported.value)\n      return Promise.reject(new Error(\"Not supported\"));\n    return screenOrientation.lock(type);\n  };\n  const unlockOrientation = () => {\n    if (isSupported.value)\n      screenOrientation.unlock();\n  };\n  return {\n    isSupported,\n    orientation,\n    angle,\n    lockOrientation,\n    unlockOrientation\n  };\n};\n\nconst topVarName = \"--vueuse-safe-area-top\";\nconst rightVarName = \"--vueuse-safe-area-right\";\nconst bottomVarName = \"--vueuse-safe-area-bottom\";\nconst leftVarName = \"--vueuse-safe-area-left\";\nfunction useScreenSafeArea() {\n  const top = ref(\"\");\n  const right = ref(\"\");\n  const bottom = ref(\"\");\n  const left = ref(\"\");\n  if (isClient) {\n    const topCssVar = useCssVar(topVarName);\n    const rightCssVar = useCssVar(rightVarName);\n    const bottomCssVar = useCssVar(bottomVarName);\n    const leftCssVar = useCssVar(leftVarName);\n    topCssVar.value = \"env(safe-area-inset-top, 0px)\";\n    rightCssVar.value = \"env(safe-area-inset-right, 0px)\";\n    bottomCssVar.value = \"env(safe-area-inset-bottom, 0px)\";\n    leftCssVar.value = \"env(safe-area-inset-left, 0px)\";\n    update();\n    useEventListener(\"resize\", useDebounceFn(update));\n  }\n  function update() {\n    top.value = getValue(topVarName);\n    right.value = getValue(rightVarName);\n    bottom.value = getValue(bottomVarName);\n    left.value = getValue(leftVarName);\n  }\n  return {\n    top,\n    right,\n    bottom,\n    left,\n    update\n  };\n}\nfunction getValue(position) {\n  return getComputedStyle(document.documentElement).getPropertyValue(position);\n}\n\nfunction useScriptTag(src, onLoaded = noop, options = {}) {\n  const {\n    immediate = true,\n    manual = false,\n    type = \"text/javascript\",\n    async = true,\n    crossOrigin,\n    referrerPolicy,\n    noModule,\n    defer,\n    document = defaultDocument,\n    attrs = {}\n  } = options;\n  const scriptTag = ref(null);\n  let _promise = null;\n  const loadScript = (waitForScriptLoad) => new Promise((resolve, reject) => {\n    const resolveWithElement = (el2) => {\n      scriptTag.value = el2;\n      resolve(el2);\n      return el2;\n    };\n    if (!document) {\n      resolve(false);\n      return;\n    }\n    let shouldAppend = false;\n    let el = document.querySelector(`script[src=\"${resolveUnref(src)}\"]`);\n    if (!el) {\n      el = document.createElement(\"script\");\n      el.type = type;\n      el.async = async;\n      el.src = resolveUnref(src);\n      if (defer)\n        el.defer = defer;\n      if (crossOrigin)\n        el.crossOrigin = crossOrigin;\n      if (noModule)\n        el.noModule = noModule;\n      if (referrerPolicy)\n        el.referrerPolicy = referrerPolicy;\n      Object.entries(attrs).forEach(([name, value]) => el == null ? void 0 : el.setAttribute(name, value));\n      shouldAppend = true;\n    } else if (el.hasAttribute(\"data-loaded\")) {\n      resolveWithElement(el);\n    }\n    el.addEventListener(\"error\", (event) => reject(event));\n    el.addEventListener(\"abort\", (event) => reject(event));\n    el.addEventListener(\"load\", () => {\n      el.setAttribute(\"data-loaded\", \"true\");\n      onLoaded(el);\n      resolveWithElement(el);\n    });\n    if (shouldAppend)\n      el = document.head.appendChild(el);\n    if (!waitForScriptLoad)\n      resolveWithElement(el);\n  });\n  const load = (waitForScriptLoad = true) => {\n    if (!_promise)\n      _promise = loadScript(waitForScriptLoad);\n    return _promise;\n  };\n  const unload = () => {\n    if (!document)\n      return;\n    _promise = null;\n    if (scriptTag.value)\n      scriptTag.value = null;\n    const el = document.querySelector(`script[src=\"${resolveUnref(src)}\"]`);\n    if (el)\n      document.head.removeChild(el);\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnUnmounted(unload);\n  return { scriptTag, load, unload };\n}\n\nfunction checkOverflowScroll(ele) {\n  const style = window.getComputedStyle(ele);\n  if (style.overflowX === \"scroll\" || style.overflowY === \"scroll\" || style.overflowX === \"auto\" && ele.clientHeight < ele.scrollHeight || style.overflowY === \"auto\" && ele.clientWidth < ele.scrollWidth) {\n    return true;\n  } else {\n    const parent = ele.parentNode;\n    if (!parent || parent.tagName === \"BODY\")\n      return false;\n    return checkOverflowScroll(parent);\n  }\n}\nfunction preventDefault(rawEvent) {\n  const e = rawEvent || window.event;\n  const _target = e.target;\n  if (checkOverflowScroll(_target))\n    return false;\n  if (e.touches.length > 1)\n    return true;\n  if (e.preventDefault)\n    e.preventDefault();\n  return false;\n}\nfunction useScrollLock(element, initialState = false) {\n  const isLocked = ref(initialState);\n  let stopTouchMoveListener = null;\n  let initialOverflow;\n  watch(resolveRef(element), (el) => {\n    if (el) {\n      const ele = el;\n      initialOverflow = ele.style.overflow;\n      if (isLocked.value)\n        ele.style.overflow = \"hidden\";\n    }\n  }, {\n    immediate: true\n  });\n  const lock = () => {\n    const ele = resolveUnref(element);\n    if (!ele || isLocked.value)\n      return;\n    if (isIOS) {\n      stopTouchMoveListener = useEventListener(ele, \"touchmove\", (e) => {\n        preventDefault(e);\n      }, { passive: false });\n    }\n    ele.style.overflow = \"hidden\";\n    isLocked.value = true;\n  };\n  const unlock = () => {\n    const ele = resolveUnref(element);\n    if (!ele || !isLocked.value)\n      return;\n    isIOS && (stopTouchMoveListener == null ? void 0 : stopTouchMoveListener());\n    ele.style.overflow = initialOverflow;\n    isLocked.value = false;\n  };\n  tryOnScopeDispose(unlock);\n  return computed({\n    get() {\n      return isLocked.value;\n    },\n    set(v) {\n      if (v)\n        lock();\n      else\n        unlock();\n    }\n  });\n}\n\nfunction useSessionStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.sessionStorage, options);\n}\n\nvar __defProp$5 = Object.defineProperty;\nvar __getOwnPropSymbols$5 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$5 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$5 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$5 = (obj, key, value) => key in obj ? __defProp$5(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$5 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$5.call(b, prop))\n      __defNormalProp$5(a, prop, b[prop]);\n  if (__getOwnPropSymbols$5)\n    for (var prop of __getOwnPropSymbols$5(b)) {\n      if (__propIsEnum$5.call(b, prop))\n        __defNormalProp$5(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useShare(shareOptions = {}, options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const _navigator = navigator;\n  const isSupported = useSupported(() => _navigator && \"canShare\" in _navigator);\n  const share = async (overrideOptions = {}) => {\n    if (isSupported.value) {\n      const data = __spreadValues$5(__spreadValues$5({}, resolveUnref(shareOptions)), resolveUnref(overrideOptions));\n      let granted = true;\n      if (data.files && _navigator.canShare)\n        granted = _navigator.canShare({ files: data.files });\n      if (granted)\n        return _navigator.share(data);\n    }\n  };\n  return {\n    isSupported,\n    share\n  };\n}\n\nconst defaultSortFn = (source, compareFn) => source.sort(compareFn);\nconst defaultCompare = (a, b) => a - b;\nfunction useSorted(...args) {\n  var _a, _b, _c, _d;\n  const [source] = args;\n  let compareFn = defaultCompare;\n  let options = {};\n  if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      options = args[1];\n      compareFn = (_a = options.compareFn) != null ? _a : defaultCompare;\n    } else {\n      compareFn = (_b = args[1]) != null ? _b : defaultCompare;\n    }\n  } else if (args.length > 2) {\n    compareFn = (_c = args[1]) != null ? _c : defaultCompare;\n    options = (_d = args[2]) != null ? _d : {};\n  }\n  const {\n    dirty = false,\n    sortFn = defaultSortFn\n  } = options;\n  if (!dirty)\n    return computed(() => sortFn([...unref(source)], compareFn));\n  watchEffect(() => {\n    const result = sortFn(unref(source), compareFn);\n    if (isRef(source))\n      source.value = result;\n    else\n      source.splice(0, source.length, ...result);\n  });\n  return source;\n}\n\nfunction useSpeechRecognition(options = {}) {\n  const {\n    interimResults = true,\n    continuous = true,\n    window = defaultWindow\n  } = options;\n  const lang = resolveRef(options.lang || \"en-US\");\n  const isListening = ref(false);\n  const isFinal = ref(false);\n  const result = ref(\"\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isListening.value) => {\n    isListening.value = value;\n  };\n  const start = () => {\n    isListening.value = true;\n  };\n  const stop = () => {\n    isListening.value = false;\n  };\n  const SpeechRecognition = window && (window.SpeechRecognition || window.webkitSpeechRecognition);\n  const isSupported = useSupported(() => SpeechRecognition);\n  let recognition;\n  if (isSupported.value) {\n    recognition = new SpeechRecognition();\n    recognition.continuous = continuous;\n    recognition.interimResults = interimResults;\n    recognition.lang = unref(lang);\n    recognition.onstart = () => {\n      isFinal.value = false;\n    };\n    watch(lang, (lang2) => {\n      if (recognition && !isListening.value)\n        recognition.lang = lang2;\n    });\n    recognition.onresult = (event) => {\n      const transcript = Array.from(event.results).map((result2) => {\n        isFinal.value = result2.isFinal;\n        return result2[0];\n      }).map((result2) => result2.transcript).join(\"\");\n      result.value = transcript;\n      error.value = void 0;\n    };\n    recognition.onerror = (event) => {\n      error.value = event;\n    };\n    recognition.onend = () => {\n      isListening.value = false;\n      recognition.lang = unref(lang);\n    };\n    watch(isListening, () => {\n      if (isListening.value)\n        recognition.start();\n      else\n        recognition.stop();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isListening.value = false;\n  });\n  return {\n    isSupported,\n    isListening,\n    isFinal,\n    recognition,\n    result,\n    error,\n    toggle,\n    start,\n    stop\n  };\n}\n\nfunction useSpeechSynthesis(text, options = {}) {\n  const {\n    pitch = 1,\n    rate = 1,\n    volume = 1,\n    window = defaultWindow\n  } = options;\n  const synth = window && window.speechSynthesis;\n  const isSupported = useSupported(() => synth);\n  const isPlaying = ref(false);\n  const status = ref(\"init\");\n  const spokenText = resolveRef(text || \"\");\n  const lang = resolveRef(options.lang || \"en-US\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isPlaying.value) => {\n    isPlaying.value = value;\n  };\n  const bindEventsForUtterance = (utterance2) => {\n    utterance2.lang = unref(lang);\n    utterance2.voice = unref(options.voice) || null;\n    utterance2.pitch = pitch;\n    utterance2.rate = rate;\n    utterance2.volume = volume;\n    utterance2.onstart = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onpause = () => {\n      isPlaying.value = false;\n      status.value = \"pause\";\n    };\n    utterance2.onresume = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onend = () => {\n      isPlaying.value = false;\n      status.value = \"end\";\n    };\n    utterance2.onerror = (event) => {\n      error.value = event;\n    };\n  };\n  const utterance = computed(() => {\n    isPlaying.value = false;\n    status.value = \"init\";\n    const newUtterance = new SpeechSynthesisUtterance(spokenText.value);\n    bindEventsForUtterance(newUtterance);\n    return newUtterance;\n  });\n  const speak = () => {\n    synth.cancel();\n    utterance && synth.speak(utterance.value);\n  };\n  const stop = () => {\n    synth.cancel();\n    isPlaying.value = false;\n  };\n  if (isSupported.value) {\n    bindEventsForUtterance(utterance.value);\n    watch(lang, (lang2) => {\n      if (utterance.value && !isPlaying.value)\n        utterance.value.lang = lang2;\n    });\n    if (options.voice) {\n      watch(options.voice, () => {\n        synth.cancel();\n      });\n    }\n    watch(isPlaying, () => {\n      if (isPlaying.value)\n        synth.resume();\n      else\n        synth.pause();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isPlaying.value = false;\n  });\n  return {\n    isSupported,\n    isPlaying,\n    status,\n    utterance,\n    error,\n    stop,\n    toggle,\n    speak\n  };\n}\n\nfunction useStepper(steps, initialStep) {\n  const stepsRef = ref(steps);\n  const stepNames = computed(() => Array.isArray(stepsRef.value) ? stepsRef.value : Object.keys(stepsRef.value));\n  const index = ref(stepNames.value.indexOf(initialStep != null ? initialStep : stepNames.value[0]));\n  const current = computed(() => at(index.value));\n  const isFirst = computed(() => index.value === 0);\n  const isLast = computed(() => index.value === stepNames.value.length - 1);\n  const next = computed(() => stepNames.value[index.value + 1]);\n  const previous = computed(() => stepNames.value[index.value - 1]);\n  function at(index2) {\n    if (Array.isArray(stepsRef.value))\n      return stepsRef.value[index2];\n    return stepsRef.value[stepNames.value[index2]];\n  }\n  function get(step) {\n    if (!stepNames.value.includes(step))\n      return;\n    return at(stepNames.value.indexOf(step));\n  }\n  function goTo(step) {\n    if (stepNames.value.includes(step))\n      index.value = stepNames.value.indexOf(step);\n  }\n  function goToNext() {\n    if (isLast.value)\n      return;\n    index.value++;\n  }\n  function goToPrevious() {\n    if (isFirst.value)\n      return;\n    index.value--;\n  }\n  function goBackTo(step) {\n    if (isAfter(step))\n      goTo(step);\n  }\n  function isNext(step) {\n    return stepNames.value.indexOf(step) === index.value + 1;\n  }\n  function isPrevious(step) {\n    return stepNames.value.indexOf(step) === index.value - 1;\n  }\n  function isCurrent(step) {\n    return stepNames.value.indexOf(step) === index.value;\n  }\n  function isBefore(step) {\n    return index.value < stepNames.value.indexOf(step);\n  }\n  function isAfter(step) {\n    return index.value > stepNames.value.indexOf(step);\n  }\n  return {\n    steps: stepsRef,\n    stepNames,\n    index,\n    current,\n    next,\n    previous,\n    isFirst,\n    isLast,\n    at,\n    get,\n    goTo,\n    goToNext,\n    goToPrevious,\n    goBackTo,\n    isNext,\n    isPrevious,\n    isCurrent,\n    isBefore,\n    isAfter\n  };\n}\n\nvar __defProp$4 = Object.defineProperty;\nvar __getOwnPropSymbols$4 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$4 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$4 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$4 = (obj, key, value) => key in obj ? __defProp$4(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$4 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$4.call(b, prop))\n      __defNormalProp$4(a, prop, b[prop]);\n  if (__getOwnPropSymbols$4)\n    for (var prop of __getOwnPropSymbols$4(b)) {\n      if (__propIsEnum$4.call(b, prop))\n        __defNormalProp$4(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useStorageAsync(key, initialValue, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const rawInit = resolveUnref(initialValue);\n  const type = guessSerializerType(rawInit);\n  const data = (shallow ? shallowRef : ref)(initialValue);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  async function read(event) {\n    if (!storage || event && event.key !== key)\n      return;\n    try {\n      const rawValue = event ? event.newValue : await storage.getItem(key);\n      if (rawValue == null) {\n        data.value = rawInit;\n        if (writeDefaults && rawInit !== null)\n          await storage.setItem(key, await serializer.write(rawInit));\n      } else if (mergeDefaults) {\n        const value = await serializer.read(rawValue);\n        if (isFunction(mergeDefaults))\n          data.value = mergeDefaults(value, rawInit);\n        else if (type === \"object\" && !Array.isArray(value))\n          data.value = __spreadValues$4(__spreadValues$4({}, rawInit), value);\n        else\n          data.value = value;\n      } else {\n        data.value = await serializer.read(rawValue);\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  read();\n  if (window && listenToStorageChanges)\n    useEventListener(window, \"storage\", (e) => setTimeout(() => read(e), 0));\n  if (storage) {\n    watchWithFilter(data, async () => {\n      try {\n        if (data.value == null)\n          await storage.removeItem(key);\n        else\n          await storage.setItem(key, await serializer.write(data.value));\n      } catch (e) {\n        onError(e);\n      }\n    }, {\n      flush,\n      deep,\n      eventFilter\n    });\n  }\n  return data;\n}\n\nlet _id = 0;\nfunction useStyleTag(css, options = {}) {\n  const isLoaded = ref(false);\n  const {\n    document = defaultDocument,\n    immediate = true,\n    manual = false,\n    id = `vueuse_styletag_${++_id}`\n  } = options;\n  const cssRef = ref(css);\n  let stop = () => {\n  };\n  const load = () => {\n    if (!document)\n      return;\n    const el = document.getElementById(id) || document.createElement(\"style\");\n    if (!el.isConnected) {\n      el.type = \"text/css\";\n      el.id = id;\n      if (options.media)\n        el.media = options.media;\n      document.head.appendChild(el);\n    }\n    if (isLoaded.value)\n      return;\n    stop = watch(cssRef, (value) => {\n      el.textContent = value;\n    }, { immediate: true });\n    isLoaded.value = true;\n  };\n  const unload = () => {\n    if (!document || !isLoaded.value)\n      return;\n    stop();\n    document.head.removeChild(document.getElementById(id));\n    isLoaded.value = false;\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnScopeDispose(unload);\n  return {\n    id,\n    css: cssRef,\n    unload,\n    load,\n    isLoaded: readonly(isLoaded)\n  };\n}\n\nfunction useTemplateRefsList() {\n  const refs = ref([]);\n  refs.value.set = (el) => {\n    if (el)\n      refs.value.push(el);\n  };\n  onBeforeUpdate(() => {\n    refs.value.length = 0;\n  });\n  return refs;\n}\n\nfunction useTextDirection(options = {}) {\n  const {\n    document = defaultDocument,\n    selector = \"html\",\n    observe = false,\n    initialValue = \"ltr\"\n  } = options;\n  function getValue() {\n    var _a, _b;\n    return (_b = (_a = document == null ? void 0 : document.querySelector(selector)) == null ? void 0 : _a.getAttribute(\"dir\")) != null ? _b : initialValue;\n  }\n  const dir = ref(getValue());\n  tryOnMounted(() => dir.value = getValue());\n  if (observe && document) {\n    useMutationObserver(document.querySelector(selector), () => dir.value = getValue(), { attributes: true });\n  }\n  return computed({\n    get() {\n      return dir.value;\n    },\n    set(v) {\n      var _a, _b;\n      dir.value = v;\n      if (!document)\n        return;\n      if (dir.value)\n        (_a = document.querySelector(selector)) == null ? void 0 : _a.setAttribute(\"dir\", dir.value);\n      else\n        (_b = document.querySelector(selector)) == null ? void 0 : _b.removeAttribute(\"dir\");\n    }\n  });\n}\n\nfunction getRangesFromSelection(selection) {\n  var _a;\n  const rangeCount = (_a = selection.rangeCount) != null ? _a : 0;\n  const ranges = new Array(rangeCount);\n  for (let i = 0; i < rangeCount; i++) {\n    const range = selection.getRangeAt(i);\n    ranges[i] = range;\n  }\n  return ranges;\n}\nfunction useTextSelection(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const selection = ref(null);\n  const text = computed(() => {\n    var _a, _b;\n    return (_b = (_a = selection.value) == null ? void 0 : _a.toString()) != null ? _b : \"\";\n  });\n  const ranges = computed(() => selection.value ? getRangesFromSelection(selection.value) : []);\n  const rects = computed(() => ranges.value.map((range) => range.getBoundingClientRect()));\n  function onSelectionChange() {\n    selection.value = null;\n    if (window)\n      selection.value = window.getSelection();\n  }\n  if (window)\n    useEventListener(window.document, \"selectionchange\", onSelectionChange);\n  return {\n    text,\n    rects,\n    ranges,\n    selection\n  };\n}\n\nfunction useTextareaAutosize(options) {\n  const textarea = ref(options == null ? void 0 : options.element);\n  const input = ref(options == null ? void 0 : options.input);\n  function triggerResize() {\n    var _a, _b;\n    if (!textarea.value)\n      return;\n    textarea.value.style.height = \"1px\";\n    textarea.value.style.height = `${(_a = textarea.value) == null ? void 0 : _a.scrollHeight}px`;\n    (_b = options == null ? void 0 : options.onResize) == null ? void 0 : _b.call(options);\n  }\n  watch([input, textarea], triggerResize, { immediate: true });\n  useResizeObserver(textarea, () => triggerResize());\n  if (options == null ? void 0 : options.watch)\n    watch(options.watch, triggerResize, { immediate: true, deep: true });\n  return {\n    textarea,\n    input,\n    triggerResize\n  };\n}\n\nvar __defProp$3 = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$3 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$3 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$3 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$3 = (obj, key, value) => key in obj ? __defProp$3(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$3 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$3.call(b, prop))\n      __defNormalProp$3(a, prop, b[prop]);\n  if (__getOwnPropSymbols$3)\n    for (var prop of __getOwnPropSymbols$3(b)) {\n      if (__propIsEnum$3.call(b, prop))\n        __defNormalProp$3(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nfunction useThrottledRefHistory(source, options = {}) {\n  const { throttle = 200, trailing = true } = options;\n  const filter = throttleFilter(throttle, trailing);\n  const history = useRefHistory(source, __spreadProps(__spreadValues$3({}, options), { eventFilter: filter }));\n  return __spreadValues$3({}, history);\n}\n\nvar __defProp$2 = Object.defineProperty;\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$2 = (obj, key, value) => key in obj ? __defProp$2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$2 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$2.call(b, prop))\n      __defNormalProp$2(a, prop, b[prop]);\n  if (__getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(b)) {\n      if (__propIsEnum$2.call(b, prop))\n        __defNormalProp$2(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$2.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$2.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nconst DEFAULT_UNITS = [\n  { max: 6e4, value: 1e3, name: \"second\" },\n  { max: 276e4, value: 6e4, name: \"minute\" },\n  { max: 72e6, value: 36e5, name: \"hour\" },\n  { max: 5184e5, value: 864e5, name: \"day\" },\n  { max: 24192e5, value: 6048e5, name: \"week\" },\n  { max: 28512e6, value: 2592e6, name: \"month\" },\n  { max: Infinity, value: 31536e6, name: \"year\" }\n];\nconst DEFAULT_MESSAGES = {\n  justNow: \"just now\",\n  past: (n) => n.match(/\\d/) ? `${n} ago` : n,\n  future: (n) => n.match(/\\d/) ? `in ${n}` : n,\n  month: (n, past) => n === 1 ? past ? \"last month\" : \"next month\" : `${n} month${n > 1 ? \"s\" : \"\"}`,\n  year: (n, past) => n === 1 ? past ? \"last year\" : \"next year\" : `${n} year${n > 1 ? \"s\" : \"\"}`,\n  day: (n, past) => n === 1 ? past ? \"yesterday\" : \"tomorrow\" : `${n} day${n > 1 ? \"s\" : \"\"}`,\n  week: (n, past) => n === 1 ? past ? \"last week\" : \"next week\" : `${n} week${n > 1 ? \"s\" : \"\"}`,\n  hour: (n) => `${n} hour${n > 1 ? \"s\" : \"\"}`,\n  minute: (n) => `${n} minute${n > 1 ? \"s\" : \"\"}`,\n  second: (n) => `${n} second${n > 1 ? \"s\" : \"\"}`,\n  invalid: \"\"\n};\nconst DEFAULT_FORMATTER = (date) => date.toISOString().slice(0, 10);\nfunction useTimeAgo(time, options = {}) {\n  const {\n    controls: exposeControls = false,\n    updateInterval = 3e4\n  } = options;\n  const _a = useNow({ interval: updateInterval, controls: true }), { now } = _a, controls = __objRest(_a, [\"now\"]);\n  const timeAgo = computed(() => formatTimeAgo(new Date(resolveUnref(time)), options, unref(now.value)));\n  if (exposeControls) {\n    return __spreadValues$2({\n      timeAgo\n    }, controls);\n  } else {\n    return timeAgo;\n  }\n}\nfunction formatTimeAgo(from, options = {}, now = Date.now()) {\n  var _a;\n  const {\n    max,\n    messages = DEFAULT_MESSAGES,\n    fullDateFormatter = DEFAULT_FORMATTER,\n    units = DEFAULT_UNITS,\n    showSecond = false,\n    rounding = \"round\"\n  } = options;\n  const roundFn = typeof rounding === \"number\" ? (n) => +n.toFixed(rounding) : Math[rounding];\n  const diff = +now - +from;\n  const absDiff = Math.abs(diff);\n  function getValue(diff2, unit) {\n    return roundFn(Math.abs(diff2) / unit.value);\n  }\n  function format(diff2, unit) {\n    const val = getValue(diff2, unit);\n    const past = diff2 > 0;\n    const str = applyFormat(unit.name, val, past);\n    return applyFormat(past ? \"past\" : \"future\", str, past);\n  }\n  function applyFormat(name, val, isPast) {\n    const formatter = messages[name];\n    if (typeof formatter === \"function\")\n      return formatter(val, isPast);\n    return formatter.replace(\"{0}\", val.toString());\n  }\n  if (absDiff < 6e4 && !showSecond)\n    return messages.justNow;\n  if (typeof max === \"number\" && absDiff > max)\n    return fullDateFormatter(new Date(from));\n  if (typeof max === \"string\") {\n    const unitMax = (_a = units.find((i) => i.name === max)) == null ? void 0 : _a.max;\n    if (unitMax && absDiff > unitMax)\n      return fullDateFormatter(new Date(from));\n  }\n  for (const [idx, unit] of units.entries()) {\n    const val = getValue(diff, unit);\n    if (val <= 0 && units[idx - 1])\n      return format(diff, units[idx - 1]);\n    if (absDiff < unit.max)\n      return format(diff, unit);\n  }\n  return messages.invalid;\n}\n\nfunction useTimeoutPoll(fn, interval, timeoutPollOptions) {\n  const { start } = useTimeoutFn(loop, interval);\n  const isActive = ref(false);\n  async function loop() {\n    if (!isActive.value)\n      return;\n    await fn();\n    start();\n  }\n  function resume() {\n    if (!isActive.value) {\n      isActive.value = true;\n      loop();\n    }\n  }\n  function pause() {\n    isActive.value = false;\n  }\n  if (timeoutPollOptions == null ? void 0 : timeoutPollOptions.immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nvar __defProp$1 = Object.defineProperty;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useTimestamp(options = {}) {\n  const {\n    controls: exposeControls = false,\n    offset = 0,\n    immediate = true,\n    interval = \"requestAnimationFrame\",\n    callback\n  } = options;\n  const ts = ref(timestamp() + offset);\n  const update = () => ts.value = timestamp() + offset;\n  const cb = callback ? () => {\n    update();\n    callback(ts.value);\n  } : update;\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(cb, { immediate }) : useIntervalFn(cb, interval, { immediate });\n  if (exposeControls) {\n    return __spreadValues$1({\n      timestamp: ts\n    }, controls);\n  } else {\n    return ts;\n  }\n}\n\nfunction useTitle(newTitle = null, options = {}) {\n  var _a, _b;\n  const {\n    document = defaultDocument\n  } = options;\n  const title = resolveRef((_a = newTitle != null ? newTitle : document == null ? void 0 : document.title) != null ? _a : null);\n  const isReadonly = newTitle && isFunction(newTitle);\n  function format(t) {\n    if (!(\"titleTemplate\" in options))\n      return t;\n    const template = options.titleTemplate || \"%s\";\n    return isFunction(template) ? template(t) : unref(template).replace(/%s/g, t);\n  }\n  watch(title, (t, o) => {\n    if (t !== o && document)\n      document.title = format(isString(t) ? t : \"\");\n  }, { immediate: true });\n  if (options.observe && !options.titleTemplate && document && !isReadonly) {\n    useMutationObserver((_b = document.head) == null ? void 0 : _b.querySelector(\"title\"), () => {\n      if (document && document.title !== title.value)\n        title.value = format(document.title);\n    }, { childList: true });\n  }\n  return title;\n}\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nconst _TransitionPresets = {\n  easeInSine: [0.12, 0, 0.39, 0],\n  easeOutSine: [0.61, 1, 0.88, 1],\n  easeInOutSine: [0.37, 0, 0.63, 1],\n  easeInQuad: [0.11, 0, 0.5, 0],\n  easeOutQuad: [0.5, 1, 0.89, 1],\n  easeInOutQuad: [0.45, 0, 0.55, 1],\n  easeInCubic: [0.32, 0, 0.67, 0],\n  easeOutCubic: [0.33, 1, 0.68, 1],\n  easeInOutCubic: [0.65, 0, 0.35, 1],\n  easeInQuart: [0.5, 0, 0.75, 0],\n  easeOutQuart: [0.25, 1, 0.5, 1],\n  easeInOutQuart: [0.76, 0, 0.24, 1],\n  easeInQuint: [0.64, 0, 0.78, 0],\n  easeOutQuint: [0.22, 1, 0.36, 1],\n  easeInOutQuint: [0.83, 0, 0.17, 1],\n  easeInExpo: [0.7, 0, 0.84, 0],\n  easeOutExpo: [0.16, 1, 0.3, 1],\n  easeInOutExpo: [0.87, 0, 0.13, 1],\n  easeInCirc: [0.55, 0, 1, 0.45],\n  easeOutCirc: [0, 0.55, 0.45, 1],\n  easeInOutCirc: [0.85, 0, 0.15, 1],\n  easeInBack: [0.36, 0, 0.66, -0.56],\n  easeOutBack: [0.34, 1.56, 0.64, 1],\n  easeInOutBack: [0.68, -0.6, 0.32, 1.6]\n};\nconst TransitionPresets = __spreadValues({\n  linear: identity\n}, _TransitionPresets);\nfunction createEasingFunction([p0, p1, p2, p3]) {\n  const a = (a1, a2) => 1 - 3 * a2 + 3 * a1;\n  const b = (a1, a2) => 3 * a2 - 6 * a1;\n  const c = (a1) => 3 * a1;\n  const calcBezier = (t, a1, a2) => ((a(a1, a2) * t + b(a1, a2)) * t + c(a1)) * t;\n  const getSlope = (t, a1, a2) => 3 * a(a1, a2) * t * t + 2 * b(a1, a2) * t + c(a1);\n  const getTforX = (x) => {\n    let aGuessT = x;\n    for (let i = 0; i < 4; ++i) {\n      const currentSlope = getSlope(aGuessT, p0, p2);\n      if (currentSlope === 0)\n        return aGuessT;\n      const currentX = calcBezier(aGuessT, p0, p2) - x;\n      aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n  };\n  return (x) => p0 === p1 && p2 === p3 ? x : calcBezier(getTforX(x), p1, p3);\n}\nfunction useTransition(source, options = {}) {\n  const {\n    delay = 0,\n    disabled = false,\n    duration = 1e3,\n    onFinished = noop,\n    onStarted = noop,\n    transition = identity\n  } = options;\n  const currentTransition = computed(() => {\n    const t = unref(transition);\n    return isFunction(t) ? t : createEasingFunction(t);\n  });\n  const sourceValue = computed(() => {\n    const s = unref(source);\n    return isNumber(s) ? s : s.map(unref);\n  });\n  const sourceVector = computed(() => isNumber(sourceValue.value) ? [sourceValue.value] : sourceValue.value);\n  const outputVector = ref(sourceVector.value.slice(0));\n  let currentDuration;\n  let diffVector;\n  let endAt;\n  let startAt;\n  let startVector;\n  const { resume, pause } = useRafFn(() => {\n    const now = Date.now();\n    const progress = clamp(1 - (endAt - now) / currentDuration, 0, 1);\n    outputVector.value = startVector.map((val, i) => {\n      var _a;\n      return val + ((_a = diffVector[i]) != null ? _a : 0) * currentTransition.value(progress);\n    });\n    if (progress >= 1) {\n      pause();\n      onFinished();\n    }\n  }, { immediate: false });\n  const start = () => {\n    pause();\n    currentDuration = unref(duration);\n    diffVector = outputVector.value.map((n, i) => {\n      var _a, _b;\n      return ((_a = sourceVector.value[i]) != null ? _a : 0) - ((_b = outputVector.value[i]) != null ? _b : 0);\n    });\n    startVector = outputVector.value.slice(0);\n    startAt = Date.now();\n    endAt = startAt + currentDuration;\n    resume();\n    onStarted();\n  };\n  const timeout = useTimeoutFn(start, delay, { immediate: false });\n  watch(sourceVector, () => {\n    if (unref(disabled))\n      return;\n    if (unref(delay) <= 0)\n      start();\n    else\n      timeout.start();\n  }, { deep: true });\n  watch(() => unref(disabled), (v) => {\n    if (v) {\n      outputVector.value = sourceVector.value.slice(0);\n      pause();\n    }\n  });\n  return computed(() => {\n    const targetVector = unref(disabled) ? sourceVector : outputVector;\n    return isNumber(sourceValue.value) ? targetVector.value[0] : targetVector.value;\n  });\n}\n\nfunction useUrlSearchParams(mode = \"history\", options = {}) {\n  const {\n    initialValue = {},\n    removeNullishValues = true,\n    removeFalsyValues = false,\n    write: enableWrite = true,\n    window = defaultWindow\n  } = options;\n  if (!window)\n    return reactive(initialValue);\n  const state = reactive({});\n  function getRawParams() {\n    if (mode === \"history\") {\n      return window.location.search || \"\";\n    } else if (mode === \"hash\") {\n      const hash = window.location.hash || \"\";\n      const index = hash.indexOf(\"?\");\n      return index > 0 ? hash.slice(index) : \"\";\n    } else {\n      return (window.location.hash || \"\").replace(/^#/, \"\");\n    }\n  }\n  function constructQuery(params) {\n    const stringified = params.toString();\n    if (mode === \"history\")\n      return `${stringified ? `?${stringified}` : \"\"}${window.location.hash || \"\"}`;\n    if (mode === \"hash-params\")\n      return `${window.location.search || \"\"}${stringified ? `#${stringified}` : \"\"}`;\n    const hash = window.location.hash || \"#\";\n    const index = hash.indexOf(\"?\");\n    if (index > 0)\n      return `${hash.slice(0, index)}${stringified ? `?${stringified}` : \"\"}`;\n    return `${hash}${stringified ? `?${stringified}` : \"\"}`;\n  }\n  function read() {\n    return new URLSearchParams(getRawParams());\n  }\n  function updateState(params) {\n    const unusedKeys = new Set(Object.keys(state));\n    for (const key of params.keys()) {\n      const paramsForKey = params.getAll(key);\n      state[key] = paramsForKey.length > 1 ? paramsForKey : params.get(key) || \"\";\n      unusedKeys.delete(key);\n    }\n    Array.from(unusedKeys).forEach((key) => delete state[key]);\n  }\n  const { pause, resume } = pausableWatch(state, () => {\n    const params = new URLSearchParams(\"\");\n    Object.keys(state).forEach((key) => {\n      const mapEntry = state[key];\n      if (Array.isArray(mapEntry))\n        mapEntry.forEach((value) => params.append(key, value));\n      else if (removeNullishValues && mapEntry == null)\n        params.delete(key);\n      else if (removeFalsyValues && !mapEntry)\n        params.delete(key);\n      else\n        params.set(key, mapEntry);\n    });\n    write(params);\n  }, { deep: true });\n  function write(params, shouldUpdate) {\n    pause();\n    if (shouldUpdate)\n      updateState(params);\n    window.history.replaceState(window.history.state, window.document.title, window.location.pathname + constructQuery(params));\n    resume();\n  }\n  function onChanged() {\n    if (!enableWrite)\n      return;\n    write(read(), true);\n  }\n  useEventListener(window, \"popstate\", onChanged, false);\n  if (mode !== \"history\")\n    useEventListener(window, \"hashchange\", onChanged, false);\n  const initial = read();\n  if (initial.keys().next().value)\n    updateState(initial);\n  else\n    Object.assign(state, initialValue);\n  return state;\n}\n\nfunction useUserMedia(options = {}) {\n  var _a, _b;\n  const enabled = ref((_a = options.enabled) != null ? _a : false);\n  const autoSwitch = ref((_b = options.autoSwitch) != null ? _b : true);\n  const videoDeviceId = ref(options.videoDeviceId);\n  const audioDeviceId = ref(options.audioDeviceId);\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getUserMedia;\n  });\n  const stream = shallowRef();\n  function getDeviceOptions(device) {\n    if (device.value === \"none\" || device.value === false)\n      return false;\n    if (device.value == null)\n      return true;\n    return {\n      deviceId: device.value\n    };\n  }\n  async function _start() {\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getUserMedia({\n      video: getDeviceOptions(videoDeviceId),\n      audio: getDeviceOptions(audioDeviceId)\n    });\n    return stream.value;\n  }\n  async function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  async function restart() {\n    _stop();\n    return await start();\n  }\n  watch(enabled, (v) => {\n    if (v)\n      _start();\n    else\n      _stop();\n  }, { immediate: true });\n  watch([videoDeviceId, audioDeviceId], () => {\n    if (autoSwitch.value && stream.value)\n      restart();\n  }, { immediate: true });\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    restart,\n    videoDeviceId,\n    audioDeviceId,\n    enabled,\n    autoSwitch\n  };\n}\n\nfunction useVModel(props, key, emit, options = {}) {\n  var _a, _b, _c, _d, _e;\n  const {\n    clone = false,\n    passive = false,\n    eventName,\n    deep = false,\n    defaultValue\n  } = options;\n  const vm = getCurrentInstance();\n  const _emit = emit || (vm == null ? void 0 : vm.emit) || ((_a = vm == null ? void 0 : vm.$emit) == null ? void 0 : _a.bind(vm)) || ((_c = (_b = vm == null ? void 0 : vm.proxy) == null ? void 0 : _b.$emit) == null ? void 0 : _c.bind(vm == null ? void 0 : vm.proxy));\n  let event = eventName;\n  if (!key) {\n    if (isVue2) {\n      const modelOptions = (_e = (_d = vm == null ? void 0 : vm.proxy) == null ? void 0 : _d.$options) == null ? void 0 : _e.model;\n      key = (modelOptions == null ? void 0 : modelOptions.value) || \"value\";\n      if (!eventName)\n        event = (modelOptions == null ? void 0 : modelOptions.event) || \"input\";\n    } else {\n      key = \"modelValue\";\n    }\n  }\n  event = eventName || event || `update:${key.toString()}`;\n  const cloneFn = (val) => !clone ? val : isFunction(clone) ? clone(val) : cloneFnJSON(val);\n  const getValue = () => isDef(props[key]) ? cloneFn(props[key]) : defaultValue;\n  if (passive) {\n    const initialValue = getValue();\n    const proxy = ref(initialValue);\n    watch(() => props[key], (v) => proxy.value = cloneFn(v));\n    watch(proxy, (v) => {\n      if (v !== props[key] || deep)\n        _emit(event, v);\n    }, { deep });\n    return proxy;\n  } else {\n    return computed({\n      get() {\n        return getValue();\n      },\n      set(value) {\n        _emit(event, value);\n      }\n    });\n  }\n}\n\nfunction useVModels(props, emit, options = {}) {\n  const ret = {};\n  for (const key in props)\n    ret[key] = useVModel(props, key, emit, options);\n  return ret;\n}\n\nfunction useVibrate(options) {\n  const {\n    pattern = [],\n    interval = 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => typeof navigator !== \"undefined\" && \"vibrate\" in navigator);\n  const patternRef = resolveRef(pattern);\n  let intervalControls;\n  const vibrate = (pattern2 = patternRef.value) => {\n    if (isSupported.value)\n      navigator.vibrate(pattern2);\n  };\n  const stop = () => {\n    if (isSupported.value)\n      navigator.vibrate(0);\n    intervalControls == null ? void 0 : intervalControls.pause();\n  };\n  if (interval > 0) {\n    intervalControls = useIntervalFn(vibrate, interval, {\n      immediate: false,\n      immediateCallback: false\n    });\n  }\n  return {\n    isSupported,\n    pattern,\n    intervalControls,\n    vibrate,\n    stop\n  };\n}\n\nfunction useVirtualList(list, options) {\n  const { containerStyle, wrapperProps, scrollTo, calculateRange, currentList, containerRef } = \"itemHeight\" in options ? useVerticalVirtualList(options, list) : useHorizontalVirtualList(options, list);\n  return {\n    list: currentList,\n    scrollTo,\n    containerProps: {\n      ref: containerRef,\n      onScroll: () => {\n        calculateRange();\n      },\n      style: containerStyle\n    },\n    wrapperProps\n  };\n}\nfunction useVirtualListResources(list) {\n  const containerRef = ref(null);\n  const size = useElementSize(containerRef);\n  const currentList = ref([]);\n  const source = shallowRef(list);\n  const state = ref({ start: 0, end: 10 });\n  return { state, source, currentList, size, containerRef };\n}\nfunction createGetViewCapacity(state, source, itemSize) {\n  return (containerSize) => {\n    if (typeof itemSize === \"number\")\n      return Math.ceil(containerSize / itemSize);\n    const { start = 0 } = state.value;\n    let sum = 0;\n    let capacity = 0;\n    for (let i = start; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      capacity = i;\n      if (sum > containerSize)\n        break;\n    }\n    return capacity - start;\n  };\n}\nfunction createGetOffset(source, itemSize) {\n  return (scrollDirection) => {\n    if (typeof itemSize === \"number\")\n      return Math.floor(scrollDirection / itemSize) + 1;\n    let sum = 0;\n    let offset = 0;\n    for (let i = 0; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      if (sum >= scrollDirection) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n}\nfunction createCalculateRange(type, overscan, getOffset, getViewCapacity, { containerRef, state, currentList, source }) {\n  return () => {\n    const element = containerRef.value;\n    if (element) {\n      const offset = getOffset(type === \"vertical\" ? element.scrollTop : element.scrollLeft);\n      const viewCapacity = getViewCapacity(type === \"vertical\" ? element.clientHeight : element.clientWidth);\n      const from = offset - overscan;\n      const to = offset + viewCapacity + overscan;\n      state.value = {\n        start: from < 0 ? 0 : from,\n        end: to > source.value.length ? source.value.length : to\n      };\n      currentList.value = source.value.slice(state.value.start, state.value.end).map((ele, index) => ({\n        data: ele,\n        index: index + state.value.start\n      }));\n    }\n  };\n}\nfunction createGetDistance(itemSize, source) {\n  return (index) => {\n    if (typeof itemSize === \"number\") {\n      const size2 = index * itemSize;\n      return size2;\n    }\n    const size = source.value.slice(0, index).reduce((sum, _, i) => sum + itemSize(i), 0);\n    return size;\n  };\n}\nfunction useWatchForSizes(size, list, calculateRange) {\n  watch([size.width, size.height, list], () => {\n    calculateRange();\n  });\n}\nfunction createComputedTotalSize(itemSize, source) {\n  return computed(() => {\n    if (typeof itemSize === \"number\")\n      return source.value.length * itemSize;\n    return source.value.reduce((sum, _, index) => sum + itemSize(index), 0);\n  });\n}\nconst scrollToDictionaryForElementScrollKey = {\n  horizontal: \"scrollLeft\",\n  vertical: \"scrollTop\"\n};\nfunction createScrollTo(type, calculateRange, getDistance, containerRef) {\n  return (index) => {\n    if (containerRef.value) {\n      containerRef.value[scrollToDictionaryForElementScrollKey[type]] = getDistance(index);\n      calculateRange();\n    }\n  };\n}\nfunction useHorizontalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowX: \"auto\" };\n  const { itemWidth, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemWidth);\n  const getOffset = createGetOffset(source, itemWidth);\n  const calculateRange = createCalculateRange(\"horizontal\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceLeft = createGetDistance(itemWidth, source);\n  const offsetLeft = computed(() => getDistanceLeft(state.value.start));\n  const totalWidth = createComputedTotalSize(itemWidth, source);\n  useWatchForSizes(size, list, calculateRange);\n  const scrollTo = createScrollTo(\"horizontal\", calculateRange, getDistanceLeft, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        height: \"100%\",\n        width: `${totalWidth.value - offsetLeft.value}px`,\n        marginLeft: `${offsetLeft.value}px`,\n        display: \"flex\"\n      }\n    };\n  });\n  return {\n    scrollTo,\n    calculateRange,\n    wrapperProps,\n    containerStyle,\n    currentList,\n    containerRef\n  };\n}\nfunction useVerticalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowY: \"auto\" };\n  const { itemHeight, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemHeight);\n  const getOffset = createGetOffset(source, itemHeight);\n  const calculateRange = createCalculateRange(\"vertical\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceTop = createGetDistance(itemHeight, source);\n  const offsetTop = computed(() => getDistanceTop(state.value.start));\n  const totalHeight = createComputedTotalSize(itemHeight, source);\n  useWatchForSizes(size, list, calculateRange);\n  const scrollTo = createScrollTo(\"vertical\", calculateRange, getDistanceTop, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        width: \"100%\",\n        height: `${totalHeight.value - offsetTop.value}px`,\n        marginTop: `${offsetTop.value}px`\n      }\n    };\n  });\n  return {\n    calculateRange,\n    scrollTo,\n    containerStyle,\n    wrapperProps,\n    currentList,\n    containerRef\n  };\n}\n\nconst useWakeLock = (options = {}) => {\n  const {\n    navigator = defaultNavigator,\n    document = defaultDocument\n  } = options;\n  let wakeLock;\n  const isSupported = useSupported(() => navigator && \"wakeLock\" in navigator);\n  const isActive = ref(false);\n  async function onVisibilityChange() {\n    if (!isSupported.value || !wakeLock)\n      return;\n    if (document && document.visibilityState === \"visible\")\n      wakeLock = await navigator.wakeLock.request(\"screen\");\n    isActive.value = !wakeLock.released;\n  }\n  if (document)\n    useEventListener(document, \"visibilitychange\", onVisibilityChange, { passive: true });\n  async function request(type) {\n    if (!isSupported.value)\n      return;\n    wakeLock = await navigator.wakeLock.request(type);\n    isActive.value = !wakeLock.released;\n  }\n  async function release() {\n    if (!isSupported.value || !wakeLock)\n      return;\n    await wakeLock.release();\n    isActive.value = !wakeLock.released;\n    wakeLock = null;\n  }\n  return {\n    isSupported,\n    isActive,\n    request,\n    release\n  };\n};\n\nconst useWebNotification = (defaultOptions = {}) => {\n  const {\n    window = defaultWindow\n  } = defaultOptions;\n  const isSupported = useSupported(() => !!window && \"Notification\" in window);\n  const notification = ref(null);\n  const requestPermission = async () => {\n    if (!isSupported.value)\n      return;\n    if (\"permission\" in Notification && Notification.permission !== \"denied\")\n      await Notification.requestPermission();\n  };\n  const onClick = createEventHook();\n  const onShow = createEventHook();\n  const onError = createEventHook();\n  const onClose = createEventHook();\n  const show = async (overrides) => {\n    if (!isSupported.value)\n      return;\n    await requestPermission();\n    const options = Object.assign({}, defaultOptions, overrides);\n    notification.value = new Notification(options.title || \"\", options);\n    notification.value.onclick = (event) => onClick.trigger(event);\n    notification.value.onshow = (event) => onShow.trigger(event);\n    notification.value.onerror = (event) => onError.trigger(event);\n    notification.value.onclose = (event) => onClose.trigger(event);\n    return notification.value;\n  };\n  const close = () => {\n    if (notification.value)\n      notification.value.close();\n    notification.value = null;\n  };\n  tryOnMounted(async () => {\n    if (isSupported.value)\n      await requestPermission();\n  });\n  tryOnScopeDispose(close);\n  if (isSupported.value && window) {\n    const document = window.document;\n    useEventListener(document, \"visibilitychange\", (e) => {\n      e.preventDefault();\n      if (document.visibilityState === \"visible\") {\n        close();\n      }\n    });\n  }\n  return {\n    isSupported,\n    notification,\n    show,\n    close,\n    onClick,\n    onShow,\n    onError,\n    onClose\n  };\n};\n\nconst DEFAULT_PING_MESSAGE = \"ping\";\nfunction resolveNestedOptions(options) {\n  if (options === true)\n    return {};\n  return options;\n}\nfunction useWebSocket(url, options = {}) {\n  const {\n    onConnected,\n    onDisconnected,\n    onError,\n    onMessage,\n    immediate = true,\n    autoClose = true,\n    protocols = []\n  } = options;\n  const data = ref(null);\n  const status = ref(\"CLOSED\");\n  const wsRef = ref();\n  const urlRef = resolveRef(url);\n  let heartbeatPause;\n  let heartbeatResume;\n  let explicitlyClosed = false;\n  let retried = 0;\n  let bufferedData = [];\n  let pongTimeoutWait;\n  const close = (code = 1e3, reason) => {\n    if (!wsRef.value)\n      return;\n    explicitlyClosed = true;\n    heartbeatPause == null ? void 0 : heartbeatPause();\n    wsRef.value.close(code, reason);\n  };\n  const _sendBuffer = () => {\n    if (bufferedData.length && wsRef.value && status.value === \"OPEN\") {\n      for (const buffer of bufferedData)\n        wsRef.value.send(buffer);\n      bufferedData = [];\n    }\n  };\n  const resetHeartbeat = () => {\n    clearTimeout(pongTimeoutWait);\n    pongTimeoutWait = void 0;\n  };\n  const send = (data2, useBuffer = true) => {\n    if (!wsRef.value || status.value !== \"OPEN\") {\n      if (useBuffer)\n        bufferedData.push(data2);\n      return false;\n    }\n    _sendBuffer();\n    wsRef.value.send(data2);\n    return true;\n  };\n  const _init = () => {\n    if (explicitlyClosed || typeof urlRef.value === \"undefined\")\n      return;\n    const ws = new WebSocket(urlRef.value, protocols);\n    wsRef.value = ws;\n    status.value = \"CONNECTING\";\n    ws.onopen = () => {\n      status.value = \"OPEN\";\n      onConnected == null ? void 0 : onConnected(ws);\n      heartbeatResume == null ? void 0 : heartbeatResume();\n      _sendBuffer();\n    };\n    ws.onclose = (ev) => {\n      status.value = \"CLOSED\";\n      wsRef.value = void 0;\n      onDisconnected == null ? void 0 : onDisconnected(ws, ev);\n      if (!explicitlyClosed && options.autoReconnect) {\n        const {\n          retries = -1,\n          delay = 1e3,\n          onFailed\n        } = resolveNestedOptions(options.autoReconnect);\n        retried += 1;\n        if (typeof retries === \"number\" && (retries < 0 || retried < retries))\n          setTimeout(_init, delay);\n        else if (typeof retries === \"function\" && retries())\n          setTimeout(_init, delay);\n        else\n          onFailed == null ? void 0 : onFailed();\n      }\n    };\n    ws.onerror = (e) => {\n      onError == null ? void 0 : onError(ws, e);\n    };\n    ws.onmessage = (e) => {\n      if (options.heartbeat) {\n        resetHeartbeat();\n        const {\n          message = DEFAULT_PING_MESSAGE\n        } = resolveNestedOptions(options.heartbeat);\n        if (e.data === message)\n          return;\n      }\n      data.value = e.data;\n      onMessage == null ? void 0 : onMessage(ws, e);\n    };\n  };\n  if (options.heartbeat) {\n    const {\n      message = DEFAULT_PING_MESSAGE,\n      interval = 1e3,\n      pongTimeout = 1e3\n    } = resolveNestedOptions(options.heartbeat);\n    const { pause, resume } = useIntervalFn(() => {\n      send(message, false);\n      if (pongTimeoutWait != null)\n        return;\n      pongTimeoutWait = setTimeout(() => {\n        close();\n      }, pongTimeout);\n    }, interval, { immediate: false });\n    heartbeatPause = pause;\n    heartbeatResume = resume;\n  }\n  if (autoClose) {\n    useEventListener(window, \"beforeunload\", () => close());\n    tryOnScopeDispose(close);\n  }\n  const open = () => {\n    close();\n    explicitlyClosed = false;\n    retried = 0;\n    _init();\n  };\n  if (immediate)\n    watch(urlRef, open, { immediate: true });\n  return {\n    data,\n    status,\n    close,\n    send,\n    open,\n    ws: wsRef\n  };\n}\n\nfunction useWebWorker(arg0, workerOptions, options) {\n  const {\n    window = defaultWindow\n  } = options != null ? options : {};\n  const data = ref(null);\n  const worker = shallowRef();\n  const post = function post2(val) {\n    if (!worker.value)\n      return;\n    worker.value.postMessage(val);\n  };\n  const terminate = function terminate2() {\n    if (!worker.value)\n      return;\n    worker.value.terminate();\n  };\n  if (window) {\n    if (isString(arg0))\n      worker.value = new Worker(arg0, workerOptions);\n    else if (isFunction(arg0))\n      worker.value = arg0();\n    else\n      worker.value = arg0;\n    worker.value.onmessage = (e) => {\n      data.value = e.data;\n    };\n    tryOnScopeDispose(() => {\n      if (worker.value)\n        worker.value.terminate();\n    });\n  }\n  return {\n    data,\n    post,\n    terminate,\n    worker\n  };\n}\n\nconst jobRunner = (userFunc) => (e) => {\n  const userFuncArgs = e.data[0];\n  return Promise.resolve(userFunc.apply(void 0, userFuncArgs)).then((result) => {\n    postMessage([\"SUCCESS\", result]);\n  }).catch((error) => {\n    postMessage([\"ERROR\", error]);\n  });\n};\n\nconst depsParser = (deps) => {\n  if (deps.length === 0)\n    return \"\";\n  const depsString = deps.map((dep) => `'${dep}'`).toString();\n  return `importScripts(${depsString})`;\n};\n\nconst createWorkerBlobUrl = (fn, deps) => {\n  const blobCode = `${depsParser(deps)}; onmessage=(${jobRunner})(${fn})`;\n  const blob = new Blob([blobCode], { type: \"text/javascript\" });\n  const url = URL.createObjectURL(blob);\n  return url;\n};\n\nconst useWebWorkerFn = (fn, options = {}) => {\n  const {\n    dependencies = [],\n    timeout,\n    window = defaultWindow\n  } = options;\n  const worker = ref();\n  const workerStatus = ref(\"PENDING\");\n  const promise = ref({});\n  const timeoutId = ref();\n  const workerTerminate = (status = \"PENDING\") => {\n    if (worker.value && worker.value._url && window) {\n      worker.value.terminate();\n      URL.revokeObjectURL(worker.value._url);\n      promise.value = {};\n      worker.value = void 0;\n      window.clearTimeout(timeoutId.value);\n      workerStatus.value = status;\n    }\n  };\n  workerTerminate();\n  tryOnScopeDispose(workerTerminate);\n  const generateWorker = () => {\n    const blobUrl = createWorkerBlobUrl(fn, dependencies);\n    const newWorker = new Worker(blobUrl);\n    newWorker._url = blobUrl;\n    newWorker.onmessage = (e) => {\n      const { resolve = () => {\n      }, reject = () => {\n      } } = promise.value;\n      const [status, result] = e.data;\n      switch (status) {\n        case \"SUCCESS\":\n          resolve(result);\n          workerTerminate(status);\n          break;\n        default:\n          reject(result);\n          workerTerminate(\"ERROR\");\n          break;\n      }\n    };\n    newWorker.onerror = (e) => {\n      const { reject = () => {\n      } } = promise.value;\n      reject(e);\n      workerTerminate(\"ERROR\");\n    };\n    if (timeout) {\n      timeoutId.value = setTimeout(() => workerTerminate(\"TIMEOUT_EXPIRED\"), timeout);\n    }\n    return newWorker;\n  };\n  const callWorker = (...fnArgs) => new Promise((resolve, reject) => {\n    promise.value = {\n      resolve,\n      reject\n    };\n    worker.value && worker.value.postMessage([[...fnArgs]]);\n    workerStatus.value = \"RUNNING\";\n  });\n  const workerFn = (...fnArgs) => {\n    if (workerStatus.value === \"RUNNING\") {\n      console.error(\"[useWebWorkerFn] You can only run one instance of the worker at a time.\");\n      return Promise.reject();\n    }\n    worker.value = generateWorker();\n    return callWorker(...fnArgs);\n  };\n  return {\n    workerFn,\n    workerStatus,\n    workerTerminate\n  };\n};\n\nfunction useWindowFocus({ window = defaultWindow } = {}) {\n  if (!window)\n    return ref(false);\n  const focused = ref(window.document.hasFocus());\n  useEventListener(window, \"blur\", () => {\n    focused.value = false;\n  });\n  useEventListener(window, \"focus\", () => {\n    focused.value = true;\n  });\n  return focused;\n}\n\nfunction useWindowScroll({ window = defaultWindow } = {}) {\n  if (!window) {\n    return {\n      x: ref(0),\n      y: ref(0)\n    };\n  }\n  const x = ref(window.scrollX);\n  const y = ref(window.scrollY);\n  useEventListener(window, \"scroll\", () => {\n    x.value = window.scrollX;\n    y.value = window.scrollY;\n  }, {\n    capture: false,\n    passive: true\n  });\n  return { x, y };\n}\n\nfunction useWindowSize(options = {}) {\n  const {\n    window = defaultWindow,\n    initialWidth = Infinity,\n    initialHeight = Infinity,\n    listenOrientation = true,\n    includeScrollbar = true\n  } = options;\n  const width = ref(initialWidth);\n  const height = ref(initialHeight);\n  const update = () => {\n    if (window) {\n      if (includeScrollbar) {\n        width.value = window.innerWidth;\n        height.value = window.innerHeight;\n      } else {\n        width.value = window.document.documentElement.clientWidth;\n        height.value = window.document.documentElement.clientHeight;\n      }\n    }\n  };\n  update();\n  tryOnMounted(update);\n  useEventListener(\"resize\", update, { passive: true });\n  if (listenOrientation)\n    useEventListener(\"orientationchange\", update, { passive: true });\n  return { width, height };\n}\n\nexport { DefaultMagicKeysAliasMap, StorageSerializers, SwipeDirection, TransitionPresets, computedAsync as asyncComputed, breakpointsAntDesign, breakpointsBootstrapV5, breakpointsMasterCss, breakpointsQuasar, breakpointsSematic, breakpointsTailwind, breakpointsVuetify, cloneFnJSON, computedAsync, computedInject, createFetch, createUnrefFn, customStorageEventName, defaultDocument, defaultLocation, defaultNavigator, defaultWindow, formatTimeAgo, getSSRHandler, mapGamepadToXbox360Controller, onClickOutside, onKeyDown, onKeyPressed, onKeyStroke, onKeyUp, onLongPress, onStartTyping, setSSRHandler, templateRef, unrefElement, useActiveElement, useAsyncQueue, useAsyncState, useBase64, useBattery, useBluetooth, useBreakpoints, useBroadcastChannel, useBrowserLocation, useCached, useClipboard, useCloned, useColorMode, useConfirmDialog, useCssVar, useCurrentElement, useCycleList, useDark, useDebouncedRefHistory, useDeviceMotion, useDeviceOrientation, useDevicePixelRatio, useDevicesList, useDisplayMedia, useDocumentVisibility, useDraggable, useDropZone, useElementBounding, useElementByPoint, useElementHover, useElementSize, useElementVisibility, useEventBus, useEventListener, useEventSource, useEyeDropper, useFavicon, useFetch, useFileDialog, useFileSystemAccess, useFocus, useFocusWithin, useFps, useFullscreen, useGamepad, useGeolocation, useIdle, useImage, useInfiniteScroll, useIntersectionObserver, useKeyModifier, useLocalStorage, useMagicKeys, useManualRefHistory, useMediaControls, useMediaQuery, useMemoize, useMemory, useMounted, useMouse, useMouseInElement, useMousePressed, useMutationObserver, useNavigatorLanguage, useNetwork, useNow, useObjectUrl, useOffsetPagination, useOnline, usePageLeave, useParallax, usePermission, usePointer, usePointerLock, usePointerSwipe, usePreferredColorScheme, usePreferredContrast, usePreferredDark, usePreferredLanguages, usePreferredReducedMotion, usePrevious, useRafFn, useRefHistory, useResizeObserver, useScreenOrientation, useScreenSafeArea, useScriptTag, useScroll, useScrollLock, useSessionStorage, useShare, useSorted, useSpeechRecognition, useSpeechSynthesis, useStepper, useStorage, useStorageAsync, useStyleTag, useSupported, useSwipe, useTemplateRefsList, useTextDirection, useTextSelection, useTextareaAutosize, useThrottledRefHistory, useTimeAgo, useTimeoutPoll, useTimestamp, useTitle, useTransition, useUrlSearchParams, useUserMedia, useVModel, useVModels, useVibrate, useVirtualList, useWakeLock, useWebNotification, useWebSocket, useWebWorker, useWebWorkerFn, useWindowFocus, useWindowScroll, useWindowSize };\n", "import type { CSSProperties } from 'vue'\nimport type { ThumbProps } from './thumb'\n\nexport const GAP = 4 // top 2 + bottom 2 of bar instance\n\nexport const BAR_MAP = {\n  vertical: {\n    offset: 'offsetHeight',\n    scroll: 'scrollTop',\n    scrollSize: 'scrollHeight',\n    size: 'height',\n    key: 'vertical',\n    axis: 'Y',\n    client: 'clientY',\n    direction: 'top',\n  },\n  horizontal: {\n    offset: 'offsetWidth',\n    scroll: 'scrollLeft',\n    scrollSize: 'scrollWidth',\n    size: 'width',\n    key: 'horizontal',\n    axis: 'X',\n    client: 'clientX',\n    direction: 'left',\n  },\n} as const\n\nexport const renderThumbStyle = ({\n  move,\n  size,\n  bar,\n}: Pick<ThumbProps, 'move' | 'size'> & {\n  bar: typeof BAR_MAP[keyof typeof BAR_MAP]\n}): CSSProperties => ({\n  [bar.size]: size,\n  transform: `translate${bar.axis}(${move}%)`,\n})\n", "import type { InjectionKey } from 'vue'\n\nexport interface ScrollbarContext {\n  scrollbarElement: HTMLDivElement\n  wrapElement: HTMLDivElement\n}\n\nexport const scrollbarContextKey: InjectionKey<ScrollbarContext> = Symbol(\n  'scrollbarContextKey'\n)\n", "import { buildProps } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Thumb from './thumb.vue'\n\nexport const thumbProps = buildProps({\n  vertical: Boolean,\n  size: String,\n  move: Number,\n  ratio: {\n    type: Number,\n    required: true,\n  },\n  always: Boolean,\n} as const)\nexport type ThumbProps = ExtractPropTypes<typeof thumbProps>\n\nexport type ThumbInstance = InstanceType<typeof Thumb>\n", "\nexport default (sfc, props) => {\n  const target = sfc.__vccOpts || sfc;\n  for (const [key, val] of props) {\n    target[key] = val;\n  }\n  return target;\n}\n", "import { computed, getCurrentInstance, inject, ref, unref } from 'vue'\n\nimport type { InjectionKey, Ref } from 'vue'\n\nexport const defaultNamespace = 'el'\nconst statePrefix = 'is-'\n\nconst _bem = (\n  namespace: string,\n  block: string,\n  blockSuffix: string,\n  element: string,\n  modifier: string\n) => {\n  let cls = `${namespace}-${block}`\n  if (blockSuffix) {\n    cls += `-${blockSuffix}`\n  }\n  if (element) {\n    cls += `__${element}`\n  }\n  if (modifier) {\n    cls += `--${modifier}`\n  }\n  return cls\n}\n\nexport const namespaceContextKey: InjectionKey<Ref<string | undefined>> =\n  Symbol('namespaceContextKey')\n\nexport const useGetDerivedNamespace = (\n  namespaceOverrides?: Ref<string | undefined>\n) => {\n  const derivedNamespace =\n    namespaceOverrides ||\n    (getCurrentInstance()\n      ? inject(namespaceContextKey, ref(defaultNamespace))\n      : ref(defaultNamespace))\n  const namespace = computed(() => {\n    return unref(derivedNamespace) || defaultNamespace\n  })\n  return namespace\n}\n\nexport const useNamespace = (\n  block: string,\n  namespaceOverrides?: Ref<string | undefined>\n) => {\n  const namespace = useGetDerivedNamespace(namespaceOverrides)\n  const b = (blockSuffix = '') =>\n    _bem(namespace.value, block, blockSuffix, '', '')\n  const e = (element?: string) =>\n    element ? _bem(namespace.value, block, '', element, '') : ''\n  const m = (modifier?: string) =>\n    modifier ? _bem(namespace.value, block, '', '', modifier) : ''\n  const be = (blockSuffix?: string, element?: string) =>\n    blockSuffix && element\n      ? _bem(namespace.value, block, blockSuffix, element, '')\n      : ''\n  const em = (element?: string, modifier?: string) =>\n    element && modifier\n      ? _bem(namespace.value, block, '', element, modifier)\n      : ''\n  const bm = (blockSuffix?: string, modifier?: string) =>\n    blockSuffix && modifier\n      ? _bem(namespace.value, block, blockSuffix, '', modifier)\n      : ''\n  const bem = (blockSuffix?: string, element?: string, modifier?: string) =>\n    blockSuffix && element && modifier\n      ? _bem(namespace.value, block, blockSuffix, element, modifier)\n      : ''\n  const is: {\n    (name: string, state: boolean | undefined): string\n    (name: string): string\n  } = (name: string, ...args: [boolean | undefined] | []) => {\n    const state = args.length >= 1 ? args[0]! : true\n    return name && state ? `${statePrefix}${name}` : ''\n  }\n\n  // for css var\n  // --el-xxx: value;\n  const cssVar = (object: Record<string, string>) => {\n    const styles: Record<string, string> = {}\n    for (const key in object) {\n      if (object[key]) {\n        styles[`--${namespace.value}-${key}`] = object[key]\n      }\n    }\n    return styles\n  }\n  // with block\n  const cssVarBlock = (object: Record<string, string>) => {\n    const styles: Record<string, string> = {}\n    for (const key in object) {\n      if (object[key]) {\n        styles[`--${namespace.value}-${block}-${key}`] = object[key]\n      }\n    }\n    return styles\n  }\n\n  const cssVarName = (name: string) => `--${namespace.value}-${name}`\n  const cssVarBlockName = (name: string) =>\n    `--${namespace.value}-${block}-${name}`\n\n  return {\n    namespace,\n    b,\n    e,\n    m,\n    be,\n    em,\n    bm,\n    bem,\n    is,\n    // css\n    cssVar,\n    cssVarName,\n    cssVarBlock,\n    cssVarBlockName,\n  }\n}\n\nexport type UseNamespaceReturn = ReturnType<typeof useNamespace>\n", "<template>\n  <transition :name=\"ns.b('fade')\">\n    <div\n      v-show=\"always || visible\"\n      ref=\"instance\"\n      :class=\"[ns.e('bar'), ns.is(bar.key)]\"\n      @mousedown=\"clickTrackHandler\"\n    >\n      <div\n        ref=\"thumb\"\n        :class=\"ns.e('thumb')\"\n        :style=\"thumbStyle\"\n        @mousedown=\"clickThumbHandler\"\n      />\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, ref, toRef } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { isClient, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { scrollbarContextKey } from './constants'\nimport { BAR_MAP, renderThumbStyle } from './util'\nimport { thumbProps } from './thumb'\n\nconst COMPONENT_NAME = 'Thumb'\nconst props = defineProps(thumbProps)\n\nconst scrollbar = inject(scrollbarContextKey)\nconst ns = useNamespace('scrollbar')\n\nif (!scrollbar) throwError(COMPONENT_NAME, 'can not inject scrollbar context')\n\nconst instance = ref<HTMLDivElement>()\nconst thumb = ref<HTMLDivElement>()\n\nconst thumbState = ref<Partial<Record<'X' | 'Y', number>>>({})\nconst visible = ref(false)\n\nlet cursorDown = false\nlet cursorLeave = false\nlet originalOnSelectStart:\n  | ((this: GlobalEventHandlers, ev: Event) => any)\n  | null = isClient ? document.onselectstart : null\n\nconst bar = computed(() => BAR_MAP[props.vertical ? 'vertical' : 'horizontal'])\n\nconst thumbStyle = computed(() =>\n  renderThumbStyle({\n    size: props.size,\n    move: props.move,\n    bar: bar.value,\n  })\n)\n\nconst offsetRatio = computed(\n  () =>\n    // offsetRatioX = original width of thumb / current width of thumb / ratioX\n    // offsetRatioY = original height of thumb / current height of thumb / ratioY\n    // instance height = wrap height - GAP\n    instance.value![bar.value.offset] ** 2 /\n    scrollbar.wrapElement![bar.value.scrollSize] /\n    props.ratio /\n    thumb.value![bar.value.offset]\n)\n\nconst clickThumbHandler = (e: MouseEvent) => {\n  // prevent click event of middle and right button\n  e.stopPropagation()\n  if (e.ctrlKey || [1, 2].includes(e.button)) return\n\n  window.getSelection()?.removeAllRanges()\n  startDrag(e)\n\n  const el = e.currentTarget as HTMLDivElement\n  if (!el) return\n  thumbState.value[bar.value.axis] =\n    el[bar.value.offset] -\n    (e[bar.value.client] - el.getBoundingClientRect()[bar.value.direction])\n}\n\nconst clickTrackHandler = (e: MouseEvent) => {\n  if (!thumb.value || !instance.value || !scrollbar.wrapElement) return\n\n  const offset = Math.abs(\n    (e.target as HTMLElement).getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]\n  )\n  const thumbHalf = thumb.value[bar.value.offset] / 2\n  const thumbPositionPercentage =\n    ((offset - thumbHalf) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize]) /\n    100\n}\n\nconst startDrag = (e: MouseEvent) => {\n  e.stopImmediatePropagation()\n  cursorDown = true\n  document.addEventListener('mousemove', mouseMoveDocumentHandler)\n  document.addEventListener('mouseup', mouseUpDocumentHandler)\n  originalOnSelectStart = document.onselectstart\n  document.onselectstart = () => false\n}\n\nconst mouseMoveDocumentHandler = (e: MouseEvent) => {\n  if (!instance.value || !thumb.value) return\n  if (cursorDown === false) return\n\n  const prevPage = thumbState.value[bar.value.axis]\n  if (!prevPage) return\n\n  const offset =\n    (instance.value.getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]) *\n    -1\n  const thumbClickPosition = thumb.value[bar.value.offset] - prevPage\n  const thumbPositionPercentage =\n    ((offset - thumbClickPosition) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize]) /\n    100\n}\n\nconst mouseUpDocumentHandler = () => {\n  cursorDown = false\n  thumbState.value[bar.value.axis] = 0\n  document.removeEventListener('mousemove', mouseMoveDocumentHandler)\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n  restoreOnselectstart()\n  if (cursorLeave) visible.value = false\n}\n\nconst mouseMoveScrollbarHandler = () => {\n  cursorLeave = false\n  visible.value = !!props.size\n}\n\nconst mouseLeaveScrollbarHandler = () => {\n  cursorLeave = true\n  visible.value = cursorDown\n}\n\nonBeforeUnmount(() => {\n  restoreOnselectstart()\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n})\n\nconst restoreOnselectstart = () => {\n  if (document.onselectstart !== originalOnSelectStart)\n    document.onselectstart = originalOnSelectStart\n}\n\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mousemove',\n  mouseMoveScrollbarHandler\n)\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mouseleave',\n  mouseLeaveScrollbarHandler\n)\n</script>\n", "import { buildProps } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Bar from './bar.vue'\n\nexport const barProps = buildProps({\n  always: {\n    type: Boolean,\n    default: true,\n  },\n  minSize: {\n    type: Number,\n    required: true,\n  },\n} as const)\nexport type BarProps = ExtractPropTypes<typeof barProps>\n\nexport type BarInstance = InstanceType<typeof Bar>\n", "<template>\n  <thumb :move=\"moveX\" :ratio=\"ratioX\" :size=\"sizeWidth\" :always=\"always\" />\n  <thumb\n    :move=\"moveY\"\n    :ratio=\"ratioY\"\n    :size=\"sizeHeight\"\n    vertical\n    :always=\"always\"\n  />\n</template>\n<script lang=\"ts\" setup>\nimport { inject, ref } from 'vue'\nimport { GAP } from './util'\nimport Thumb from './thumb.vue'\nimport { barProps } from './bar'\nimport { scrollbarContextKey } from './constants'\n\nconst props = defineProps(barProps)\n\nconst scrollbar = inject(scrollbarContextKey)\n\nconst moveX = ref(0)\nconst moveY = ref(0)\nconst sizeWidth = ref('')\nconst sizeHeight = ref('')\nconst ratioY = ref(1)\nconst ratioX = ref(1)\n\nconst handleScroll = (wrap: HTMLDivElement) => {\n  if (wrap) {\n    const offsetHeight = wrap.offsetHeight - GAP\n    const offsetWidth = wrap.offsetWidth - GAP\n\n    moveY.value = ((wrap.scrollTop * 100) / offsetHeight) * ratioY.value\n    moveX.value = ((wrap.scrollLeft * 100) / offsetWidth) * ratioX.value\n  }\n}\n\nconst update = () => {\n  const wrap = scrollbar?.wrapElement\n  if (!wrap) return\n  const offsetHeight = wrap.offsetHeight - GAP\n  const offsetWidth = wrap.offsetWidth - GAP\n\n  const originalHeight = offsetHeight ** 2 / wrap.scrollHeight\n  const originalWidth = offsetWidth ** 2 / wrap.scrollWidth\n  const height = Math.max(originalHeight, props.minSize)\n  const width = Math.max(originalWidth, props.minSize)\n\n  ratioY.value =\n    originalHeight /\n    (offsetHeight - originalHeight) /\n    (height / (offsetHeight - height))\n  ratioX.value =\n    originalWidth /\n    (offsetWidth - originalWidth) /\n    (width / (offsetWidth - width))\n\n  sizeHeight.value = height + GAP < offsetHeight ? `${height}px` : ''\n  sizeWidth.value = width + GAP < offsetWidth ? `${width}px` : ''\n}\n\ndefineExpose({\n  handleScroll,\n  update,\n})\n</script>\n", "import { pick } from 'lodash-unified'\nimport { buildProps } from '@element-plus/utils'\n\nexport const ariaProps = buildProps({\n  /**\n   * @description native `aria-label` attribute\n   */\n  ariaLabel: String,\n  /**\n   * @description native `aria-orientation` attribute\n   */\n  ariaOrientation: {\n    type: String,\n    values: ['horizontal', 'vertical', 'undefined'],\n  },\n  /**\n   * @description native `aria-controls` attribute\n   */\n  ariaControls: String,\n})\n\nexport const useAriaProps = <T extends keyof typeof ariaProps>(\n  arias: Array<T>\n) => {\n  return pick<typeof ariaProps, T>(ariaProps, arias)\n}\n", "import { isArray, isObject, isString } from '@vue/shared'\nimport { isNil } from 'lodash-unified'\n\nexport {\n  isArray,\n  isFunction,\n  isObject,\n  isString,\n  isDate,\n  isPromise,\n  isSymbol,\n  isPlainObject,\n} from '@vue/shared'\n\nexport const isUndefined = (val: any): val is undefined => val === undefined\nexport const isBoolean = (val: any): val is boolean => typeof val === 'boolean'\nexport const isNumber = (val: any): val is number => typeof val === 'number'\n\nexport const isEmpty = (val: unknown) =>\n  (!val && val !== 0) ||\n  (isArray(val) && val.length === 0) ||\n  (isObject(val) && !Object.keys(val).length)\n\nexport const isElement = (e: unknown): e is Element => {\n  if (typeof Element === 'undefined') return false\n  return e instanceof Element\n}\n\nexport const isPropAbsent = (prop: unknown): prop is null | undefined =>\n  isNil(prop)\n\nexport const isStringNumber = (val: string): boolean => {\n  if (!isString(val)) {\n    return false\n  }\n  return !Number.isNaN(Number(val))\n}\n\nexport const isWindow = (val: unknown): val is Window => val === window\n", "import { buildProps, definePropType, isNumber } from '@element-plus/utils'\nimport { useAriaProps } from '@element-plus/hooks'\nimport type { ExtractPropTypes, StyleValue } from 'vue'\nimport type Scrollbar from './scrollbar.vue'\n\nexport const scrollbarProps = buildProps({\n  /**\n   * @description height of scrollbar\n   */\n  height: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description max height of scrollbar\n   */\n  maxHeight: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description whether to use the native scrollbar\n   */\n  native: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description style of wrap\n   */\n  wrapStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n    default: '',\n  },\n  /**\n   * @description class of wrap\n   */\n  wrapClass: {\n    type: [String, Array],\n    default: '',\n  },\n  /**\n   * @description class of view\n   */\n  viewClass: {\n    type: [String, Array],\n    default: '',\n  },\n  /**\n   * @description style of view\n   */\n  viewStyle: {\n    type: [String, Array, Object],\n    default: '',\n  },\n  /**\n   * @description do not respond to container size changes, if the container size does not change, it is better to set it to optimize performance\n   */\n  noresize: Boolean, // 如果 container 尺寸不会发生变化，最好设置它可以优化性能\n  /**\n   * @description element tag of the view\n   */\n  tag: {\n    type: String,\n    default: 'div',\n  },\n  /**\n   * @description always show\n   */\n  always: Boolean,\n  /**\n   * @description minimum size of scrollbar\n   */\n  minSize: {\n    type: Number,\n    default: 20,\n  },\n  /**\n   * @description Wrap tabindex\n   */\n  tabindex: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @description id of view\n   */\n  id: String,\n  /**\n   * @description role of view\n   */\n  role: String,\n  ...useAriaProps(['ariaLabel', 'ariaOrientation']),\n} as const)\nexport type ScrollbarProps = ExtractPropTypes<typeof scrollbarProps>\n\nexport const scrollbarEmits = {\n  scroll: ({\n    scrollTop,\n    scrollLeft,\n  }: {\n    scrollTop: number\n    scrollLeft: number\n  }) => [scrollTop, scrollLeft].every(isNumber),\n}\nexport type ScrollbarEmits = typeof scrollbarEmits\n\nexport type ScrollbarInstance = InstanceType<typeof Scrollbar>\n", "import { get, set } from 'lodash-unified'\nimport type { Entries } from 'type-fest'\nimport type { Arrayable } from '.'\n\nexport const keysOf = <T extends object>(arr: T) =>\n  Object.keys(arr) as Array<keyof T>\nexport const entriesOf = <T extends object>(arr: T) =>\n  Object.entries(arr) as Entries<T>\nexport { hasOwn } from '@vue/shared'\n\nexport const getProp = <T = any>(\n  obj: Record<string, any>,\n  path: Arrayable<string>,\n  defaultValue?: any\n): { value: T } => {\n  return {\n    get value() {\n      return get(obj, path, defaultValue)\n    },\n    set value(val: any) {\n      set(obj, path, val)\n    },\n  }\n}\n", "import { isNumber, isObject, isString, isStringNumber } from '../types'\nimport { isClient } from '../browser'\nimport { camelize } from '../strings'\nimport { entriesOf, keysOf } from '../objects'\nimport { debugWarn } from '../error'\nimport type { CSSProperties } from 'vue'\n\nconst SCOPE = 'utils/dom/style'\n\nexport const classNameToArray = (cls = '') =>\n  cls.split(' ').filter((item) => !!item.trim())\n\nexport const hasClass = (el: Element, cls: string): boolean => {\n  if (!el || !cls) return false\n  if (cls.includes(' ')) throw new Error('className should not contain space.')\n  return el.classList.contains(cls)\n}\n\nexport const addClass = (el: Element, cls: string) => {\n  if (!el || !cls.trim()) return\n  el.classList.add(...classNameToArray(cls))\n}\n\nexport const removeClass = (el: Element, cls: string) => {\n  if (!el || !cls.trim()) return\n  el.classList.remove(...classNameToArray(cls))\n}\n\nexport const getStyle = (\n  element: HTMLElement,\n  styleName: keyof CSSProperties\n): string => {\n  if (!isClient || !element || !styleName) return ''\n\n  let key = camelize(styleName)\n  if (key === 'float') key = 'cssFloat'\n  try {\n    const style = (element.style as any)[key]\n    if (style) return style\n    const computed: any = document.defaultView?.getComputedStyle(element, '')\n    return computed ? computed[key] : ''\n  } catch {\n    return (element.style as any)[key]\n  }\n}\n\nexport const setStyle = (\n  element: HTMLElement,\n  styleName: CSSProperties | keyof CSSProperties,\n  value?: string | number\n) => {\n  if (!element || !styleName) return\n\n  if (isObject(styleName)) {\n    entriesOf(styleName).forEach(([prop, value]) =>\n      setStyle(element, prop, value)\n    )\n  } else {\n    const key: any = camelize(styleName)\n    element.style[key] = value as any\n  }\n}\n\nexport const removeStyle = (\n  element: HTMLElement,\n  style: CSSProperties | keyof CSSProperties\n) => {\n  if (!element || !style) return\n\n  if (isObject(style)) {\n    keysOf(style).forEach((prop) => removeStyle(element, prop))\n  } else {\n    setStyle(element, style, '')\n  }\n}\n\nexport function addUnit(value?: string | number, defaultUnit = 'px') {\n  if (!value) return ''\n  if (isNumber(value) || isStringNumber(value)) {\n    return `${value}${defaultUnit}`\n  } else if (isString(value)) {\n    return value\n  }\n  debugWarn(SCOPE, 'binding value must be a string or number')\n}\n", "<template>\n  <div ref=\"scrollbarRef\" :class=\"ns.b()\">\n    <div\n      ref=\"wrapRef\"\n      :class=\"wrapKls\"\n      :style=\"wrapStyle\"\n      :tabindex=\"tabindex\"\n      @scroll=\"handleScroll\"\n    >\n      <component\n        :is=\"tag\"\n        :id=\"id\"\n        ref=\"resizeRef\"\n        :class=\"resizeKls\"\n        :style=\"viewStyle\"\n        :role=\"role\"\n        :aria-label=\"ariaLabel\"\n        :aria-orientation=\"ariaOrientation\"\n      >\n        <slot />\n      </component>\n    </div>\n    <template v-if=\"!native\">\n      <bar ref=\"barRef\" :always=\"always\" :min-size=\"minSize\" />\n    </template>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onActivated,\n  onMounted,\n  onUpdated,\n  provide,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { useEventListener, useResizeObserver } from '@vueuse/core'\nimport { addUnit, debugWarn, isNumber, isObject } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport Bar from './bar.vue'\nimport { scrollbarContextKey } from './constants'\nimport { scrollbarEmits, scrollbarProps } from './scrollbar'\nimport type { BarInstance } from './bar'\nimport type { CSSProperties, StyleValue } from 'vue'\n\nconst COMPONENT_NAME = 'ElScrollbar'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(scrollbarProps)\nconst emit = defineEmits(scrollbarEmits)\n\nconst ns = useNamespace('scrollbar')\n\nlet stopResizeObserver: (() => void) | undefined = undefined\nlet stopResizeListener: (() => void) | undefined = undefined\nlet wrapScrollTop = 0\nlet wrapScrollLeft = 0\n\nconst scrollbarRef = ref<HTMLDivElement>()\nconst wrapRef = ref<HTMLDivElement>()\nconst resizeRef = ref<HTMLElement>()\nconst barRef = ref<BarInstance>()\n\nconst wrapStyle = computed<StyleValue>(() => {\n  const style: CSSProperties = {}\n  if (props.height) style.height = addUnit(props.height)\n  if (props.maxHeight) style.maxHeight = addUnit(props.maxHeight)\n  return [props.wrapStyle, style]\n})\n\nconst wrapKls = computed(() => {\n  return [\n    props.wrapClass,\n    ns.e('wrap'),\n    { [ns.em('wrap', 'hidden-default')]: !props.native },\n  ]\n})\n\nconst resizeKls = computed(() => {\n  return [ns.e('view'), props.viewClass]\n})\n\nconst handleScroll = () => {\n  if (wrapRef.value) {\n    barRef.value?.handleScroll(wrapRef.value)\n    wrapScrollTop = wrapRef.value.scrollTop\n    wrapScrollLeft = wrapRef.value.scrollLeft\n\n    emit('scroll', {\n      scrollTop: wrapRef.value.scrollTop,\n      scrollLeft: wrapRef.value.scrollLeft,\n    })\n  }\n}\n\n// TODO: refactor method overrides, due to script setup dts\n// @ts-nocheck\nfunction scrollTo(xCord: number, yCord?: number): void\nfunction scrollTo(options: ScrollToOptions): void\nfunction scrollTo(arg1: unknown, arg2?: number) {\n  if (isObject(arg1)) {\n    wrapRef.value!.scrollTo(arg1)\n  } else if (isNumber(arg1) && isNumber(arg2)) {\n    wrapRef.value!.scrollTo(arg1, arg2)\n  }\n}\n\nconst setScrollTop = (value: number) => {\n  if (!isNumber(value)) {\n    debugWarn(COMPONENT_NAME, 'value must be a number')\n    return\n  }\n  wrapRef.value!.scrollTop = value\n}\n\nconst setScrollLeft = (value: number) => {\n  if (!isNumber(value)) {\n    debugWarn(COMPONENT_NAME, 'value must be a number')\n    return\n  }\n  wrapRef.value!.scrollLeft = value\n}\n\nconst update = () => {\n  barRef.value?.update()\n}\n\nwatch(\n  () => props.noresize,\n  (noresize) => {\n    if (noresize) {\n      stopResizeObserver?.()\n      stopResizeListener?.()\n    } else {\n      ;({ stop: stopResizeObserver } = useResizeObserver(resizeRef, update))\n      stopResizeListener = useEventListener('resize', update)\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => [props.maxHeight, props.height],\n  () => {\n    if (!props.native)\n      nextTick(() => {\n        update()\n        if (wrapRef.value) {\n          barRef.value?.handleScroll(wrapRef.value)\n        }\n      })\n  }\n)\n\nprovide(\n  scrollbarContextKey,\n  reactive({\n    scrollbarElement: scrollbarRef,\n    wrapElement: wrapRef,\n  })\n)\n\nonActivated(() => {\n  if (wrapRef.value) {\n    wrapRef.value.scrollTop = wrapScrollTop\n    wrapRef.value.scrollLeft = wrapScrollLeft\n  }\n})\n\nonMounted(() => {\n  if (!props.native)\n    nextTick(() => {\n      update()\n    })\n})\nonUpdated(() => update())\n\ndefineExpose({\n  /** @description scrollbar wrap ref */\n  wrapRef,\n  /** @description update scrollbar state manually */\n  update,\n  /** @description scrolls to a particular set of coordinates */\n  scrollTo,\n  /** @description set distance to scroll top */\n  setScrollTop,\n  /** @description set distance to scroll left */\n  setScrollLeft,\n  /** @description handle scroll event */\n  handleScroll,\n})\n</script>\n", "import { NOOP } from '../functions'\n\nimport type { App, Directive } from 'vue'\nimport type { SFCInstallWithContext, SFCWithInstall } from './typescript'\n\nexport const withInstall = <T, E extends Record<string, any>>(\n  main: T,\n  extra?: E\n) => {\n  ;(main as SFCWithInstall<T>).install = (app): void => {\n    for (const comp of [main, ...Object.values(extra ?? {})]) {\n      app.component(comp.name, comp)\n    }\n  }\n\n  if (extra) {\n    for (const [key, comp] of Object.entries(extra)) {\n      ;(main as any)[key] = comp\n    }\n  }\n  return main as SFCWithInstall<T> & E\n}\n\nexport const withInstallFunction = <T>(fn: T, name: string) => {\n  ;(fn as SFCWithInstall<T>).install = (app: App) => {\n    ;(fn as SFCInstallWithContext<T>)._context = app._context\n    app.config.globalProperties[name] = fn\n  }\n\n  return fn as SFCInstallWithContext<T>\n}\n\nexport const withInstallDirective = <T extends Directive>(\n  directive: T,\n  name: string\n) => {\n  ;(directive as SFCWithInstall<T>).install = (app: App): void => {\n    app.directive(name, directive)\n  }\n\n  return directive as SFCWithInstall<T>\n}\n\nexport const withNoopInstall = <T>(component: T) => {\n  ;(component as SFCWithInstall<T>).install = NOOP\n\n  return component as SFCWithInstall<T>\n}\n", "import { withInstall } from '@element-plus/utils'\n\nimport Scrollbar from './src/scrollbar.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElScrollbar: SFCWithInstall<typeof Scrollbar> =\n  withInstall(Scrollbar)\nexport default ElScrollbar\n\nexport * from './src/util'\nexport * from './src/scrollbar'\nexport * from './src/thumb'\nexport * from './src/constants'\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,eAAe,OAAO;AAC1B,IAAI,sBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,GAAG,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC;AACtE,SAAS,cAAc,IAAI,SAAS;AAClC,MAAIA;AACJ,QAAM,SAAS,WAAW;AAC1B,cAAY,MAAM;AAChB,WAAO,QAAQ,GAAG;AAAA,EACpB,GAAG,gBAAgB,iBAAiB,CAAC,GAAG,OAAO,GAAG;AAAA,IAChD,QAAQA,MAAK,WAAW,OAAO,SAAS,QAAQ,UAAU,OAAOA,MAAK;AAAA,EACxE,CAAC,CAAC;AACF,SAAO,SAAS,MAAM;AACxB;AAEA,IAAI;AACJ,IAAM,WAAW,OAAO,WAAW;AACnC,IAAM,QAAQ,CAAC,QAAQ,OAAO,QAAQ;AAOtC,IAAM,aAAa,CAAC,QAAQ,OAAO,QAAQ;AAE3C,IAAMC,YAAW,CAAC,QAAQ,OAAO,QAAQ;AAMzC,IAAM,OAAO,MAAM;AACnB;AAMA,IAAM,QAAQ,cAAc,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,cAAc,iBAAiB,KAAK,OAAO,UAAU,SAAS;AAGjK,SAAS,aAAa,GAAG;AACvB,SAAO,OAAO,MAAM,aAAa,EAAE,IAAI,MAAM,CAAC;AAChD;AAEA,SAAS,oBAAoB,QAAQ,IAAI;AACvC,WAAS,WAAW,MAAM;AACxB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ,QAAQ,OAAO,MAAM,GAAG,MAAM,MAAM,IAAI,GAAG,EAAE,IAAI,SAAS,MAAM,KAAK,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,IAC7G,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAIA,SAAS,eAAe,IAAI,UAAU,CAAC,GAAG;AACxC,MAAI;AACJ,MAAI;AACJ,MAAI,eAAe;AACnB,QAAM,gBAAgB,CAAC,WAAW;AAChC,iBAAa,MAAM;AACnB,iBAAa;AACb,mBAAe;AAAA,EACjB;AACA,QAAM,SAAS,CAAC,WAAW;AACzB,UAAM,WAAW,aAAa,EAAE;AAChC,UAAM,cAAc,aAAa,QAAQ,OAAO;AAChD,QAAI;AACF,oBAAc,KAAK;AACrB,QAAI,YAAY,KAAK,gBAAgB,UAAU,eAAe,GAAG;AAC/D,UAAI,UAAU;AACZ,sBAAc,QAAQ;AACtB,mBAAW;AAAA,MACb;AACA,aAAO,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACjC;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,qBAAe,QAAQ,iBAAiB,SAAS;AACjD,UAAI,eAAe,CAAC,UAAU;AAC5B,mBAAW,WAAW,MAAM;AAC1B,cAAI;AACF,0BAAc,KAAK;AACrB,qBAAW;AACX,kBAAQ,OAAO,CAAC;AAAA,QAClB,GAAG,WAAW;AAAA,MAChB;AACA,cAAQ,WAAW,MAAM;AACvB,YAAI;AACF,wBAAc,QAAQ;AACxB,mBAAW;AACX,gBAAQ,OAAO,CAAC;AAAA,MAClB,GAAG,QAAQ;AAAA,IACb,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,eAAe,IAAI,WAAW,MAAM,UAAU,MAAM,iBAAiB,OAAO;AACnF,MAAI,WAAW;AACf,MAAI;AACJ,MAAI,YAAY;AAChB,MAAI,eAAe;AACnB,MAAI;AACJ,QAAM,QAAQ,MAAM;AAClB,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AACR,mBAAa;AACb,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,QAAM,SAAS,CAAC,YAAY;AAC1B,UAAM,WAAW,aAAa,EAAE;AAChC,UAAM,UAAU,KAAK,IAAI,IAAI;AAC7B,UAAM,SAAS,MAAM;AACnB,aAAO,YAAY,QAAQ;AAAA,IAC7B;AACA,UAAM;AACN,QAAI,YAAY,GAAG;AACjB,iBAAW,KAAK,IAAI;AACpB,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,UAAU,aAAa,WAAW,CAAC,YAAY;AACjD,iBAAW,KAAK,IAAI;AACpB,aAAO;AAAA,IACT,WAAW,UAAU;AACnB,kBAAY,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC3C,uBAAe,iBAAiB,SAAS;AACzC,gBAAQ,WAAW,MAAM;AACvB,qBAAW,KAAK,IAAI;AACpB,sBAAY;AACZ,kBAAQ,OAAO,CAAC;AAChB,gBAAM;AAAA,QACR,GAAG,KAAK,IAAI,GAAG,WAAW,OAAO,CAAC;AAAA,MACpC,CAAC;AAAA,IACH;AACA,QAAI,CAAC,WAAW,CAAC;AACf,cAAQ,WAAW,MAAM,YAAY,MAAM,QAAQ;AACrD,gBAAY;AACZ,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAwCA,SAAS,SAAS,KAAK;AACrB,SAAO;AACT;AA2CA,SAAS,oBAAoB,QAAQ,IAAI;AACvC,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AACJ,QAAM,QAAQ,IAAI,IAAI;AACtB,QAAM,SAAS,MAAM;AACnB,UAAM,QAAQ;AACd,YAAQ;AAAA,EACV;AACA,QAAM,QAAQ,QAAQ,EAAE,OAAO,OAAO,CAAC;AACvC,QAAM,MAAM,WAAW,EAAE,IAAI,KAAK,GAAG;AACrC,QAAMC,OAAM,WAAW,EAAE,IAAI,SAAS,GAAG;AACzC,QAAM,SAAS,UAAU,CAAC,QAAQ,aAAa;AAC7C,YAAQ;AACR,cAAU;AACV,WAAO;AAAA,MACL,MAAM;AACJ,YAAI,MAAM,OAAO;AACf,cAAI,IAAI;AACR,gBAAM,QAAQ;AAAA,QAChB;AACA,cAAM;AACN,eAAO;AAAA,MACT;AAAA,MACA,IAAI,IAAI;AACN,QAAAA,QAAO,OAAO,SAASA,KAAI,EAAE;AAAA,MAC/B;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,OAAO,aAAa,MAAM;AAC5B,WAAO,UAAU;AACnB,SAAO;AACT;AAEA,SAAS,kBAAkB,IAAI;AAC7B,MAAI,gBAAgB,GAAG;AACrB,mBAAe,EAAE;AACjB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAoPA,SAAS,cAAc,IAAI,KAAK,KAAK,UAAU,CAAC,GAAG;AACjD,SAAO,oBAAoB,eAAe,IAAI,OAAO,GAAG,EAAE;AAC5D;AAEA,SAAS,aAAa,OAAO,KAAK,KAAK,UAAU,CAAC,GAAG;AACnD,QAAM,YAAY,IAAI,MAAM,KAAK;AACjC,QAAM,UAAU,cAAc,MAAM;AAClC,cAAU,QAAQ,MAAM;AAAA,EAC1B,GAAG,IAAI,OAAO;AACd,QAAM,OAAO,MAAM,QAAQ,CAAC;AAC5B,SAAO;AACT;AAcA,SAAS,cAAc,IAAI,KAAK,KAAK,WAAW,OAAO,UAAU,MAAM,iBAAiB,OAAO;AAC7F,SAAO,oBAAoB,eAAe,IAAI,UAAU,SAAS,cAAc,GAAG,EAAE;AACtF;AA8KA,SAAS,aAAa,IAAI,OAAO,MAAM;AACrC,MAAI,mBAAmB;AACrB,cAAU,EAAE;AAAA,WACL;AACP,OAAG;AAAA;AAEH,aAAS,EAAE;AACf;AAwVA,SAAS,aAAa,IAAI,UAAU,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,YAAY,IAAI,KAAK;AAC3B,MAAI,QAAQ;AACZ,WAAS,QAAQ;AACf,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,WAAS,OAAO;AACd,cAAU,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,WAAS,SAAS,MAAM;AACtB,UAAM;AACN,cAAU,QAAQ;AAClB,YAAQ,WAAW,MAAM;AACvB,gBAAU,QAAQ;AAClB,cAAQ;AACR,SAAG,GAAG,IAAI;AAAA,IACZ,GAAG,aAAa,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,WAAW;AACb,cAAU,QAAQ;AAClB,QAAI;AACF,YAAM;AAAA,EACV;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL,WAAW,SAAS,SAAS;AAAA,IAC7B;AAAA,IACA;AAAA,EACF;AACF;;;ACzgCA,SAAS,aAAa,OAAO;AAC3B,MAAIC;AACJ,QAAM,QAAQ,aAAa,KAAK;AAChC,UAAQA,MAAK,SAAS,OAAO,SAAS,MAAM,QAAQ,OAAOA,MAAK;AAClE;AAEA,IAAM,gBAAgB,WAAW,SAAS;AAC1C,IAAM,kBAAkB,WAAW,OAAO,WAAW;AACrD,IAAM,mBAAmB,WAAW,OAAO,YAAY;AACvD,IAAM,kBAAkB,WAAW,OAAO,WAAW;AAErD,SAAS,oBAAoB,MAAM;AACjC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAIC,UAAS,KAAK,CAAC,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC,CAAC,GAAG;AAC/C,KAAC,QAAQ,WAAW,OAAO,IAAI;AAC/B,aAAS;AAAA,EACX,OAAO;AACL,KAAC,QAAQ,QAAQ,WAAW,OAAO,IAAI;AAAA,EACzC;AACA,MAAI,CAAC;AACH,WAAO;AACT,MAAI,CAAC,MAAM,QAAQ,MAAM;AACvB,aAAS,CAAC,MAAM;AAClB,MAAI,CAAC,MAAM,QAAQ,SAAS;AAC1B,gBAAY,CAAC,SAAS;AACxB,QAAM,WAAW,CAAC;AAClB,QAAM,UAAU,MAAM;AACpB,aAAS,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC7B,aAAS,SAAS;AAAA,EACpB;AACA,QAAM,WAAW,CAAC,IAAI,OAAO,UAAU,aAAa;AAClD,OAAG,iBAAiB,OAAO,UAAU,QAAQ;AAC7C,WAAO,MAAM,GAAG,oBAAoB,OAAO,UAAU,QAAQ;AAAA,EAC/D;AACA,QAAM,YAAY,MAAM,MAAM,CAAC,aAAa,MAAM,GAAG,aAAa,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,MAAM;AAC/F,YAAQ;AACR,QAAI,CAAC;AACH;AACF,aAAS,KAAK,GAAG,OAAO,QAAQ,CAAC,UAAU;AACzC,aAAO,UAAU,IAAI,CAAC,aAAa,SAAS,IAAI,OAAO,UAAU,QAAQ,CAAC;AAAA,IAC5E,CAAC,CAAC;AAAA,EACJ,GAAG,EAAE,WAAW,MAAM,OAAO,OAAO,CAAC;AACrC,QAAM,OAAO,MAAM;AACjB,cAAU;AACV,YAAQ;AAAA,EACV;AACA,oBAAkB,IAAI;AACtB,SAAO;AACT;AAEA,IAAI,iBAAiB;AACrB,SAAS,eAAe,QAAQ,SAAS,UAAU,CAAC,GAAG;AACrD,QAAM,EAAE,QAAAC,UAAS,eAAe,SAAS,CAAC,GAAG,UAAU,MAAM,eAAe,MAAM,IAAI;AACtF,MAAI,CAACA;AACH;AACF,MAAI,SAAS,CAAC,gBAAgB;AAC5B,qBAAiB;AACjB,UAAM,KAAKA,QAAO,SAAS,KAAK,QAAQ,EAAE,QAAQ,CAAC,OAAO,GAAG,iBAAiB,SAAS,IAAI,CAAC;AAAA,EAC9F;AACA,MAAI,eAAe;AACnB,QAAM,eAAe,CAAC,UAAU;AAC9B,WAAO,OAAO,KAAK,CAAC,YAAY;AAC9B,UAAI,OAAO,YAAY,UAAU;AAC/B,eAAO,MAAM,KAAKA,QAAO,SAAS,iBAAiB,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,OAAO,MAAM,UAAU,MAAM,aAAa,EAAE,SAAS,EAAE,CAAC;AAAA,MACpI,OAAO;AACL,cAAM,KAAK,aAAa,OAAO;AAC/B,eAAO,OAAO,MAAM,WAAW,MAAM,MAAM,aAAa,EAAE,SAAS,EAAE;AAAA,MACvE;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,WAAW,CAAC,UAAU;AAC1B,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC,MAAM,OAAO,MAAM,UAAU,MAAM,aAAa,EAAE,SAAS,EAAE;AAChE;AACF,QAAI,MAAM,WAAW;AACnB,qBAAe,CAAC,aAAa,KAAK;AACpC,QAAI,CAAC,cAAc;AACjB,qBAAe;AACf;AAAA,IACF;AACA,YAAQ,KAAK;AAAA,EACf;AACA,QAAM,UAAU;AAAA,IACd,iBAAiBA,SAAQ,SAAS,UAAU,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,IACtE,iBAAiBA,SAAQ,eAAe,CAAC,MAAM;AAC7C,YAAM,KAAK,aAAa,MAAM;AAC9B,UAAI;AACF,uBAAe,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC,aAAa,CAAC;AAAA,IACpE,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,IACpB,gBAAgB,iBAAiBA,SAAQ,QAAQ,CAAC,UAAU;AAC1D,UAAIF;AACJ,YAAM,KAAK,aAAa,MAAM;AAC9B,YAAMA,MAAKE,QAAO,SAAS,kBAAkB,OAAO,SAASF,IAAG,aAAa,YAAY,EAAE,MAAM,OAAO,SAAS,GAAG,SAASE,QAAO,SAAS,aAAa;AACxJ,gBAAQ,KAAK;AAAA,IACjB,CAAC;AAAA,EACH,EAAE,OAAO,OAAO;AAChB,QAAM,OAAO,MAAM,QAAQ,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC/C,SAAO;AACT;AA6JA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACtC,MAAIC;AACJ,QAAM,EAAE,QAAAC,UAAS,cAAc,IAAI;AACnC,QAAMC,aAAYF,MAAK,QAAQ,aAAa,OAAOA,MAAKC,WAAU,OAAO,SAASA,QAAO;AACzF,QAAM,gBAAgB,oBAAoB,MAAM,MAAM,MAAMC,aAAY,OAAO,SAASA,UAAS,aAAa;AAC9G,MAAID,SAAQ;AACV,qBAAiBA,SAAQ,QAAQ,CAAC,UAAU;AAC1C,UAAI,MAAM,kBAAkB;AAC1B;AACF,oBAAc,QAAQ;AAAA,IACxB,GAAG,IAAI;AACP,qBAAiBA,SAAQ,SAAS,cAAc,SAAS,IAAI;AAAA,EAC/D;AACA,SAAO;AACT;AAsMA,SAAS,aAAa,UAAU,OAAO,OAAO;AAC5C,QAAM,cAAc,IAAI;AACxB,QAAM,SAAS,MAAM,YAAY,QAAQ,QAAQ,SAAS,CAAC;AAC3D,SAAO;AACP,eAAa,QAAQ,IAAI;AACzB,SAAO;AACT;AAuaA,SAAS,YAAY,QAAQ;AAC3B,SAAO,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC;AAC1C;AAuBA,IAAM,UAAU,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AACzL,IAAM,YAAY;AAClB,QAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,CAAC;AAC5C,IAAM,WAAW,QAAQ,SAAS;AA8RlC,SAAS,UAAU,MAAM,QAAQ,EAAE,QAAAE,UAAS,eAAe,eAAe,GAAG,IAAI,CAAC,GAAG;AACnF,QAAM,WAAW,IAAI,YAAY;AACjC,QAAM,QAAQ,SAAS,MAAM;AAC3B,QAAIC;AACJ,WAAO,aAAa,MAAM,OAAOA,MAAKD,WAAU,OAAO,SAASA,QAAO,aAAa,OAAO,SAASC,IAAG;AAAA,EACzG,CAAC;AACD,QAAM,CAAC,OAAO,MAAM,aAAa,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,MAAM;AACxD,QAAIA;AACJ,QAAI,MAAMD,SAAQ;AAChB,YAAM,SAASC,MAAKD,QAAO,iBAAiB,EAAE,EAAE,iBAAiB,KAAK,MAAM,OAAO,SAASC,IAAG,KAAK;AACpG,eAAS,QAAQ,SAAS;AAAA,IAC5B;AAAA,EACF,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,QAAM,UAAU,CAAC,QAAQ;AACvB,QAAIA;AACJ,SAAKA,MAAK,MAAM,UAAU,OAAO,SAASA,IAAG;AAC3C,YAAM,MAAM,MAAM,YAAY,aAAa,IAAI,GAAG,GAAG;AAAA,EACzD,CAAC;AACD,SAAO;AACT;AA6eA,SAAS,sBAAsB,EAAE,UAAAC,YAAW,gBAAgB,IAAI,CAAC,GAAG;AAClE,MAAI,CAACA;AACH,WAAO,IAAI,SAAS;AACtB,QAAM,aAAa,IAAIA,UAAS,eAAe;AAC/C,mBAAiBA,WAAU,oBAAoB,MAAM;AACnD,eAAW,QAAQA,UAAS;AAAA,EAC9B,CAAC;AACD,SAAO;AACT;AAyHA,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,cAAc,CAAC,QAAQ,YAAY;AACrC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,sBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,eAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,kBAAkB,QAAQ,UAAU,UAAU,CAAC,GAAG;AACzD,QAAMC,MAAK,SAAS,EAAE,QAAAC,UAAS,cAAc,IAAID,KAAI,kBAAkB,YAAYA,KAAI,CAAC,QAAQ,CAAC;AACjG,MAAI;AACJ,QAAM,cAAc,aAAa,MAAMC,WAAU,oBAAoBA,OAAM;AAC3E,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,YAAY,MAAM,MAAM,aAAa,MAAM,GAAG,CAAC,OAAO;AAC1D,YAAQ;AACR,QAAI,YAAY,SAASA,WAAU,IAAI;AACrC,iBAAW,IAAI,eAAe,QAAQ;AACtC,eAAS,QAAQ,IAAI,eAAe;AAAA,IACtC;AAAA,EACF,GAAG,EAAE,WAAW,MAAM,OAAO,OAAO,CAAC;AACrC,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AAAA,EACZ;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,QAAQ,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,SAAS,IAAI,CAAC;AACpB,QAAM,SAAS,IAAI,CAAC;AACpB,QAAM,OAAO,IAAI,CAAC;AAClB,QAAM,QAAQ,IAAI,CAAC;AACnB,QAAM,MAAM,IAAI,CAAC;AACjB,QAAM,QAAQ,IAAI,CAAC;AACnB,QAAM,IAAI,IAAI,CAAC;AACf,QAAM,IAAI,IAAI,CAAC;AACf,WAAS,SAAS;AAChB,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC,IAAI;AACP,UAAI,OAAO;AACT,eAAO,QAAQ;AACf,eAAO,QAAQ;AACf,aAAK,QAAQ;AACb,cAAM,QAAQ;AACd,YAAI,QAAQ;AACZ,cAAM,QAAQ;AACd,UAAE,QAAQ;AACV,UAAE,QAAQ;AAAA,MACZ;AACA;AAAA,IACF;AACA,UAAM,OAAO,GAAG,sBAAsB;AACtC,WAAO,QAAQ,KAAK;AACpB,WAAO,QAAQ,KAAK;AACpB,SAAK,QAAQ,KAAK;AAClB,UAAM,QAAQ,KAAK;AACnB,QAAI,QAAQ,KAAK;AACjB,UAAM,QAAQ,KAAK;AACnB,MAAE,QAAQ,KAAK;AACf,MAAE,QAAQ,KAAK;AAAA,EACjB;AACA,oBAAkB,QAAQ,MAAM;AAChC,QAAM,MAAM,aAAa,MAAM,GAAG,CAAC,QAAQ,CAAC,OAAO,OAAO,CAAC;AAC3D,MAAI;AACF,qBAAiB,UAAU,QAAQ,EAAE,SAAS,MAAM,SAAS,KAAK,CAAC;AACrE,MAAI;AACF,qBAAiB,UAAU,QAAQ,EAAE,SAAS,KAAK,CAAC;AACtD,eAAa,MAAM;AACjB,QAAI;AACF,aAAO;AAAA,EACX,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAu3DA,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,cAAc,CAAC,QAAQ,YAAY;AACrC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,sBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,eAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,oBAAoB,QAAQ,UAAU,UAAU,CAAC,GAAG;AAC3D,QAAMC,MAAK,SAAS,EAAE,QAAAC,UAAS,cAAc,IAAID,KAAI,kBAAkB,YAAYA,KAAI,CAAC,QAAQ,CAAC;AACjG,MAAI;AACJ,QAAM,cAAc,aAAa,MAAMC,WAAU,sBAAsBA,OAAM;AAC7E,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,YAAY,MAAM,MAAM,aAAa,MAAM,GAAG,CAAC,OAAO;AAC1D,YAAQ;AACR,QAAI,YAAY,SAASA,WAAU,IAAI;AACrC,iBAAW,IAAI,iBAAiB,QAAQ;AACxC,eAAS,QAAQ,IAAI,eAAe;AAAA,IACtC;AAAA,EACF,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AAAA,EACZ;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAwQA,IAAM,eAAe;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,WAAW;AAAA,EACX,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AACf;AACA,IAAM,OAAuB,OAAO,KAAK,YAAY;AA4ErD,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,kBAAgB,IAAI,IAAI;AACxB,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,MAAM,IAAI;AAC1B,kBAAgB,MAAM,IAAI;AAC1B,kBAAgB,MAAM,IAAI;AAC5B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAguC1C,IAAI,YAAY,OAAO;AACvB,IAAI,sBAAsB,OAAO;AACjC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,iBAAiB,CAAC,GAAG,MAAM;AAC7B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,sBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AACpC,MAAI;AACF,aAAS,QAAQ,oBAAoB,CAAC,GAAG;AACvC,UAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,wBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACpC;AACF,SAAO;AACT;AACA,IAAM,qBAAqB;AAAA,EACzB,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC7B,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC5B,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC9B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC5B,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,GAAG,IAAI;AAAA,EAC7B,aAAa,CAAC,GAAG,MAAM,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,MAAM,KAAK;AAAA,EACjC,aAAa,CAAC,MAAM,MAAM,MAAM,CAAC;AAAA,EACjC,eAAe,CAAC,MAAM,MAAM,MAAM,GAAG;AACvC;AACA,IAAM,oBAAoB,eAAe;AAAA,EACvC,QAAQ;AACV,GAAG,kBAAkB;AAsPrB,SAAS,UAAU,OAAO,KAAK,MAAM,UAAU,CAAC,GAAG;AACjD,MAAIC,KAAI,IAAI,IAAI,IAAI;AACpB,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV;AAAA,IACA,OAAO;AAAA,IACP;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,mBAAmB;AAC9B,QAAM,QAAQ,SAAS,MAAM,OAAO,SAAS,GAAG,WAAWA,MAAK,MAAM,OAAO,SAAS,GAAG,UAAU,OAAO,SAASA,IAAG,KAAK,EAAE,QAAQ,MAAM,KAAK,MAAM,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,KAAK,MAAM,OAAO,SAAS,GAAG,KAAK;AACtQ,MAAI,QAAQ;AACZ,MAAI,CAAC,KAAK;AACR,QAAI,QAAQ;AACV,YAAM,gBAAgB,MAAM,KAAK,MAAM,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,aAAa,OAAO,SAAS,GAAG;AACvH,aAAO,gBAAgB,OAAO,SAAS,aAAa,UAAU;AAC9D,UAAI,CAAC;AACH,iBAAS,gBAAgB,OAAO,SAAS,aAAa,UAAU;AAAA,IACpE,OAAO;AACL,YAAM;AAAA,IACR;AAAA,EACF;AACA,UAAQ,aAAa,SAAS,UAAU,IAAI,SAAS,CAAC;AACtD,QAAM,UAAU,CAAC,QAAQ,CAAC,QAAQ,MAAM,WAAW,KAAK,IAAI,MAAM,GAAG,IAAI,YAAY,GAAG;AACxF,QAAM,WAAW,MAAM,MAAM,MAAM,GAAG,CAAC,IAAI,QAAQ,MAAM,GAAG,CAAC,IAAI;AACjE,MAAI,SAAS;AACX,UAAM,eAAe,SAAS;AAC9B,UAAM,QAAQ,IAAI,YAAY;AAC9B,UAAM,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,MAAM,QAAQ,QAAQ,CAAC,CAAC;AACvD,UAAM,OAAO,CAAC,MAAM;AAClB,UAAI,MAAM,MAAM,GAAG,KAAK;AACtB,cAAM,OAAO,CAAC;AAAA,IAClB,GAAG,EAAE,KAAK,CAAC;AACX,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS;AAAA,MACd,MAAM;AACJ,eAAO,SAAS;AAAA,MAClB;AAAA,MACA,IAAI,OAAO;AACT,cAAM,OAAO,KAAK;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AA+kBA,SAAS,eAAe,EAAE,QAAAC,UAAS,cAAc,IAAI,CAAC,GAAG;AACvD,MAAI,CAACA;AACH,WAAO,IAAI,KAAK;AAClB,QAAM,UAAU,IAAIA,QAAO,SAAS,SAAS,CAAC;AAC9C,mBAAiBA,SAAQ,QAAQ,MAAM;AACrC,YAAQ,QAAQ;AAAA,EAClB,CAAC;AACD,mBAAiBA,SAAQ,SAAS,MAAM;AACtC,YAAQ,QAAQ;AAAA,EAClB,CAAC;AACD,SAAO;AACT;AAqBA,SAAS,cAAc,UAAU,CAAC,GAAG;AACnC,QAAM;AAAA,IACJ,QAAAC,UAAS;AAAA,IACT,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,EACrB,IAAI;AACJ,QAAM,QAAQ,IAAI,YAAY;AAC9B,QAAM,SAAS,IAAI,aAAa;AAChC,QAAM,SAAS,MAAM;AACnB,QAAIA,SAAQ;AACV,UAAI,kBAAkB;AACpB,cAAM,QAAQA,QAAO;AACrB,eAAO,QAAQA,QAAO;AAAA,MACxB,OAAO;AACL,cAAM,QAAQA,QAAO,SAAS,gBAAgB;AAC9C,eAAO,QAAQA,QAAO,SAAS,gBAAgB;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACP,eAAa,MAAM;AACnB,mBAAiB,UAAU,QAAQ,EAAE,SAAS,KAAK,CAAC;AACpD,MAAI;AACF,qBAAiB,qBAAqB,QAAQ,EAAE,SAAS,KAAK,CAAC;AACjE,SAAO,EAAE,OAAO,OAAO;AACzB;;;ACz7MY,IAAC,MAAM;AACP,IAAC,UAAU;EACrB,UAAU;IACR,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,MAAM;IACN,KAAK;IACL,MAAM;IACN,QAAQ;IACR,WAAW;EACf;EACE,YAAY;IACV,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,MAAM;IACN,KAAK;IACL,MAAM;IACN,QAAQ;IACR,WAAW;EACf;AACA;AACY,IAAC,mBAAmB,CAAC;EAC/B;EACA;EACA;AACF,OAAO;EACL,CAAC,IAAI,IAAI,GAAG;EACZ,WAAW,YAAY,IAAI,IAAI,IAAI,IAAI;AACzC;;;AC9BY,IAAC,sBAAsB,OAAO,qBAAqB;;;ACCnD,IAAC,aAAa,WAAW;EACnC,UAAU;EACV,MAAM;EACN,MAAM;EACN,OAAO;IACL,MAAM;IACN,UAAU;EACd;EACE,QAAQ;AACV,CAAC;;;ACTD,IAAA,cAAe,CAAC,KAAK,UAAU;AAC7B,QAAM,SAAS,IAAI,aAAa;AAChC,aAAW,CAAC,KAAK,GAAG,KAAK,OAAO;AAC9B,WAAO,GAAG,IAAI;EAClB;AACE,SAAO;AACT;;;ACNY,IAAC,mBAAmB;AAChC,IAAM,cAAc;AACpB,IAAM,OAAO,CAAC,WAAW,OAAO,aAAa,SAAS,aAAa;AACjE,MAAI,MAAM,GAAG,SAAS,IAAI,KAAK;AAC/B,MAAI,aAAa;AACf,WAAO,IAAI,WAAW;EAC1B;AACE,MAAI,SAAS;AACX,WAAO,KAAK,OAAO;EACvB;AACE,MAAI,UAAU;AACZ,WAAO,KAAK,QAAQ;EACxB;AACE,SAAO;AACT;AACY,IAAC,sBAAsB,OAAO,qBAAqB;AACnD,IAAC,yBAAyB,CAAC,uBAAuB;AAC5D,QAAM,mBAAmB,uBAAuB,mBAAkB,IAAK,OAAO,qBAAqB,IAAI,gBAAgB,CAAC,IAAI,IAAI,gBAAgB;AAChJ,QAAM,YAAY,SAAS,MAAM;AAC/B,WAAO,MAAM,gBAAgB,KAAK;EACtC,CAAG;AACD,SAAO;AACT;AACY,IAAC,eAAe,CAAC,OAAO,uBAAuB;AACzD,QAAM,YAAY,uBAAuB,kBAAkB;AAC3D,QAAM,IAAI,CAAC,cAAc,OAAO,KAAK,UAAU,OAAO,OAAO,aAAa,IAAI,EAAE;AAChF,QAAM,IAAI,CAAC,YAAY,UAAU,KAAK,UAAU,OAAO,OAAO,IAAI,SAAS,EAAE,IAAI;AACjF,QAAM,IAAI,CAAC,aAAa,WAAW,KAAK,UAAU,OAAO,OAAO,IAAI,IAAI,QAAQ,IAAI;AACpF,QAAM,KAAK,CAAC,aAAa,YAAY,eAAe,UAAU,KAAK,UAAU,OAAO,OAAO,aAAa,SAAS,EAAE,IAAI;AACvH,QAAM,KAAK,CAAC,SAAS,aAAa,WAAW,WAAW,KAAK,UAAU,OAAO,OAAO,IAAI,SAAS,QAAQ,IAAI;AAC9G,QAAM,KAAK,CAAC,aAAa,aAAa,eAAe,WAAW,KAAK,UAAU,OAAO,OAAO,aAAa,IAAI,QAAQ,IAAI;AAC1H,QAAM,MAAM,CAAC,aAAa,SAAS,aAAa,eAAe,WAAW,WAAW,KAAK,UAAU,OAAO,OAAO,aAAa,SAAS,QAAQ,IAAI;AACpJ,QAAM,KAAK,CAAC,SAAS,SAAS;AAC5B,UAAM,QAAQ,KAAK,UAAU,IAAI,KAAK,CAAC,IAAI;AAC3C,WAAO,QAAQ,QAAQ,GAAG,WAAW,GAAG,IAAI,KAAK;EACrD;AACE,QAAM,SAAS,CAAC,WAAW;AACzB,UAAM,SAAS,CAAA;AACf,eAAW,OAAO,QAAQ;AACxB,UAAI,OAAO,GAAG,GAAG;AACf,eAAO,KAAK,UAAU,KAAK,IAAI,GAAG,EAAE,IAAI,OAAO,GAAG;MAC1D;IACA;AACI,WAAO;EACX;AACE,QAAM,cAAc,CAAC,WAAW;AAC9B,UAAM,SAAS,CAAA;AACf,eAAW,OAAO,QAAQ;AACxB,UAAI,OAAO,GAAG,GAAG;AACf,eAAO,KAAK,UAAU,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,IAAI,OAAO,GAAG;MACnE;IACA;AACI,WAAO;EACX;AACE,QAAM,aAAa,CAAC,SAAS,KAAK,UAAU,KAAK,IAAI,IAAI;AACzD,QAAM,kBAAkB,CAAC,SAAS,KAAK,UAAU,KAAK,IAAI,KAAK,IAAI,IAAI;AACvE,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ;AACA;;;;;;;;;AC1CM,UAAA,YAAY,OAAO,mBAAmB;AACtC,UAAA,KAAK,aAAa,WAAW;AAEnC,QAAI,CAAC;AAEL,iBAAA,gBAAqC,kCAAA;AACrC,UAAM,WAA4B,IAAA;AAE5B,UAAA,QAAA,IAAA;AACA,UAAA,aAAU,IAAS,CAAA,CAAA;AAEzB,UAAiB,UAAA,IAAA,KAAA;AACjB,QAAI,aAAc;AACd,QAAA,cAAA;AAIE,QAAA,wBAAqB,WAAA,SAAyB,gBAAA;AAEpD,UAAM,MAAa,SAAA,MAAA,QAAA,MAAA,WAAA,aAAA,YAAA,CAAA;AAAA,UAAA,aACA,SAAA,MAAA,iBAAA;MAAA,MAAA,MACH;MAAA,MAAA,MACA;MAAA,KAAA,IACH;IAAA,CAAA,CACX;AACF,UAAA,cAAA,SAAA,MAAA,SAAA,MAAA,IAAA,MAAA,MAAA,KAAA,IAAA,UAAA,YAAA,IAAA,MAAA,UAAA,IAAA,MAAA,QAAA,MAAA,MAAA,IAAA,MAAA,MAAA,CAAA;AAEA,UAAM,oBAAc,CAAA,MAAA;AAClB,UAAAC;AAAA,QAAA,gBAAA;AAAA,UAAA,EAAA,WAAA,CAAA,GAAA,CAAA,EAAA,SAAA,EAAA,MAAA;AAAA;AAAA,OAAAA,MAAA,OAIkB,aAAA,MAAgB,OAAA,SACtBA,IAAA,gBAAA;AAEmB,gBAAA,CAAA;AACjC,YAAA,KAAA,EAAA;AAEM,UAAA,CAAA;AAEF;AACE,iBAAA,MAAa,IAAI,MAAG,IAAA,IAAW,GAAA,IAAS,MAAA,MAAA,KAAA,EAAA,IAAA,MAAA,MAAA,IAAA,GAAA,sBAAA,EAAA,IAAA,MAAA,SAAA;IAE5C;AACA,UAAA,oBAAW,CAAA,MAAA;AAEX,UAAA,CAAA,MAAW,SAAE,CAAA,SAAA,SAAA,CAAA,UAAA;AACb;AACW,YAAA,SAAA,KAAA,IAAgB,EAAA,OAAA,sBACN,EAAA,IAChB,MAAI,SAAA,IAAgB,EAAA,IAAA,MAAA,MAAA,CAAA;AAC3B,YAAA,YAAA,MAAA,MAAA,IAAA,MAAA,MAAA,IAAA;AAEM,YAAA,2BAAuC,SAAA,aAAA,MAAA,YAAA,QAAA,SAAA,MAAA,IAAA,MAAA,MAAA;AACvC,gBAAA,YAAiB,IAAA,MAAkB,MAAA,IAAA,0BAAwB,UAAA,YAAA,IAAA,MAAA,UAAA,IAAA;IAE/D;AACG,UAAE,YAAuB,CAAA,MAAA;AAE5B,QAAA,yBAAA;AACA,mBAAA;AACM,eAAA,iBAAA,aACO,wBAAmB;AAGtB,eAAA,iBAAgB,WAAY,sBACT;AAE/B,8BAAA,SAAA;AAEM,eAAA,gBAA+B,MAAA;IACnC;AACa,UAAA,2BAAA,CAAA,MAAA;AACJ,UAAA,CAAA,SAAA,SAAA,CAAA,MAAA;AACA;AACT,UAAA,eAAA;AACA;AACF,YAAA,WAAA,WAAA,MAAA,IAAA,MAAA,IAAA;AAEM,UAAA,CAAA;AACJ;AACA,YAAA,UAAA,SAA0B,MAAA,sBAAA,EAAA,IAAA,MAAA,SAAA,IAAA,EAAA,IAAA,MAAA,MAAA,KAAA;AAE1B,YAAM,qBAAW,MAAiB,MAAI,IAAA,MAAU,MAAA,IAAA;AAChD,YAAe,2BAAA,SAAA,sBAAA,MAAA,YAAA,QAAA,SAAA,MAAA,IAAA,MAAA,MAAA;AAEf,gBACG,YAAA,IAAe,MAAA,MAAA,IAAA,0BAAkC,UAChD,YAAY,IAAA,MACd,UAAA,IAAA;IACF;AACM,UAAA,yBAAA,MACO;AAEH,mBAAA;AAGZ,iBAAA,MAAA,IAAA,MAAA,IAAA,IAAA;AAEA,eAAA,oBAA+B,aAAM,wBAAA;AACtB,eAAA,oBAAA,WAAA,sBAAA;AACb,2BAAiB;AACR,UAAA;AACA,gBAAA,QAAA;IACT;AACI,UAAA,4BAA6B,MAAA;AACnC,oBAAA;AAEA,cAAA,QAAA,CAAA,CAAA,MAAA;IACE;AACQ,UAAA,6BAAgB,MAAA;AAC1B,oBAAA;AAEA,cAAA,QAAA;IACE;AACA,oBAAgB,MAAA;AAClB,2BAAA;AAEA,eAAA,oBAAsB,WAAA,sBAAA;IACpB,CAAqB;AACZ,UAAA,uBAAA,MAAA;AACV,UAAA,SAAA,kBAAA;AAED,iBAAA,gBAA6B;IAC3B;AACE,qBAAyB,MAAA,WAAA,kBAAA,GAAA,aAAA,yBAAA;AAC7B,qBAAA,MAAA,WAAA,kBAAA,GAAA,cAAA,0BAAA;AAEA,WAAA,CAAA,MAAA,WAAA;AACE,aAAA,UAAiB,GAAkBC,YAAAC,YAAA;QACnC,MAAAC,MAAA,EAAA,EAAA,EAAA,MAAA;QACA,WAAA;MAAA,GACF;QACA,SAAAC,QAAA,MAAA;UACEC,eAAmCC,gBAAA,OAAA;YACnC,SAAA;YACA,KAAA;YACF,OAAAC,eAAA,CAAAJ,MAAA,EAAA,EAAA,EAAA,KAAA,GAAAA,MAAA,EAAA,EAAA,GAAAA,MAAA,GAAA,EAAA,GAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;ACtKY,IAAC,WAAW,WAAW;EACjC,QAAQ;IACN,MAAM;IACN,SAAS;EACb;EACE,SAAS;IACP,MAAM;IACN,UAAU;EACd;AACA,CAAC;;;;;;;;ACSK,UAAA,YAAY,OAAO,mBAAmB;AAEtC,UAAA,QAAQ,IAAI,CAAC;AACb,UAAA,QAAQ,IAAI,CAAC;AACb,UAAA,YAAY,IAAI,EAAE;AAClB,UAAA,aAAa,IAAI,EAAE;AACnB,UAAA,SAAS,IAAI,CAAC;AACd,UAAA,SAAS,IAAI,CAAC;AAEd,UAAA,eAAe,CAAC,SAAyB;AAC7C,UAAI,MAAM;AACF,cAAA,eAAe,KAAK,eAAe;AACnC,cAAA,cAAc,KAAK,cAAc;AAEvC,cAAM,QAAU,KAAK,YAAY,MAAO,eAAgB,OAAO;AAC/D,cAAM,QAAU,KAAK,aAAa,MAAO,cAAe,OAAO;MAAA;IACjE;AAGF,UAAM,SAAS,MAAM;AACnB,YAAM,OAAO,aAAW,OAAA,SAAA,UAAA;AACxB,UAAI,CAAC;AACC;AACA,YAAA,eAAA,KAAiC,eAAA;AAEjC,YAAA,cAAA,KAAiC,cAAA;AACjC,YAAA,iBAA+B,gBAAA,IAAS,KAAA;AAC9C,YAAM,gBAAkB,eAAA,IAAA,KAAsB;AAC9C,YAAM,SAAQ,KAAS,IAAA,gBAAe,MAAa,OAAA;AAEnD,YAAA,QACE,KAAA,IAAA,eACgB,MAAA,OAAA;AAElB,aAAO,QACL,kBACe,eAAA,mBAAA,UACS,eAAA;AAE1B,aAAA,QAAA,iBAA4B,cAAqB,kBAAgB,SAAA,cAAA;AACjE,iBAAU,QAAgB,SAAA,MAAoB,eAAG,GAAA,MAAY,OAAA;AAC/D,gBAAA,QAAA,QAAA,MAAA,cAAA,GAAA,KAAA,OAAA;IAEA;AACE,WAAA;MACA;MACD;;;;;;;;;;;;;;;;;;;;;;;;AC/DW,IAAC,YAAY,WAAW;EAClC,WAAW;EACX,iBAAiB;IACf,MAAM;IACN,QAAQ,CAAC,cAAc,YAAY,WAAW;EAClD;EACE,cAAc;AAChB,CAAC;AACW,IAAC,eAAe,CAAC,UAAU;AACrC,SAAO,aAAK,WAAW,KAAK;AAC9B;;;;;ACAY,IAAC,cAAc,CAAC,QAAQ,QAAQ;AAChC,IAAC,YAAY,CAAC,QAAQ,OAAO,QAAQ;AACrC,IAACK,YAAW,CAAC,QAAQ,OAAO,QAAQ;AACpC,IAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,QAAQ,KAAK,QAAQ,GAAG,KAAK,IAAI,WAAW,KAAK,SAAS,GAAG,KAAK,CAAC,OAAO,KAAK,GAAG,EAAE;AAChH,IAAC,YAAY,CAAC,MAAM;AAC9B,MAAI,OAAO,YAAY;AACrB,WAAO;AACT,SAAO,aAAa;AACtB;AACY,IAAC,eAAe,CAAC,SAAS,cAAM,IAAI;AACpC,IAAC,iBAAiB,CAAC,QAAQ;AACrC,MAAI,CAAC,SAAS,GAAG,GAAG;AAClB,WAAO;EACX;AACE,SAAO,CAAC,OAAO,MAAM,OAAO,GAAG,CAAC;AAClC;AACY,IAAC,WAAW,CAAC,QAAQ,QAAQ;;;AC1B7B,IAAC,iBAAiB,WAAW;EACvC,QAAQ;IACN,MAAM,CAAC,QAAQ,MAAM;IACrB,SAAS;EACb;EACE,WAAW;IACT,MAAM,CAAC,QAAQ,MAAM;IACrB,SAAS;EACb;EACE,QAAQ;IACN,MAAM;IACN,SAAS;EACb;EACE,WAAW;IACT,MAAM,eAAe,CAAC,QAAQ,QAAQ,KAAK,CAAC;IAC5C,SAAS;EACb;EACE,WAAW;IACT,MAAM,CAAC,QAAQ,KAAK;IACpB,SAAS;EACb;EACE,WAAW;IACT,MAAM,CAAC,QAAQ,KAAK;IACpB,SAAS;EACb;EACE,WAAW;IACT,MAAM,CAAC,QAAQ,OAAO,MAAM;IAC5B,SAAS;EACb;EACE,UAAU;EACV,KAAK;IACH,MAAM;IACN,SAAS;EACb;EACE,QAAQ;EACR,SAAS;IACP,MAAM;IACN,SAAS;EACb;EACE,UAAU;IACR,MAAM,CAAC,QAAQ,MAAM;IACrB,SAAS;EACb;EACE,IAAI;EACJ,MAAM;EACN,GAAG,aAAa,CAAC,aAAa,iBAAiB,CAAC;AAClD,CAAC;AACW,IAAC,iBAAiB;EAC5B,QAAQ,CAAC;IACP;IACA;EACJ,MAAQ,CAAC,WAAW,UAAU,EAAE,MAAMC,SAAQ;AAC9C;;;;ACrDY,IAAC,SAAS,CAAC,QAAQ,OAAO,KAAK,GAAG;AAClC,IAAC,YAAY,CAAC,QAAQ,OAAO,QAAQ,GAAG;AAExC,IAAC,UAAU,CAAC,KAAK,MAAM,iBAAiB;AAClD,SAAO;IACL,IAAI,QAAQ;AACV,aAAO,YAAI,KAAK,MAAM,YAAY;IACxC;IACI,IAAI,MAAM,KAAK;AACb,kBAAI,KAAK,MAAM,GAAG;IACxB;EACA;AACA;;;;ACRA,IAAM,QAAQ;AACF,IAAC,mBAAmB,CAAC,MAAM,OAAO,IAAI,MAAM,GAAG,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,KAAK,KAAI,CAAE;AAC/E,IAAC,WAAW,CAAC,IAAI,QAAQ;AACnC,MAAI,CAAC,MAAM,CAAC;AACV,WAAO;AACT,MAAI,IAAI,SAAS,GAAG;AAClB,UAAM,IAAI,MAAM,qCAAqC;AACvD,SAAO,GAAG,UAAU,SAAS,GAAG;AAClC;AACY,IAAC,WAAW,CAAC,IAAI,QAAQ;AACnC,MAAI,CAAC,MAAM,CAAC,IAAI,KAAI;AAClB;AACF,KAAG,UAAU,IAAI,GAAG,iBAAiB,GAAG,CAAC;AAC3C;AACY,IAAC,cAAc,CAAC,IAAI,QAAQ;AACtC,MAAI,CAAC,MAAM,CAAC,IAAI,KAAI;AAClB;AACF,KAAG,UAAU,OAAO,GAAG,iBAAiB,GAAG,CAAC;AAC9C;AACY,IAAC,WAAW,CAAC,SAAS,cAAc;AAC9C,MAAIC;AACJ,MAAI,CAAC,YAAY,CAAC,WAAW,CAAC;AAC5B,WAAO;AACT,MAAI,MAAM,SAAS,SAAS;AAC5B,MAAI,QAAQ;AACV,UAAM;AACR,MAAI;AACF,UAAM,QAAQ,QAAQ,MAAM,GAAG;AAC/B,QAAI;AACF,aAAO;AACT,UAAMC,aAAYD,MAAK,SAAS,gBAAgB,OAAO,SAASA,IAAG,iBAAiB,SAAS,EAAE;AAC/F,WAAOC,YAAWA,UAAS,GAAG,IAAI;EACtC,SAAW,GAAG;AACV,WAAO,QAAQ,MAAM,GAAG;EAC5B;AACA;AACY,IAAC,WAAW,CAAC,SAAS,WAAW,UAAU;AACrD,MAAI,CAAC,WAAW,CAAC;AACf;AACF,MAAI,SAAS,SAAS,GAAG;AACvB,cAAU,SAAS,EAAE,QAAQ,CAAC,CAAC,MAAM,MAAM,MAAM,SAAS,SAAS,MAAM,MAAM,CAAC;EACpF,OAAS;AACL,UAAM,MAAM,SAAS,SAAS;AAC9B,YAAQ,MAAM,GAAG,IAAI;EACzB;AACA;AAUO,SAAS,QAAQ,OAAO,cAAc,MAAM;AACjD,MAAI,CAAC;AACH,WAAO;AACT,MAAIC,UAAS,KAAK,KAAK,eAAe,KAAK,GAAG;AAC5C,WAAO,GAAG,KAAK,GAAG,WAAW;EACjC,WAAa,SAAS,KAAK,GAAG;AAC1B,WAAO;EACX;AACE,YAAU,OAAO,0CAA0C;AAC7D;;;;;kCCnBc;EACZ,MAAMC;AACR,CAAA;;;;;;;AAKM,UAAA,KAAK,aAAa,WAAW;AAEnC,QAAI,qBAA+C;AACnD,QAAI,qBAA+C;AACnD,QAAI,gBAAgB;AACpB,QAAI,iBAAiB;AAErB,UAAM,eAAe,IAAoB;AACzC,UAAM,UAAU,IAAoB;AACpC,UAAM,YAAY,IAAiB;AACnC,UAAM,SAAS,IAAiB;AAE1B,UAAA,YAAY,SAAqB,MAAM;AAC3C,YAAM,QAAuB,CAAA;AAC7B,UAAI,MAAM;AACV,cAAA,SAAqB,QAAA,MAAkB,MAAA;AAChC,UAAA,MAAO;AACf,cAAA,YAAA,QAAA,MAAA,SAAA;AAEK,aAAA,CAAA,MAAU,WAAe,KAAA;IAC7B,CAAO;AAAA,UACC,UAAA,SAAA,MAAA;AACN,aAAK;QACL,MAAM;QACR,GAAA,EAAA,MAAA;QACD,EAAA,CAAA,GAAA,GAAA,QAAA,gBAAA,CAAA,GAAA,CAAA,MAAA,OAAA;MAED;IACE,CAAA;AACF,UAAC,YAAA,SAAA,MAAA;AAED,aAAA,CAAA,GAAA,EAAA,MAAqB,GAAM,MAAA,SAAA;IACzB,CAAA;AACS,UAAA,eAAoB,MAAA;AAC3B,UAAAC;AACA,UAAA,QAAA,OAAiB;AAEjB,SAAAA,MAAe,OAAA,UAAA,OAAA,SAAAA,IAAA,aAAA,QAAA,KAAA;AACb,wBAAW,QAAc,MAAA;AACzB,yBAAY,QAAc,MAAA;AAC5B,aAAC,UAAA;UACH,WAAA,QAAA,MAAA;UACF,YAAA,QAAA,MAAA;QAMA,CAAS;MACP;IACE;AAA4B,aAAA,SACV,MAAA,MAAS;AACnB,UAAA,SAAA,IAAgB,GAAA;AAC1B,gBAAA,MAAA,SAAA,IAAA;MAAA,WACFC,UAAA,IAAA,KAAAA,UAAA,IAAA,GAAA;AAEM,gBAAA,MAAA,SAAkC,MAAA,IAAA;MACtC;IACE;AACA,UAAA,eAAA,CAAA,UAAA;AACF,UAAA,CAAAA,UAAA,KAAA,GAAA;AACA,kBAAAF,iBAA2B,wBAAA;AAC7B;MAEA;AACM,cAAU,MAAA,YAAQ;IACpB;AACA,UAAA,gBAAA,CAAA,UAAA;AACF,UAAA,CAAAE,UAAA,KAAA,GAAA;AACA,kBAAAF,iBAA4B,wBAAA;AAC9B;MAEA;AACE,cAAO,MAAO,aAAO;IAAA;AAGvB,UAAA,SAAA,MAAA;AACE,UAAAC;AACA,OAACA,MAAa,OAAA,UAAA,OAAA,SAAAA,IAAA,OAAA;IACZ;AACuB,UAAA,MAAA,MAAA,UAAA,CAAA,aAAA;AACA,UAAA,UAAA;AACvB,8BAAO,OAAA,SAAA,mBAAA;AACL,8BAAA,OAAA,SAAA,mBAAA;MAAC,OAAG;AAEN,SAAA,EAAA,MAAA,mBAAA,IAAA,kBAAA,WAAA,MAAA;AACF,6BAAA,iBAAA,UAAA,MAAA;MAAA;IACkB,GACpB,EAAA,WAAA,KAAA,CAAA;AAEA,UAAA,MAAA,CAAA,MAAA,WAAA,MAAA,MAAA,GAAA,MAAA;AACE,UAAM,CAAA,MAAO;AACP,iBAAA,MAAA;AACJ,cAAWA;AACT,iBAAA;AACS,cAAA,QAAA,OAAA;AACP,aAAAA,MAAA,OAAmB,UAAA,OAAA,SAAAA,IAAA,aAAA,QAAA,KAAA;UACjB;QAAwC,CAC1C;IAAA,CAAA;AAEN,YAAA,qBAAA,SAAA;MACF,kBAAA;MAEA,aAAA;IAAA,CACE,CAAA;AAAA,gBACS,MAAA;AAAA,UACW,QAAA,OAAA;AAClB,gBAAa,MAAA,YAAA;AACd,gBAAA,MAAA,aAAA;MAAA;IAGH,CAAA;AACE,cAAA,MAAmB;AACjB,UAAA,CAAA,MAAQ;AACR,iBAAQ,MAAmB;AAC7B,iBAAA;QAAA,CACD;IAED,CAAA;AACE,cAAW,MAAA,OAAA,CAAA;AACT,WAAA;MACS;MAAA;MAEZ;MACS;MAEG;MAAA;IAAA,CAEX;AAAA,WAAA,CAAA,MAAA,WAAA;AAEA,aAAAE,UAAA,GAAAC,mBAAA,OAAA;QAAA,SAAA;QAEA,KAAA;QAAA,OAAAC,eAAAC,MAAA,EAAA,EAAA,EAAA,CAAA;MAAA,GAEA;QAAAC,gBAAA,OAAA;UAEA,SAAA;UAAA,KAAA;UAEA,OAAAF,eAAAC,MAAA,OAAA,CAAA;UACD,OAAAE,eAAAF,MAAA,SAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnMW,IAAC,cAAc,CAAC,MAAM,UAAU;AAE1C,OAAK,UAAU,CAAC,QAAQ;AACtB,eAAW,QAAQ,CAAC,MAAM,GAAG,OAAO,OAAO,SAAS,OAAO,QAAQ,CAAA,CAAE,CAAC,GAAG;AACvE,UAAI,UAAU,KAAK,MAAM,IAAI;IACnC;EACA;AACE,MAAI,OAAO;AACT,eAAW,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,KAAK,GAAG;AAE/C,WAAK,GAAG,IAAI;IAClB;EACA;AACE,SAAO;AACT;AACY,IAAC,sBAAsB,CAAC,IAAI,SAAS;AAE/C,KAAG,UAAU,CAAC,QAAQ;AAEpB,OAAG,WAAW,IAAI;AAClB,QAAI,OAAO,iBAAiB,IAAI,IAAI;EACxC;AACE,SAAO;AACT;AACY,IAAC,uBAAuB,CAAC,WAAW,SAAS;AAEvD,YAAU,UAAU,CAAC,QAAQ;AAC3B,QAAI,UAAU,MAAM,SAAS;EACjC;AACE,SAAO;AACT;AACY,IAAC,kBAAkB,CAAC,cAAc;AAE5C,YAAU,UAAU;AACpB,SAAO;AACT;;;AClCY,IAAC,cAAc,YAAY,SAAS;", "names": ["_a", "isString", "set", "_a", "isString", "window", "_a", "window", "document", "window", "_a", "document", "_a", "window", "_a", "window", "_a", "window", "window", "_a", "_createBlock", "_Transition", "_unref", "_withCtx", "_withDirectives", "_createElementVNode", "_normalizeClass", "isNumber", "isNumber", "_a", "computed", "isNumber", "COMPONENT_NAME", "_a", "isNumber", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode", "_normalizeStyle"]}