import {
  toPoint
} from "./chunk-M4AQHOXJ.js";
import {
  classes,
  closest,
  event
} from "./chunk-PBO22ZEO.js";
import "./chunk-GFT2G5UO.js";

// node_modules/.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/Cursor.js
var CURSOR_CLS_PATTERN = /^djs-cursor-.*$/;
function set(mode) {
  var classes2 = classes(document.body);
  classes2.removeMatching(CURSOR_CLS_PATTERN);
  if (mode) {
    classes2.add("djs-cursor-" + mode);
  }
}
function unset() {
  set(null);
}

// node_modules/.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/ClickTrap.js
var TRAP_PRIORITY = 5e3;
function install(eventBus, eventName) {
  eventName = eventName || "element.click";
  function trap() {
    return false;
  }
  eventBus.once(eventName, TRAP_PRIORITY, trap);
  return function() {
    eventBus.off(eventName, trap);
  };
}

// node_modules/.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/PositionUtil.js
function delta(a, b) {
  return {
    x: a.x - b.x,
    y: a.y - b.y
  };
}

// node_modules/.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/navigation/movecanvas/MoveCanvas.js
var THRESHOLD = 15;
function MoveCanvas(eventBus, canvas) {
  var context;
  eventBus.on("element.mousedown", 500, function(e) {
    return handleStart(e.originalEvent);
  });
  function handleMove(event2) {
    var start = context.start, button = context.button, position = toPoint(event2), delta2 = delta(position, start);
    if (!context.dragging && length(delta2) > THRESHOLD) {
      context.dragging = true;
      if (button === 0) {
        install(eventBus);
      }
      set("grab");
    }
    if (context.dragging) {
      var lastPosition = context.last || context.start;
      delta2 = delta(position, lastPosition);
      canvas.scroll({
        dx: delta2.x,
        dy: delta2.y
      });
      context.last = position;
    }
    event2.preventDefault();
  }
  function handleEnd(event2) {
    event.unbind(document, "mousemove", handleMove);
    event.unbind(document, "mouseup", handleEnd);
    context = null;
    unset();
  }
  function handleStart(event2) {
    if (closest(event2.target, ".djs-draggable")) {
      return;
    }
    var button = event2.button;
    if (button >= 2 || event2.ctrlKey || event2.shiftKey || event2.altKey) {
      return;
    }
    context = {
      button,
      start: toPoint(event2)
    };
    event.bind(document, "mousemove", handleMove);
    event.bind(document, "mouseup", handleEnd);
    return true;
  }
  this.isActive = function() {
    return !!context;
  };
}
MoveCanvas.$inject = [
  "eventBus",
  "canvas"
];
function length(point) {
  return Math.sqrt(Math.pow(point.x, 2) + Math.pow(point.y, 2));
}

// node_modules/.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/navigation/movecanvas/index.js
var movecanvas_default = {
  __init__: ["moveCanvas"],
  moveCanvas: ["type", MoveCanvas]
};
export {
  movecanvas_default as default
};
//# sourceMappingURL=diagram-js_lib_navigation_movecanvas.js.map
