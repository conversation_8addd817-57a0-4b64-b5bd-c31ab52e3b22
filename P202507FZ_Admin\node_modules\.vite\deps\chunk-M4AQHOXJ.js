// node_modules/.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/Event.js
function getOriginal(event) {
  return event.originalEvent || event.srcEvent;
}
function toPoint(event) {
  if (event.pointers && event.pointers.length) {
    event = event.pointers[0];
  }
  if (event.touches && event.touches.length) {
    event = event.touches[0];
  }
  return event ? {
    x: event.clientX,
    y: event.clientY
  } : null;
}

export {
  getOriginal,
  toPoint
};
//# sourceMappingURL=chunk-M4AQHOXJ.js.map
