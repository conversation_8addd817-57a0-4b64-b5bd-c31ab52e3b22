# CSRF问题解决方案

## 🚨 问题描述
在iframe中访问标注工具时出现CSRF验证失败错误：
```
Forbidden (403)
CSRF verification failed. Request aborted.
```

## 🔍 问题原因
1. **Cookie策略限制**: iframe环境下第三方cookie被阻止
2. **CSRF Token缺失**: 跨域请求无法获取CSRF token
3. **SameSite策略**: 浏览器默认的SameSite=Lax策略阻止跨站cookie

## ✅ 解决方案

### 方案1：新窗口打开（推荐，立即可用）
**优点**: 无需修改外部系统，立即可用
**操作**: 点击页面上的"在新窗口中打开标注工具"按钮

### 方案2：外部系统配置（需要系统管理员）

#### Django系统配置（如果外部系统是Django）

**1. 修改settings.py**
```python
# 允许iframe嵌套
X_FRAME_OPTIONS = 'SAMEORIGIN'
# 或者完全允许
# X_FRAME_OPTIONS = None

# CSRF设置
CSRF_COOKIE_SAMESITE = 'None'
CSRF_COOKIE_SECURE = False  # HTTP环境下设为False
CSRF_TRUSTED_ORIGINS = [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://your-admin-domain.com'  # 替换为实际的管理后台域名
]

# Session设置
SESSION_COOKIE_SAMESITE = 'None'
SESSION_COOKIE_SECURE = False  # HTTP环境下设为False
```

**2. 中间件配置**
```python
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # 如果使用django-cors-headers
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    # ... 其他中间件
]

# CORS设置（如果使用django-cors-headers）
CORS_ALLOW_ALL_ORIGINS = True  # 开发环境
# 或者指定特定域名
# CORS_ALLOWED_ORIGINS = [
#     "http://localhost:3000",
#     "http://127.0.0.1:3000",
# ]
CORS_ALLOW_CREDENTIALS = True
```

#### Nginx配置
```nginx
server {
    listen 19003;
    server_name ***************;
    
    # 允许iframe嵌套
    add_header X-Frame-Options "SAMEORIGIN";
    
    # CORS设置
    add_header Access-Control-Allow-Origin "*";
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-CSRFToken";
    add_header Access-Control-Allow-Credentials "true";
    
    location / {
        proxy_pass http://localhost:8000;  # 假设Django运行在8000端口
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 方案3：前端代理（临时解决方案）

在开发环境中，可以通过Vite代理来解决：

**vite.config.ts配置**
```typescript
export default defineConfig({
  server: {
    proxy: {
      '/annotation-api': {
        target: 'http://***************:19003',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/annotation-api/, ''),
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 转发cookie
            if (req.headers.cookie) {
              proxyReq.setHeader('cookie', req.headers.cookie);
            }
          });
        }
      }
    }
  }
})
```

然后修改iframe的src为：
```html
<iframe src="/annotation-api" ...>
```

## 🔧 测试验证

### 1. 检查Cookie设置
在浏览器开发者工具中：
1. 打开Network标签
2. 访问标注工具
3. 查看Response Headers中的Set-Cookie
4. 确认SameSite属性设置

### 2. 检查CSRF Token
```javascript
// 在浏览器控制台执行
document.querySelector('[name=csrfmiddlewaretoken]')?.value
// 或者
document.cookie.split(';').find(c => c.trim().startsWith('csrftoken='))
```

### 3. 网络请求检查
1. 查看POST请求是否包含CSRF token
2. 检查cookie是否正确发送
3. 确认响应状态码

## 🛡️ 安全考虑

### 生产环境建议
1. **使用HTTPS**: 避免安全警告和cookie问题
2. **限制域名**: 不要使用CORS_ALLOW_ALL_ORIGINS = True
3. **定期更新**: 保持Django和相关包的最新版本

### 最小权限原则
```python
# 生产环境推荐配置
CSRF_TRUSTED_ORIGINS = [
    'https://your-admin-domain.com'  # 只允许特定域名
]
CORS_ALLOWED_ORIGINS = [
    'https://your-admin-domain.com'
]
```

## 📞 联系支持

如果需要修改外部系统配置，请联系标注工具的系统管理员，并提供以下信息：
1. 管理后台的域名和端口
2. 需要在iframe中嵌套的需求
3. 当前遇到的CSRF错误信息

**临时解决方案**: 使用"新窗口打开"功能可以立即解决问题，无需等待系统配置修改。
