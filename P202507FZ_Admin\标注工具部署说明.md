# 标注工具页面部署说明

## 概述
本次更新在管理端新增了一个名为"标注工具"的页面，该页面通过iframe嵌套的方式集成了外部标注工具系统。

## 新增文件

### 1. 前端页面文件
- **文件路径**: `P202507FZ_Admin/src/views/tools/annotation/index.vue`
- **功能**: 标注工具的主页面，通过iframe嵌套外部系统
- **特性**:
  - 响应式设计，支持移动端
  - 加载状态提示
  - 错误处理和重试机制
  - 支持新窗口打开
  - 安全的iframe沙箱设置

### 2. 数据库菜单配置
- **文件路径**: `P202507FZ_Api/sql/mysql/annotation-tool-menu.sql`
- **功能**: 添加标注工具相关的菜单项到系统中
- **包含内容**:
  - 工具管理目录菜单
  - 标注工具子菜单
  - 相关权限按钮

## 部署步骤

### 第一步：执行数据库脚本
1. 连接到MySQL数据库
2. 执行SQL脚本添加菜单项：
   ```bash
   mysql -u [username] -p [database_name] < P202507FZ_Api/sql/mysql/annotation-tool-menu.sql
   ```

### 第二步：重启后端服务
重启Spring Boot应用以刷新菜单缓存。

### 第三步：分配菜单权限
1. 登录管理后台
2. 进入"系统管理" -> "角色管理"
3. 为相应角色分配"工具管理" -> "标注工具"的访问权限

### 第四步：验证功能
1. 使用有权限的账号登录
2. 在左侧菜单中找到"工具管理" -> "标注工具"
3. 点击进入页面，验证iframe是否正常加载外部系统

## 菜单结构
```
工具管理 (目录)
└── 标注工具 (菜单)
    ├── 标注工具查询 (按钮权限)
    └── 标注工具访问 (按钮权限)
```

## 技术特性

### 安全性
- iframe使用sandbox属性限制权限
- 仅允许必要的脚本和表单操作
- 支持同源策略

### 用户体验
- 加载状态指示器
- 错误处理和重试机制
- 支持新窗口打开功能
- 响应式设计适配不同屏幕

### 性能优化
- 15秒加载超时设置
- 缓存破坏机制（重新加载时添加时间戳）
- 组件卸载时清理定时器

## 配置说明

### 外部系统URL
当前配置的外部系统地址为：`http://***************:19003`

如需修改，请编辑文件：
`P202507FZ_Admin/src/views/tools/annotation/index.vue`

找到以下行并修改URL：
```javascript
const annotationUrl = 'http://***************:19003'
```

### 权限配置
- 查询权限：`tools:annotation:query`
- 访问权限：`tools:annotation:access`

## 故障排除

### 1. 菜单不显示
- 检查数据库脚本是否执行成功
- 确认角色是否分配了相应权限
- 重启后端服务刷新缓存

### 2. iframe加载失败
- 检查外部系统是否可访问
- 确认网络连接是否正常
- 查看浏览器控制台错误信息

### 3. 跨域问题
- 确认外部系统是否设置了正确的CORS策略
- 检查X-Frame-Options头部设置

## 维护建议

1. **定期检查外部系统可用性**
2. **监控页面加载性能**
3. **根据需要调整超时设置**
4. **定期更新安全策略**

## 联系支持
如遇到问题，请联系开发团队或查看系统日志获取详细错误信息。
