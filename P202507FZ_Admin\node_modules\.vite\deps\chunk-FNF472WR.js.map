{"version": 3, "sources": ["../../.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/util/ModelUtil.js"], "sourcesContent": ["import {\n  some\n} from 'min-dash';\n\n/**\n * @typedef { import('../model/Types').Element } Element\n * @typedef { import('../model/Types').ModdleElement } ModdleElement\n */\n\n/**\n * Is an element of the given BPMN type?\n *\n * @param  {Element|ModdleElement} element\n * @param  {string} type\n *\n * @return {boolean}\n */\nexport function is(element, type) {\n  var bo = getBusinessObject(element);\n\n  return bo && (typeof bo.$instanceOf === 'function') && bo.$instanceOf(type);\n}\n\n\n/**\n * Return true if element has any of the given types.\n *\n * @param {Element|ModdleElement} element\n * @param {string[]} types\n *\n * @return {boolean}\n */\nexport function isAny(element, types) {\n  return some(types, function(t) {\n    return is(element, t);\n  });\n}\n\n/**\n * Return the business object for a given element.\n *\n * @param {Element|ModdleElement} element\n *\n * @return {ModdleElement}\n */\nexport function getBusinessObject(element) {\n  return (element && element.businessObject) || element;\n}\n\n/**\n * Return the di object for a given element.\n *\n * @param {Element} element\n *\n * @return {ModdleElement}\n */\nexport function getDi(element) {\n  return element && element.di;\n}"], "mappings": ";;;;;AAiBO,SAAS,GAAG,SAAS,MAAM;AAChC,MAAI,KAAK,kBAAkB,OAAO;AAElC,SAAO,MAAO,OAAO,GAAG,gBAAgB,cAAe,GAAG,YAAY,IAAI;AAC5E;AAWO,SAAS,MAAM,SAAS,OAAO;AACpC,SAAO,KAAK,OAAO,SAAS,GAAG;AAC7B,WAAO,GAAG,SAAS,CAAC;AAAA,EACtB,CAAC;AACH;AASO,SAAS,kBAAkB,SAAS;AACzC,SAAQ,WAAW,QAAQ,kBAAmB;AAChD;AASO,SAAS,MAAM,SAAS;AAC7B,SAAO,WAAW,QAAQ;AAC5B;", "names": []}