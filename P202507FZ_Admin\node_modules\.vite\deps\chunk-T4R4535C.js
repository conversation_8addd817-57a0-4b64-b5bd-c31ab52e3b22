import {
  assign,
  every,
  filter,
  find,
  forEach,
  groupBy,
  has,
  isArray,
  isNumber,
  isObject,
  isUndefined,
  sortBy
} from "./chunk-YTJ5ESGD.js";

// node_modules/.pnpm/diagram-js@14.11.3/node_modules/diagram-js/lib/util/Geometry.js
function pointDistance(a, b) {
  if (!a || !b) {
    return -1;
  }
  return Math.sqrt(
    Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2)
  );
}
function pointsOnLine(p, q, r, accuracy) {
  if (typeof accuracy === "undefined") {
    accuracy = 5;
  }
  if (!p || !q || !r) {
    return false;
  }
  var val = (q.x - p.x) * (r.y - p.y) - (q.y - p.y) * (r.x - p.x), dist = pointDistance(p, q);
  return Math.abs(val / dist) <= accuracy;
}
var ALIGNED_THRESHOLD = 2;
function pointsAligned(a, b) {
  var points = Array.from(arguments).flat();
  const axisMap = {
    "x": "v",
    "y": "h"
  };
  for (const [axis, orientation] of Object.entries(axisMap)) {
    if (pointsAlignedOnAxis(axis, points)) {
      return orientation;
    }
  }
  return false;
}
function pointsAlignedOnAxis(axis, points) {
  const referencePoint = points[0];
  return every(points, function(point) {
    return Math.abs(referencePoint[axis] - point[axis]) <= ALIGNED_THRESHOLD;
  });
}
function pointInRect(p, rect, tolerance) {
  tolerance = tolerance || 0;
  return p.x > rect.x - tolerance && p.y > rect.y - tolerance && p.x < rect.x + rect.width + tolerance && p.y < rect.y + rect.height + tolerance;
}
function getMidPoint(p, q) {
  return {
    x: Math.round(p.x + (q.x - p.x) / 2),
    y: Math.round(p.y + (q.y - p.y) / 2)
  };
}

// node_modules/.pnpm/path-intersection@3.1.0/node_modules/path-intersection/intersect.js
var p2s = /,?([a-z]),?/gi;
var toFloat = parseFloat;
var math = Math;
var PI = math.PI;
var mmin = math.min;
var mmax = math.max;
var pow = math.pow;
var abs = math.abs;
var pathCommand = /([a-z])[\s,]*((-?\d*\.?\d*(?:e[-+]?\d+)?[\s]*,?[\s]*)+)/ig;
var pathValues = /(-?\d*\.?\d*(?:e[-+]?\d+)?)[\s]*,?[\s]*/ig;
var isArray2 = Array.isArray || function(o) {
  return o instanceof Array;
};
function hasProperty(obj, property) {
  return Object.prototype.hasOwnProperty.call(obj, property);
}
function clone(obj) {
  if (typeof obj == "function" || Object(obj) !== obj) {
    return obj;
  }
  var res = new obj.constructor();
  for (var key in obj) {
    if (hasProperty(obj, key)) {
      res[key] = clone(obj[key]);
    }
  }
  return res;
}
function repush(array, item) {
  for (var i = 0, ii = array.length; i < ii; i++)
    if (array[i] === item) {
      return array.push(array.splice(i, 1)[0]);
    }
}
function cacher(f) {
  function newf() {
    var arg = Array.prototype.slice.call(arguments, 0), args = arg.join("␀"), cache = newf.cache = newf.cache || {}, count = newf.count = newf.count || [];
    if (hasProperty(cache, args)) {
      repush(count, args);
      return cache[args];
    }
    count.length >= 1e3 && delete cache[count.shift()];
    count.push(args);
    cache[args] = f(...arguments);
    return cache[args];
  }
  return newf;
}
function parsePathString(pathString) {
  if (!pathString) {
    return null;
  }
  var pth = paths(pathString);
  if (pth.arr) {
    return clone(pth.arr);
  }
  var paramCounts = { a: 7, c: 6, h: 1, l: 2, m: 2, q: 4, s: 4, t: 2, v: 1, z: 0 }, data = [];
  if (isArray2(pathString) && isArray2(pathString[0])) {
    data = clone(pathString);
  }
  if (!data.length) {
    String(pathString).replace(pathCommand, function(a, b, c) {
      var params = [], name = b.toLowerCase();
      c.replace(pathValues, function(a2, b2) {
        b2 && params.push(+b2);
      });
      if (name == "m" && params.length > 2) {
        data.push([b, ...params.splice(0, 2)]);
        name = "l";
        b = b == "m" ? "l" : "L";
      }
      while (params.length >= paramCounts[name]) {
        data.push([b, ...params.splice(0, paramCounts[name])]);
        if (!paramCounts[name]) {
          break;
        }
      }
    });
  }
  data.toString = paths.toString;
  pth.arr = clone(data);
  return data;
}
function paths(ps) {
  var p = paths.ps = paths.ps || {};
  if (p[ps]) {
    p[ps].sleep = 100;
  } else {
    p[ps] = {
      sleep: 100
    };
  }
  setTimeout(function() {
    for (var key in p) {
      if (hasProperty(p, key) && key != ps) {
        p[key].sleep--;
        !p[key].sleep && delete p[key];
      }
    }
  });
  return p[ps];
}
function rectBBox(x, y, width, height) {
  if (arguments.length === 1) {
    y = x.y;
    width = x.width;
    height = x.height;
    x = x.x;
  }
  return {
    x,
    y,
    width,
    height,
    x2: x + width,
    y2: y + height
  };
}
function pathToString() {
  return this.join(",").replace(p2s, "$1");
}
function pathClone(pathArray) {
  var res = clone(pathArray);
  res.toString = pathToString;
  return res;
}
function findDotsAtSegment(p1x, p1y, c1x, c1y, c2x, c2y, p2x, p2y, t) {
  var t1 = 1 - t, t13 = pow(t1, 3), t12 = pow(t1, 2), t2 = t * t, t3 = t2 * t, x = t13 * p1x + t12 * 3 * t * c1x + t1 * 3 * t * t * c2x + t3 * p2x, y = t13 * p1y + t12 * 3 * t * c1y + t1 * 3 * t * t * c2y + t3 * p2y;
  return {
    x: fixError(x),
    y: fixError(y)
  };
}
function bezierBBox(points) {
  var bbox = curveBBox(...points);
  return rectBBox(
    bbox.x0,
    bbox.y0,
    bbox.x1 - bbox.x0,
    bbox.y1 - bbox.y0
  );
}
function isPointInsideBBox(bbox, x, y) {
  return x >= bbox.x && x <= bbox.x + bbox.width && y >= bbox.y && y <= bbox.y + bbox.height;
}
function isBBoxIntersect(bbox1, bbox2) {
  bbox1 = rectBBox(bbox1);
  bbox2 = rectBBox(bbox2);
  return isPointInsideBBox(bbox2, bbox1.x, bbox1.y) || isPointInsideBBox(bbox2, bbox1.x2, bbox1.y) || isPointInsideBBox(bbox2, bbox1.x, bbox1.y2) || isPointInsideBBox(bbox2, bbox1.x2, bbox1.y2) || isPointInsideBBox(bbox1, bbox2.x, bbox2.y) || isPointInsideBBox(bbox1, bbox2.x2, bbox2.y) || isPointInsideBBox(bbox1, bbox2.x, bbox2.y2) || isPointInsideBBox(bbox1, bbox2.x2, bbox2.y2) || (bbox1.x < bbox2.x2 && bbox1.x > bbox2.x || bbox2.x < bbox1.x2 && bbox2.x > bbox1.x) && (bbox1.y < bbox2.y2 && bbox1.y > bbox2.y || bbox2.y < bbox1.y2 && bbox2.y > bbox1.y);
}
function base3(t, p1, p2, p3, p4) {
  var t1 = -3 * p1 + 9 * p2 - 9 * p3 + 3 * p4, t2 = t * t1 + 6 * p1 - 12 * p2 + 6 * p3;
  return t * t2 - 3 * p1 + 3 * p2;
}
function bezlen(x1, y1, x2, y2, x3, y3, x4, y4, z) {
  if (z == null) {
    z = 1;
  }
  z = z > 1 ? 1 : z < 0 ? 0 : z;
  var z2 = z / 2, n = 12, Tvalues = [-0.1252, 0.1252, -0.3678, 0.3678, -0.5873, 0.5873, -0.7699, 0.7699, -0.9041, 0.9041, -0.9816, 0.9816], Cvalues = [0.2491, 0.2491, 0.2335, 0.2335, 0.2032, 0.2032, 0.1601, 0.1601, 0.1069, 0.1069, 0.0472, 0.0472], sum = 0;
  for (var i = 0; i < n; i++) {
    var ct = z2 * Tvalues[i] + z2, xbase = base3(ct, x1, x2, x3, x4), ybase = base3(ct, y1, y2, y3, y4), comb = xbase * xbase + ybase * ybase;
    sum += Cvalues[i] * math.sqrt(comb);
  }
  return z2 * sum;
}
function intersectLines(x1, y1, x2, y2, x3, y3, x4, y4) {
  if (mmax(x1, x2) < mmin(x3, x4) || mmin(x1, x2) > mmax(x3, x4) || mmax(y1, y2) < mmin(y3, y4) || mmin(y1, y2) > mmax(y3, y4)) {
    return;
  }
  var nx = (x1 * y2 - y1 * x2) * (x3 - x4) - (x1 - x2) * (x3 * y4 - y3 * x4), ny = (x1 * y2 - y1 * x2) * (y3 - y4) - (y1 - y2) * (x3 * y4 - y3 * x4), denominator = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
  if (!denominator) {
    return;
  }
  var px = fixError(nx / denominator), py = fixError(ny / denominator), px2 = +px.toFixed(2), py2 = +py.toFixed(2);
  if (px2 < +mmin(x1, x2).toFixed(2) || px2 > +mmax(x1, x2).toFixed(2) || px2 < +mmin(x3, x4).toFixed(2) || px2 > +mmax(x3, x4).toFixed(2) || py2 < +mmin(y1, y2).toFixed(2) || py2 > +mmax(y1, y2).toFixed(2) || py2 < +mmin(y3, y4).toFixed(2) || py2 > +mmax(y3, y4).toFixed(2)) {
    return;
  }
  return { x: px, y: py };
}
function fixError(number) {
  return Math.round(number * 1e11) / 1e11;
}
function findBezierIntersections(bez1, bez2, justCount) {
  var bbox1 = bezierBBox(bez1), bbox2 = bezierBBox(bez2);
  if (!isBBoxIntersect(bbox1, bbox2)) {
    return justCount ? 0 : [];
  }
  var l1 = bezlen(...bez1), l2 = bezlen(...bez2), n1 = isLine(bez1) ? 1 : ~~(l1 / 5) || 1, n2 = isLine(bez2) ? 1 : ~~(l2 / 5) || 1, dots1 = [], dots2 = [], xy = {}, res = justCount ? 0 : [];
  for (var i = 0; i < n1 + 1; i++) {
    var p = findDotsAtSegment(...bez1, i / n1);
    dots1.push({ x: p.x, y: p.y, t: i / n1 });
  }
  for (i = 0; i < n2 + 1; i++) {
    p = findDotsAtSegment(...bez2, i / n2);
    dots2.push({ x: p.x, y: p.y, t: i / n2 });
  }
  for (i = 0; i < n1; i++) {
    for (var j = 0; j < n2; j++) {
      var di = dots1[i], di1 = dots1[i + 1], dj = dots2[j], dj1 = dots2[j + 1], ci = abs(di1.x - di.x) < 0.01 ? "y" : "x", cj = abs(dj1.x - dj.x) < 0.01 ? "y" : "x", is = intersectLines(di.x, di.y, di1.x, di1.y, dj.x, dj.y, dj1.x, dj1.y), key;
      if (is) {
        key = is.x.toFixed(9) + "#" + is.y.toFixed(9);
        if (xy[key]) {
          continue;
        }
        xy[key] = true;
        var t1 = di.t + abs((is[ci] - di[ci]) / (di1[ci] - di[ci])) * (di1.t - di.t), t2 = dj.t + abs((is[cj] - dj[cj]) / (dj1[cj] - dj[cj])) * (dj1.t - dj.t);
        if (t1 >= 0 && t1 <= 1 && t2 >= 0 && t2 <= 1) {
          if (justCount) {
            res++;
          } else {
            res.push({
              x: is.x,
              y: is.y,
              t1,
              t2
            });
          }
        }
      }
    }
  }
  return res;
}
function findPathIntersections(path1, path2, justCount) {
  path1 = pathToCurve(path1);
  path2 = pathToCurve(path2);
  var x1, y1, x2, y2, x1m, y1m, x2m, y2m, bez1, bez2, res = justCount ? 0 : [];
  for (var i = 0, ii = path1.length; i < ii; i++) {
    var pi = path1[i];
    if (pi[0] == "M") {
      x1 = x1m = pi[1];
      y1 = y1m = pi[2];
    } else {
      if (pi[0] == "C") {
        bez1 = [x1, y1, ...pi.slice(1)];
        x1 = bez1[6];
        y1 = bez1[7];
      } else {
        bez1 = [x1, y1, x1, y1, x1m, y1m, x1m, y1m];
        x1 = x1m;
        y1 = y1m;
      }
      for (var j = 0, jj = path2.length; j < jj; j++) {
        var pj = path2[j];
        if (pj[0] == "M") {
          x2 = x2m = pj[1];
          y2 = y2m = pj[2];
        } else {
          if (pj[0] == "C") {
            bez2 = [x2, y2, ...pj.slice(1)];
            x2 = bez2[6];
            y2 = bez2[7];
          } else {
            bez2 = [x2, y2, x2, y2, x2m, y2m, x2m, y2m];
            x2 = x2m;
            y2 = y2m;
          }
          var intr = findBezierIntersections(bez1, bez2, justCount);
          if (justCount) {
            res += intr;
          } else {
            for (var k = 0, kk = intr.length; k < kk; k++) {
              intr[k].segment1 = i;
              intr[k].segment2 = j;
              intr[k].bez1 = bez1;
              intr[k].bez2 = bez2;
            }
            res = res.concat(intr);
          }
        }
      }
    }
  }
  return res;
}
function pathToAbsolute(pathArray) {
  var pth = paths(pathArray);
  if (pth.abs) {
    return pathClone(pth.abs);
  }
  if (!isArray2(pathArray) || !isArray2(pathArray && pathArray[0])) {
    pathArray = parsePathString(pathArray);
  }
  if (!pathArray || !pathArray.length) {
    return [["M", 0, 0]];
  }
  var res = [], x = 0, y = 0, mx = 0, my = 0, start = 0, pa0;
  if (pathArray[0][0] == "M") {
    x = +pathArray[0][1];
    y = +pathArray[0][2];
    mx = x;
    my = y;
    start++;
    res[0] = ["M", x, y];
  }
  for (var r, pa, i = start, ii = pathArray.length; i < ii; i++) {
    res.push(r = []);
    pa = pathArray[i];
    pa0 = pa[0];
    if (pa0 != pa0.toUpperCase()) {
      r[0] = pa0.toUpperCase();
      switch (r[0]) {
        case "A":
          r[1] = pa[1];
          r[2] = pa[2];
          r[3] = pa[3];
          r[4] = pa[4];
          r[5] = pa[5];
          r[6] = +pa[6] + x;
          r[7] = +pa[7] + y;
          break;
        case "V":
          r[1] = +pa[1] + y;
          break;
        case "H":
          r[1] = +pa[1] + x;
          break;
        case "M":
          mx = +pa[1] + x;
          my = +pa[2] + y;
        default:
          for (var j = 1, jj = pa.length; j < jj; j++) {
            r[j] = +pa[j] + (j % 2 ? x : y);
          }
      }
    } else {
      for (var k = 0, kk = pa.length; k < kk; k++) {
        r[k] = pa[k];
      }
    }
    pa0 = pa0.toUpperCase();
    switch (r[0]) {
      case "Z":
        x = +mx;
        y = +my;
        break;
      case "H":
        x = r[1];
        break;
      case "V":
        y = r[1];
        break;
      case "M":
        mx = r[r.length - 2];
        my = r[r.length - 1];
      default:
        x = r[r.length - 2];
        y = r[r.length - 1];
    }
  }
  res.toString = pathToString;
  pth.abs = pathClone(res);
  return res;
}
function isLine(bez) {
  return bez[0] === bez[2] && bez[1] === bez[3] && bez[4] === bez[6] && bez[5] === bez[7];
}
function lineToCurve(x1, y1, x2, y2) {
  return [
    x1,
    y1,
    x2,
    y2,
    x2,
    y2
  ];
}
function qubicToCurve(x1, y1, ax, ay, x2, y2) {
  var _13 = 1 / 3, _23 = 2 / 3;
  return [
    _13 * x1 + _23 * ax,
    _13 * y1 + _23 * ay,
    _13 * x2 + _23 * ax,
    _13 * y2 + _23 * ay,
    x2,
    y2
  ];
}
function arcToCurve(x1, y1, rx, ry, angle, large_arc_flag, sweep_flag, x2, y2, recursive) {
  var _120 = PI * 120 / 180, rad = PI / 180 * (+angle || 0), res = [], xy, rotate = cacher(function(x3, y3, rad2) {
    var X = x3 * math.cos(rad2) - y3 * math.sin(rad2), Y = x3 * math.sin(rad2) + y3 * math.cos(rad2);
    return { x: X, y: Y };
  });
  if (!recursive) {
    xy = rotate(x1, y1, -rad);
    x1 = xy.x;
    y1 = xy.y;
    xy = rotate(x2, y2, -rad);
    x2 = xy.x;
    y2 = xy.y;
    var x = (x1 - x2) / 2, y = (y1 - y2) / 2;
    var h = x * x / (rx * rx) + y * y / (ry * ry);
    if (h > 1) {
      h = math.sqrt(h);
      rx = h * rx;
      ry = h * ry;
    }
    var rx2 = rx * rx, ry2 = ry * ry, k = (large_arc_flag == sweep_flag ? -1 : 1) * math.sqrt(abs((rx2 * ry2 - rx2 * y * y - ry2 * x * x) / (rx2 * y * y + ry2 * x * x))), cx = k * rx * y / ry + (x1 + x2) / 2, cy = k * -ry * x / rx + (y1 + y2) / 2, f1 = math.asin(((y1 - cy) / ry).toFixed(9)), f2 = math.asin(((y2 - cy) / ry).toFixed(9));
    f1 = x1 < cx ? PI - f1 : f1;
    f2 = x2 < cx ? PI - f2 : f2;
    f1 < 0 && (f1 = PI * 2 + f1);
    f2 < 0 && (f2 = PI * 2 + f2);
    if (sweep_flag && f1 > f2) {
      f1 = f1 - PI * 2;
    }
    if (!sweep_flag && f2 > f1) {
      f2 = f2 - PI * 2;
    }
  } else {
    f1 = recursive[0];
    f2 = recursive[1];
    cx = recursive[2];
    cy = recursive[3];
  }
  var df = f2 - f1;
  if (abs(df) > _120) {
    var f2old = f2, x2old = x2, y2old = y2;
    f2 = f1 + _120 * (sweep_flag && f2 > f1 ? 1 : -1);
    x2 = cx + rx * math.cos(f2);
    y2 = cy + ry * math.sin(f2);
    res = arcToCurve(x2, y2, rx, ry, angle, 0, sweep_flag, x2old, y2old, [f2, f2old, cx, cy]);
  }
  df = f2 - f1;
  var c1 = math.cos(f1), s1 = math.sin(f1), c2 = math.cos(f2), s2 = math.sin(f2), t = math.tan(df / 4), hx = 4 / 3 * rx * t, hy = 4 / 3 * ry * t, m1 = [x1, y1], m2 = [x1 + hx * s1, y1 - hy * c1], m3 = [x2 + hx * s2, y2 - hy * c2], m4 = [x2, y2];
  m2[0] = 2 * m1[0] - m2[0];
  m2[1] = 2 * m1[1] - m2[1];
  if (recursive) {
    return [m2, m3, m4].concat(res);
  } else {
    res = [m2, m3, m4].concat(res).join().split(",");
    var newres = [];
    for (var i = 0, ii = res.length; i < ii; i++) {
      newres[i] = i % 2 ? rotate(res[i - 1], res[i], rad).y : rotate(res[i], res[i + 1], rad).x;
    }
    return newres;
  }
}
function curveBBox(x0, y0, x1, y1, x2, y2, x3, y3) {
  var tvalues = [], bounds = [[], []], a, b, c, t, t1, t2, b2ac, sqrtb2ac;
  for (var i = 0; i < 2; ++i) {
    if (i == 0) {
      b = 6 * x0 - 12 * x1 + 6 * x2;
      a = -3 * x0 + 9 * x1 - 9 * x2 + 3 * x3;
      c = 3 * x1 - 3 * x0;
    } else {
      b = 6 * y0 - 12 * y1 + 6 * y2;
      a = -3 * y0 + 9 * y1 - 9 * y2 + 3 * y3;
      c = 3 * y1 - 3 * y0;
    }
    if (abs(a) < 1e-12) {
      if (abs(b) < 1e-12) {
        continue;
      }
      t = -c / b;
      if (0 < t && t < 1) {
        tvalues.push(t);
      }
      continue;
    }
    b2ac = b * b - 4 * c * a;
    sqrtb2ac = math.sqrt(b2ac);
    if (b2ac < 0) {
      continue;
    }
    t1 = (-b + sqrtb2ac) / (2 * a);
    if (0 < t1 && t1 < 1) {
      tvalues.push(t1);
    }
    t2 = (-b - sqrtb2ac) / (2 * a);
    if (0 < t2 && t2 < 1) {
      tvalues.push(t2);
    }
  }
  var j = tvalues.length, jlen = j, mt;
  while (j--) {
    t = tvalues[j];
    mt = 1 - t;
    bounds[0][j] = mt * mt * mt * x0 + 3 * mt * mt * t * x1 + 3 * mt * t * t * x2 + t * t * t * x3;
    bounds[1][j] = mt * mt * mt * y0 + 3 * mt * mt * t * y1 + 3 * mt * t * t * y2 + t * t * t * y3;
  }
  bounds[0][jlen] = x0;
  bounds[1][jlen] = y0;
  bounds[0][jlen + 1] = x3;
  bounds[1][jlen + 1] = y3;
  bounds[0].length = bounds[1].length = jlen + 2;
  return {
    x0: mmin(...bounds[0]),
    y0: mmin(...bounds[1]),
    x1: mmax(...bounds[0]),
    y1: mmax(...bounds[1])
  };
}
function pathToCurve(path) {
  var pth = paths(path);
  if (pth.curve) {
    return pathClone(pth.curve);
  }
  var curvedPath = pathToAbsolute(path), attrs = { x: 0, y: 0, bx: 0, by: 0, X: 0, Y: 0, qx: null, qy: null }, processPath = function(path2, d, pathCommand3) {
    var nx, ny;
    if (!path2) {
      return ["C", d.x, d.y, d.x, d.y, d.x, d.y];
    }
    !(path2[0] in { T: 1, Q: 1 }) && (d.qx = d.qy = null);
    switch (path2[0]) {
      case "M":
        d.X = path2[1];
        d.Y = path2[2];
        break;
      case "A":
        path2 = ["C", ...arcToCurve(d.x, d.y, ...path2.slice(1))];
        break;
      case "S":
        if (pathCommand3 == "C" || pathCommand3 == "S") {
          nx = d.x * 2 - d.bx;
          ny = d.y * 2 - d.by;
        } else {
          nx = d.x;
          ny = d.y;
        }
        path2 = ["C", nx, ny, ...path2.slice(1)];
        break;
      case "T":
        if (pathCommand3 == "Q" || pathCommand3 == "T") {
          d.qx = d.x * 2 - d.qx;
          d.qy = d.y * 2 - d.qy;
        } else {
          d.qx = d.x;
          d.qy = d.y;
        }
        path2 = ["C", ...qubicToCurve(d.x, d.y, d.qx, d.qy, path2[1], path2[2])];
        break;
      case "Q":
        d.qx = path2[1];
        d.qy = path2[2];
        path2 = ["C", ...qubicToCurve(d.x, d.y, path2[1], path2[2], path2[3], path2[4])];
        break;
      case "L":
        path2 = ["C", ...lineToCurve(d.x, d.y, path2[1], path2[2])];
        break;
      case "H":
        path2 = ["C", ...lineToCurve(d.x, d.y, path2[1], d.y)];
        break;
      case "V":
        path2 = ["C", ...lineToCurve(d.x, d.y, d.x, path2[1])];
        break;
      case "Z":
        path2 = ["C", ...lineToCurve(d.x, d.y, d.X, d.Y)];
        break;
    }
    return path2;
  }, fixArc = function(pp, i2) {
    if (pp[i2].length > 7) {
      pp[i2].shift();
      var pi = pp[i2];
      while (pi.length) {
        pathCommands[i2] = "A";
        pp.splice(i2++, 0, ["C", ...pi.splice(0, 6)]);
      }
      pp.splice(i2, 1);
      ii = curvedPath.length;
    }
  }, pathCommands = [], pfirst = "", pathCommand2 = "";
  for (var i = 0, ii = curvedPath.length; i < ii; i++) {
    curvedPath[i] && (pfirst = curvedPath[i][0]);
    if (pfirst != "C") {
      pathCommands[i] = pfirst;
      i && (pathCommand2 = pathCommands[i - 1]);
    }
    curvedPath[i] = processPath(curvedPath[i], attrs, pathCommand2);
    if (pathCommands[i] != "A" && pfirst == "C")
      pathCommands[i] = "C";
    fixArc(curvedPath, i);
    var seg = curvedPath[i], seglen = seg.length;
    attrs.x = seg[seglen - 2];
    attrs.y = seg[seglen - 1];
    attrs.bx = toFloat(seg[seglen - 4]) || attrs.x;
    attrs.by = toFloat(seg[seglen - 3]) || attrs.y;
  }
  pth.curve = pathClone(curvedPath);
  return curvedPath;
}

// node_modules/.pnpm/diagram-js@14.11.3/node_modules/diagram-js/lib/util/ModelUtil.js
function isConnection(value) {
  return isObject(value) && has(value, "waypoints");
}
function isLabel(value) {
  return isObject(value) && has(value, "labelTarget");
}

// node_modules/.pnpm/diagram-js@14.11.3/node_modules/diagram-js/lib/layout/LayoutUtil.js
function roundBounds(bounds) {
  return {
    x: Math.round(bounds.x),
    y: Math.round(bounds.y),
    width: Math.round(bounds.width),
    height: Math.round(bounds.height)
  };
}
function roundPoint(point) {
  return {
    x: Math.round(point.x),
    y: Math.round(point.y)
  };
}
function asTRBL(bounds) {
  return {
    top: bounds.y,
    right: bounds.x + (bounds.width || 0),
    bottom: bounds.y + (bounds.height || 0),
    left: bounds.x
  };
}
function asBounds(trbl) {
  return {
    x: trbl.left,
    y: trbl.top,
    width: trbl.right - trbl.left,
    height: trbl.bottom - trbl.top
  };
}
function getBoundsMid(bounds) {
  return roundPoint({
    x: bounds.x + (bounds.width || 0) / 2,
    y: bounds.y + (bounds.height || 0) / 2
  });
}
function getConnectionMid(connection) {
  var waypoints = connection.waypoints;
  var parts = waypoints.reduce(function(parts2, point, index) {
    var lastPoint = waypoints[index - 1];
    if (lastPoint) {
      var lastPart = parts2[parts2.length - 1];
      var startLength = lastPart && lastPart.endLength || 0;
      var length = distance(lastPoint, point);
      parts2.push({
        start: lastPoint,
        end: point,
        startLength,
        endLength: startLength + length,
        length
      });
    }
    return parts2;
  }, []);
  var totalLength = parts.reduce(function(length, part) {
    return length + part.length;
  }, 0);
  var midLength = totalLength / 2;
  var i = 0;
  var midSegment = parts[i];
  while (midSegment.endLength < midLength) {
    midSegment = parts[++i];
  }
  var segmentProgress = (midLength - midSegment.startLength) / midSegment.length;
  var midPoint = {
    x: midSegment.start.x + (midSegment.end.x - midSegment.start.x) * segmentProgress,
    y: midSegment.start.y + (midSegment.end.y - midSegment.start.y) * segmentProgress
  };
  return midPoint;
}
function getMid(element) {
  if (isConnection(element)) {
    return getConnectionMid(element);
  }
  return getBoundsMid(element);
}
function getOrientation(rect, reference, padding) {
  padding = padding || 0;
  if (!isObject(padding)) {
    padding = { x: padding, y: padding };
  }
  var rectOrientation = asTRBL(rect), referenceOrientation = asTRBL(reference);
  var top = rectOrientation.bottom + padding.y <= referenceOrientation.top, right = rectOrientation.left - padding.x >= referenceOrientation.right, bottom = rectOrientation.top - padding.y >= referenceOrientation.bottom, left = rectOrientation.right + padding.x <= referenceOrientation.left;
  var vertical = top ? "top" : bottom ? "bottom" : null, horizontal = left ? "left" : right ? "right" : null;
  if (horizontal && vertical) {
    return vertical + "-" + horizontal;
  } else {
    return horizontal || vertical || "intersect";
  }
}
function getElementLineIntersection(elementPath, linePath, cropStart) {
  var intersections = getIntersections(elementPath, linePath);
  if (intersections.length === 1) {
    return roundPoint(intersections[0]);
  } else if (intersections.length === 2 && pointDistance(intersections[0], intersections[1]) < 1) {
    return roundPoint(intersections[0]);
  } else if (intersections.length > 1) {
    intersections = sortBy(intersections, function(i) {
      var distance2 = Math.floor(i.t2 * 100) || 1;
      distance2 = 100 - distance2;
      distance2 = (distance2 < 10 ? "0" : "") + distance2;
      return i.segment2 + "#" + distance2;
    });
    return roundPoint(intersections[cropStart ? 0 : intersections.length - 1]);
  }
  return null;
}
function getIntersections(a, b) {
  return findPathIntersections(a, b);
}
function filterRedundantWaypoints(waypoints) {
  waypoints = waypoints.slice();
  var idx = 0, point, previousPoint, nextPoint;
  while (waypoints[idx]) {
    point = waypoints[idx];
    previousPoint = waypoints[idx - 1];
    nextPoint = waypoints[idx + 1];
    if (pointDistance(point, nextPoint) === 0 || pointsOnLine(previousPoint, nextPoint, point)) {
      waypoints.splice(idx, 1);
    } else {
      idx++;
    }
  }
  return waypoints;
}
function distance(a, b) {
  return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2));
}

// node_modules/.pnpm/diagram-js@14.11.3/node_modules/diagram-js/lib/util/Elements.js
function getParents(elements) {
  return filter(elements, function(element) {
    return !find(elements, function(e) {
      return e !== element && getParent(element, e);
    });
  });
}
function getParent(element, parent) {
  if (!parent) {
    return;
  }
  if (element === parent) {
    return parent;
  }
  if (!element.parent) {
    return;
  }
  return getParent(element.parent, parent);
}
function add(elements, element, unique) {
  var canAdd = !unique || elements.indexOf(element) === -1;
  if (canAdd) {
    elements.push(element);
  }
  return canAdd;
}
function eachElement(elements, fn, depth) {
  depth = depth || 0;
  if (!isArray(elements)) {
    elements = [elements];
  }
  forEach(elements, function(s, i) {
    var filter2 = fn(s, i, depth);
    if (isArray(filter2) && filter2.length) {
      eachElement(filter2, fn, depth + 1);
    }
  });
}
function selfAndChildren(elements, unique, maxDepth) {
  var result = [], processedChildren = [];
  eachElement(elements, function(element, i, depth) {
    add(result, element, unique);
    var children = element.children;
    if (maxDepth === -1 || depth < maxDepth) {
      if (children && add(processedChildren, children, unique)) {
        return children;
      }
    }
  });
  return result;
}
function selfAndAllChildren(elements, allowDuplicates) {
  return selfAndChildren(elements, !allowDuplicates, -1);
}
function getClosure(elements, isTopLevel, closure) {
  if (isUndefined(isTopLevel)) {
    isTopLevel = true;
  }
  if (isObject(isTopLevel)) {
    closure = isTopLevel;
    isTopLevel = true;
  }
  closure = closure || {};
  var allShapes = copyObject(closure.allShapes), allConnections = copyObject(closure.allConnections), enclosedElements = copyObject(closure.enclosedElements), enclosedConnections = copyObject(closure.enclosedConnections);
  var topLevel = copyObject(
    closure.topLevel,
    isTopLevel && groupBy(elements, function(e) {
      return e.id;
    })
  );
  function handleConnection(c) {
    if (topLevel[c.source.id] && topLevel[c.target.id]) {
      topLevel[c.id] = [c];
    }
    if (allShapes[c.source.id] && allShapes[c.target.id]) {
      enclosedConnections[c.id] = enclosedElements[c.id] = c;
    }
    allConnections[c.id] = c;
  }
  function handleElement(element) {
    enclosedElements[element.id] = element;
    if (element.waypoints) {
      enclosedConnections[element.id] = allConnections[element.id] = element;
    } else {
      allShapes[element.id] = element;
      forEach(element.incoming, handleConnection);
      forEach(element.outgoing, handleConnection);
      return element.children;
    }
  }
  eachElement(elements, handleElement);
  return {
    allShapes,
    allConnections,
    topLevel,
    enclosedConnections,
    enclosedElements
  };
}
function getBBox(elements, stopRecursion) {
  stopRecursion = !!stopRecursion;
  if (!isArray(elements)) {
    elements = [elements];
  }
  var minX, minY, maxX, maxY;
  forEach(elements, function(element) {
    var bbox = element;
    if (element.waypoints && !stopRecursion) {
      bbox = getBBox(element.waypoints, true);
    }
    var x = bbox.x, y = bbox.y, height = bbox.height || 0, width = bbox.width || 0;
    if (x < minX || minX === void 0) {
      minX = x;
    }
    if (y < minY || minY === void 0) {
      minY = y;
    }
    if (x + width > maxX || maxX === void 0) {
      maxX = x + width;
    }
    if (y + height > maxY || maxY === void 0) {
      maxY = y + height;
    }
  });
  return {
    x: minX,
    y: minY,
    height: maxY - minY,
    width: maxX - minX
  };
}
function getEnclosedElements(elements, bbox) {
  var filteredElements = {};
  forEach(elements, function(element) {
    var e = element;
    if (e.waypoints) {
      e = getBBox(e);
    }
    if (!isNumber(bbox.y) && e.x > bbox.x) {
      filteredElements[element.id] = element;
    }
    if (!isNumber(bbox.x) && e.y > bbox.y) {
      filteredElements[element.id] = element;
    }
    if (e.x > bbox.x && e.y > bbox.y) {
      if (isNumber(bbox.width) && isNumber(bbox.height) && e.width + e.x < bbox.width + bbox.x && e.height + e.y < bbox.height + bbox.y) {
        filteredElements[element.id] = element;
      } else if (!isNumber(bbox.width) || !isNumber(bbox.height)) {
        filteredElements[element.id] = element;
      }
    }
  });
  return filteredElements;
}
function getType(element) {
  if ("waypoints" in element) {
    return "connection";
  }
  if ("x" in element) {
    return "shape";
  }
  return "root";
}
function isFrameElement(element) {
  return !!(element && element.isFrame);
}
function copyObject(src1, src2) {
  return assign({}, src1 || {}, src2 || {});
}

export {
  pointDistance,
  pointsOnLine,
  pointsAligned,
  pointInRect,
  getMidPoint,
  findPathIntersections,
  isConnection,
  isLabel,
  roundBounds,
  roundPoint,
  asTRBL,
  asBounds,
  getMid,
  getOrientation,
  getElementLineIntersection,
  filterRedundantWaypoints,
  getParents,
  eachElement,
  selfAndAllChildren,
  getClosure,
  getBBox,
  getEnclosedElements,
  getType,
  isFrameElement
};
//# sourceMappingURL=chunk-T4R4535C.js.map
