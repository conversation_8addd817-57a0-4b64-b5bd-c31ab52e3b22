{"version": 3, "sources": ["../../.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/Event.js"], "sourcesContent": ["/**\n * @typedef {import('../util/Types').Point} Point\n */\n\nfunction __stopPropagation(event) {\n  if (!event || typeof event.stopPropagation !== 'function') {\n    return;\n  }\n\n  event.stopPropagation();\n}\n\n/**\n * @param {import('../core/EventBus').Event} event\n *\n * @return {Event}\n */\nexport function getOriginal(event) {\n  return event.originalEvent || event.srcEvent;\n}\n\n/**\n * @param {Event|import('../core/EventBus').Event} event\n */\nexport function stopPropagation(event) {\n  __stopPropagation(event);\n  __stopPropagation(getOriginal(event));\n}\n\n/**\n * @param {Event} event\n *\n * @return {Point|null}\n */\nexport function toPoint(event) {\n\n  if (event.pointers && event.pointers.length) {\n    event = event.pointers[0];\n  }\n\n  if (event.touches && event.touches.length) {\n    event = event.touches[0];\n  }\n\n  return event ? {\n    x: event.clientX,\n    y: event.clientY\n  } : null;\n}"], "mappings": ";AAiBO,SAAS,YAAY,OAAO;AACjC,SAAO,MAAM,iBAAiB,MAAM;AACtC;AAeO,SAAS,QAAQ,OAAO;AAE7B,MAAI,MAAM,YAAY,MAAM,SAAS,QAAQ;AAC3C,YAAQ,MAAM,SAAS,CAAC;AAAA,EAC1B;AAEA,MAAI,MAAM,WAAW,MAAM,QAAQ,QAAQ;AACzC,YAAQ,MAAM,QAAQ,CAAC;AAAA,EACzB;AAEA,SAAO,QAAQ;AAAA,IACb,GAAG,MAAM;AAAA,IACT,GAAG,MAAM;AAAA,EACX,IAAI;AACN;", "names": []}