{"version": 3, "sources": ["../../.pnpm/@iconify+iconify@3.1.1/node_modules/@iconify/iconify/dist/iconify.mjs"], "sourcesContent": ["/**\n* (c) Iconify\n*\n* For the full copyright and license information, please view the license.txt or license.gpl.txt\n* files at https://github.com/iconify/iconify\n*\n* Licensed under MIT.\n*\n* @license MIT\n* @version 3.1.1\n*/\nconst defaultIconDimensions = Object.freeze(\n  {\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n  }\n);\nconst defaultIconTransformations = Object.freeze({\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n  ...defaultIconDimensions,\n  ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n  ...defaultIconProps,\n  body: \"\",\n  hidden: false\n});\n\nfunction mergeIconTransformations(obj1, obj2) {\n  const result = {};\n  if (!obj1.hFlip !== !obj2.hFlip) {\n    result.hFlip = true;\n  }\n  if (!obj1.vFlip !== !obj2.vFlip) {\n    result.vFlip = true;\n  }\n  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n  if (rotate) {\n    result.rotate = rotate;\n  }\n  return result;\n}\n\nfunction mergeIconData(parent, child) {\n  const result = mergeIconTransformations(parent, child);\n  for (const key in defaultExtendedIconProps) {\n    if (key in defaultIconTransformations) {\n      if (key in parent && !(key in result)) {\n        result[key] = defaultIconTransformations[key];\n      }\n    } else if (key in child) {\n      result[key] = child[key];\n    } else if (key in parent) {\n      result[key] = parent[key];\n    }\n  }\n  return result;\n}\n\nfunction getIconsTree(data, names) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  const resolved = /* @__PURE__ */ Object.create(null);\n  function resolve(name) {\n    if (icons[name]) {\n      return resolved[name] = [];\n    }\n    if (!(name in resolved)) {\n      resolved[name] = null;\n      const parent = aliases[name] && aliases[name].parent;\n      const value = parent && resolve(parent);\n      if (value) {\n        resolved[name] = [parent].concat(value);\n      }\n    }\n    return resolved[name];\n  }\n  (names || Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n  return resolved;\n}\n\nfunction internalGetIconData(data, name, tree) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  let currentProps = {};\n  function parse(name2) {\n    currentProps = mergeIconData(\n      icons[name2] || aliases[name2],\n      currentProps\n    );\n  }\n  parse(name);\n  tree.forEach(parse);\n  return mergeIconData(data, currentProps);\n}\n\nfunction parseIconSet(data, callback) {\n  const names = [];\n  if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n    return names;\n  }\n  if (data.not_found instanceof Array) {\n    data.not_found.forEach((name) => {\n      callback(name, null);\n      names.push(name);\n    });\n  }\n  const tree = getIconsTree(data);\n  for (const name in tree) {\n    const item = tree[name];\n    if (item) {\n      callback(name, internalGetIconData(data, name, item));\n      names.push(name);\n    }\n  }\n  return names;\n}\n\nconst matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      // Allow provider without '@': \"provider:prefix:name\"\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIconName(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIconName = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!((icon.provider === \"\" || icon.provider.match(matchIconName)) && (allowSimpleName && icon.prefix === \"\" || icon.prefix.match(matchIconName)) && icon.name.match(matchIconName));\n};\n\nconst optionalPropertyDefaults = {\n  provider: \"\",\n  aliases: {},\n  not_found: {},\n  ...defaultIconDimensions\n};\nfunction checkOptionalProps(item, defaults) {\n  for (const prop in defaults) {\n    if (prop in item && typeof item[prop] !== typeof defaults[prop]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction quicklyValidateIconSet(obj) {\n  if (typeof obj !== \"object\" || obj === null) {\n    return null;\n  }\n  const data = obj;\n  if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n    return null;\n  }\n  if (!checkOptionalProps(obj, optionalPropertyDefaults)) {\n    return null;\n  }\n  const icons = data.icons;\n  for (const name in icons) {\n    const icon = icons[name];\n    if (!name.match(matchIconName) || typeof icon.body !== \"string\" || !checkOptionalProps(\n      icon,\n      defaultExtendedIconProps\n    )) {\n      return null;\n    }\n  }\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  for (const name in aliases) {\n    const icon = aliases[name];\n    const parent = icon.parent;\n    if (!name.match(matchIconName) || typeof parent !== \"string\" || !icons[parent] && !aliases[parent] || !checkOptionalProps(\n      icon,\n      defaultExtendedIconProps\n    )) {\n      return null;\n    }\n  }\n  return data;\n}\n\nconst dataStorage = /* @__PURE__ */ Object.create(null);\nfunction newStorage(provider, prefix) {\n  return {\n    provider,\n    prefix,\n    icons: /* @__PURE__ */ Object.create(null),\n    missing: /* @__PURE__ */ new Set()\n  };\n}\nfunction getStorage(provider, prefix) {\n  const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));\n  return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));\n}\nfunction addIconSet(storage, data) {\n  if (!quicklyValidateIconSet(data)) {\n    return [];\n  }\n  return parseIconSet(data, (name, icon) => {\n    if (icon) {\n      storage.icons[name] = icon;\n    } else {\n      storage.missing.add(name);\n    }\n  });\n}\nfunction addIconToStorage(storage, name, icon) {\n  try {\n    if (typeof icon.body === \"string\") {\n      storage.icons[name] = { ...icon };\n      return true;\n    }\n  } catch (err) {\n  }\n  return false;\n}\nfunction listIcons(provider, prefix) {\n  let allIcons = [];\n  const providers = typeof provider === \"string\" ? [provider] : Object.keys(dataStorage);\n  providers.forEach((provider2) => {\n    const prefixes = typeof provider2 === \"string\" && typeof prefix === \"string\" ? [prefix] : Object.keys(dataStorage[provider2] || {});\n    prefixes.forEach((prefix2) => {\n      const storage = getStorage(provider2, prefix2);\n      allIcons = allIcons.concat(\n        Object.keys(storage.icons).map(\n          (name) => (provider2 !== \"\" ? \"@\" + provider2 + \":\" : \"\") + prefix2 + \":\" + name\n        )\n      );\n    });\n  });\n  return allIcons;\n}\n\nlet simpleNames = false;\nfunction allowSimpleNames(allow) {\n  if (typeof allow === \"boolean\") {\n    simpleNames = allow;\n  }\n  return simpleNames;\n}\nfunction getIconData(name) {\n  const icon = typeof name === \"string\" ? stringToIcon(name, true, simpleNames) : name;\n  if (icon) {\n    const storage = getStorage(icon.provider, icon.prefix);\n    const iconName = icon.name;\n    return storage.icons[iconName] || (storage.missing.has(iconName) ? null : void 0);\n  }\n}\nfunction addIcon(name, data) {\n  const icon = stringToIcon(name, true, simpleNames);\n  if (!icon) {\n    return false;\n  }\n  const storage = getStorage(icon.provider, icon.prefix);\n  return addIconToStorage(storage, icon.name, data);\n}\nfunction addCollection(data, provider) {\n  if (typeof data !== \"object\") {\n    return false;\n  }\n  if (typeof provider !== \"string\") {\n    provider = data.provider || \"\";\n  }\n  if (simpleNames && !provider && !data.prefix) {\n    let added = false;\n    if (quicklyValidateIconSet(data)) {\n      data.prefix = \"\";\n      parseIconSet(data, (name, icon) => {\n        if (icon && addIcon(name, icon)) {\n          added = true;\n        }\n      });\n    }\n    return added;\n  }\n  const prefix = data.prefix;\n  if (!validateIconName({\n    provider,\n    prefix,\n    name: \"a\"\n  })) {\n    return false;\n  }\n  const storage = getStorage(provider, prefix);\n  return !!addIconSet(storage, data);\n}\nfunction iconExists(name) {\n  return !!getIconData(name);\n}\nfunction getIcon(name) {\n  const result = getIconData(name);\n  return result ? {\n    ...defaultIconProps,\n    ...result\n  } : null;\n}\n\nconst defaultIconSizeCustomisations = Object.freeze({\n  width: null,\n  height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n  // Dimensions\n  ...defaultIconSizeCustomisations,\n  // Transformations\n  ...defaultIconTransformations\n});\n\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision || 100;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\nconst isUnsetKeyword = (value) => value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n  const fullIcon = {\n    ...defaultIconProps,\n    ...icon\n  };\n  const fullCustomisations = {\n    ...defaultIconCustomisations,\n    ...customisations\n  };\n  const box = {\n    left: fullIcon.left,\n    top: fullIcon.top,\n    width: fullIcon.width,\n    height: fullIcon.height\n  };\n  let body = fullIcon.body;\n  [fullIcon, fullCustomisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\n          \"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\"\n        );\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\n        \"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\"\n      );\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\n          \"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n      case 2:\n        transformations.unshift(\n          \"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\"\n        );\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\n          \"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== box.top) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = '<g transform=\"' + transformations.join(\" \") + '\">' + body + \"</g>\";\n    }\n  });\n  const customisationsWidth = fullCustomisations.width;\n  const customisationsHeight = fullCustomisations.height;\n  const boxWidth = box.width;\n  const boxHeight = box.height;\n  let width;\n  let height;\n  if (customisationsWidth === null) {\n    height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    width = calculateSize(height, boxWidth / boxHeight);\n  } else {\n    width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n  }\n  const attributes = {};\n  const setAttr = (prop, value) => {\n    if (!isUnsetKeyword(value)) {\n      attributes[prop] = value.toString();\n    }\n  };\n  setAttr(\"width\", width);\n  setAttr(\"height\", height);\n  attributes.viewBox = box.left.toString() + \" \" + box.top.toString() + \" \" + boxWidth.toString() + \" \" + boxHeight.toString();\n  return {\n    attributes,\n    body\n  };\n}\n\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(\n      // Allowed characters before id: [#;\"]\n      // Allowed characters after id: [)\"], .[a-z]\n      new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"),\n      \"$1\" + newID + suffix + \"$3\"\n    );\n  });\n  body = body.replace(new RegExp(suffix, \"g\"), \"\");\n  return body;\n}\n\nconst browserStorageConfig = {\n  local: true,\n  session: true\n};\nconst browserStorageEmptyItems = {\n  local: /* @__PURE__ */ new Set(),\n  session: /* @__PURE__ */ new Set()\n};\nlet browserStorageStatus = false;\nfunction setBrowserStorageStatus(status) {\n  browserStorageStatus = status;\n}\n\nconst browserCacheVersion = \"iconify2\";\nconst browserCachePrefix = \"iconify\";\nconst browserCacheCountKey = browserCachePrefix + \"-count\";\nconst browserCacheVersionKey = browserCachePrefix + \"-version\";\nconst browserStorageHour = 36e5;\nconst browserStorageCacheExpiration = 168;\n\nfunction getStoredItem(func, key) {\n  try {\n    return func.getItem(key);\n  } catch (err) {\n  }\n}\nfunction setStoredItem(func, key, value) {\n  try {\n    func.setItem(key, value);\n    return true;\n  } catch (err) {\n  }\n}\nfunction removeStoredItem(func, key) {\n  try {\n    func.removeItem(key);\n  } catch (err) {\n  }\n}\n\nfunction setBrowserStorageItemsCount(storage, value) {\n  return setStoredItem(storage, browserCacheCountKey, value.toString());\n}\nfunction getBrowserStorageItemsCount(storage) {\n  return parseInt(getStoredItem(storage, browserCacheCountKey)) || 0;\n}\n\nlet _window = typeof window === \"undefined\" ? {} : window;\nfunction getBrowserStorage(key) {\n  const attr = key + \"Storage\";\n  try {\n    if (_window && _window[attr] && typeof _window[attr].length === \"number\") {\n      return _window[attr];\n    }\n  } catch (err) {\n  }\n  browserStorageConfig[key] = false;\n}\n\nfunction iterateBrowserStorage(key, callback) {\n  const func = getBrowserStorage(key);\n  if (!func) {\n    return;\n  }\n  const version = getStoredItem(func, browserCacheVersionKey);\n  if (version !== browserCacheVersion) {\n    if (version) {\n      const total2 = getBrowserStorageItemsCount(func);\n      for (let i = 0; i < total2; i++) {\n        removeStoredItem(func, browserCachePrefix + i.toString());\n      }\n    }\n    setStoredItem(func, browserCacheVersionKey, browserCacheVersion);\n    setBrowserStorageItemsCount(func, 0);\n    return;\n  }\n  const minTime = Math.floor(Date.now() / browserStorageHour) - browserStorageCacheExpiration;\n  const parseItem = (index) => {\n    const name = browserCachePrefix + index.toString();\n    const item = getStoredItem(func, name);\n    if (typeof item !== \"string\") {\n      return;\n    }\n    try {\n      const data = JSON.parse(item);\n      if (typeof data === \"object\" && typeof data.cached === \"number\" && data.cached > minTime && typeof data.provider === \"string\" && typeof data.data === \"object\" && typeof data.data.prefix === \"string\" && // Valid item: run callback\n      callback(data, index)) {\n        return true;\n      }\n    } catch (err) {\n    }\n    removeStoredItem(func, name);\n  };\n  let total = getBrowserStorageItemsCount(func);\n  for (let i = total - 1; i >= 0; i--) {\n    if (!parseItem(i)) {\n      if (i === total - 1) {\n        total--;\n        setBrowserStorageItemsCount(func, total);\n      } else {\n        browserStorageEmptyItems[key].add(i);\n      }\n    }\n  }\n}\n\nfunction initBrowserStorage() {\n  if (browserStorageStatus) {\n    return;\n  }\n  setBrowserStorageStatus(true);\n  for (const key in browserStorageConfig) {\n    iterateBrowserStorage(key, (item) => {\n      const iconSet = item.data;\n      const provider = item.provider;\n      const prefix = iconSet.prefix;\n      const storage = getStorage(\n        provider,\n        prefix\n      );\n      if (!addIconSet(storage, iconSet).length) {\n        return false;\n      }\n      const lastModified = iconSet.lastModified || -1;\n      storage.lastModifiedCached = storage.lastModifiedCached ? Math.min(storage.lastModifiedCached, lastModified) : lastModified;\n      return true;\n    });\n  }\n}\n\nfunction toggleBrowserCache(storage, value) {\n  switch (storage) {\n    case \"local\":\n    case \"session\":\n      browserStorageConfig[storage] = value;\n      break;\n    case \"all\":\n      for (const key in browserStorageConfig) {\n        browserStorageConfig[key] = value;\n      }\n      break;\n  }\n}\n\nconst storage = /* @__PURE__ */ Object.create(null);\nfunction setAPIModule(provider, item) {\n  storage[provider] = item;\n}\nfunction getAPIModule(provider) {\n  return storage[provider] || storage[\"\"];\n}\n\nfunction createAPIConfig(source) {\n  let resources;\n  if (typeof source.resources === \"string\") {\n    resources = [source.resources];\n  } else {\n    resources = source.resources;\n    if (!(resources instanceof Array) || !resources.length) {\n      return null;\n    }\n  }\n  const result = {\n    // API hosts\n    resources,\n    // Root path\n    path: source.path || \"/\",\n    // URL length limit\n    maxURL: source.maxURL || 500,\n    // Timeout before next host is used.\n    rotate: source.rotate || 750,\n    // Timeout before failing query.\n    timeout: source.timeout || 5e3,\n    // Randomise default API end point.\n    random: source.random === true,\n    // Start index\n    index: source.index || 0,\n    // Receive data after time out (used if time out kicks in first, then API module sends data anyway).\n    dataAfterTimeout: source.dataAfterTimeout !== false\n  };\n  return result;\n}\nconst configStorage = /* @__PURE__ */ Object.create(null);\nconst fallBackAPISources = [\n  \"https://api.simplesvg.com\",\n  \"https://api.unisvg.com\"\n];\nconst fallBackAPI = [];\nwhile (fallBackAPISources.length > 0) {\n  if (fallBackAPISources.length === 1) {\n    fallBackAPI.push(fallBackAPISources.shift());\n  } else {\n    if (Math.random() > 0.5) {\n      fallBackAPI.push(fallBackAPISources.shift());\n    } else {\n      fallBackAPI.push(fallBackAPISources.pop());\n    }\n  }\n}\nconfigStorage[\"\"] = createAPIConfig({\n  resources: [\"https://api.iconify.design\"].concat(fallBackAPI)\n});\nfunction addAPIProvider(provider, customConfig) {\n  const config = createAPIConfig(customConfig);\n  if (config === null) {\n    return false;\n  }\n  configStorage[provider] = config;\n  return true;\n}\nfunction getAPIConfig(provider) {\n  return configStorage[provider];\n}\nfunction listAPIProviders() {\n  return Object.keys(configStorage);\n}\n\nconst detectFetch = () => {\n  let callback;\n  try {\n    callback = fetch;\n    if (typeof callback === \"function\") {\n      return callback;\n    }\n  } catch (err) {\n  }\n};\nlet fetchModule = detectFetch();\nfunction setFetch(fetch2) {\n  fetchModule = fetch2;\n}\nfunction getFetch() {\n  return fetchModule;\n}\nfunction calculateMaxLength(provider, prefix) {\n  const config = getAPIConfig(provider);\n  if (!config) {\n    return 0;\n  }\n  let result;\n  if (!config.maxURL) {\n    result = 0;\n  } else {\n    let maxHostLength = 0;\n    config.resources.forEach((item) => {\n      const host = item;\n      maxHostLength = Math.max(maxHostLength, host.length);\n    });\n    const url = prefix + \".json?icons=\";\n    result = config.maxURL - maxHostLength - config.path.length - url.length;\n  }\n  return result;\n}\nfunction shouldAbort(status) {\n  return status === 404;\n}\nconst prepare = (provider, prefix, icons) => {\n  const results = [];\n  const maxLength = calculateMaxLength(provider, prefix);\n  const type = \"icons\";\n  let item = {\n    type,\n    provider,\n    prefix,\n    icons: []\n  };\n  let length = 0;\n  icons.forEach((name, index) => {\n    length += name.length + 1;\n    if (length >= maxLength && index > 0) {\n      results.push(item);\n      item = {\n        type,\n        provider,\n        prefix,\n        icons: []\n      };\n      length = name.length;\n    }\n    item.icons.push(name);\n  });\n  results.push(item);\n  return results;\n};\nfunction getPath(provider) {\n  if (typeof provider === \"string\") {\n    const config = getAPIConfig(provider);\n    if (config) {\n      return config.path;\n    }\n  }\n  return \"/\";\n}\nconst send = (host, params, callback) => {\n  if (!fetchModule) {\n    callback(\"abort\", 424);\n    return;\n  }\n  let path = getPath(params.provider);\n  switch (params.type) {\n    case \"icons\": {\n      const prefix = params.prefix;\n      const icons = params.icons;\n      const iconsList = icons.join(\",\");\n      const urlParams = new URLSearchParams({\n        icons: iconsList\n      });\n      path += prefix + \".json?\" + urlParams.toString();\n      break;\n    }\n    case \"custom\": {\n      const uri = params.uri;\n      path += uri.slice(0, 1) === \"/\" ? uri.slice(1) : uri;\n      break;\n    }\n    default:\n      callback(\"abort\", 400);\n      return;\n  }\n  let defaultError = 503;\n  fetchModule(host + path).then((response) => {\n    const status = response.status;\n    if (status !== 200) {\n      setTimeout(() => {\n        callback(shouldAbort(status) ? \"abort\" : \"next\", status);\n      });\n      return;\n    }\n    defaultError = 501;\n    return response.json();\n  }).then((data) => {\n    if (typeof data !== \"object\" || data === null) {\n      setTimeout(() => {\n        if (data === 404) {\n          callback(\"abort\", data);\n        } else {\n          callback(\"next\", defaultError);\n        }\n      });\n      return;\n    }\n    setTimeout(() => {\n      callback(\"success\", data);\n    });\n  }).catch(() => {\n    callback(\"next\", defaultError);\n  });\n};\nconst fetchAPIModule = {\n  prepare,\n  send\n};\n\nfunction sortIcons(icons) {\n  const result = {\n    loaded: [],\n    missing: [],\n    pending: []\n  };\n  const storage = /* @__PURE__ */ Object.create(null);\n  icons.sort((a, b) => {\n    if (a.provider !== b.provider) {\n      return a.provider.localeCompare(b.provider);\n    }\n    if (a.prefix !== b.prefix) {\n      return a.prefix.localeCompare(b.prefix);\n    }\n    return a.name.localeCompare(b.name);\n  });\n  let lastIcon = {\n    provider: \"\",\n    prefix: \"\",\n    name: \"\"\n  };\n  icons.forEach((icon) => {\n    if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {\n      return;\n    }\n    lastIcon = icon;\n    const provider = icon.provider;\n    const prefix = icon.prefix;\n    const name = icon.name;\n    const providerStorage = storage[provider] || (storage[provider] = /* @__PURE__ */ Object.create(null));\n    const localStorage = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));\n    let list;\n    if (name in localStorage.icons) {\n      list = result.loaded;\n    } else if (prefix === \"\" || localStorage.missing.has(name)) {\n      list = result.missing;\n    } else {\n      list = result.pending;\n    }\n    const item = {\n      provider,\n      prefix,\n      name\n    };\n    list.push(item);\n  });\n  return result;\n}\n\nfunction removeCallback(storages, id) {\n  storages.forEach((storage) => {\n    const items = storage.loaderCallbacks;\n    if (items) {\n      storage.loaderCallbacks = items.filter((row) => row.id !== id);\n    }\n  });\n}\nfunction updateCallbacks(storage) {\n  if (!storage.pendingCallbacksFlag) {\n    storage.pendingCallbacksFlag = true;\n    setTimeout(() => {\n      storage.pendingCallbacksFlag = false;\n      const items = storage.loaderCallbacks ? storage.loaderCallbacks.slice(0) : [];\n      if (!items.length) {\n        return;\n      }\n      let hasPending = false;\n      const provider = storage.provider;\n      const prefix = storage.prefix;\n      items.forEach((item) => {\n        const icons = item.icons;\n        const oldLength = icons.pending.length;\n        icons.pending = icons.pending.filter((icon) => {\n          if (icon.prefix !== prefix) {\n            return true;\n          }\n          const name = icon.name;\n          if (storage.icons[name]) {\n            icons.loaded.push({\n              provider,\n              prefix,\n              name\n            });\n          } else if (storage.missing.has(name)) {\n            icons.missing.push({\n              provider,\n              prefix,\n              name\n            });\n          } else {\n            hasPending = true;\n            return true;\n          }\n          return false;\n        });\n        if (icons.pending.length !== oldLength) {\n          if (!hasPending) {\n            removeCallback([storage], item.id);\n          }\n          item.callback(\n            icons.loaded.slice(0),\n            icons.missing.slice(0),\n            icons.pending.slice(0),\n            item.abort\n          );\n        }\n      });\n    });\n  }\n}\nlet idCounter = 0;\nfunction storeCallback(callback, icons, pendingSources) {\n  const id = idCounter++;\n  const abort = removeCallback.bind(null, pendingSources, id);\n  if (!icons.pending.length) {\n    return abort;\n  }\n  const item = {\n    id,\n    icons,\n    callback,\n    abort\n  };\n  pendingSources.forEach((storage) => {\n    (storage.loaderCallbacks || (storage.loaderCallbacks = [])).push(item);\n  });\n  return abort;\n}\n\nfunction listToIcons(list, validate = true, simpleNames = false) {\n  const result = [];\n  list.forEach((item) => {\n    const icon = typeof item === \"string\" ? stringToIcon(item, validate, simpleNames) : item;\n    if (icon) {\n      result.push(icon);\n    }\n  });\n  return result;\n}\n\n// src/config.ts\nvar defaultConfig = {\n  resources: [],\n  index: 0,\n  timeout: 2e3,\n  rotate: 750,\n  random: false,\n  dataAfterTimeout: false\n};\n\n// src/query.ts\nfunction sendQuery(config, payload, query, done) {\n  const resourcesCount = config.resources.length;\n  const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;\n  let resources;\n  if (config.random) {\n    let list = config.resources.slice(0);\n    resources = [];\n    while (list.length > 1) {\n      const nextIndex = Math.floor(Math.random() * list.length);\n      resources.push(list[nextIndex]);\n      list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));\n    }\n    resources = resources.concat(list);\n  } else {\n    resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));\n  }\n  const startTime = Date.now();\n  let status = \"pending\";\n  let queriesSent = 0;\n  let lastError;\n  let timer = null;\n  let queue = [];\n  let doneCallbacks = [];\n  if (typeof done === \"function\") {\n    doneCallbacks.push(done);\n  }\n  function resetTimer() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function abort() {\n    if (status === \"pending\") {\n      status = \"aborted\";\n    }\n    resetTimer();\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function subscribe(callback, overwrite) {\n    if (overwrite) {\n      doneCallbacks = [];\n    }\n    if (typeof callback === \"function\") {\n      doneCallbacks.push(callback);\n    }\n  }\n  function getQueryStatus() {\n    return {\n      startTime,\n      payload,\n      status,\n      queriesSent,\n      queriesPending: queue.length,\n      subscribe,\n      abort\n    };\n  }\n  function failQuery() {\n    status = \"failed\";\n    doneCallbacks.forEach((callback) => {\n      callback(void 0, lastError);\n    });\n  }\n  function clearQueue() {\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function moduleResponse(item, response, data) {\n    const isError = response !== \"success\";\n    queue = queue.filter((queued) => queued !== item);\n    switch (status) {\n      case \"pending\":\n        break;\n      case \"failed\":\n        if (isError || !config.dataAfterTimeout) {\n          return;\n        }\n        break;\n      default:\n        return;\n    }\n    if (response === \"abort\") {\n      lastError = data;\n      failQuery();\n      return;\n    }\n    if (isError) {\n      lastError = data;\n      if (!queue.length) {\n        if (!resources.length) {\n          failQuery();\n        } else {\n          execNext();\n        }\n      }\n      return;\n    }\n    resetTimer();\n    clearQueue();\n    if (!config.random) {\n      const index = config.resources.indexOf(item.resource);\n      if (index !== -1 && index !== config.index) {\n        config.index = index;\n      }\n    }\n    status = \"completed\";\n    doneCallbacks.forEach((callback) => {\n      callback(data);\n    });\n  }\n  function execNext() {\n    if (status !== \"pending\") {\n      return;\n    }\n    resetTimer();\n    const resource = resources.shift();\n    if (resource === void 0) {\n      if (queue.length) {\n        timer = setTimeout(() => {\n          resetTimer();\n          if (status === \"pending\") {\n            clearQueue();\n            failQuery();\n          }\n        }, config.timeout);\n        return;\n      }\n      failQuery();\n      return;\n    }\n    const item = {\n      status: \"pending\",\n      resource,\n      callback: (status2, data) => {\n        moduleResponse(item, status2, data);\n      }\n    };\n    queue.push(item);\n    queriesSent++;\n    timer = setTimeout(execNext, config.rotate);\n    query(resource, payload, item.callback);\n  }\n  setTimeout(execNext);\n  return getQueryStatus;\n}\n\n// src/index.ts\nfunction initRedundancy(cfg) {\n  const config = {\n    ...defaultConfig,\n    ...cfg\n  };\n  let queries = [];\n  function cleanup() {\n    queries = queries.filter((item) => item().status === \"pending\");\n  }\n  function query(payload, queryCallback, doneCallback) {\n    const query2 = sendQuery(\n      config,\n      payload,\n      queryCallback,\n      (data, error) => {\n        cleanup();\n        if (doneCallback) {\n          doneCallback(data, error);\n        }\n      }\n    );\n    queries.push(query2);\n    return query2;\n  }\n  function find(callback) {\n    return queries.find((value) => {\n      return callback(value);\n    }) || null;\n  }\n  const instance = {\n    query,\n    find,\n    setIndex: (index) => {\n      config.index = index;\n    },\n    getIndex: () => config.index,\n    cleanup\n  };\n  return instance;\n}\n\nfunction emptyCallback$1() {\n}\nconst redundancyCache = /* @__PURE__ */ Object.create(null);\nfunction getRedundancyCache(provider) {\n  if (!redundancyCache[provider]) {\n    const config = getAPIConfig(provider);\n    if (!config) {\n      return;\n    }\n    const redundancy = initRedundancy(config);\n    const cachedReundancy = {\n      config,\n      redundancy\n    };\n    redundancyCache[provider] = cachedReundancy;\n  }\n  return redundancyCache[provider];\n}\nfunction sendAPIQuery(target, query, callback) {\n  let redundancy;\n  let send;\n  if (typeof target === \"string\") {\n    const api = getAPIModule(target);\n    if (!api) {\n      callback(void 0, 424);\n      return emptyCallback$1;\n    }\n    send = api.send;\n    const cached = getRedundancyCache(target);\n    if (cached) {\n      redundancy = cached.redundancy;\n    }\n  } else {\n    const config = createAPIConfig(target);\n    if (config) {\n      redundancy = initRedundancy(config);\n      const moduleKey = target.resources ? target.resources[0] : \"\";\n      const api = getAPIModule(moduleKey);\n      if (api) {\n        send = api.send;\n      }\n    }\n  }\n  if (!redundancy || !send) {\n    callback(void 0, 424);\n    return emptyCallback$1;\n  }\n  return redundancy.query(query, send, callback)().abort;\n}\n\nfunction updateLastModified(storage, lastModified) {\n  const lastValue = storage.lastModifiedCached;\n  if (\n    // Matches or newer\n    lastValue && lastValue >= lastModified\n  ) {\n    return lastValue === lastModified;\n  }\n  storage.lastModifiedCached = lastModified;\n  if (lastValue) {\n    for (const key in browserStorageConfig) {\n      iterateBrowserStorage(key, (item) => {\n        const iconSet = item.data;\n        return item.provider !== storage.provider || iconSet.prefix !== storage.prefix || iconSet.lastModified === lastModified;\n      });\n    }\n  }\n  return true;\n}\nfunction storeInBrowserStorage(storage, data) {\n  if (!browserStorageStatus) {\n    initBrowserStorage();\n  }\n  function store(key) {\n    let func;\n    if (!browserStorageConfig[key] || !(func = getBrowserStorage(key))) {\n      return;\n    }\n    const set = browserStorageEmptyItems[key];\n    let index;\n    if (set.size) {\n      set.delete(index = Array.from(set).shift());\n    } else {\n      index = getBrowserStorageItemsCount(func);\n      if (!setBrowserStorageItemsCount(func, index + 1)) {\n        return;\n      }\n    }\n    const item = {\n      cached: Math.floor(Date.now() / browserStorageHour),\n      provider: storage.provider,\n      data\n    };\n    return setStoredItem(\n      func,\n      browserCachePrefix + index.toString(),\n      JSON.stringify(item)\n    );\n  }\n  if (data.lastModified && !updateLastModified(storage, data.lastModified)) {\n    return;\n  }\n  if (!Object.keys(data.icons).length) {\n    return;\n  }\n  if (data.not_found) {\n    data = Object.assign({}, data);\n    delete data.not_found;\n  }\n  if (!store(\"local\")) {\n    store(\"session\");\n  }\n}\n\nfunction emptyCallback() {\n}\nfunction loadedNewIcons(storage) {\n  if (!storage.iconsLoaderFlag) {\n    storage.iconsLoaderFlag = true;\n    setTimeout(() => {\n      storage.iconsLoaderFlag = false;\n      updateCallbacks(storage);\n    });\n  }\n}\nfunction loadNewIcons(storage, icons) {\n  if (!storage.iconsToLoad) {\n    storage.iconsToLoad = icons;\n  } else {\n    storage.iconsToLoad = storage.iconsToLoad.concat(icons).sort();\n  }\n  if (!storage.iconsQueueFlag) {\n    storage.iconsQueueFlag = true;\n    setTimeout(() => {\n      storage.iconsQueueFlag = false;\n      const { provider, prefix } = storage;\n      const icons2 = storage.iconsToLoad;\n      delete storage.iconsToLoad;\n      let api;\n      if (!icons2 || !(api = getAPIModule(provider))) {\n        return;\n      }\n      const params = api.prepare(provider, prefix, icons2);\n      params.forEach((item) => {\n        sendAPIQuery(provider, item, (data) => {\n          if (typeof data !== \"object\") {\n            item.icons.forEach((name) => {\n              storage.missing.add(name);\n            });\n          } else {\n            try {\n              const parsed = addIconSet(\n                storage,\n                data\n              );\n              if (!parsed.length) {\n                return;\n              }\n              const pending = storage.pendingIcons;\n              if (pending) {\n                parsed.forEach((name) => {\n                  pending.delete(name);\n                });\n              }\n              storeInBrowserStorage(storage, data);\n            } catch (err) {\n              console.error(err);\n            }\n          }\n          loadedNewIcons(storage);\n        });\n      });\n    });\n  }\n}\nconst isPending = (icon) => {\n  const storage = getStorage(\n    icon.provider,\n    icon.prefix\n  );\n  const pending = storage.pendingIcons;\n  return !!(pending && pending.has(icon.name));\n};\nconst loadIcons = (icons, callback) => {\n  const cleanedIcons = listToIcons(icons, true, allowSimpleNames());\n  const sortedIcons = sortIcons(cleanedIcons);\n  if (!sortedIcons.pending.length) {\n    let callCallback = true;\n    if (callback) {\n      setTimeout(() => {\n        if (callCallback) {\n          callback(\n            sortedIcons.loaded,\n            sortedIcons.missing,\n            sortedIcons.pending,\n            emptyCallback\n          );\n        }\n      });\n    }\n    return () => {\n      callCallback = false;\n    };\n  }\n  const newIcons = /* @__PURE__ */ Object.create(null);\n  const sources = [];\n  let lastProvider, lastPrefix;\n  sortedIcons.pending.forEach((icon) => {\n    const { provider, prefix } = icon;\n    if (prefix === lastPrefix && provider === lastProvider) {\n      return;\n    }\n    lastProvider = provider;\n    lastPrefix = prefix;\n    sources.push(getStorage(provider, prefix));\n    const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));\n    if (!providerNewIcons[prefix]) {\n      providerNewIcons[prefix] = [];\n    }\n  });\n  sortedIcons.pending.forEach((icon) => {\n    const { provider, prefix, name } = icon;\n    const storage = getStorage(provider, prefix);\n    const pendingQueue = storage.pendingIcons || (storage.pendingIcons = /* @__PURE__ */ new Set());\n    if (!pendingQueue.has(name)) {\n      pendingQueue.add(name);\n      newIcons[provider][prefix].push(name);\n    }\n  });\n  sources.forEach((storage) => {\n    const { provider, prefix } = storage;\n    if (newIcons[provider][prefix].length) {\n      loadNewIcons(storage, newIcons[provider][prefix]);\n    }\n  });\n  return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;\n};\nconst loadIcon = (icon) => {\n  return new Promise((fulfill, reject) => {\n    const iconObj = typeof icon === \"string\" ? stringToIcon(icon, true) : icon;\n    if (!iconObj) {\n      reject(icon);\n      return;\n    }\n    loadIcons([iconObj || icon], (loaded) => {\n      if (loaded.length && iconObj) {\n        const data = getIconData(iconObj);\n        if (data) {\n          fulfill({\n            ...defaultIconProps,\n            ...data\n          });\n          return;\n        }\n      }\n      reject(icon);\n    });\n  });\n};\n\nfunction mergeCustomisations(defaults, item) {\n  const result = {\n    ...defaults\n  };\n  for (const key in item) {\n    const value = item[key];\n    const valueType = typeof value;\n    if (key in defaultIconSizeCustomisations) {\n      if (value === null || value && (valueType === \"string\" || valueType === \"number\")) {\n        result[key] = value;\n      }\n    } else if (valueType === typeof result[key]) {\n      result[key] = key === \"rotate\" ? value % 4 : value;\n    }\n  }\n  return result;\n}\n\nconst defaultExtendedIconCustomisations = {\n    ...defaultIconCustomisations,\n    inline: false,\n};\n/**\n * Class names\n */\nconst blockClass = 'iconify';\nconst inlineClass = 'iconify-inline';\n/**\n * Names of properties to add to nodes\n */\nconst elementDataProperty = ('iconifyData' + Date.now());\n\n/**\n * List of root nodes\n */\nlet nodes = [];\n/**\n * Find node\n */\nfunction findRootNode(node) {\n    for (let i = 0; i < nodes.length; i++) {\n        const item = nodes[i];\n        const root = typeof item.node === 'function' ? item.node() : item.node;\n        if (root === node) {\n            return item;\n        }\n    }\n}\n/**\n * Add extra root node\n */\nfunction addRootNode(root, autoRemove = false) {\n    let node = findRootNode(root);\n    if (node) {\n        // Node already exist: switch type if needed\n        if (node.temporary) {\n            node.temporary = autoRemove;\n        }\n        return node;\n    }\n    // Create item, add it to list\n    node = {\n        node: root,\n        temporary: autoRemove,\n    };\n    nodes.push(node);\n    return node;\n}\n/**\n * Add document.body node\n */\nfunction addBodyNode() {\n    if (document.documentElement) {\n        return addRootNode(document.documentElement);\n    }\n    nodes.push({\n        node: () => {\n            return document.documentElement;\n        },\n    });\n}\n/**\n * Remove root node\n */\nfunction removeRootNode(root) {\n    nodes = nodes.filter((node) => root !== node &&\n        root !== (typeof node.node === 'function' ? node.node() : node.node));\n}\n/**\n * Get list of root nodes\n */\nfunction listRootNodes() {\n    return nodes;\n}\n\n/**\n * Execute function when DOM is ready\n */\nfunction onReady(callback) {\n    const doc = document;\n    if (doc.readyState && doc.readyState !== 'loading') {\n        callback();\n    }\n    else {\n        doc.addEventListener('DOMContentLoaded', callback);\n    }\n}\n\n/**\n * Callback\n */\nlet callback = null;\n/**\n * Parameters for mutation observer\n */\nconst observerParams = {\n    childList: true,\n    subtree: true,\n    attributes: true,\n};\n/**\n * Queue DOM scan\n */\nfunction queueScan(node) {\n    if (!node.observer) {\n        return;\n    }\n    const observer = node.observer;\n    if (!observer.pendingScan) {\n        observer.pendingScan = setTimeout(() => {\n            delete observer.pendingScan;\n            if (callback) {\n                callback(node);\n            }\n        });\n    }\n}\n/**\n * Check mutations for added nodes\n */\nfunction checkMutations(node, mutations) {\n    if (!node.observer) {\n        return;\n    }\n    const observer = node.observer;\n    if (!observer.pendingScan) {\n        for (let i = 0; i < mutations.length; i++) {\n            const item = mutations[i];\n            if (\n            // Check for added nodes\n            (item.addedNodes && item.addedNodes.length > 0) ||\n                // Check for icon or placeholder with modified attributes\n                (item.type === 'attributes' &&\n                    item.target[elementDataProperty] !==\n                        void 0)) {\n                if (!observer.paused) {\n                    queueScan(node);\n                }\n                return;\n            }\n        }\n    }\n}\n/**\n * Start/resume observer\n */\nfunction continueObserving(node, root) {\n    node.observer.instance.observe(root, observerParams);\n}\n/**\n * Start mutation observer\n */\nfunction startObserver(node) {\n    let observer = node.observer;\n    if (observer && observer.instance) {\n        // Already started\n        return;\n    }\n    const root = typeof node.node === 'function' ? node.node() : node.node;\n    if (!root || !window) {\n        // document.body is not available yet or window is missing\n        return;\n    }\n    if (!observer) {\n        observer = {\n            paused: 0,\n        };\n        node.observer = observer;\n    }\n    // Create new instance, observe\n    observer.instance = new window.MutationObserver(checkMutations.bind(null, node));\n    continueObserving(node, root);\n    // Scan immediately\n    if (!observer.paused) {\n        queueScan(node);\n    }\n}\n/**\n * Start all observers\n */\nfunction startObservers() {\n    listRootNodes().forEach(startObserver);\n}\n/**\n * Stop observer\n */\nfunction stopObserver(node) {\n    if (!node.observer) {\n        return;\n    }\n    const observer = node.observer;\n    // Stop scan\n    if (observer.pendingScan) {\n        clearTimeout(observer.pendingScan);\n        delete observer.pendingScan;\n    }\n    // Disconnect observer\n    if (observer.instance) {\n        observer.instance.disconnect();\n        delete observer.instance;\n    }\n}\n/**\n * Start observer when DOM is ready\n */\nfunction initObserver(cb) {\n    const isRestart = callback !== null;\n    if (callback !== cb) {\n        // Change callback and stop all pending observers\n        callback = cb;\n        if (isRestart) {\n            listRootNodes().forEach(stopObserver);\n        }\n    }\n    if (isRestart) {\n        // Restart instances\n        startObservers();\n        return;\n    }\n    // Start observers when document is ready\n    onReady(startObservers);\n}\n/**\n * Pause observing node\n */\nfunction pauseObservingNode(node) {\n    (node ? [node] : listRootNodes()).forEach((node) => {\n        if (!node.observer) {\n            node.observer = {\n                paused: 1,\n            };\n            return;\n        }\n        const observer = node.observer;\n        observer.paused++;\n        if (observer.paused > 1 || !observer.instance) {\n            return;\n        }\n        // Disconnect observer\n        const instance = observer.instance;\n        // checkMutations(node, instance.takeRecords());\n        instance.disconnect();\n    });\n}\n/**\n * Pause observer\n */\nfunction pauseObserver(root) {\n    if (root) {\n        const node = findRootNode(root);\n        if (node) {\n            pauseObservingNode(node);\n        }\n    }\n    else {\n        pauseObservingNode();\n    }\n}\n/**\n * Resume observer\n */\nfunction resumeObservingNode(observer) {\n    (observer ? [observer] : listRootNodes()).forEach((node) => {\n        if (!node.observer) {\n            // Start observer\n            startObserver(node);\n            return;\n        }\n        const observer = node.observer;\n        if (observer.paused) {\n            observer.paused--;\n            if (!observer.paused) {\n                // Start / resume\n                const root = typeof node.node === 'function' ? node.node() : node.node;\n                if (!root) {\n                    return;\n                }\n                else if (observer.instance) {\n                    continueObserving(node, root);\n                }\n                else {\n                    startObserver(node);\n                }\n            }\n        }\n    });\n}\n/**\n * Resume observer\n */\nfunction resumeObserver(root) {\n    if (root) {\n        const node = findRootNode(root);\n        if (node) {\n            resumeObservingNode(node);\n        }\n    }\n    else {\n        resumeObservingNode();\n    }\n}\n/**\n * Observe node\n */\nfunction observe(root, autoRemove = false) {\n    const node = addRootNode(root, autoRemove);\n    startObserver(node);\n    return node;\n}\n/**\n * Remove observed node\n */\nfunction stopObserving(root) {\n    const node = findRootNode(root);\n    if (node) {\n        stopObserver(node);\n        removeRootNode(root);\n    }\n}\n\n/**\n * Compare props\n */\nfunction propsChanged(props1, props2) {\n    if (props1.name !== props2.name || props1.mode !== props2.mode) {\n        return true;\n    }\n    const customisations1 = props1.customisations;\n    const customisations2 = props2.customisations;\n    for (const key in defaultExtendedIconCustomisations) {\n        if (customisations1[key] !== customisations2[key]) {\n            return true;\n        }\n    }\n    return false;\n}\n\nfunction rotateFromString(value, defaultValue = 0) {\n  const units = value.replace(/^-?[0-9.]*/, \"\");\n  function cleanup(value2) {\n    while (value2 < 0) {\n      value2 += 4;\n    }\n    return value2 % 4;\n  }\n  if (units === \"\") {\n    const num = parseInt(value);\n    return isNaN(num) ? 0 : cleanup(num);\n  } else if (units !== value) {\n    let split = 0;\n    switch (units) {\n      case \"%\":\n        split = 25;\n        break;\n      case \"deg\":\n        split = 90;\n    }\n    if (split) {\n      let num = parseFloat(value.slice(0, value.length - units.length));\n      if (isNaN(num)) {\n        return 0;\n      }\n      num = num / split;\n      return num % 1 === 0 ? cleanup(num) : 0;\n    }\n  }\n  return defaultValue;\n}\n\nconst separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n  flip.split(separator).forEach((str) => {\n    const value = str.trim();\n    switch (value) {\n      case \"horizontal\":\n        custom.hFlip = true;\n        break;\n      case \"vertical\":\n        custom.vFlip = true;\n        break;\n    }\n  });\n}\n\n/**\n * Size attributes\n */\nconst sizeAttributes = ['width', 'height'];\n/**\n * Boolean attributes\n */\nconst booleanAttributes = [\n    'inline',\n    'hFlip',\n    'vFlip',\n];\n/**\n * Get attribute value\n */\nfunction getBooleanAttribute(value, key) {\n    if (value === key || value === 'true') {\n        return true;\n    }\n    if (value === '' || value === 'false') {\n        return false;\n    }\n    return null;\n}\n/**\n * Get element properties from HTML element\n */\nfunction getElementProps(element) {\n    // Get icon name\n    const name = element.getAttribute('data-icon');\n    const icon = typeof name === 'string' && stringToIcon(name, true);\n    if (!icon) {\n        return null;\n    }\n    // Get defaults and inline\n    const customisations = {\n        ...defaultExtendedIconCustomisations,\n        inline: element.classList && element.classList.contains(inlineClass),\n    };\n    // Get dimensions\n    sizeAttributes.forEach((attr) => {\n        const value = element.getAttribute('data-' + attr);\n        if (value) {\n            customisations[attr] = value;\n        }\n    });\n    // Get rotation\n    const rotation = element.getAttribute('data-rotate');\n    if (typeof rotation === 'string') {\n        customisations.rotate = rotateFromString(rotation);\n    }\n    // Get flip shorthand\n    const flip = element.getAttribute('data-flip');\n    if (typeof flip === 'string') {\n        flipFromString(customisations, flip);\n    }\n    // Boolean attributes\n    booleanAttributes.forEach((attr) => {\n        const key = 'data-' + attr;\n        const value = getBooleanAttribute(element.getAttribute(key), key);\n        if (typeof value === 'boolean') {\n            customisations[attr] = value;\n        }\n    });\n    // Get render mode. Not checking actual value because incorrect values are treated as inline\n    const mode = element.getAttribute('data-mode');\n    return {\n        name,\n        icon,\n        customisations,\n        mode,\n    };\n}\n\n/**\n * Selector combining class names and tags\n */\nconst selector = 'svg.' +\n    blockClass +\n    ', i.' +\n    blockClass +\n    ', span.' +\n    blockClass +\n    ', i.' +\n    inlineClass +\n    ', span.' +\n    inlineClass;\n/**\n * Find all parent nodes in DOM\n */\nfunction scanRootNode(root) {\n    const nodes = [];\n    root.querySelectorAll(selector).forEach((node) => {\n        // Get props, ignore SVG rendered outside of SVG framework\n        const props = node[elementDataProperty] || node.tagName.toLowerCase() !== 'svg'\n            ? getElementProps(node)\n            : null;\n        if (props) {\n            nodes.push({\n                node,\n                props,\n            });\n        }\n    });\n    return nodes;\n}\n\nfunction iconToHTML(body, attributes) {\n  let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n  for (const attr in attributes) {\n    renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n  }\n  return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\n\nlet policy;\nfunction createPolicy() {\n  try {\n    policy = window.trustedTypes.createPolicy(\"iconify\", {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n      createHTML: (s) => s\n    });\n  } catch (err) {\n    policy = null;\n  }\n}\nfunction cleanUpInnerHTML(html) {\n  if (policy === void 0) {\n    createPolicy();\n  }\n  return policy ? policy.createHTML(html) : html;\n}\n\n/**\n * Get classes to add from icon name\n */\nfunction iconClasses(iconName) {\n    const classesToAdd = new Set(['iconify']);\n    ['provider', 'prefix'].forEach((attr) => {\n        if (iconName[attr]) {\n            classesToAdd.add('iconify--' + iconName[attr]);\n        }\n    });\n    return classesToAdd;\n}\n/**\n * Add classes to SVG, removing previously added classes, keeping custom classes\n */\nfunction applyClasses(svg, classes, previouslyAddedClasses, placeholder) {\n    const svgClasses = svg.classList;\n    // Copy classes from placeholder\n    if (placeholder) {\n        const placeholderClasses = placeholder.classList;\n        Array.from(placeholderClasses).forEach((item) => {\n            svgClasses.add(item);\n        });\n    }\n    // Add new classes\n    const addedClasses = [];\n    classes.forEach((item) => {\n        if (!svgClasses.contains(item)) {\n            // Add new class\n            svgClasses.add(item);\n            addedClasses.push(item);\n        }\n        else if (previouslyAddedClasses.has(item)) {\n            // Was added before: keep it\n            addedClasses.push(item);\n        }\n    });\n    // Remove previously added classes\n    previouslyAddedClasses.forEach((item) => {\n        if (!classes.has(item)) {\n            // Class that was added before, but no longer needed\n            svgClasses.remove(item);\n        }\n    });\n    return addedClasses;\n}\n\n/**\n * Copy old styles, apply new styles\n */\nfunction applyStyle(svg, styles, previouslyAddedStyles) {\n    const svgStyle = svg.style;\n    // Remove previously added styles\n    (previouslyAddedStyles || []).forEach((prop) => {\n        svgStyle.removeProperty(prop);\n    });\n    // Apply new styles, ignoring styles that already exist\n    const appliedStyles = [];\n    for (const prop in styles) {\n        if (!svgStyle.getPropertyValue(prop)) {\n            appliedStyles.push(prop);\n            svgStyle.setProperty(prop, styles[prop]);\n        }\n    }\n    return appliedStyles;\n}\n\n/**\n * Render icon as inline SVG\n */\nfunction renderInlineSVG(element, props, iconData) {\n    // Create placeholder. Why placeholder? innerHTML setter on SVG does not work in some environments.\n    let span;\n    try {\n        span = document.createElement('span');\n    }\n    catch (err) {\n        return element;\n    }\n    // Generate data to render\n    const customisations = props.customisations;\n    const renderData = iconToSVG(iconData, customisations);\n    // Get old data\n    const oldData = element[elementDataProperty];\n    // Generate SVG\n    const html = iconToHTML(replaceIDs(renderData.body), {\n        'aria-hidden': 'true',\n        'role': 'img',\n        ...renderData.attributes,\n    });\n    span.innerHTML = cleanUpInnerHTML(html);\n    // Get SVG element\n    const svg = span.childNodes[0];\n    // Add attributes\n    const placeholderAttributes = element.attributes;\n    for (let i = 0; i < placeholderAttributes.length; i++) {\n        const item = placeholderAttributes.item(i);\n        const name = item.name;\n        if (name !== 'class' && !svg.hasAttribute(name)) {\n            svg.setAttribute(name, item.value);\n        }\n    }\n    // Add classes\n    const classesToAdd = iconClasses(props.icon);\n    const addedClasses = applyClasses(svg, classesToAdd, new Set(oldData && oldData.addedClasses), element);\n    // Update style\n    const addedStyles = applyStyle(svg, customisations.inline\n        ? {\n            'vertical-align': '-0.125em',\n        }\n        : {}, oldData && oldData.addedStyles);\n    // Add data to element\n    const newData = {\n        ...props,\n        status: 'loaded',\n        addedClasses,\n        addedStyles,\n    };\n    svg[elementDataProperty] = newData;\n    // Replace old element\n    if (element.parentNode) {\n        element.parentNode.replaceChild(svg, element);\n    }\n    return svg;\n}\n\nfunction encodeSVGforURL(svg) {\n  return svg.replace(/\"/g, \"'\").replace(/%/g, \"%25\").replace(/#/g, \"%23\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\").replace(/\\s+/g, \" \");\n}\nfunction svgToData(svg) {\n  return \"data:image/svg+xml,\" + encodeSVGforURL(svg);\n}\nfunction svgToURL(svg) {\n  return 'url(\"' + svgToData(svg) + '\")';\n}\n\nconst commonProps = {\n    display: 'inline-block',\n};\nconst monotoneProps = {\n    'background-color': 'currentColor',\n};\nconst coloredProps = {\n    'background-color': 'transparent',\n};\n// Dynamically add common props to variables above\nconst propsToAdd = {\n    image: 'var(--svg)',\n    repeat: 'no-repeat',\n    size: '100% 100%',\n};\nconst propsToAddTo = {\n    '-webkit-mask': monotoneProps,\n    'mask': monotoneProps,\n    'background': coloredProps,\n};\nfor (const prefix in propsToAddTo) {\n    const list = propsToAddTo[prefix];\n    for (const prop in propsToAdd) {\n        list[prefix + '-' + prop] = propsToAdd[prop];\n    }\n}\n/**\n * Fix size: add 'px' to numbers\n */\nfunction fixSize(value) {\n    return value + (value.match(/^[-0-9.]+$/) ? 'px' : '');\n}\n/**\n * Render icon as inline SVG\n */\nfunction renderBackground(element, props, iconData, useMask) {\n    // Generate data to render\n    const customisations = props.customisations;\n    const renderData = iconToSVG(iconData, customisations);\n    const renderAttribs = renderData.attributes;\n    // Get old data\n    const oldData = element[elementDataProperty];\n    // Generate SVG\n    const html = iconToHTML(renderData.body, {\n        ...renderAttribs,\n        width: iconData.width + '',\n        height: iconData.height + '',\n    });\n    // Add classes\n    const classesToAdd = iconClasses(props.icon);\n    const addedClasses = applyClasses(element, classesToAdd, new Set(oldData && oldData.addedClasses));\n    // Update style\n    const url = svgToURL(html);\n    const newStyles = {\n        '--svg': url,\n        'width': fixSize(renderAttribs.width),\n        'height': fixSize(renderAttribs.height),\n        ...commonProps,\n        ...(useMask ? monotoneProps : coloredProps),\n    };\n    if (customisations.inline) {\n        newStyles['vertical-align'] = '-0.125em';\n    }\n    const addedStyles = applyStyle(element, newStyles, oldData && oldData.addedStyles);\n    // Add data to element\n    const newData = {\n        ...props,\n        status: 'loaded',\n        addedClasses,\n        addedStyles,\n    };\n    element[elementDataProperty] = newData;\n    return element;\n}\n\n/**\n * Flag to avoid scanning DOM too often\n */\nlet scanQueued = false;\n/**\n * Icons have been loaded\n */\nfunction checkPendingIcons() {\n    if (!scanQueued) {\n        scanQueued = true;\n        setTimeout(() => {\n            if (scanQueued) {\n                scanQueued = false;\n                scanDOM();\n            }\n        });\n    }\n}\n/**\n * Scan node for placeholders\n */\nfunction scanDOM(rootNode, addTempNode = false) {\n    // List of icons to load: [provider][prefix] = Set<name>\n    const iconsToLoad = Object.create(null);\n    function getIcon(icon, load) {\n        const { provider, prefix, name } = icon;\n        const storage = getStorage(provider, prefix);\n        const storedIcon = storage.icons[name];\n        if (storedIcon) {\n            return {\n                status: 'loaded',\n                icon: storedIcon,\n            };\n        }\n        if (storage.missing.has(name)) {\n            return {\n                status: 'missing',\n            };\n        }\n        if (load && !isPending(icon)) {\n            const providerIconsToLoad = iconsToLoad[provider] ||\n                (iconsToLoad[provider] = Object.create(null));\n            const set = providerIconsToLoad[prefix] ||\n                (providerIconsToLoad[prefix] = new Set());\n            set.add(name);\n        }\n        return {\n            status: 'loading',\n        };\n    }\n    // Parse all root nodes\n    (rootNode ? [rootNode] : listRootNodes()).forEach((observedNode) => {\n        const root = typeof observedNode.node === 'function'\n            ? observedNode.node()\n            : observedNode.node;\n        if (!root || !root.querySelectorAll) {\n            return;\n        }\n        // Track placeholders\n        let hasPlaceholders = false;\n        // Observer\n        let paused = false;\n        /**\n         * Render icon\n         */\n        function render(element, props, iconData) {\n            if (!paused) {\n                paused = true;\n                pauseObservingNode(observedNode);\n            }\n            if (element.tagName.toUpperCase() !== 'SVG') {\n                // Check for one of style modes\n                const mode = props.mode;\n                const isMask = mode === 'mask' ||\n                    (mode === 'bg'\n                        ? false\n                        : mode === 'style'\n                            ? iconData.body.indexOf('currentColor') !== -1\n                            : null);\n                if (typeof isMask === 'boolean') {\n                    renderBackground(element, props, {\n                        ...defaultIconProps,\n                        ...iconData,\n                    }, isMask);\n                    return;\n                }\n            }\n            renderInlineSVG(element, props, iconData);\n        }\n        // Find all elements\n        scanRootNode(root).forEach(({ node, props }) => {\n            // Check if item already has props\n            const oldData = node[elementDataProperty];\n            if (!oldData) {\n                // New icon without data\n                const { status, icon } = getIcon(props.icon, true);\n                if (icon) {\n                    // Ready to render!\n                    render(node, props, icon);\n                    return;\n                }\n                // Loading or missing\n                hasPlaceholders = hasPlaceholders || status === 'loading';\n                node[elementDataProperty] = {\n                    ...props,\n                    status,\n                };\n                return;\n            }\n            // Previously found icon\n            let item;\n            if (!propsChanged(oldData, props)) {\n                // Props have not changed. Check status\n                const oldStatus = oldData.status;\n                if (oldStatus !== 'loading') {\n                    return;\n                }\n                item = getIcon(props.icon, false);\n                if (!item.icon) {\n                    // Nothing to render\n                    oldData.status = item.status;\n                    return;\n                }\n            }\n            else {\n                // Properties have changed: load icon if name has changed\n                item = getIcon(props.icon, oldData.name !== props.name);\n                if (!item.icon) {\n                    // Cannot render icon: update status and props\n                    hasPlaceholders =\n                        hasPlaceholders || item.status === 'loading';\n                    Object.assign(oldData, {\n                        ...props,\n                        status: item.status,\n                    });\n                    return;\n                }\n            }\n            // Re-render icon\n            render(node, props, item.icon);\n        });\n        // Observed node stuff\n        if (observedNode.temporary && !hasPlaceholders) {\n            // Remove temporary node\n            stopObserving(root);\n        }\n        else if (addTempNode && hasPlaceholders) {\n            // Add new temporary node\n            observe(root, true);\n        }\n        else if (paused && observedNode.observer) {\n            // Resume observer\n            resumeObservingNode(observedNode);\n        }\n    });\n    // Load icons\n    for (const provider in iconsToLoad) {\n        const providerIconsToLoad = iconsToLoad[provider];\n        for (const prefix in providerIconsToLoad) {\n            const set = providerIconsToLoad[prefix];\n            loadIcons(Array.from(set).map((name) => ({\n                provider,\n                prefix,\n                name,\n            })), checkPendingIcons);\n        }\n    }\n}\n/**\n * Scan node for placeholders\n */\nfunction scanElement(root) {\n    // Add temporary node\n    const node = findRootNode(root);\n    if (!node) {\n        scanDOM({\n            node: root,\n            temporary: true,\n        }, true);\n    }\n    else {\n        scanDOM(node);\n    }\n}\n\nfunction generateIcon(name, customisations, returnString = false) {\n    // Get icon data\n    const iconData = getIconData(name);\n    if (!iconData) {\n        return null;\n    }\n    // Split name\n    const iconName = stringToIcon(name);\n    // Clean up customisations\n    const changes = mergeCustomisations(defaultExtendedIconCustomisations, customisations || {});\n    // Get data\n    const result = renderInlineSVG(document.createElement('span'), {\n        name,\n        icon: iconName,\n        customisations: changes,\n    }, iconData);\n    return returnString\n        ? result.outerHTML\n        : result;\n}\n/**\n * Get version\n */\nfunction getVersion() {\n    return '3.1.1';\n}\n/**\n * Generate SVG element\n */\nfunction renderSVG(name, customisations) {\n    return generateIcon(name, customisations, false);\n}\n/**\n * Generate SVG as string\n */\nfunction renderHTML(name, customisations) {\n    return generateIcon(name, customisations, true);\n}\n/**\n * Get rendered icon as object that can be used to create SVG (use replaceIDs on body)\n */\nfunction renderIcon(name, customisations) {\n    // Get icon data\n    const iconData = getIconData(name);\n    if (!iconData) {\n        return null;\n    }\n    // Clean up customisations\n    const changes = mergeCustomisations(defaultExtendedIconCustomisations, customisations || {});\n    // Get data\n    return iconToSVG(iconData, changes);\n}\n/**\n * Scan DOM\n */\nfunction scan(root) {\n    if (root) {\n        scanElement(root);\n    }\n    else {\n        scanDOM();\n    }\n}\n/**\n * Initialise stuff\n */\nif (typeof document !== 'undefined' && typeof window !== 'undefined') {\n    // Add document.body node\n    addBodyNode();\n    const _window = window;\n    // Load icons from global \"IconifyPreload\"\n    if (_window.IconifyPreload !== void 0) {\n        const preload = _window.IconifyPreload;\n        const err = 'Invalid IconifyPreload syntax.';\n        if (typeof preload === 'object' && preload !== null) {\n            (preload instanceof Array ? preload : [preload]).forEach((item) => {\n                try {\n                    if (\n                    // Check if item is an object and not null/array\n                    typeof item !== 'object' ||\n                        item === null ||\n                        item instanceof Array ||\n                        // Check for 'icons' and 'prefix'\n                        typeof item.icons !== 'object' ||\n                        typeof item.prefix !== 'string' ||\n                        // Add icon set\n                        !addCollection(item)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            });\n        }\n    }\n    // Load observer and scan DOM on next tick\n    setTimeout(() => {\n        initObserver(scanDOM);\n        scanDOM();\n    });\n}\n\n/**\n * Enable cache\n */\nfunction enableCache(storage, enable) {\n    toggleBrowserCache(storage, enable !== false);\n}\n/**\n * Disable cache\n */\nfunction disableCache(storage) {\n    toggleBrowserCache(storage, true);\n}\n/**\n * Initialise stuff\n */\n// Set API module\nsetAPIModule('', fetchAPIModule);\n/**\n * Browser stuff\n */\nif (typeof document !== 'undefined' && typeof window !== 'undefined') {\n    // Set cache and load existing cache\n    initBrowserStorage();\n    const _window = window;\n    // Set API from global \"IconifyProviders\"\n    if (_window.IconifyProviders !== void 0) {\n        const providers = _window.IconifyProviders;\n        if (typeof providers === 'object' && providers !== null) {\n            for (const key in providers) {\n                const err = 'IconifyProviders[' + key + '] is invalid.';\n                try {\n                    const value = providers[key];\n                    if (typeof value !== 'object' ||\n                        !value ||\n                        value.resources === void 0) {\n                        continue;\n                    }\n                    if (!addAPIProvider(key, value)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            }\n        }\n    }\n}\n/**\n * Internal API\n */\nconst _api = {\n    getAPIConfig,\n    setAPIModule,\n    sendAPIQuery,\n    setFetch,\n    getFetch,\n    listAPIProviders,\n};\n/**\n * Global variable\n */\nconst Iconify = {\n    // IconifyAPIInternalFunctions\n    _api,\n    // IconifyAPIFunctions\n    addAPIProvider,\n    loadIcons,\n    loadIcon,\n    // IconifyStorageFunctions\n    iconExists,\n    getIcon,\n    listIcons,\n    addIcon,\n    addCollection,\n    // IconifyBuilderFunctions\n    replaceIDs,\n    calculateSize,\n    buildIcon: iconToSVG,\n    // IconifyCommonFunctions\n    getVersion,\n    renderSVG,\n    renderHTML,\n    renderIcon,\n    scan,\n    observe,\n    stopObserving,\n    pauseObserver,\n    resumeObserver,\n    // IconifyBrowserCacheFunctions\n    enableCache,\n    disableCache,\n};\n\nexport { _api, addAPIProvider, addCollection, addIcon, iconToSVG as buildIcon, calculateSize, Iconify as default, disableCache, enableCache, getIcon, getVersion, iconExists, listIcons, loadIcon, loadIcons, observe, pauseObserver, renderHTML, renderIcon, renderSVG, replaceIDs, resumeObserver, scan, stopObserving };\n\n// Export to window or web worker\ntry {\n\tif (self.Iconify === void 0) {\n\t\tself.Iconify = Iconify;\n\t}\n} catch (err) {\n}\n"], "mappings": ";;;AAWA,IAAM,wBAAwB,OAAO;AAAA,EACnC;AAAA,IACE,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF;AACA,IAAM,6BAA6B,OAAO,OAAO;AAAA,EAC/C,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACT,CAAC;AACD,IAAM,mBAAmB,OAAO,OAAO;AAAA,EACrC,GAAG;AAAA,EACH,GAAG;AACL,CAAC;AACD,IAAM,2BAA2B,OAAO,OAAO;AAAA,EAC7C,GAAG;AAAA,EACH,MAAM;AAAA,EACN,QAAQ;AACV,CAAC;AAED,SAAS,yBAAyB,MAAM,MAAM;AAC5C,QAAM,SAAS,CAAC;AAChB,MAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,WAAO,QAAQ;AAAA,EACjB;AACA,QAAM,WAAW,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM;AAC3D,MAAI,QAAQ;AACV,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT;AAEA,SAAS,cAAc,QAAQ,OAAO;AACpC,QAAM,SAAS,yBAAyB,QAAQ,KAAK;AACrD,aAAW,OAAO,0BAA0B;AAC1C,QAAI,OAAO,4BAA4B;AACrC,UAAI,OAAO,UAAU,EAAE,OAAO,SAAS;AACrC,eAAO,GAAG,IAAI,2BAA2B,GAAG;AAAA,MAC9C;AAAA,IACF,WAAW,OAAO,OAAO;AACvB,aAAO,GAAG,IAAI,MAAM,GAAG;AAAA,IACzB,WAAW,OAAO,QAAQ;AACxB,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa,MAAM,OAAO;AACjC,QAAM,QAAQ,KAAK;AACnB,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,QAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,WAAS,QAAQ,MAAM;AACrB,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,SAAS,IAAI,IAAI,CAAC;AAAA,IAC3B;AACA,QAAI,EAAE,QAAQ,WAAW;AACvB,eAAS,IAAI,IAAI;AACjB,YAAM,SAAS,QAAQ,IAAI,KAAK,QAAQ,IAAI,EAAE;AAC9C,YAAM,QAAQ,UAAU,QAAQ,MAAM;AACtC,UAAI,OAAO;AACT,iBAAS,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK;AAAA,MACxC;AAAA,IACF;AACA,WAAO,SAAS,IAAI;AAAA,EACtB;AACA,GAAC,SAAS,OAAO,KAAK,KAAK,EAAE,OAAO,OAAO,KAAK,OAAO,CAAC,GAAG,QAAQ,OAAO;AAC1E,SAAO;AACT;AAEA,SAAS,oBAAoB,MAAM,MAAM,MAAM;AAC7C,QAAM,QAAQ,KAAK;AACnB,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,MAAI,eAAe,CAAC;AACpB,WAAS,MAAM,OAAO;AACpB,mBAAe;AAAA,MACb,MAAM,KAAK,KAAK,QAAQ,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACA,QAAM,IAAI;AACV,OAAK,QAAQ,KAAK;AAClB,SAAO,cAAc,MAAM,YAAY;AACzC;AAEA,SAAS,aAAa,MAAMA,WAAU;AACpC,QAAM,QAAQ,CAAC;AACf,MAAI,OAAO,SAAS,YAAY,OAAO,KAAK,UAAU,UAAU;AAC9D,WAAO;AAAA,EACT;AACA,MAAI,KAAK,qBAAqB,OAAO;AACnC,SAAK,UAAU,QAAQ,CAAC,SAAS;AAC/B,MAAAA,UAAS,MAAM,IAAI;AACnB,YAAM,KAAK,IAAI;AAAA,IACjB,CAAC;AAAA,EACH;AACA,QAAM,OAAO,aAAa,IAAI;AAC9B,aAAW,QAAQ,MAAM;AACvB,UAAM,OAAO,KAAK,IAAI;AACtB,QAAI,MAAM;AACR,MAAAA,UAAS,MAAM,oBAAoB,MAAM,MAAM,IAAI,CAAC;AACpD,YAAM,KAAK,IAAI;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,gBAAgB;AACtB,IAAM,eAAe,CAAC,OAAO,UAAU,iBAAiB,WAAW,OAAO;AACxE,QAAM,iBAAiB,MAAM,MAAM,GAAG;AACtC,MAAI,MAAM,MAAM,GAAG,CAAC,MAAM,KAAK;AAC7B,QAAI,eAAe,SAAS,KAAK,eAAe,SAAS,GAAG;AAC1D,aAAO;AAAA,IACT;AACA,eAAW,eAAe,MAAM,EAAE,MAAM,CAAC;AAAA,EAC3C;AACA,MAAI,eAAe,SAAS,KAAK,CAAC,eAAe,QAAQ;AACvD,WAAO;AAAA,EACT;AACA,MAAI,eAAe,SAAS,GAAG;AAC7B,UAAM,QAAQ,eAAe,IAAI;AACjC,UAAM,SAAS,eAAe,IAAI;AAClC,UAAM,SAAS;AAAA;AAAA,MAEb,UAAU,eAAe,SAAS,IAAI,eAAe,CAAC,IAAI;AAAA,MAC1D;AAAA,MACA,MAAM;AAAA,IACR;AACA,WAAO,YAAY,CAAC,iBAAiB,MAAM,IAAI,OAAO;AAAA,EACxD;AACA,QAAM,OAAO,eAAe,CAAC;AAC7B,QAAM,gBAAgB,KAAK,MAAM,GAAG;AACpC,MAAI,cAAc,SAAS,GAAG;AAC5B,UAAM,SAAS;AAAA,MACb;AAAA,MACA,QAAQ,cAAc,MAAM;AAAA,MAC5B,MAAM,cAAc,KAAK,GAAG;AAAA,IAC9B;AACA,WAAO,YAAY,CAAC,iBAAiB,MAAM,IAAI,OAAO;AAAA,EACxD;AACA,MAAI,mBAAmB,aAAa,IAAI;AACtC,UAAM,SAAS;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF;AACA,WAAO,YAAY,CAAC,iBAAiB,QAAQ,eAAe,IAAI,OAAO;AAAA,EACzE;AACA,SAAO;AACT;AACA,IAAM,mBAAmB,CAAC,MAAM,oBAAoB;AAClD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,CAAC,GAAG,KAAK,aAAa,MAAM,KAAK,SAAS,MAAM,aAAa,OAAO,mBAAmB,KAAK,WAAW,MAAM,KAAK,OAAO,MAAM,aAAa,MAAM,KAAK,KAAK,MAAM,aAAa;AACxL;AAEA,IAAM,2BAA2B;AAAA,EAC/B,UAAU;AAAA,EACV,SAAS,CAAC;AAAA,EACV,WAAW,CAAC;AAAA,EACZ,GAAG;AACL;AACA,SAAS,mBAAmB,MAAM,UAAU;AAC1C,aAAW,QAAQ,UAAU;AAC3B,QAAI,QAAQ,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,SAAS,IAAI,GAAG;AAC/D,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,KAAK;AACnC,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,WAAO;AAAA,EACT;AACA,QAAM,OAAO;AACb,MAAI,OAAO,KAAK,WAAW,YAAY,CAAC,IAAI,SAAS,OAAO,IAAI,UAAU,UAAU;AAClF,WAAO;AAAA,EACT;AACA,MAAI,CAAC,mBAAmB,KAAK,wBAAwB,GAAG;AACtD,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,KAAK;AACnB,aAAW,QAAQ,OAAO;AACxB,UAAM,OAAO,MAAM,IAAI;AACvB,QAAI,CAAC,KAAK,MAAM,aAAa,KAAK,OAAO,KAAK,SAAS,YAAY,CAAC;AAAA,MAClE;AAAA,MACA;AAAA,IACF,GAAG;AACD,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,aAAW,QAAQ,SAAS;AAC1B,UAAM,OAAO,QAAQ,IAAI;AACzB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,KAAK,MAAM,aAAa,KAAK,OAAO,WAAW,YAAY,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,MAAM,KAAK,CAAC;AAAA,MACrG;AAAA,MACA;AAAA,IACF,GAAG;AACD,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,cAA8B,uBAAO,OAAO,IAAI;AACtD,SAAS,WAAW,UAAU,QAAQ;AACpC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAuB,uBAAO,OAAO,IAAI;AAAA,IACzC,SAAyB,oBAAI,IAAI;AAAA,EACnC;AACF;AACA,SAAS,WAAW,UAAU,QAAQ;AACpC,QAAM,kBAAkB,YAAY,QAAQ,MAAM,YAAY,QAAQ,IAAoB,uBAAO,OAAO,IAAI;AAC5G,SAAO,gBAAgB,MAAM,MAAM,gBAAgB,MAAM,IAAI,WAAW,UAAU,MAAM;AAC1F;AACA,SAAS,WAAWC,UAAS,MAAM;AACjC,MAAI,CAAC,uBAAuB,IAAI,GAAG;AACjC,WAAO,CAAC;AAAA,EACV;AACA,SAAO,aAAa,MAAM,CAAC,MAAM,SAAS;AACxC,QAAI,MAAM;AACR,MAAAA,SAAQ,MAAM,IAAI,IAAI;AAAA,IACxB,OAAO;AACL,MAAAA,SAAQ,QAAQ,IAAI,IAAI;AAAA,IAC1B;AAAA,EACF,CAAC;AACH;AACA,SAAS,iBAAiBA,UAAS,MAAM,MAAM;AAC7C,MAAI;AACF,QAAI,OAAO,KAAK,SAAS,UAAU;AACjC,MAAAA,SAAQ,MAAM,IAAI,IAAI,EAAE,GAAG,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF,SAAS,KAAK;AAAA,EACd;AACA,SAAO;AACT;AACA,SAAS,UAAU,UAAU,QAAQ;AACnC,MAAI,WAAW,CAAC;AAChB,QAAM,YAAY,OAAO,aAAa,WAAW,CAAC,QAAQ,IAAI,OAAO,KAAK,WAAW;AACrF,YAAU,QAAQ,CAAC,cAAc;AAC/B,UAAM,WAAW,OAAO,cAAc,YAAY,OAAO,WAAW,WAAW,CAAC,MAAM,IAAI,OAAO,KAAK,YAAY,SAAS,KAAK,CAAC,CAAC;AAClI,aAAS,QAAQ,CAAC,YAAY;AAC5B,YAAMA,WAAU,WAAW,WAAW,OAAO;AAC7C,iBAAW,SAAS;AAAA,QAClB,OAAO,KAAKA,SAAQ,KAAK,EAAE;AAAA,UACzB,CAAC,UAAU,cAAc,KAAK,MAAM,YAAY,MAAM,MAAM,UAAU,MAAM;AAAA,QAC9E;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAEA,IAAI,cAAc;AAClB,SAAS,iBAAiB,OAAO;AAC/B,MAAI,OAAO,UAAU,WAAW;AAC9B,kBAAc;AAAA,EAChB;AACA,SAAO;AACT;AACA,SAAS,YAAY,MAAM;AACzB,QAAM,OAAO,OAAO,SAAS,WAAW,aAAa,MAAM,MAAM,WAAW,IAAI;AAChF,MAAI,MAAM;AACR,UAAMA,WAAU,WAAW,KAAK,UAAU,KAAK,MAAM;AACrD,UAAM,WAAW,KAAK;AACtB,WAAOA,SAAQ,MAAM,QAAQ,MAAMA,SAAQ,QAAQ,IAAI,QAAQ,IAAI,OAAO;AAAA,EAC5E;AACF;AACA,SAAS,QAAQ,MAAM,MAAM;AAC3B,QAAM,OAAO,aAAa,MAAM,MAAM,WAAW;AACjD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,QAAMA,WAAU,WAAW,KAAK,UAAU,KAAK,MAAM;AACrD,SAAO,iBAAiBA,UAAS,KAAK,MAAM,IAAI;AAClD;AACA,SAAS,cAAc,MAAM,UAAU;AACrC,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,aAAa,UAAU;AAChC,eAAW,KAAK,YAAY;AAAA,EAC9B;AACA,MAAI,eAAe,CAAC,YAAY,CAAC,KAAK,QAAQ;AAC5C,QAAI,QAAQ;AACZ,QAAI,uBAAuB,IAAI,GAAG;AAChC,WAAK,SAAS;AACd,mBAAa,MAAM,CAAC,MAAM,SAAS;AACjC,YAAI,QAAQ,QAAQ,MAAM,IAAI,GAAG;AAC/B,kBAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,KAAK;AACpB,MAAI,CAAC,iBAAiB;AAAA,IACpB;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,CAAC,GAAG;AACF,WAAO;AAAA,EACT;AACA,QAAMA,WAAU,WAAW,UAAU,MAAM;AAC3C,SAAO,CAAC,CAAC,WAAWA,UAAS,IAAI;AACnC;AACA,SAAS,WAAW,MAAM;AACxB,SAAO,CAAC,CAAC,YAAY,IAAI;AAC3B;AACA,SAAS,QAAQ,MAAM;AACrB,QAAM,SAAS,YAAY,IAAI;AAC/B,SAAO,SAAS;AAAA,IACd,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AACN;AAEA,IAAM,gCAAgC,OAAO,OAAO;AAAA,EAClD,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AACD,IAAM,4BAA4B,OAAO,OAAO;AAAA;AAAA,EAE9C,GAAG;AAAA;AAAA,EAEH,GAAG;AACL,CAAC;AAED,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,SAAS,cAAc,MAAM,OAAO,WAAW;AAC7C,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT;AACA,cAAY,aAAa;AACzB,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,KAAK,KAAK,OAAO,QAAQ,SAAS,IAAI;AAAA,EAC/C;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,UAAU;AACtC,MAAI,aAAa,QAAQ,CAAC,SAAS,QAAQ;AACzC,WAAO;AAAA,EACT;AACA,QAAM,WAAW,CAAC;AAClB,MAAI,OAAO,SAAS,MAAM;AAC1B,MAAI,WAAW,UAAU,KAAK,IAAI;AAClC,SAAO,MAAM;AACX,QAAI,UAAU;AACZ,YAAM,MAAM,WAAW,IAAI;AAC3B,UAAI,MAAM,GAAG,GAAG;AACd,iBAAS,KAAK,IAAI;AAAA,MACpB,OAAO;AACL,iBAAS,KAAK,KAAK,KAAK,MAAM,QAAQ,SAAS,IAAI,SAAS;AAAA,MAC9D;AAAA,IACF,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,WAAO,SAAS,MAAM;AACtB,QAAI,SAAS,QAAQ;AACnB,aAAO,SAAS,KAAK,EAAE;AAAA,IACzB;AACA,eAAW,CAAC;AAAA,EACd;AACF;AAEA,IAAM,iBAAiB,CAAC,UAAU,UAAU,WAAW,UAAU,eAAe,UAAU;AAC1F,SAAS,UAAU,MAAM,gBAAgB;AACvC,QAAM,WAAW;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,qBAAqB;AAAA,IACzB,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,MAAM;AAAA,IACV,MAAM,SAAS;AAAA,IACf,KAAK,SAAS;AAAA,IACd,OAAO,SAAS;AAAA,IAChB,QAAQ,SAAS;AAAA,EACnB;AACA,MAAI,OAAO,SAAS;AACpB,GAAC,UAAU,kBAAkB,EAAE,QAAQ,CAAC,UAAU;AAChD,UAAM,kBAAkB,CAAC;AACzB,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ,MAAM;AACpB,QAAI,WAAW,MAAM;AACrB,QAAI,OAAO;AACT,UAAI,OAAO;AACT,oBAAY;AAAA,MACd,OAAO;AACL,wBAAgB;AAAA,UACd,gBAAgB,IAAI,QAAQ,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACtF;AACA,wBAAgB,KAAK,aAAa;AAClC,YAAI,MAAM,IAAI,OAAO;AAAA,MACvB;AAAA,IACF,WAAW,OAAO;AAChB,sBAAgB;AAAA,QACd,gBAAgB,IAAI,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,KAAK,SAAS,IAAI;AAAA,MACvF;AACA,sBAAgB,KAAK,aAAa;AAClC,UAAI,MAAM,IAAI,OAAO;AAAA,IACvB;AACA,QAAI;AACJ,QAAI,WAAW,GAAG;AAChB,kBAAY,KAAK,MAAM,WAAW,CAAC,IAAI;AAAA,IACzC;AACA,eAAW,WAAW;AACtB,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,oBAAY,IAAI,SAAS,IAAI,IAAI;AACjC,wBAAgB;AAAA,UACd,eAAe,UAAU,SAAS,IAAI,MAAM,UAAU,SAAS,IAAI;AAAA,QACrE;AACA;AAAA,MACF,KAAK;AACH,wBAAgB;AAAA,UACd,iBAAiB,IAAI,QAAQ,IAAI,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACxG;AACA;AAAA,MACF,KAAK;AACH,oBAAY,IAAI,QAAQ,IAAI,IAAI;AAChC,wBAAgB;AAAA,UACd,gBAAgB,UAAU,SAAS,IAAI,MAAM,UAAU,SAAS,IAAI;AAAA,QACtE;AACA;AAAA,IACJ;AACA,QAAI,WAAW,MAAM,GAAG;AACtB,UAAI,IAAI,SAAS,IAAI,KAAK;AACxB,oBAAY,IAAI;AAChB,YAAI,OAAO,IAAI;AACf,YAAI,MAAM;AAAA,MACZ;AACA,UAAI,IAAI,UAAU,IAAI,QAAQ;AAC5B,oBAAY,IAAI;AAChB,YAAI,QAAQ,IAAI;AAChB,YAAI,SAAS;AAAA,MACf;AAAA,IACF;AACA,QAAI,gBAAgB,QAAQ;AAC1B,aAAO,mBAAmB,gBAAgB,KAAK,GAAG,IAAI,OAAO,OAAO;AAAA,IACtE;AAAA,EACF,CAAC;AACD,QAAM,sBAAsB,mBAAmB;AAC/C,QAAM,uBAAuB,mBAAmB;AAChD,QAAM,WAAW,IAAI;AACrB,QAAM,YAAY,IAAI;AACtB,MAAI;AACJ,MAAI;AACJ,MAAI,wBAAwB,MAAM;AAChC,aAAS,yBAAyB,OAAO,QAAQ,yBAAyB,SAAS,YAAY;AAC/F,YAAQ,cAAc,QAAQ,WAAW,SAAS;AAAA,EACpD,OAAO;AACL,YAAQ,wBAAwB,SAAS,WAAW;AACpD,aAAS,yBAAyB,OAAO,cAAc,OAAO,YAAY,QAAQ,IAAI,yBAAyB,SAAS,YAAY;AAAA,EACtI;AACA,QAAM,aAAa,CAAC;AACpB,QAAM,UAAU,CAAC,MAAM,UAAU;AAC/B,QAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,iBAAW,IAAI,IAAI,MAAM,SAAS;AAAA,IACpC;AAAA,EACF;AACA,UAAQ,SAAS,KAAK;AACtB,UAAQ,UAAU,MAAM;AACxB,aAAW,UAAU,IAAI,KAAK,SAAS,IAAI,MAAM,IAAI,IAAI,SAAS,IAAI,MAAM,SAAS,SAAS,IAAI,MAAM,UAAU,SAAS;AAC3H,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,QAAQ;AACd,IAAM,eAAe,cAAc,KAAK,IAAI,EAAE,SAAS,EAAE,KAAK,KAAK,OAAO,IAAI,WAAW,GAAG,SAAS,EAAE;AACvG,IAAI,UAAU;AACd,SAAS,WAAW,MAAM,SAAS,cAAc;AAC/C,QAAM,MAAM,CAAC;AACb,MAAI;AACJ,SAAO,QAAQ,MAAM,KAAK,IAAI,GAAG;AAC/B,QAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EACnB;AACA,MAAI,CAAC,IAAI,QAAQ;AACf,WAAO;AAAA,EACT;AACA,QAAM,SAAS,YAAY,KAAK,OAAO,IAAI,WAAW,KAAK,IAAI,GAAG,SAAS,EAAE;AAC7E,MAAI,QAAQ,CAAC,OAAO;AAClB,UAAM,QAAQ,OAAO,WAAW,aAAa,OAAO,EAAE,IAAI,UAAU,WAAW,SAAS;AACxF,UAAM,YAAY,GAAG,QAAQ,uBAAuB,MAAM;AAC1D,WAAO,KAAK;AAAA;AAAA;AAAA,MAGV,IAAI,OAAO,aAAa,YAAY,oBAAoB,GAAG;AAAA,MAC3D,OAAO,QAAQ,SAAS;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,SAAO,KAAK,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG,EAAE;AAC/C,SAAO;AACT;AAEA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,SAAS;AACX;AACA,IAAM,2BAA2B;AAAA,EAC/B,OAAuB,oBAAI,IAAI;AAAA,EAC/B,SAAyB,oBAAI,IAAI;AACnC;AACA,IAAI,uBAAuB;AAC3B,SAAS,wBAAwB,QAAQ;AACvC,yBAAuB;AACzB;AAEA,IAAM,sBAAsB;AAC5B,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB,qBAAqB;AAClD,IAAM,yBAAyB,qBAAqB;AACpD,IAAM,qBAAqB;AAC3B,IAAM,gCAAgC;AAEtC,SAAS,cAAc,MAAM,KAAK;AAChC,MAAI;AACF,WAAO,KAAK,QAAQ,GAAG;AAAA,EACzB,SAAS,KAAK;AAAA,EACd;AACF;AACA,SAAS,cAAc,MAAM,KAAK,OAAO;AACvC,MAAI;AACF,SAAK,QAAQ,KAAK,KAAK;AACvB,WAAO;AAAA,EACT,SAAS,KAAK;AAAA,EACd;AACF;AACA,SAAS,iBAAiB,MAAM,KAAK;AACnC,MAAI;AACF,SAAK,WAAW,GAAG;AAAA,EACrB,SAAS,KAAK;AAAA,EACd;AACF;AAEA,SAAS,4BAA4BA,UAAS,OAAO;AACnD,SAAO,cAAcA,UAAS,sBAAsB,MAAM,SAAS,CAAC;AACtE;AACA,SAAS,4BAA4BA,UAAS;AAC5C,SAAO,SAAS,cAAcA,UAAS,oBAAoB,CAAC,KAAK;AACnE;AAEA,IAAI,UAAU,OAAO,WAAW,cAAc,CAAC,IAAI;AACnD,SAAS,kBAAkB,KAAK;AAC9B,QAAM,OAAO,MAAM;AACnB,MAAI;AACF,QAAI,WAAW,QAAQ,IAAI,KAAK,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAU;AACxE,aAAO,QAAQ,IAAI;AAAA,IACrB;AAAA,EACF,SAAS,KAAK;AAAA,EACd;AACA,uBAAqB,GAAG,IAAI;AAC9B;AAEA,SAAS,sBAAsB,KAAKD,WAAU;AAC5C,QAAM,OAAO,kBAAkB,GAAG;AAClC,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AACA,QAAM,UAAU,cAAc,MAAM,sBAAsB;AAC1D,MAAI,YAAY,qBAAqB;AACnC,QAAI,SAAS;AACX,YAAM,SAAS,4BAA4B,IAAI;AAC/C,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,yBAAiB,MAAM,qBAAqB,EAAE,SAAS,CAAC;AAAA,MAC1D;AAAA,IACF;AACA,kBAAc,MAAM,wBAAwB,mBAAmB;AAC/D,gCAA4B,MAAM,CAAC;AACnC;AAAA,EACF;AACA,QAAM,UAAU,KAAK,MAAM,KAAK,IAAI,IAAI,kBAAkB,IAAI;AAC9D,QAAM,YAAY,CAAC,UAAU;AAC3B,UAAM,OAAO,qBAAqB,MAAM,SAAS;AACjD,UAAM,OAAO,cAAc,MAAM,IAAI;AACrC,QAAI,OAAO,SAAS,UAAU;AAC5B;AAAA,IACF;AACA,QAAI;AACF,YAAM,OAAO,KAAK,MAAM,IAAI;AAC5B,UAAI,OAAO,SAAS,YAAY,OAAO,KAAK,WAAW,YAAY,KAAK,SAAS,WAAW,OAAO,KAAK,aAAa,YAAY,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,KAAK,WAAW;AAAA,MAC9LA,UAAS,MAAM,KAAK,GAAG;AACrB,eAAO;AAAA,MACT;AAAA,IACF,SAAS,KAAK;AAAA,IACd;AACA,qBAAiB,MAAM,IAAI;AAAA,EAC7B;AACA,MAAI,QAAQ,4BAA4B,IAAI;AAC5C,WAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,KAAK;AACnC,QAAI,CAAC,UAAU,CAAC,GAAG;AACjB,UAAI,MAAM,QAAQ,GAAG;AACnB;AACA,oCAA4B,MAAM,KAAK;AAAA,MACzC,OAAO;AACL,iCAAyB,GAAG,EAAE,IAAI,CAAC;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB;AAC5B,MAAI,sBAAsB;AACxB;AAAA,EACF;AACA,0BAAwB,IAAI;AAC5B,aAAW,OAAO,sBAAsB;AACtC,0BAAsB,KAAK,CAAC,SAAS;AACnC,YAAM,UAAU,KAAK;AACrB,YAAM,WAAW,KAAK;AACtB,YAAM,SAAS,QAAQ;AACvB,YAAMC,WAAU;AAAA,QACd;AAAA,QACA;AAAA,MACF;AACA,UAAI,CAAC,WAAWA,UAAS,OAAO,EAAE,QAAQ;AACxC,eAAO;AAAA,MACT;AACA,YAAM,eAAe,QAAQ,gBAAgB;AAC7C,MAAAA,SAAQ,qBAAqBA,SAAQ,qBAAqB,KAAK,IAAIA,SAAQ,oBAAoB,YAAY,IAAI;AAC/G,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAEA,SAAS,mBAAmBA,UAAS,OAAO;AAC1C,UAAQA,UAAS;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AACH,2BAAqBA,QAAO,IAAI;AAChC;AAAA,IACF,KAAK;AACH,iBAAW,OAAO,sBAAsB;AACtC,6BAAqB,GAAG,IAAI;AAAA,MAC9B;AACA;AAAA,EACJ;AACF;AAEA,IAAM,UAA0B,uBAAO,OAAO,IAAI;AAClD,SAAS,aAAa,UAAU,MAAM;AACpC,UAAQ,QAAQ,IAAI;AACtB;AACA,SAAS,aAAa,UAAU;AAC9B,SAAO,QAAQ,QAAQ,KAAK,QAAQ,EAAE;AACxC;AAEA,SAAS,gBAAgB,QAAQ;AAC/B,MAAI;AACJ,MAAI,OAAO,OAAO,cAAc,UAAU;AACxC,gBAAY,CAAC,OAAO,SAAS;AAAA,EAC/B,OAAO;AACL,gBAAY,OAAO;AACnB,QAAI,EAAE,qBAAqB,UAAU,CAAC,UAAU,QAAQ;AACtD,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,SAAS;AAAA;AAAA,IAEb;AAAA;AAAA,IAEA,MAAM,OAAO,QAAQ;AAAA;AAAA,IAErB,QAAQ,OAAO,UAAU;AAAA;AAAA,IAEzB,QAAQ,OAAO,UAAU;AAAA;AAAA,IAEzB,SAAS,OAAO,WAAW;AAAA;AAAA,IAE3B,QAAQ,OAAO,WAAW;AAAA;AAAA,IAE1B,OAAO,OAAO,SAAS;AAAA;AAAA,IAEvB,kBAAkB,OAAO,qBAAqB;AAAA,EAChD;AACA,SAAO;AACT;AACA,IAAM,gBAAgC,uBAAO,OAAO,IAAI;AACxD,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AACF;AACA,IAAM,cAAc,CAAC;AACrB,OAAO,mBAAmB,SAAS,GAAG;AACpC,MAAI,mBAAmB,WAAW,GAAG;AACnC,gBAAY,KAAK,mBAAmB,MAAM,CAAC;AAAA,EAC7C,OAAO;AACL,QAAI,KAAK,OAAO,IAAI,KAAK;AACvB,kBAAY,KAAK,mBAAmB,MAAM,CAAC;AAAA,IAC7C,OAAO;AACL,kBAAY,KAAK,mBAAmB,IAAI,CAAC;AAAA,IAC3C;AAAA,EACF;AACF;AACA,cAAc,EAAE,IAAI,gBAAgB;AAAA,EAClC,WAAW,CAAC,4BAA4B,EAAE,OAAO,WAAW;AAC9D,CAAC;AACD,SAAS,eAAe,UAAU,cAAc;AAC9C,QAAM,SAAS,gBAAgB,YAAY;AAC3C,MAAI,WAAW,MAAM;AACnB,WAAO;AAAA,EACT;AACA,gBAAc,QAAQ,IAAI;AAC1B,SAAO;AACT;AACA,SAAS,aAAa,UAAU;AAC9B,SAAO,cAAc,QAAQ;AAC/B;AACA,SAAS,mBAAmB;AAC1B,SAAO,OAAO,KAAK,aAAa;AAClC;AAEA,IAAM,cAAc,MAAM;AACxB,MAAID;AACJ,MAAI;AACF,IAAAA,YAAW;AACX,QAAI,OAAOA,cAAa,YAAY;AAClC,aAAOA;AAAA,IACT;AAAA,EACF,SAAS,KAAK;AAAA,EACd;AACF;AACA,IAAI,cAAc,YAAY;AAC9B,SAAS,SAAS,QAAQ;AACxB,gBAAc;AAChB;AACA,SAAS,WAAW;AAClB,SAAO;AACT;AACA,SAAS,mBAAmB,UAAU,QAAQ;AAC5C,QAAM,SAAS,aAAa,QAAQ;AACpC,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI,CAAC,OAAO,QAAQ;AAClB,aAAS;AAAA,EACX,OAAO;AACL,QAAI,gBAAgB;AACpB,WAAO,UAAU,QAAQ,CAAC,SAAS;AACjC,YAAM,OAAO;AACb,sBAAgB,KAAK,IAAI,eAAe,KAAK,MAAM;AAAA,IACrD,CAAC;AACD,UAAM,MAAM,SAAS;AACrB,aAAS,OAAO,SAAS,gBAAgB,OAAO,KAAK,SAAS,IAAI;AAAA,EACpE;AACA,SAAO;AACT;AACA,SAAS,YAAY,QAAQ;AAC3B,SAAO,WAAW;AACpB;AACA,IAAM,UAAU,CAAC,UAAU,QAAQ,UAAU;AAC3C,QAAM,UAAU,CAAC;AACjB,QAAM,YAAY,mBAAmB,UAAU,MAAM;AACrD,QAAM,OAAO;AACb,MAAI,OAAO;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,EACV;AACA,MAAI,SAAS;AACb,QAAM,QAAQ,CAAC,MAAM,UAAU;AAC7B,cAAU,KAAK,SAAS;AACxB,QAAI,UAAU,aAAa,QAAQ,GAAG;AACpC,cAAQ,KAAK,IAAI;AACjB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO,CAAC;AAAA,MACV;AACA,eAAS,KAAK;AAAA,IAChB;AACA,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB,CAAC;AACD,UAAQ,KAAK,IAAI;AACjB,SAAO;AACT;AACA,SAAS,QAAQ,UAAU;AACzB,MAAI,OAAO,aAAa,UAAU;AAChC,UAAM,SAAS,aAAa,QAAQ;AACpC,QAAI,QAAQ;AACV,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,OAAO,CAAC,MAAM,QAAQA,cAAa;AACvC,MAAI,CAAC,aAAa;AAChB,IAAAA,UAAS,SAAS,GAAG;AACrB;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,OAAO,QAAQ;AAClC,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK,SAAS;AACZ,YAAM,SAAS,OAAO;AACtB,YAAM,QAAQ,OAAO;AACrB,YAAM,YAAY,MAAM,KAAK,GAAG;AAChC,YAAM,YAAY,IAAI,gBAAgB;AAAA,QACpC,OAAO;AAAA,MACT,CAAC;AACD,cAAQ,SAAS,WAAW,UAAU,SAAS;AAC/C;AAAA,IACF;AAAA,IACA,KAAK,UAAU;AACb,YAAM,MAAM,OAAO;AACnB,cAAQ,IAAI,MAAM,GAAG,CAAC,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI;AACjD;AAAA,IACF;AAAA,IACA;AACE,MAAAA,UAAS,SAAS,GAAG;AACrB;AAAA,EACJ;AACA,MAAI,eAAe;AACnB,cAAY,OAAO,IAAI,EAAE,KAAK,CAAC,aAAa;AAC1C,UAAM,SAAS,SAAS;AACxB,QAAI,WAAW,KAAK;AAClB,iBAAW,MAAM;AACf,QAAAA,UAAS,YAAY,MAAM,IAAI,UAAU,QAAQ,MAAM;AAAA,MACzD,CAAC;AACD;AAAA,IACF;AACA,mBAAe;AACf,WAAO,SAAS,KAAK;AAAA,EACvB,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,QAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC7C,iBAAW,MAAM;AACf,YAAI,SAAS,KAAK;AAChB,UAAAA,UAAS,SAAS,IAAI;AAAA,QACxB,OAAO;AACL,UAAAA,UAAS,QAAQ,YAAY;AAAA,QAC/B;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,eAAW,MAAM;AACf,MAAAA,UAAS,WAAW,IAAI;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,EAAE,MAAM,MAAM;AACb,IAAAA,UAAS,QAAQ,YAAY;AAAA,EAC/B,CAAC;AACH;AACA,IAAM,iBAAiB;AAAA,EACrB;AAAA,EACA;AACF;AAEA,SAAS,UAAU,OAAO;AACxB,QAAM,SAAS;AAAA,IACb,QAAQ,CAAC;AAAA,IACT,SAAS,CAAC;AAAA,IACV,SAAS,CAAC;AAAA,EACZ;AACA,QAAMC,WAA0B,uBAAO,OAAO,IAAI;AAClD,QAAM,KAAK,CAAC,GAAG,MAAM;AACnB,QAAI,EAAE,aAAa,EAAE,UAAU;AAC7B,aAAO,EAAE,SAAS,cAAc,EAAE,QAAQ;AAAA,IAC5C;AACA,QAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,aAAO,EAAE,OAAO,cAAc,EAAE,MAAM;AAAA,IACxC;AACA,WAAO,EAAE,KAAK,cAAc,EAAE,IAAI;AAAA,EACpC,CAAC;AACD,MAAI,WAAW;AAAA,IACb,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACA,QAAM,QAAQ,CAAC,SAAS;AACtB,QAAI,SAAS,SAAS,KAAK,QAAQ,SAAS,WAAW,KAAK,UAAU,SAAS,aAAa,KAAK,UAAU;AACzG;AAAA,IACF;AACA,eAAW;AACX,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,KAAK;AAClB,UAAM,kBAAkBA,SAAQ,QAAQ,MAAMA,SAAQ,QAAQ,IAAoB,uBAAO,OAAO,IAAI;AACpG,UAAM,eAAe,gBAAgB,MAAM,MAAM,gBAAgB,MAAM,IAAI,WAAW,UAAU,MAAM;AACtG,QAAI;AACJ,QAAI,QAAQ,aAAa,OAAO;AAC9B,aAAO,OAAO;AAAA,IAChB,WAAW,WAAW,MAAM,aAAa,QAAQ,IAAI,IAAI,GAAG;AAC1D,aAAO,OAAO;AAAA,IAChB,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AACA,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,SAAK,KAAK,IAAI;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,eAAe,UAAU,IAAI;AACpC,WAAS,QAAQ,CAACA,aAAY;AAC5B,UAAM,QAAQA,SAAQ;AACtB,QAAI,OAAO;AACT,MAAAA,SAAQ,kBAAkB,MAAM,OAAO,CAAC,QAAQ,IAAI,OAAO,EAAE;AAAA,IAC/D;AAAA,EACF,CAAC;AACH;AACA,SAAS,gBAAgBA,UAAS;AAChC,MAAI,CAACA,SAAQ,sBAAsB;AACjC,IAAAA,SAAQ,uBAAuB;AAC/B,eAAW,MAAM;AACf,MAAAA,SAAQ,uBAAuB;AAC/B,YAAM,QAAQA,SAAQ,kBAAkBA,SAAQ,gBAAgB,MAAM,CAAC,IAAI,CAAC;AAC5E,UAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,MACF;AACA,UAAI,aAAa;AACjB,YAAM,WAAWA,SAAQ;AACzB,YAAM,SAASA,SAAQ;AACvB,YAAM,QAAQ,CAAC,SAAS;AACtB,cAAM,QAAQ,KAAK;AACnB,cAAM,YAAY,MAAM,QAAQ;AAChC,cAAM,UAAU,MAAM,QAAQ,OAAO,CAAC,SAAS;AAC7C,cAAI,KAAK,WAAW,QAAQ;AAC1B,mBAAO;AAAA,UACT;AACA,gBAAM,OAAO,KAAK;AAClB,cAAIA,SAAQ,MAAM,IAAI,GAAG;AACvB,kBAAM,OAAO,KAAK;AAAA,cAChB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,WAAWA,SAAQ,QAAQ,IAAI,IAAI,GAAG;AACpC,kBAAM,QAAQ,KAAK;AAAA,cACjB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,yBAAa;AACb,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,CAAC;AACD,YAAI,MAAM,QAAQ,WAAW,WAAW;AACtC,cAAI,CAAC,YAAY;AACf,2BAAe,CAACA,QAAO,GAAG,KAAK,EAAE;AAAA,UACnC;AACA,eAAK;AAAA,YACH,MAAM,OAAO,MAAM,CAAC;AAAA,YACpB,MAAM,QAAQ,MAAM,CAAC;AAAA,YACrB,MAAM,QAAQ,MAAM,CAAC;AAAA,YACrB,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAI,YAAY;AAChB,SAAS,cAAcD,WAAU,OAAO,gBAAgB;AACtD,QAAM,KAAK;AACX,QAAM,QAAQ,eAAe,KAAK,MAAM,gBAAgB,EAAE;AAC1D,MAAI,CAAC,MAAM,QAAQ,QAAQ;AACzB,WAAO;AAAA,EACT;AACA,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA,UAAAA;AAAA,IACA;AAAA,EACF;AACA,iBAAe,QAAQ,CAACC,aAAY;AAClC,KAACA,SAAQ,oBAAoBA,SAAQ,kBAAkB,CAAC,IAAI,KAAK,IAAI;AAAA,EACvE,CAAC;AACD,SAAO;AACT;AAEA,SAAS,YAAY,MAAM,WAAW,MAAMC,eAAc,OAAO;AAC/D,QAAM,SAAS,CAAC;AAChB,OAAK,QAAQ,CAAC,SAAS;AACrB,UAAM,OAAO,OAAO,SAAS,WAAW,aAAa,MAAM,UAAUA,YAAW,IAAI;AACpF,QAAI,MAAM;AACR,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAGA,IAAI,gBAAgB;AAAA,EAClB,WAAW,CAAC;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,kBAAkB;AACpB;AAGA,SAAS,UAAU,QAAQ,SAAS,OAAO,MAAM;AAC/C,QAAM,iBAAiB,OAAO,UAAU;AACxC,QAAM,aAAa,OAAO,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,cAAc,IAAI,OAAO;AACvF,MAAI;AACJ,MAAI,OAAO,QAAQ;AACjB,QAAI,OAAO,OAAO,UAAU,MAAM,CAAC;AACnC,gBAAY,CAAC;AACb,WAAO,KAAK,SAAS,GAAG;AACtB,YAAM,YAAY,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,MAAM;AACxD,gBAAU,KAAK,KAAK,SAAS,CAAC;AAC9B,aAAO,KAAK,MAAM,GAAG,SAAS,EAAE,OAAO,KAAK,MAAM,YAAY,CAAC,CAAC;AAAA,IAClE;AACA,gBAAY,UAAU,OAAO,IAAI;AAAA,EACnC,OAAO;AACL,gBAAY,OAAO,UAAU,MAAM,UAAU,EAAE,OAAO,OAAO,UAAU,MAAM,GAAG,UAAU,CAAC;AAAA,EAC7F;AACA,QAAM,YAAY,KAAK,IAAI;AAC3B,MAAI,SAAS;AACb,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI,QAAQ;AACZ,MAAI,QAAQ,CAAC;AACb,MAAI,gBAAgB,CAAC;AACrB,MAAI,OAAO,SAAS,YAAY;AAC9B,kBAAc,KAAK,IAAI;AAAA,EACzB;AACA,WAAS,aAAa;AACpB,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,WAAS,QAAQ;AACf,QAAI,WAAW,WAAW;AACxB,eAAS;AAAA,IACX;AACA,eAAW;AACX,UAAM,QAAQ,CAAC,SAAS;AACtB,UAAI,KAAK,WAAW,WAAW;AAC7B,aAAK,SAAS;AAAA,MAChB;AAAA,IACF,CAAC;AACD,YAAQ,CAAC;AAAA,EACX;AACA,WAAS,UAAUF,WAAU,WAAW;AACtC,QAAI,WAAW;AACb,sBAAgB,CAAC;AAAA,IACnB;AACA,QAAI,OAAOA,cAAa,YAAY;AAClC,oBAAc,KAAKA,SAAQ;AAAA,IAC7B;AAAA,EACF;AACA,WAAS,iBAAiB;AACxB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBAAgB,MAAM;AAAA,MACtB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,WAAS,YAAY;AACnB,aAAS;AACT,kBAAc,QAAQ,CAACA,cAAa;AAClC,MAAAA,UAAS,QAAQ,SAAS;AAAA,IAC5B,CAAC;AAAA,EACH;AACA,WAAS,aAAa;AACpB,UAAM,QAAQ,CAAC,SAAS;AACtB,UAAI,KAAK,WAAW,WAAW;AAC7B,aAAK,SAAS;AAAA,MAChB;AAAA,IACF,CAAC;AACD,YAAQ,CAAC;AAAA,EACX;AACA,WAAS,eAAe,MAAM,UAAU,MAAM;AAC5C,UAAM,UAAU,aAAa;AAC7B,YAAQ,MAAM,OAAO,CAAC,WAAW,WAAW,IAAI;AAChD,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH;AAAA,MACF,KAAK;AACH,YAAI,WAAW,CAAC,OAAO,kBAAkB;AACvC;AAAA,QACF;AACA;AAAA,MACF;AACE;AAAA,IACJ;AACA,QAAI,aAAa,SAAS;AACxB,kBAAY;AACZ,gBAAU;AACV;AAAA,IACF;AACA,QAAI,SAAS;AACX,kBAAY;AACZ,UAAI,CAAC,MAAM,QAAQ;AACjB,YAAI,CAAC,UAAU,QAAQ;AACrB,oBAAU;AAAA,QACZ,OAAO;AACL,mBAAS;AAAA,QACX;AAAA,MACF;AACA;AAAA,IACF;AACA,eAAW;AACX,eAAW;AACX,QAAI,CAAC,OAAO,QAAQ;AAClB,YAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,QAAQ;AACpD,UAAI,UAAU,MAAM,UAAU,OAAO,OAAO;AAC1C,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF;AACA,aAAS;AACT,kBAAc,QAAQ,CAACA,cAAa;AAClC,MAAAA,UAAS,IAAI;AAAA,IACf,CAAC;AAAA,EACH;AACA,WAAS,WAAW;AAClB,QAAI,WAAW,WAAW;AACxB;AAAA,IACF;AACA,eAAW;AACX,UAAM,WAAW,UAAU,MAAM;AACjC,QAAI,aAAa,QAAQ;AACvB,UAAI,MAAM,QAAQ;AAChB,gBAAQ,WAAW,MAAM;AACvB,qBAAW;AACX,cAAI,WAAW,WAAW;AACxB,uBAAW;AACX,sBAAU;AAAA,UACZ;AAAA,QACF,GAAG,OAAO,OAAO;AACjB;AAAA,MACF;AACA,gBAAU;AACV;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX,QAAQ;AAAA,MACR;AAAA,MACA,UAAU,CAAC,SAAS,SAAS;AAC3B,uBAAe,MAAM,SAAS,IAAI;AAAA,MACpC;AAAA,IACF;AACA,UAAM,KAAK,IAAI;AACf;AACA,YAAQ,WAAW,UAAU,OAAO,MAAM;AAC1C,UAAM,UAAU,SAAS,KAAK,QAAQ;AAAA,EACxC;AACA,aAAW,QAAQ;AACnB,SAAO;AACT;AAGA,SAAS,eAAe,KAAK;AAC3B,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,MAAI,UAAU,CAAC;AACf,WAAS,UAAU;AACjB,cAAU,QAAQ,OAAO,CAAC,SAAS,KAAK,EAAE,WAAW,SAAS;AAAA,EAChE;AACA,WAAS,MAAM,SAAS,eAAe,cAAc;AACnD,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,MAAM,UAAU;AACf,gBAAQ;AACR,YAAI,cAAc;AAChB,uBAAa,MAAM,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,YAAQ,KAAK,MAAM;AACnB,WAAO;AAAA,EACT;AACA,WAAS,KAAKA,WAAU;AACtB,WAAO,QAAQ,KAAK,CAAC,UAAU;AAC7B,aAAOA,UAAS,KAAK;AAAA,IACvB,CAAC,KAAK;AAAA,EACR;AACA,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA,UAAU,CAAC,UAAU;AACnB,aAAO,QAAQ;AAAA,IACjB;AAAA,IACA,UAAU,MAAM,OAAO;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB;AAC3B;AACA,IAAM,kBAAkC,uBAAO,OAAO,IAAI;AAC1D,SAAS,mBAAmB,UAAU;AACpC,MAAI,CAAC,gBAAgB,QAAQ,GAAG;AAC9B,UAAM,SAAS,aAAa,QAAQ;AACpC,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,aAAa,eAAe,MAAM;AACxC,UAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,IACF;AACA,oBAAgB,QAAQ,IAAI;AAAA,EAC9B;AACA,SAAO,gBAAgB,QAAQ;AACjC;AACA,SAAS,aAAa,QAAQ,OAAOA,WAAU;AAC7C,MAAI;AACJ,MAAIG;AACJ,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,MAAM,aAAa,MAAM;AAC/B,QAAI,CAAC,KAAK;AACR,MAAAH,UAAS,QAAQ,GAAG;AACpB,aAAO;AAAA,IACT;AACA,IAAAG,QAAO,IAAI;AACX,UAAM,SAAS,mBAAmB,MAAM;AACxC,QAAI,QAAQ;AACV,mBAAa,OAAO;AAAA,IACtB;AAAA,EACF,OAAO;AACL,UAAM,SAAS,gBAAgB,MAAM;AACrC,QAAI,QAAQ;AACV,mBAAa,eAAe,MAAM;AAClC,YAAM,YAAY,OAAO,YAAY,OAAO,UAAU,CAAC,IAAI;AAC3D,YAAM,MAAM,aAAa,SAAS;AAClC,UAAI,KAAK;AACP,QAAAA,QAAO,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,cAAc,CAACA,OAAM;AACxB,IAAAH,UAAS,QAAQ,GAAG;AACpB,WAAO;AAAA,EACT;AACA,SAAO,WAAW,MAAM,OAAOG,OAAMH,SAAQ,EAAE,EAAE;AACnD;AAEA,SAAS,mBAAmBC,UAAS,cAAc;AACjD,QAAM,YAAYA,SAAQ;AAC1B;AAAA;AAAA,IAEE,aAAa,aAAa;AAAA,IAC1B;AACA,WAAO,cAAc;AAAA,EACvB;AACA,EAAAA,SAAQ,qBAAqB;AAC7B,MAAI,WAAW;AACb,eAAW,OAAO,sBAAsB;AACtC,4BAAsB,KAAK,CAAC,SAAS;AACnC,cAAM,UAAU,KAAK;AACrB,eAAO,KAAK,aAAaA,SAAQ,YAAY,QAAQ,WAAWA,SAAQ,UAAU,QAAQ,iBAAiB;AAAA,MAC7G,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,sBAAsBA,UAAS,MAAM;AAC5C,MAAI,CAAC,sBAAsB;AACzB,uBAAmB;AAAA,EACrB;AACA,WAAS,MAAM,KAAK;AAClB,QAAI;AACJ,QAAI,CAAC,qBAAqB,GAAG,KAAK,EAAE,OAAO,kBAAkB,GAAG,IAAI;AAClE;AAAA,IACF;AACA,UAAM,MAAM,yBAAyB,GAAG;AACxC,QAAI;AACJ,QAAI,IAAI,MAAM;AACZ,UAAI,OAAO,QAAQ,MAAM,KAAK,GAAG,EAAE,MAAM,CAAC;AAAA,IAC5C,OAAO;AACL,cAAQ,4BAA4B,IAAI;AACxC,UAAI,CAAC,4BAA4B,MAAM,QAAQ,CAAC,GAAG;AACjD;AAAA,MACF;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX,QAAQ,KAAK,MAAM,KAAK,IAAI,IAAI,kBAAkB;AAAA,MAClD,UAAUA,SAAQ;AAAA,MAClB;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA,qBAAqB,MAAM,SAAS;AAAA,MACpC,KAAK,UAAU,IAAI;AAAA,IACrB;AAAA,EACF;AACA,MAAI,KAAK,gBAAgB,CAAC,mBAAmBA,UAAS,KAAK,YAAY,GAAG;AACxE;AAAA,EACF;AACA,MAAI,CAAC,OAAO,KAAK,KAAK,KAAK,EAAE,QAAQ;AACnC;AAAA,EACF;AACA,MAAI,KAAK,WAAW;AAClB,WAAO,OAAO,OAAO,CAAC,GAAG,IAAI;AAC7B,WAAO,KAAK;AAAA,EACd;AACA,MAAI,CAAC,MAAM,OAAO,GAAG;AACnB,UAAM,SAAS;AAAA,EACjB;AACF;AAEA,SAAS,gBAAgB;AACzB;AACA,SAAS,eAAeA,UAAS;AAC/B,MAAI,CAACA,SAAQ,iBAAiB;AAC5B,IAAAA,SAAQ,kBAAkB;AAC1B,eAAW,MAAM;AACf,MAAAA,SAAQ,kBAAkB;AAC1B,sBAAgBA,QAAO;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AACA,SAAS,aAAaA,UAAS,OAAO;AACpC,MAAI,CAACA,SAAQ,aAAa;AACxB,IAAAA,SAAQ,cAAc;AAAA,EACxB,OAAO;AACL,IAAAA,SAAQ,cAAcA,SAAQ,YAAY,OAAO,KAAK,EAAE,KAAK;AAAA,EAC/D;AACA,MAAI,CAACA,SAAQ,gBAAgB;AAC3B,IAAAA,SAAQ,iBAAiB;AACzB,eAAW,MAAM;AACf,MAAAA,SAAQ,iBAAiB;AACzB,YAAM,EAAE,UAAU,OAAO,IAAIA;AAC7B,YAAM,SAASA,SAAQ;AACvB,aAAOA,SAAQ;AACf,UAAI;AACJ,UAAI,CAAC,UAAU,EAAE,MAAM,aAAa,QAAQ,IAAI;AAC9C;AAAA,MACF;AACA,YAAM,SAAS,IAAI,QAAQ,UAAU,QAAQ,MAAM;AACnD,aAAO,QAAQ,CAAC,SAAS;AACvB,qBAAa,UAAU,MAAM,CAAC,SAAS;AACrC,cAAI,OAAO,SAAS,UAAU;AAC5B,iBAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,cAAAA,SAAQ,QAAQ,IAAI,IAAI;AAAA,YAC1B,CAAC;AAAA,UACH,OAAO;AACL,gBAAI;AACF,oBAAM,SAAS;AAAA,gBACbA;AAAA,gBACA;AAAA,cACF;AACA,kBAAI,CAAC,OAAO,QAAQ;AAClB;AAAA,cACF;AACA,oBAAM,UAAUA,SAAQ;AACxB,kBAAI,SAAS;AACX,uBAAO,QAAQ,CAAC,SAAS;AACvB,0BAAQ,OAAO,IAAI;AAAA,gBACrB,CAAC;AAAA,cACH;AACA,oCAAsBA,UAAS,IAAI;AAAA,YACrC,SAAS,KAAK;AACZ,sBAAQ,MAAM,GAAG;AAAA,YACnB;AAAA,UACF;AACA,yBAAeA,QAAO;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAM,YAAY,CAAC,SAAS;AAC1B,QAAMA,WAAU;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACA,QAAM,UAAUA,SAAQ;AACxB,SAAO,CAAC,EAAE,WAAW,QAAQ,IAAI,KAAK,IAAI;AAC5C;AACA,IAAM,YAAY,CAAC,OAAOD,cAAa;AACrC,QAAM,eAAe,YAAY,OAAO,MAAM,iBAAiB,CAAC;AAChE,QAAM,cAAc,UAAU,YAAY;AAC1C,MAAI,CAAC,YAAY,QAAQ,QAAQ;AAC/B,QAAI,eAAe;AACnB,QAAIA,WAAU;AACZ,iBAAW,MAAM;AACf,YAAI,cAAc;AAChB,UAAAA;AAAA,YACE,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,MAAM;AACX,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,QAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,QAAM,UAAU,CAAC;AACjB,MAAI,cAAc;AAClB,cAAY,QAAQ,QAAQ,CAAC,SAAS;AACpC,UAAM,EAAE,UAAU,OAAO,IAAI;AAC7B,QAAI,WAAW,cAAc,aAAa,cAAc;AACtD;AAAA,IACF;AACA,mBAAe;AACf,iBAAa;AACb,YAAQ,KAAK,WAAW,UAAU,MAAM,CAAC;AACzC,UAAM,mBAAmB,SAAS,QAAQ,MAAM,SAAS,QAAQ,IAAoB,uBAAO,OAAO,IAAI;AACvG,QAAI,CAAC,iBAAiB,MAAM,GAAG;AAC7B,uBAAiB,MAAM,IAAI,CAAC;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,cAAY,QAAQ,QAAQ,CAAC,SAAS;AACpC,UAAM,EAAE,UAAU,QAAQ,KAAK,IAAI;AACnC,UAAMC,WAAU,WAAW,UAAU,MAAM;AAC3C,UAAM,eAAeA,SAAQ,iBAAiBA,SAAQ,eAA+B,oBAAI,IAAI;AAC7F,QAAI,CAAC,aAAa,IAAI,IAAI,GAAG;AAC3B,mBAAa,IAAI,IAAI;AACrB,eAAS,QAAQ,EAAE,MAAM,EAAE,KAAK,IAAI;AAAA,IACtC;AAAA,EACF,CAAC;AACD,UAAQ,QAAQ,CAACA,aAAY;AAC3B,UAAM,EAAE,UAAU,OAAO,IAAIA;AAC7B,QAAI,SAAS,QAAQ,EAAE,MAAM,EAAE,QAAQ;AACrC,mBAAaA,UAAS,SAAS,QAAQ,EAAE,MAAM,CAAC;AAAA,IAClD;AAAA,EACF,CAAC;AACD,SAAOD,YAAW,cAAcA,WAAU,aAAa,OAAO,IAAI;AACpE;AACA,IAAM,WAAW,CAAC,SAAS;AACzB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,UAAU,OAAO,SAAS,WAAW,aAAa,MAAM,IAAI,IAAI;AACtE,QAAI,CAAC,SAAS;AACZ,aAAO,IAAI;AACX;AAAA,IACF;AACA,cAAU,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW;AACvC,UAAI,OAAO,UAAU,SAAS;AAC5B,cAAM,OAAO,YAAY,OAAO;AAChC,YAAI,MAAM;AACR,kBAAQ;AAAA,YACN,GAAG;AAAA,YACH,GAAG;AAAA,UACL,CAAC;AACD;AAAA,QACF;AAAA,MACF;AACA,aAAO,IAAI;AAAA,IACb,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,oBAAoB,UAAU,MAAM;AAC3C,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,EACL;AACA,aAAW,OAAO,MAAM;AACtB,UAAM,QAAQ,KAAK,GAAG;AACtB,UAAM,YAAY,OAAO;AACzB,QAAI,OAAO,+BAA+B;AACxC,UAAI,UAAU,QAAQ,UAAU,cAAc,YAAY,cAAc,WAAW;AACjF,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF,WAAW,cAAc,OAAO,OAAO,GAAG,GAAG;AAC3C,aAAO,GAAG,IAAI,QAAQ,WAAW,QAAQ,IAAI;AAAA,IAC/C;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,oCAAoC;AAAA,EACtC,GAAG;AAAA,EACH,QAAQ;AACZ;AAIA,IAAM,aAAa;AACnB,IAAM,cAAc;AAIpB,IAAM,sBAAuB,gBAAgB,KAAK,IAAI;AAKtD,IAAI,QAAQ,CAAC;AAIb,SAAS,aAAa,MAAM;AACxB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,OAAO,OAAO,KAAK,SAAS,aAAa,KAAK,KAAK,IAAI,KAAK;AAClE,QAAI,SAAS,MAAM;AACf,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAIA,SAAS,YAAY,MAAM,aAAa,OAAO;AAC3C,MAAI,OAAO,aAAa,IAAI;AAC5B,MAAI,MAAM;AAEN,QAAI,KAAK,WAAW;AAChB,WAAK,YAAY;AAAA,IACrB;AACA,WAAO;AAAA,EACX;AAEA,SAAO;AAAA,IACH,MAAM;AAAA,IACN,WAAW;AAAA,EACf;AACA,QAAM,KAAK,IAAI;AACf,SAAO;AACX;AAIA,SAAS,cAAc;AACnB,MAAI,SAAS,iBAAiB;AAC1B,WAAO,YAAY,SAAS,eAAe;AAAA,EAC/C;AACA,QAAM,KAAK;AAAA,IACP,MAAM,MAAM;AACR,aAAO,SAAS;AAAA,IACpB;AAAA,EACJ,CAAC;AACL;AAIA,SAAS,eAAe,MAAM;AAC1B,UAAQ,MAAM,OAAO,CAAC,SAAS,SAAS,QACpC,UAAU,OAAO,KAAK,SAAS,aAAa,KAAK,KAAK,IAAI,KAAK,KAAK;AAC5E;AAIA,SAAS,gBAAgB;AACrB,SAAO;AACX;AAKA,SAAS,QAAQA,WAAU;AACvB,QAAM,MAAM;AACZ,MAAI,IAAI,cAAc,IAAI,eAAe,WAAW;AAChD,IAAAA,UAAS;AAAA,EACb,OACK;AACD,QAAI,iBAAiB,oBAAoBA,SAAQ;AAAA,EACrD;AACJ;AAKA,IAAI,WAAW;AAIf,IAAM,iBAAiB;AAAA,EACnB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAChB;AAIA,SAAS,UAAU,MAAM;AACrB,MAAI,CAAC,KAAK,UAAU;AAChB;AAAA,EACJ;AACA,QAAM,WAAW,KAAK;AACtB,MAAI,CAAC,SAAS,aAAa;AACvB,aAAS,cAAc,WAAW,MAAM;AACpC,aAAO,SAAS;AAChB,UAAI,UAAU;AACV,iBAAS,IAAI;AAAA,MACjB;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAIA,SAAS,eAAe,MAAM,WAAW;AACrC,MAAI,CAAC,KAAK,UAAU;AAChB;AAAA,EACJ;AACA,QAAM,WAAW,KAAK;AACtB,MAAI,CAAC,SAAS,aAAa;AACvB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,YAAM,OAAO,UAAU,CAAC;AACxB;AAAA;AAAA,QAEC,KAAK,cAAc,KAAK,WAAW,SAAS;AAAA,QAExC,KAAK,SAAS,gBACX,KAAK,OAAO,mBAAmB,MAC3B;AAAA,QAAS;AACjB,YAAI,CAAC,SAAS,QAAQ;AAClB,oBAAU,IAAI;AAAA,QAClB;AACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAIA,SAAS,kBAAkB,MAAM,MAAM;AACnC,OAAK,SAAS,SAAS,QAAQ,MAAM,cAAc;AACvD;AAIA,SAAS,cAAc,MAAM;AACzB,MAAI,WAAW,KAAK;AACpB,MAAI,YAAY,SAAS,UAAU;AAE/B;AAAA,EACJ;AACA,QAAM,OAAO,OAAO,KAAK,SAAS,aAAa,KAAK,KAAK,IAAI,KAAK;AAClE,MAAI,CAAC,QAAQ,CAAC,QAAQ;AAElB;AAAA,EACJ;AACA,MAAI,CAAC,UAAU;AACX,eAAW;AAAA,MACP,QAAQ;AAAA,IACZ;AACA,SAAK,WAAW;AAAA,EACpB;AAEA,WAAS,WAAW,IAAI,OAAO,iBAAiB,eAAe,KAAK,MAAM,IAAI,CAAC;AAC/E,oBAAkB,MAAM,IAAI;AAE5B,MAAI,CAAC,SAAS,QAAQ;AAClB,cAAU,IAAI;AAAA,EAClB;AACJ;AAIA,SAAS,iBAAiB;AACtB,gBAAc,EAAE,QAAQ,aAAa;AACzC;AAIA,SAAS,aAAa,MAAM;AACxB,MAAI,CAAC,KAAK,UAAU;AAChB;AAAA,EACJ;AACA,QAAM,WAAW,KAAK;AAEtB,MAAI,SAAS,aAAa;AACtB,iBAAa,SAAS,WAAW;AACjC,WAAO,SAAS;AAAA,EACpB;AAEA,MAAI,SAAS,UAAU;AACnB,aAAS,SAAS,WAAW;AAC7B,WAAO,SAAS;AAAA,EACpB;AACJ;AAIA,SAAS,aAAa,IAAI;AACtB,QAAM,YAAY,aAAa;AAC/B,MAAI,aAAa,IAAI;AAEjB,eAAW;AACX,QAAI,WAAW;AACX,oBAAc,EAAE,QAAQ,YAAY;AAAA,IACxC;AAAA,EACJ;AACA,MAAI,WAAW;AAEX,mBAAe;AACf;AAAA,EACJ;AAEA,UAAQ,cAAc;AAC1B;AAIA,SAAS,mBAAmB,MAAM;AAC9B,GAAC,OAAO,CAAC,IAAI,IAAI,cAAc,GAAG,QAAQ,CAACI,UAAS;AAChD,QAAI,CAACA,MAAK,UAAU;AAChB,MAAAA,MAAK,WAAW;AAAA,QACZ,QAAQ;AAAA,MACZ;AACA;AAAA,IACJ;AACA,UAAM,WAAWA,MAAK;AACtB,aAAS;AACT,QAAI,SAAS,SAAS,KAAK,CAAC,SAAS,UAAU;AAC3C;AAAA,IACJ;AAEA,UAAM,WAAW,SAAS;AAE1B,aAAS,WAAW;AAAA,EACxB,CAAC;AACL;AAIA,SAAS,cAAc,MAAM;AACzB,MAAI,MAAM;AACN,UAAM,OAAO,aAAa,IAAI;AAC9B,QAAI,MAAM;AACN,yBAAmB,IAAI;AAAA,IAC3B;AAAA,EACJ,OACK;AACD,uBAAmB;AAAA,EACvB;AACJ;AAIA,SAAS,oBAAoB,UAAU;AACnC,GAAC,WAAW,CAAC,QAAQ,IAAI,cAAc,GAAG,QAAQ,CAAC,SAAS;AACxD,QAAI,CAAC,KAAK,UAAU;AAEhB,oBAAc,IAAI;AAClB;AAAA,IACJ;AACA,UAAMC,YAAW,KAAK;AACtB,QAAIA,UAAS,QAAQ;AACjB,MAAAA,UAAS;AACT,UAAI,CAACA,UAAS,QAAQ;AAElB,cAAM,OAAO,OAAO,KAAK,SAAS,aAAa,KAAK,KAAK,IAAI,KAAK;AAClE,YAAI,CAAC,MAAM;AACP;AAAA,QACJ,WACSA,UAAS,UAAU;AACxB,4BAAkB,MAAM,IAAI;AAAA,QAChC,OACK;AACD,wBAAc,IAAI;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAIA,SAAS,eAAe,MAAM;AAC1B,MAAI,MAAM;AACN,UAAM,OAAO,aAAa,IAAI;AAC9B,QAAI,MAAM;AACN,0BAAoB,IAAI;AAAA,IAC5B;AAAA,EACJ,OACK;AACD,wBAAoB;AAAA,EACxB;AACJ;AAIA,SAAS,QAAQ,MAAM,aAAa,OAAO;AACvC,QAAM,OAAO,YAAY,MAAM,UAAU;AACzC,gBAAc,IAAI;AAClB,SAAO;AACX;AAIA,SAAS,cAAc,MAAM;AACzB,QAAM,OAAO,aAAa,IAAI;AAC9B,MAAI,MAAM;AACN,iBAAa,IAAI;AACjB,mBAAe,IAAI;AAAA,EACvB;AACJ;AAKA,SAAS,aAAa,QAAQ,QAAQ;AAClC,MAAI,OAAO,SAAS,OAAO,QAAQ,OAAO,SAAS,OAAO,MAAM;AAC5D,WAAO;AAAA,EACX;AACA,QAAM,kBAAkB,OAAO;AAC/B,QAAM,kBAAkB,OAAO;AAC/B,aAAW,OAAO,mCAAmC;AACjD,QAAI,gBAAgB,GAAG,MAAM,gBAAgB,GAAG,GAAG;AAC/C,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,iBAAiB,OAAO,eAAe,GAAG;AACjD,QAAM,QAAQ,MAAM,QAAQ,cAAc,EAAE;AAC5C,WAAS,QAAQ,QAAQ;AACvB,WAAO,SAAS,GAAG;AACjB,gBAAU;AAAA,IACZ;AACA,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,UAAU,IAAI;AAChB,UAAM,MAAM,SAAS,KAAK;AAC1B,WAAO,MAAM,GAAG,IAAI,IAAI,QAAQ,GAAG;AAAA,EACrC,WAAW,UAAU,OAAO;AAC1B,QAAI,QAAQ;AACZ,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,gBAAQ;AACR;AAAA,MACF,KAAK;AACH,gBAAQ;AAAA,IACZ;AACA,QAAI,OAAO;AACT,UAAI,MAAM,WAAW,MAAM,MAAM,GAAG,MAAM,SAAS,MAAM,MAAM,CAAC;AAChE,UAAI,MAAM,GAAG,GAAG;AACd,eAAO;AAAA,MACT;AACA,YAAM,MAAM;AACZ,aAAO,MAAM,MAAM,IAAI,QAAQ,GAAG,IAAI;AAAA,IACxC;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,YAAY;AAClB,SAAS,eAAe,QAAQ,MAAM;AACpC,OAAK,MAAM,SAAS,EAAE,QAAQ,CAAC,QAAQ;AACrC,UAAM,QAAQ,IAAI,KAAK;AACvB,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,QAAQ;AACf;AAAA,MACF,KAAK;AACH,eAAO,QAAQ;AACf;AAAA,IACJ;AAAA,EACF,CAAC;AACH;AAKA,IAAM,iBAAiB,CAAC,SAAS,QAAQ;AAIzC,IAAM,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACJ;AAIA,SAAS,oBAAoB,OAAO,KAAK;AACrC,MAAI,UAAU,OAAO,UAAU,QAAQ;AACnC,WAAO;AAAA,EACX;AACA,MAAI,UAAU,MAAM,UAAU,SAAS;AACnC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAIA,SAAS,gBAAgB,SAAS;AAE9B,QAAM,OAAO,QAAQ,aAAa,WAAW;AAC7C,QAAM,OAAO,OAAO,SAAS,YAAY,aAAa,MAAM,IAAI;AAChE,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,EACX;AAEA,QAAM,iBAAiB;AAAA,IACnB,GAAG;AAAA,IACH,QAAQ,QAAQ,aAAa,QAAQ,UAAU,SAAS,WAAW;AAAA,EACvE;AAEA,iBAAe,QAAQ,CAAC,SAAS;AAC7B,UAAM,QAAQ,QAAQ,aAAa,UAAU,IAAI;AACjD,QAAI,OAAO;AACP,qBAAe,IAAI,IAAI;AAAA,IAC3B;AAAA,EACJ,CAAC;AAED,QAAM,WAAW,QAAQ,aAAa,aAAa;AACnD,MAAI,OAAO,aAAa,UAAU;AAC9B,mBAAe,SAAS,iBAAiB,QAAQ;AAAA,EACrD;AAEA,QAAM,OAAO,QAAQ,aAAa,WAAW;AAC7C,MAAI,OAAO,SAAS,UAAU;AAC1B,mBAAe,gBAAgB,IAAI;AAAA,EACvC;AAEA,oBAAkB,QAAQ,CAAC,SAAS;AAChC,UAAM,MAAM,UAAU;AACtB,UAAM,QAAQ,oBAAoB,QAAQ,aAAa,GAAG,GAAG,GAAG;AAChE,QAAI,OAAO,UAAU,WAAW;AAC5B,qBAAe,IAAI,IAAI;AAAA,IAC3B;AAAA,EACJ,CAAC;AAED,QAAM,OAAO,QAAQ,aAAa,WAAW;AAC7C,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAKA,IAAM,WAAW,SACb,aACA,SACA,aACA,YACA,aACA,SACA,cACA,YACA;AAIJ,SAAS,aAAa,MAAM;AACxB,QAAMC,SAAQ,CAAC;AACf,OAAK,iBAAiB,QAAQ,EAAE,QAAQ,CAAC,SAAS;AAE9C,UAAM,QAAQ,KAAK,mBAAmB,KAAK,KAAK,QAAQ,YAAY,MAAM,QACpE,gBAAgB,IAAI,IACpB;AACN,QAAI,OAAO;AACP,MAAAA,OAAM,KAAK;AAAA,QACP;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AACD,SAAOA;AACX;AAEA,SAAS,WAAW,MAAM,YAAY;AACpC,MAAI,oBAAoB,KAAK,QAAQ,QAAQ,MAAM,KAAK,KAAK;AAC7D,aAAW,QAAQ,YAAY;AAC7B,yBAAqB,MAAM,OAAO,OAAO,WAAW,IAAI,IAAI;AAAA,EAC9D;AACA,SAAO,4CAA4C,oBAAoB,MAAM,OAAO;AACtF;AAEA,IAAI;AACJ,SAAS,eAAe;AACtB,MAAI;AACF,aAAS,OAAO,aAAa,aAAa,WAAW;AAAA;AAAA,MAEnD,YAAY,CAAC,MAAM;AAAA,IACrB,CAAC;AAAA,EACH,SAAS,KAAK;AACZ,aAAS;AAAA,EACX;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,WAAW,QAAQ;AACrB,iBAAa;AAAA,EACf;AACA,SAAO,SAAS,OAAO,WAAW,IAAI,IAAI;AAC5C;AAKA,SAAS,YAAY,UAAU;AAC3B,QAAM,eAAe,oBAAI,IAAI,CAAC,SAAS,CAAC;AACxC,GAAC,YAAY,QAAQ,EAAE,QAAQ,CAAC,SAAS;AACrC,QAAI,SAAS,IAAI,GAAG;AAChB,mBAAa,IAAI,cAAc,SAAS,IAAI,CAAC;AAAA,IACjD;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAIA,SAAS,aAAa,KAAK,SAAS,wBAAwB,aAAa;AACrE,QAAM,aAAa,IAAI;AAEvB,MAAI,aAAa;AACb,UAAM,qBAAqB,YAAY;AACvC,UAAM,KAAK,kBAAkB,EAAE,QAAQ,CAAC,SAAS;AAC7C,iBAAW,IAAI,IAAI;AAAA,IACvB,CAAC;AAAA,EACL;AAEA,QAAM,eAAe,CAAC;AACtB,UAAQ,QAAQ,CAAC,SAAS;AACtB,QAAI,CAAC,WAAW,SAAS,IAAI,GAAG;AAE5B,iBAAW,IAAI,IAAI;AACnB,mBAAa,KAAK,IAAI;AAAA,IAC1B,WACS,uBAAuB,IAAI,IAAI,GAAG;AAEvC,mBAAa,KAAK,IAAI;AAAA,IAC1B;AAAA,EACJ,CAAC;AAED,yBAAuB,QAAQ,CAAC,SAAS;AACrC,QAAI,CAAC,QAAQ,IAAI,IAAI,GAAG;AAEpB,iBAAW,OAAO,IAAI;AAAA,IAC1B;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAKA,SAAS,WAAW,KAAK,QAAQ,uBAAuB;AACpD,QAAM,WAAW,IAAI;AAErB,GAAC,yBAAyB,CAAC,GAAG,QAAQ,CAAC,SAAS;AAC5C,aAAS,eAAe,IAAI;AAAA,EAChC,CAAC;AAED,QAAM,gBAAgB,CAAC;AACvB,aAAW,QAAQ,QAAQ;AACvB,QAAI,CAAC,SAAS,iBAAiB,IAAI,GAAG;AAClC,oBAAc,KAAK,IAAI;AACvB,eAAS,YAAY,MAAM,OAAO,IAAI,CAAC;AAAA,IAC3C;AAAA,EACJ;AACA,SAAO;AACX;AAKA,SAAS,gBAAgB,SAAS,OAAO,UAAU;AAE/C,MAAI;AACJ,MAAI;AACA,WAAO,SAAS,cAAc,MAAM;AAAA,EACxC,SACO,KAAK;AACR,WAAO;AAAA,EACX;AAEA,QAAM,iBAAiB,MAAM;AAC7B,QAAM,aAAa,UAAU,UAAU,cAAc;AAErD,QAAM,UAAU,QAAQ,mBAAmB;AAE3C,QAAM,OAAO,WAAW,WAAW,WAAW,IAAI,GAAG;AAAA,IACjD,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,GAAG,WAAW;AAAA,EAClB,CAAC;AACD,OAAK,YAAY,iBAAiB,IAAI;AAEtC,QAAM,MAAM,KAAK,WAAW,CAAC;AAE7B,QAAM,wBAAwB,QAAQ;AACtC,WAAS,IAAI,GAAG,IAAI,sBAAsB,QAAQ,KAAK;AACnD,UAAM,OAAO,sBAAsB,KAAK,CAAC;AACzC,UAAM,OAAO,KAAK;AAClB,QAAI,SAAS,WAAW,CAAC,IAAI,aAAa,IAAI,GAAG;AAC7C,UAAI,aAAa,MAAM,KAAK,KAAK;AAAA,IACrC;AAAA,EACJ;AAEA,QAAM,eAAe,YAAY,MAAM,IAAI;AAC3C,QAAM,eAAe,aAAa,KAAK,cAAc,IAAI,IAAI,WAAW,QAAQ,YAAY,GAAG,OAAO;AAEtG,QAAM,cAAc,WAAW,KAAK,eAAe,SAC7C;AAAA,IACE,kBAAkB;AAAA,EACtB,IACE,CAAC,GAAG,WAAW,QAAQ,WAAW;AAExC,QAAM,UAAU;AAAA,IACZ,GAAG;AAAA,IACH,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACJ;AACA,MAAI,mBAAmB,IAAI;AAE3B,MAAI,QAAQ,YAAY;AACpB,YAAQ,WAAW,aAAa,KAAK,OAAO;AAAA,EAChD;AACA,SAAO;AACX;AAEA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,QAAQ,GAAG;AACvI;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,wBAAwB,gBAAgB,GAAG;AACpD;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,UAAU,UAAU,GAAG,IAAI;AACpC;AAEA,IAAM,cAAc;AAAA,EAChB,SAAS;AACb;AACA,IAAM,gBAAgB;AAAA,EAClB,oBAAoB;AACxB;AACA,IAAM,eAAe;AAAA,EACjB,oBAAoB;AACxB;AAEA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACV;AACA,IAAM,eAAe;AAAA,EACjB,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,cAAc;AAClB;AACA,WAAW,UAAU,cAAc;AAC/B,QAAM,OAAO,aAAa,MAAM;AAChC,aAAW,QAAQ,YAAY;AAC3B,SAAK,SAAS,MAAM,IAAI,IAAI,WAAW,IAAI;AAAA,EAC/C;AACJ;AAIA,SAAS,QAAQ,OAAO;AACpB,SAAO,SAAS,MAAM,MAAM,YAAY,IAAI,OAAO;AACvD;AAIA,SAAS,iBAAiB,SAAS,OAAO,UAAU,SAAS;AAEzD,QAAM,iBAAiB,MAAM;AAC7B,QAAM,aAAa,UAAU,UAAU,cAAc;AACrD,QAAM,gBAAgB,WAAW;AAEjC,QAAM,UAAU,QAAQ,mBAAmB;AAE3C,QAAM,OAAO,WAAW,WAAW,MAAM;AAAA,IACrC,GAAG;AAAA,IACH,OAAO,SAAS,QAAQ;AAAA,IACxB,QAAQ,SAAS,SAAS;AAAA,EAC9B,CAAC;AAED,QAAM,eAAe,YAAY,MAAM,IAAI;AAC3C,QAAM,eAAe,aAAa,SAAS,cAAc,IAAI,IAAI,WAAW,QAAQ,YAAY,CAAC;AAEjG,QAAM,MAAM,SAAS,IAAI;AACzB,QAAM,YAAY;AAAA,IACd,SAAS;AAAA,IACT,SAAS,QAAQ,cAAc,KAAK;AAAA,IACpC,UAAU,QAAQ,cAAc,MAAM;AAAA,IACtC,GAAG;AAAA,IACH,GAAI,UAAU,gBAAgB;AAAA,EAClC;AACA,MAAI,eAAe,QAAQ;AACvB,cAAU,gBAAgB,IAAI;AAAA,EAClC;AACA,QAAM,cAAc,WAAW,SAAS,WAAW,WAAW,QAAQ,WAAW;AAEjF,QAAM,UAAU;AAAA,IACZ,GAAG;AAAA,IACH,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACJ;AACA,UAAQ,mBAAmB,IAAI;AAC/B,SAAO;AACX;AAKA,IAAI,aAAa;AAIjB,SAAS,oBAAoB;AACzB,MAAI,CAAC,YAAY;AACb,iBAAa;AACb,eAAW,MAAM;AACb,UAAI,YAAY;AACZ,qBAAa;AACb,gBAAQ;AAAA,MACZ;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAIA,SAAS,QAAQ,UAAU,cAAc,OAAO;AAE5C,QAAM,cAAc,uBAAO,OAAO,IAAI;AACtC,WAASC,SAAQ,MAAM,MAAM;AACzB,UAAM,EAAE,UAAU,QAAQ,KAAK,IAAI;AACnC,UAAMN,WAAU,WAAW,UAAU,MAAM;AAC3C,UAAM,aAAaA,SAAQ,MAAM,IAAI;AACrC,QAAI,YAAY;AACZ,aAAO;AAAA,QACH,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,IACJ;AACA,QAAIA,SAAQ,QAAQ,IAAI,IAAI,GAAG;AAC3B,aAAO;AAAA,QACH,QAAQ;AAAA,MACZ;AAAA,IACJ;AACA,QAAI,QAAQ,CAAC,UAAU,IAAI,GAAG;AAC1B,YAAM,sBAAsB,YAAY,QAAQ,MAC3C,YAAY,QAAQ,IAAI,uBAAO,OAAO,IAAI;AAC/C,YAAM,MAAM,oBAAoB,MAAM,MACjC,oBAAoB,MAAM,IAAI,oBAAI,IAAI;AAC3C,UAAI,IAAI,IAAI;AAAA,IAChB;AACA,WAAO;AAAA,MACH,QAAQ;AAAA,IACZ;AAAA,EACJ;AAEA,GAAC,WAAW,CAAC,QAAQ,IAAI,cAAc,GAAG,QAAQ,CAAC,iBAAiB;AAChE,UAAM,OAAO,OAAO,aAAa,SAAS,aACpC,aAAa,KAAK,IAClB,aAAa;AACnB,QAAI,CAAC,QAAQ,CAAC,KAAK,kBAAkB;AACjC;AAAA,IACJ;AAEA,QAAI,kBAAkB;AAEtB,QAAI,SAAS;AAIb,aAAS,OAAO,SAAS,OAAO,UAAU;AACtC,UAAI,CAAC,QAAQ;AACT,iBAAS;AACT,2BAAmB,YAAY;AAAA,MACnC;AACA,UAAI,QAAQ,QAAQ,YAAY,MAAM,OAAO;AAEzC,cAAM,OAAO,MAAM;AACnB,cAAM,SAAS,SAAS,WACnB,SAAS,OACJ,QACA,SAAS,UACL,SAAS,KAAK,QAAQ,cAAc,MAAM,KAC1C;AACd,YAAI,OAAO,WAAW,WAAW;AAC7B,2BAAiB,SAAS,OAAO;AAAA,YAC7B,GAAG;AAAA,YACH,GAAG;AAAA,UACP,GAAG,MAAM;AACT;AAAA,QACJ;AAAA,MACJ;AACA,sBAAgB,SAAS,OAAO,QAAQ;AAAA,IAC5C;AAEA,iBAAa,IAAI,EAAE,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM;AAE5C,YAAM,UAAU,KAAK,mBAAmB;AACxC,UAAI,CAAC,SAAS;AAEV,cAAM,EAAE,QAAQ,KAAK,IAAIM,SAAQ,MAAM,MAAM,IAAI;AACjD,YAAI,MAAM;AAEN,iBAAO,MAAM,OAAO,IAAI;AACxB;AAAA,QACJ;AAEA,0BAAkB,mBAAmB,WAAW;AAChD,aAAK,mBAAmB,IAAI;AAAA,UACxB,GAAG;AAAA,UACH;AAAA,QACJ;AACA;AAAA,MACJ;AAEA,UAAI;AACJ,UAAI,CAAC,aAAa,SAAS,KAAK,GAAG;AAE/B,cAAM,YAAY,QAAQ;AAC1B,YAAI,cAAc,WAAW;AACzB;AAAA,QACJ;AACA,eAAOA,SAAQ,MAAM,MAAM,KAAK;AAChC,YAAI,CAAC,KAAK,MAAM;AAEZ,kBAAQ,SAAS,KAAK;AACtB;AAAA,QACJ;AAAA,MACJ,OACK;AAED,eAAOA,SAAQ,MAAM,MAAM,QAAQ,SAAS,MAAM,IAAI;AACtD,YAAI,CAAC,KAAK,MAAM;AAEZ,4BACI,mBAAmB,KAAK,WAAW;AACvC,iBAAO,OAAO,SAAS;AAAA,YACnB,GAAG;AAAA,YACH,QAAQ,KAAK;AAAA,UACjB,CAAC;AACD;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO,MAAM,OAAO,KAAK,IAAI;AAAA,IACjC,CAAC;AAED,QAAI,aAAa,aAAa,CAAC,iBAAiB;AAE5C,oBAAc,IAAI;AAAA,IACtB,WACS,eAAe,iBAAiB;AAErC,cAAQ,MAAM,IAAI;AAAA,IACtB,WACS,UAAU,aAAa,UAAU;AAEtC,0BAAoB,YAAY;AAAA,IACpC;AAAA,EACJ,CAAC;AAED,aAAW,YAAY,aAAa;AAChC,UAAM,sBAAsB,YAAY,QAAQ;AAChD,eAAW,UAAU,qBAAqB;AACtC,YAAM,MAAM,oBAAoB,MAAM;AACtC,gBAAU,MAAM,KAAK,GAAG,EAAE,IAAI,CAAC,UAAU;AAAA,QACrC;AAAA,QACA;AAAA,QACA;AAAA,MACJ,EAAE,GAAG,iBAAiB;AAAA,IAC1B;AAAA,EACJ;AACJ;AAIA,SAAS,YAAY,MAAM;AAEvB,QAAM,OAAO,aAAa,IAAI;AAC9B,MAAI,CAAC,MAAM;AACP,YAAQ;AAAA,MACJ,MAAM;AAAA,MACN,WAAW;AAAA,IACf,GAAG,IAAI;AAAA,EACX,OACK;AACD,YAAQ,IAAI;AAAA,EAChB;AACJ;AAEA,SAAS,aAAa,MAAM,gBAAgB,eAAe,OAAO;AAE9D,QAAM,WAAW,YAAY,IAAI;AACjC,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AAEA,QAAM,WAAW,aAAa,IAAI;AAElC,QAAM,UAAU,oBAAoB,mCAAmC,kBAAkB,CAAC,CAAC;AAE3F,QAAM,SAAS,gBAAgB,SAAS,cAAc,MAAM,GAAG;AAAA,IAC3D;AAAA,IACA,MAAM;AAAA,IACN,gBAAgB;AAAA,EACpB,GAAG,QAAQ;AACX,SAAO,eACD,OAAO,YACP;AACV;AAIA,SAAS,aAAa;AAClB,SAAO;AACX;AAIA,SAAS,UAAU,MAAM,gBAAgB;AACrC,SAAO,aAAa,MAAM,gBAAgB,KAAK;AACnD;AAIA,SAAS,WAAW,MAAM,gBAAgB;AACtC,SAAO,aAAa,MAAM,gBAAgB,IAAI;AAClD;AAIA,SAAS,WAAW,MAAM,gBAAgB;AAEtC,QAAM,WAAW,YAAY,IAAI;AACjC,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AAEA,QAAM,UAAU,oBAAoB,mCAAmC,kBAAkB,CAAC,CAAC;AAE3F,SAAO,UAAU,UAAU,OAAO;AACtC;AAIA,SAAS,KAAK,MAAM;AAChB,MAAI,MAAM;AACN,gBAAY,IAAI;AAAA,EACpB,OACK;AACD,YAAQ;AAAA,EACZ;AACJ;AAIA,IAAI,OAAO,aAAa,eAAe,OAAO,WAAW,aAAa;AAElE,cAAY;AACZ,QAAMC,WAAU;AAEhB,MAAIA,SAAQ,mBAAmB,QAAQ;AACnC,UAAM,UAAUA,SAAQ;AACxB,UAAM,MAAM;AACZ,QAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACjD,OAAC,mBAAmB,QAAQ,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC,SAAS;AAC/D,YAAI;AACA;AAAA;AAAA,YAEA,OAAO,SAAS,YACZ,SAAS,QACT,gBAAgB;AAAA,YAEhB,OAAO,KAAK,UAAU,YACtB,OAAO,KAAK,WAAW;AAAA,YAEvB,CAAC,cAAc,IAAI;AAAA,YAAG;AACtB,oBAAQ,MAAM,GAAG;AAAA,UACrB;AAAA,QACJ,SACO,GAAG;AACN,kBAAQ,MAAM,GAAG;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,aAAW,MAAM;AACb,iBAAa,OAAO;AACpB,YAAQ;AAAA,EACZ,CAAC;AACL;AAKA,SAAS,YAAYP,UAAS,QAAQ;AAClC,qBAAmBA,UAAS,WAAW,KAAK;AAChD;AAIA,SAAS,aAAaA,UAAS;AAC3B,qBAAmBA,UAAS,IAAI;AACpC;AAKA,aAAa,IAAI,cAAc;AAI/B,IAAI,OAAO,aAAa,eAAe,OAAO,WAAW,aAAa;AAElE,qBAAmB;AACnB,QAAMO,WAAU;AAEhB,MAAIA,SAAQ,qBAAqB,QAAQ;AACrC,UAAM,YAAYA,SAAQ;AAC1B,QAAI,OAAO,cAAc,YAAY,cAAc,MAAM;AACrD,iBAAW,OAAO,WAAW;AACzB,cAAM,MAAM,sBAAsB,MAAM;AACxC,YAAI;AACA,gBAAM,QAAQ,UAAU,GAAG;AAC3B,cAAI,OAAO,UAAU,YACjB,CAAC,SACD,MAAM,cAAc,QAAQ;AAC5B;AAAA,UACJ;AACA,cAAI,CAAC,eAAe,KAAK,KAAK,GAAG;AAC7B,oBAAQ,MAAM,GAAG;AAAA,UACrB;AAAA,QACJ,SACO,GAAG;AACN,kBAAQ,MAAM,GAAG;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAIA,IAAM,OAAO;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAIA,IAAM,UAAU;AAAA;AAAA,EAEZ;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA,WAAW;AAAA;AAAA,EAEX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AACJ;AAKA,IAAI;AACH,MAAI,KAAK,YAAY,QAAQ;AAC5B,SAAK,UAAU;AAAA,EAChB;AACD,SAAS,KAAK;AACd;", "names": ["callback", "storage", "simpleNames", "send", "node", "observer", "nodes", "getIcon", "_window"]}