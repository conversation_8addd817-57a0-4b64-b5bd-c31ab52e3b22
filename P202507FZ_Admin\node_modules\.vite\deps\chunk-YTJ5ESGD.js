// node_modules/.pnpm/min-dash@4.2.2/node_modules/min-dash/dist/index.esm.js
function flatten(arr) {
  return Array.prototype.concat.apply([], arr);
}
var nativeToString = Object.prototype.toString;
var nativeHasOwnProperty = Object.prototype.hasOwnProperty;
function isUndefined(obj) {
  return obj === void 0;
}
function isDefined(obj) {
  return obj !== void 0;
}
function isNil(obj) {
  return obj == null;
}
function isArray(obj) {
  return nativeToString.call(obj) === "[object Array]";
}
function isObject(obj) {
  return nativeToString.call(obj) === "[object Object]";
}
function isNumber(obj) {
  return nativeToString.call(obj) === "[object Number]";
}
function isFunction(obj) {
  const tag = nativeToString.call(obj);
  return tag === "[object Function]" || tag === "[object AsyncFunction]" || tag === "[object GeneratorFunction]" || tag === "[object AsyncGeneratorFunction]" || tag === "[object Proxy]";
}
function isString(obj) {
  return nativeToString.call(obj) === "[object String]";
}
function ensureArray(obj) {
  if (isArray(obj)) {
    return;
  }
  throw new Error("must supply array");
}
function has(target, key) {
  return !isNil(target) && nativeHasOwnProperty.call(target, key);
}
function find(collection, matcher) {
  const matchFn = toMatcher(matcher);
  let match;
  forEach(collection, function(val, key) {
    if (matchFn(val, key)) {
      match = val;
      return false;
    }
  });
  return match;
}
function findIndex(collection, matcher) {
  const matchFn = toMatcher(matcher);
  let idx = isArray(collection) ? -1 : void 0;
  forEach(collection, function(val, key) {
    if (matchFn(val, key)) {
      idx = key;
      return false;
    }
  });
  return idx;
}
function filter(collection, matcher) {
  const matchFn = toMatcher(matcher);
  let result = [];
  forEach(collection, function(val, key) {
    if (matchFn(val, key)) {
      result.push(val);
    }
  });
  return result;
}
function forEach(collection, iterator) {
  let val, result;
  if (isUndefined(collection)) {
    return;
  }
  const convertKey = isArray(collection) ? toNum : identity;
  for (let key in collection) {
    if (has(collection, key)) {
      val = collection[key];
      result = iterator(val, convertKey(key));
      if (result === false) {
        return val;
      }
    }
  }
}
function without(arr, matcher) {
  if (isUndefined(arr)) {
    return [];
  }
  ensureArray(arr);
  const matchFn = toMatcher(matcher);
  return arr.filter(function(el, idx) {
    return !matchFn(el, idx);
  });
}
function reduce(collection, iterator, result) {
  forEach(collection, function(value, idx) {
    result = iterator(result, value, idx);
  });
  return result;
}
function every(collection, matcher) {
  return !!reduce(collection, function(matches, val, key) {
    return matches && matcher(val, key);
  }, true);
}
function some(collection, matcher) {
  return !!find(collection, matcher);
}
function map(collection, fn) {
  let result = [];
  forEach(collection, function(val, key) {
    result.push(fn(val, key));
  });
  return result;
}
function keys(collection) {
  return collection && Object.keys(collection) || [];
}
function size(collection) {
  return keys(collection).length;
}
function values(collection) {
  return map(collection, (val) => val);
}
function groupBy(collection, extractor, grouped = {}) {
  extractor = toExtractor(extractor);
  forEach(collection, function(val) {
    let discriminator = extractor(val) || "_";
    let group = grouped[discriminator];
    if (!group) {
      group = grouped[discriminator] = [];
    }
    group.push(val);
  });
  return grouped;
}
function uniqueBy(extractor, ...collections) {
  extractor = toExtractor(extractor);
  let grouped = {};
  forEach(collections, (c) => groupBy(c, extractor, grouped));
  let result = map(grouped, function(val, key) {
    return val[0];
  });
  return result;
}
var unionBy = uniqueBy;
function sortBy(collection, extractor) {
  extractor = toExtractor(extractor);
  let sorted = [];
  forEach(collection, function(value, key) {
    let disc = extractor(value, key);
    let entry = {
      d: disc,
      v: value
    };
    for (var idx = 0; idx < sorted.length; idx++) {
      let { d } = sorted[idx];
      if (disc < d) {
        sorted.splice(idx, 0, entry);
        return;
      }
    }
    sorted.push(entry);
  });
  return map(sorted, (e) => e.v);
}
function matchPattern(pattern) {
  return function(el) {
    return every(pattern, function(val, key) {
      return el[key] === val;
    });
  };
}
function toExtractor(extractor) {
  return isFunction(extractor) ? extractor : (e) => {
    return e[extractor];
  };
}
function toMatcher(matcher) {
  return isFunction(matcher) ? matcher : (e) => {
    return e === matcher;
  };
}
function identity(arg) {
  return arg;
}
function toNum(arg) {
  return Number(arg);
}
function debounce(fn, timeout) {
  let timer;
  let lastArgs;
  let lastThis;
  let lastNow;
  function fire(force) {
    let now = Date.now();
    let scheduledDiff = force ? 0 : lastNow + timeout - now;
    if (scheduledDiff > 0) {
      return schedule(scheduledDiff);
    }
    fn.apply(lastThis, lastArgs);
    clear();
  }
  function schedule(timeout2) {
    timer = setTimeout(fire, timeout2);
  }
  function clear() {
    if (timer) {
      clearTimeout(timer);
    }
    timer = lastNow = lastArgs = lastThis = void 0;
  }
  function flush() {
    if (timer) {
      fire(true);
    }
    clear();
  }
  function callback(...args) {
    lastNow = Date.now();
    lastArgs = args;
    lastThis = this;
    if (!timer) {
      schedule(timeout);
    }
  }
  callback.flush = flush;
  callback.cancel = clear;
  return callback;
}
function throttle(fn, interval) {
  let throttling = false;
  return function(...args) {
    if (throttling) {
      return;
    }
    fn(...args);
    throttling = true;
    setTimeout(() => {
      throttling = false;
    }, interval);
  };
}
function bind(fn, target) {
  return fn.bind(target);
}
function assign(target, ...others) {
  return Object.assign(target, ...others);
}
function set(target, path, value) {
  let currentTarget = target;
  forEach(path, function(key, idx) {
    if (typeof key !== "number" && typeof key !== "string") {
      throw new Error("illegal key type: " + typeof key + ". Key should be of type number or string.");
    }
    if (key === "constructor") {
      throw new Error("illegal key: constructor");
    }
    if (key === "__proto__") {
      throw new Error("illegal key: __proto__");
    }
    let nextKey = path[idx + 1];
    let nextTarget = currentTarget[key];
    if (isDefined(nextKey) && isNil(nextTarget)) {
      nextTarget = currentTarget[key] = isNaN(+nextKey) ? {} : [];
    }
    if (isUndefined(nextKey)) {
      if (isUndefined(value)) {
        delete currentTarget[key];
      } else {
        currentTarget[key] = value;
      }
    } else {
      currentTarget = nextTarget;
    }
  });
  return target;
}
function get(target, path, defaultValue) {
  let currentTarget = target;
  forEach(path, function(key) {
    if (isNil(currentTarget)) {
      currentTarget = void 0;
      return false;
    }
    currentTarget = currentTarget[key];
  });
  return isUndefined(currentTarget) ? defaultValue : currentTarget;
}
function pick(target, properties) {
  let result = {};
  let obj = Object(target);
  forEach(properties, function(prop) {
    if (prop in obj) {
      result[prop] = target[prop];
    }
  });
  return result;
}
function omit(target, properties) {
  let result = {};
  let obj = Object(target);
  forEach(obj, function(prop, key) {
    if (properties.indexOf(key) === -1) {
      result[key] = prop;
    }
  });
  return result;
}
function merge(target, ...sources) {
  if (!sources.length) {
    return target;
  }
  forEach(sources, function(source) {
    if (!source || !isObject(source)) {
      return;
    }
    forEach(source, function(sourceVal, key) {
      if (key === "__proto__") {
        return;
      }
      let targetVal = target[key];
      if (isObject(sourceVal)) {
        if (!isObject(targetVal)) {
          targetVal = {};
        }
        target[key] = merge(targetVal, sourceVal);
      } else {
        target[key] = sourceVal;
      }
    });
  });
  return target;
}

export {
  flatten,
  isUndefined,
  isDefined,
  isNil,
  isArray,
  isObject,
  isNumber,
  isFunction,
  isString,
  ensureArray,
  has,
  find,
  findIndex,
  filter,
  forEach,
  without,
  reduce,
  every,
  some,
  map,
  keys,
  size,
  values,
  groupBy,
  uniqueBy,
  unionBy,
  sortBy,
  matchPattern,
  debounce,
  throttle,
  bind,
  assign,
  set,
  get,
  pick,
  omit,
  merge
};
//# sourceMappingURL=chunk-YTJ5ESGD.js.map
