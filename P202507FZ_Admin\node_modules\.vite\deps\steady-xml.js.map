{"version": 3, "sources": ["../../.pnpm/steady-xml@0.1.0/node_modules/steady-xml/dist/index.mjs"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\n\n// src/shared.ts\nvar toString = Object.prototype.toString;\nfunction is(value, type) {\n  return toString.call(value) === `[object ${type}]`;\n}\nfunction isNull(value) {\n  return value === void 0 || value === null;\n}\nfunction isString(value) {\n  return typeof value === \"string\";\n}\nfunction isBoolean(value) {\n  return typeof value === \"boolean\";\n}\nfunction isArray(value) {\n  return Array.isArray(value);\n}\nfunction isObject(value) {\n  return is(value, \"Object\");\n}\n\n// src/node.ts\nvar XmlNodeType;\n(function(XmlNodeType2) {\n  XmlNodeType2[\"Root\"] = \"Root\";\n  XmlNodeType2[\"Declaration\"] = \"Declaration\";\n  XmlNodeType2[\"Comment\"] = \"Comment\";\n  XmlNodeType2[\"DocumentType\"] = \"DocumentType\";\n  XmlNodeType2[\"Element\"] = \"Element\";\n  XmlNodeType2[\"Text\"] = \"Text\";\n  XmlNodeType2[\"Instruction\"] = \"Instruction\";\n  XmlNodeType2[\"CDATA\"] = \"CDATA\";\n})(XmlNodeType || (XmlNodeType = {}));\nvar XmlNode = class {\n  constructor(type, parent = null, value = null) {\n    __publicField(this, \"name\");\n    __publicField(this, \"type\");\n    __publicField(this, \"parent\");\n    __publicField(this, \"children\");\n    __publicField(this, \"attributes\");\n    __publicField(this, \"value\");\n    __publicField(this, \"selfClosing\");\n    __publicField(this, \"prefix\");\n    this.name = \"\";\n    this.type = type;\n    this.parent = parent;\n    this.value = value;\n    this.children = null;\n    this.attributes = {};\n    this.selfClosing = false;\n    this.prefix = \"\";\n  }\n  setName(value) {\n    this.name = value;\n    return this;\n  }\n  setType(value) {\n    this.type = value;\n    return this;\n  }\n  setParent(value) {\n    this.parent = value;\n    return this;\n  }\n  setChildren(value) {\n    this.children = value ? Array.from(value) : value;\n    return this;\n  }\n  setAttributes(value) {\n    this.attributes = __spreadValues({}, value);\n    return this;\n  }\n  setValue(value) {\n    this.value = value;\n    return this;\n  }\n  setSelfClosing(value) {\n    this.selfClosing = value;\n    return this;\n  }\n  setPrefix(value) {\n    this.prefix = value;\n    return this;\n  }\n  addAttribute(name, value) {\n    this.attributes[name] = value;\n    return this;\n  }\n  removeAttribute(name) {\n    delete this.attributes[name];\n    return this;\n  }\n  addChild(childNode) {\n    if (childNode === this)\n      return this;\n    if (!this.children) {\n      this.children = [];\n    }\n    this.children.push(childNode);\n    if (childNode.parent !== this) {\n      childNode.parent = this;\n    }\n    return this;\n  }\n  removeChild(childNode) {\n    if (this.children && this.children.length) {\n      const index = this.children.findIndex((node) => node === childNode);\n      if (~index) {\n        this.children.splice(index, 1);\n        childNode.parent = null;\n      }\n    }\n    return this;\n  }\n  toJsObject() {\n    return {\n      name: this.name || void 0,\n      prefix: this.prefix || void 0,\n      type: this.type,\n      attributes: Object.keys(this.attributes).length ? this.attributes : void 0,\n      value: isNull(this.value) ? void 0 : this.value,\n      selfClosing: this.selfClosing || void 0,\n      children: (this.type === XmlNodeType.Element || this.type === XmlNodeType.Root) && this.children && this.children.length ? this.children.map((child) => child.toJSON()) : void 0\n    };\n  }\n  toXmlString(indentChar = \"  \", newLine = \"\\n\", indentCount = 0) {\n    const indent = indentChar.repeat(indentCount);\n    let xml = \"\";\n    switch (this.type) {\n      case XmlNodeType.Root: {\n        xml += this.children && this.children.length ? this.children.map((node) => node.toXmlString(indentChar, newLine, indentCount)).join(newLine) : \"\";\n        break;\n      }\n      case XmlNodeType.Element: {\n        if (!this.name)\n          return \"\";\n        const name = this.prefix ? `${this.prefix}:${this.name}` : this.name;\n        xml += `${indent}<${name}`;\n        const attributes = buildAttributeString(this.attributes || {});\n        if (attributes) {\n          xml += ` ${attributes}`;\n        }\n        if (this.children && this.children.length) {\n          xml += `>${newLine}${this.children.map((node) => node.toXmlString(indentChar, newLine, indentCount + 1)).join(newLine)}${newLine}${indent}</${name}>`;\n        } else {\n          xml += this.selfClosing ? \" />\" : `></${name}>`;\n        }\n        break;\n      }\n      case XmlNodeType.CDATA: {\n        xml += `${indent}<![CDATA[${isNull(this.value) ? \"\" : this.value}]]>`;\n        break;\n      }\n      case XmlNodeType.Text: {\n        xml += isNull(this.value) ? \"\" : `${indent}${this.value}`;\n        break;\n      }\n      case XmlNodeType.DocumentType: {\n        xml += isNull(this.value) ? \"\" : `${indent}<!DOCTYPE ${this.value}>`;\n        break;\n      }\n      case XmlNodeType.Comment: {\n        xml += `${indent}<!-- ${isNull(this.value) ? \"\" : this.value + \" \"}-->`;\n        break;\n      }\n      case XmlNodeType.Declaration: {\n        xml += `${indent}<?xml `;\n        if (!this.attributes || isNull(this.attributes.version)) {\n          xml += 'version=\"1.0\" ';\n        } else {\n          const version = parseFloat(this.attributes.version);\n          xml += `version=\"${Number.isNaN(version) ? \"1.0\" : version.toFixed(1)}\" `;\n        }\n        if (this.attributes) {\n          const _a = this.attributes, { version } = _a, attributes = __objRest(_a, [\"version\"]);\n          if (attributes) {\n            xml += buildAttributeString(attributes);\n          }\n        }\n        xml += \"?>\";\n        break;\n      }\n      case XmlNodeType.Instruction: {\n        xml += isNull(this.value) ? \"\" : `${indent}<?${this.value}?>`;\n        break;\n      }\n    }\n    return xml;\n  }\n  toJSON() {\n    return this.toJsObject();\n  }\n  toString() {\n    return this.toXmlString(\"\", \"\");\n  }\n};\nfunction buildAttributeString(attributes) {\n  return Object.keys(attributes).map((key) => {\n    const value = attributes[key];\n    if (isNull(value)) {\n      return null;\n    }\n    if (isBoolean(value)) {\n      return value ? key : null;\n    }\n    return `${key}=\"${value}\"`;\n  }).filter(Boolean).join(\" \");\n}\n\n// src/props.ts\nvar defaultProcessor = (v) => v;\nvar defaultParseProps = {\n  ignoreAttributes: false,\n  parseNodeValue: true,\n  trimValues: true,\n  prefixInName: false,\n  valueProcessor: defaultProcessor,\n  attributeProcessor: defaultProcessor\n};\nvar defaultBuildProps = {\n  nameKey: \"name\",\n  typeKey: \"type\",\n  valueKey: \"value\",\n  attributesKey: \"attributes\",\n  childrenKey: \"children\",\n  selfClosingKey: \"selfClosing\",\n  prefixKey: \"prefix\",\n  trimValues: true,\n  isRoot: false,\n  prefixInName: false,\n  valueProcessor: defaultProcessor,\n  attributeProcessor: defaultProcessor\n};\nvar parsePropKeys = Object.keys(defaultParseProps);\nfunction normalizeParseProps(props = {}) {\n  const normalizedProps = __spreadValues({}, props);\n  parsePropKeys.forEach((key) => {\n    if (isNull(normalizedProps[key])) {\n      normalizedProps[key] = defaultParseProps[key];\n    }\n  });\n  return normalizedProps;\n}\nvar buildPropKeys = Object.keys(defaultBuildProps);\nfunction normalizeBuildProps(props = {}) {\n  const normalizedProps = __spreadValues({}, props);\n  buildPropKeys.forEach((key) => {\n    if (isNull(normalizedProps[key])) {\n      normalizedProps[key] = defaultBuildProps[key];\n    }\n  });\n  return normalizedProps;\n}\n\n// src/parser.ts\nvar tagNotClosed = \"Tag is not closed.\";\nfunction parseXmlString(xmlString, props = {}) {\n  const normalizedXml = xmlString.replace(/\\r\\n?/g, \"\\n\");\n  const normalizedProps = normalizeParseProps(props);\n  const xmlLength = normalizedXml.length;\n  const rootXmlNode = new XmlNode(XmlNodeType.Root);\n  let currentNode = rootXmlNode;\n  let textData = \"\";\n  for (let i = 0; i < xmlLength; i++) {\n    const char = normalizedXml[i];\n    if (char !== \"<\") {\n      textData += char;\n    } else {\n      if (normalizedXml[i + 1] === \"/\") {\n        const endIndex = findEndIndexOrThrow(normalizedXml, \">\", i, `Element End ${tagNotClosed}`);\n        let tagName = normalizedXml.substring(i + 2, endIndex);\n        let prefix = \"\";\n        if (!normalizedProps.prefixInName) {\n          const prefixIndex = tagName.indexOf(\":\");\n          if (~prefixIndex) {\n            prefix = tagName.substring(0, prefixIndex);\n            tagName = tagName.substring(prefixIndex + 1);\n          }\n        }\n        if (currentNode.prefix !== prefix || currentNode.name !== tagName) {\n          throw new Error(`End Tag is incorrect.`);\n        }\n        if (textData) {\n          const textValue = toTextValue(processNodeValue(\"\", XmlNodeType.Text, textData, normalizedProps));\n          if (textValue) {\n            currentNode.addChild(new XmlNode(XmlNodeType.Text, currentNode, textValue));\n          }\n        }\n        currentNode = currentNode.parent;\n        textData = \"\";\n        i = endIndex;\n      } else if (normalizedXml[i + 1] === \"?\") {\n        const endIndex = findEndIndexOrThrow(normalizedXml, \"?>\", i, `Processing Instruction ${tagNotClosed}`);\n        const content = normalizedXml.substring(i + 2, endIndex - 1);\n        if (currentNode) {\n          if (content.startsWith(\"xml \") && content.includes(\"version=\") && currentNode.type === XmlNodeType.Root) {\n            const childNode = new XmlNode(XmlNodeType.Declaration, currentNode);\n            childNode.attributes = parseAttributes(content.substr(4), XmlNodeType.Declaration, normalizedProps);\n            currentNode.addChild(childNode);\n          } else {\n            currentNode.addChild(new XmlNode(XmlNodeType.Instruction, currentNode, processNodeValue(\"\", XmlNodeType.Instruction, content, normalizedProps)));\n          }\n        }\n        i = endIndex;\n      } else if (normalizedXml.substr(i + 1, 3) === \"!--\") {\n        const endIndex = findEndIndexOrThrow(normalizedXml, \"-->\", i, `Comment ${tagNotClosed}`);\n        const content = normalizedXml.substring(i + 4, endIndex - 2);\n        if (currentNode) {\n          currentNode.addChild(new XmlNode(XmlNodeType.Comment, currentNode, processNodeValue(\"\", XmlNodeType.Comment, content, normalizedProps)));\n        }\n        i = endIndex;\n      } else if (normalizedXml.substr(i + 1, 8) === \"!DOCTYPE\") {\n        let endIndex = findEndIndexOrThrow(normalizedXml, \">\", i, `Document Type ${tagNotClosed}`);\n        let content = normalizedXml.substring(i + 9, endIndex);\n        if (content.includes(\"[\")) {\n          endIndex = findEndIndexOrThrow(normalizedXml, \"]>\", i, `Document Type ${tagNotClosed}`);\n          content = normalizedXml.substring(i + 9, endIndex);\n        }\n        if (currentNode) {\n          currentNode.addChild(new XmlNode(XmlNodeType.DocumentType, currentNode, processNodeValue(\"\", XmlNodeType.DocumentType, content, normalizedProps)));\n        }\n        i = endIndex;\n      } else if (normalizedXml.substr(i + 1, 8) === \"![CDATA[\") {\n        const endIndex = findEndIndexOrThrow(normalizedXml, \"]]>\", i, `CDATA Section ${tagNotClosed}`);\n        const content = normalizedXml.substring(i + 9, endIndex - 2);\n        if (currentNode && textData) {\n          const textValue = toTextValue(processNodeValue(\"\", XmlNodeType.Text, textData, normalizedProps));\n          if (textValue) {\n            currentNode.addChild(new XmlNode(XmlNodeType.Text, currentNode, textValue));\n          }\n        }\n        currentNode.addChild(new XmlNode(XmlNodeType.CDATA, currentNode, processNodeValue(\"\", XmlNodeType.CDATA, content, normalizedProps)));\n        textData = \"\";\n        i = endIndex;\n      } else {\n        let attrBoundary = \"\";\n        let content = \"\";\n        let endIndex = i + 1;\n        while (endIndex <= xmlLength) {\n          let char2 = normalizedXml[endIndex];\n          if (attrBoundary) {\n            if (char2 === attrBoundary)\n              attrBoundary = \"\";\n          } else if (char2 === '\"' || char2 === \"'\") {\n            attrBoundary = char2;\n          } else if (char2 === \"\t\") {\n            char2 = \" \";\n          } else if (char2 === \">\") {\n            break;\n          }\n          content += char2;\n          endIndex++;\n        }\n        if (endIndex > xmlLength) {\n          throw new Error(`Element ${tagNotClosed}`);\n        }\n        content = content.trim();\n        const separatorIndex = content.indexOf(\" \");\n        let tagName = content;\n        let prefix = \"\";\n        if (~separatorIndex) {\n          tagName = content.substr(0, separatorIndex);\n          content = content.substr(separatorIndex + 1);\n        } else {\n          content = \"\";\n        }\n        if (!normalizedProps.prefixInName) {\n          const prefixIndex = tagName.indexOf(\":\");\n          if (~prefixIndex) {\n            prefix = tagName.substring(0, prefixIndex);\n            tagName = tagName.substring(prefixIndex + 1);\n          }\n        }\n        if (currentNode && textData) {\n          const textValue = toTextValue(processNodeValue(\"\", XmlNodeType.Text, textData, normalizedProps));\n          if (textValue) {\n            currentNode.addChild(new XmlNode(XmlNodeType.Text, currentNode, textValue));\n          }\n        }\n        if (content.length && content.lastIndexOf(\"/\") === content.length - 1) {\n          if (tagName[tagName.length - 1] === \"/\") {\n            tagName = tagName.substr(0, tagName.length - 1);\n            content = \"\";\n          } else {\n            content = content.substr(0, content.length - 1);\n          }\n          const childNode = new XmlNode(XmlNodeType.Element, currentNode);\n          childNode.name = tagName;\n          childNode.selfClosing = true;\n          childNode.prefix = prefix;\n          if (content && !normalizedProps.ignoreAttributes) {\n            childNode.attributes = parseAttributes(content, XmlNodeType.Element, normalizedProps);\n          }\n          currentNode.addChild(childNode);\n        } else {\n          const childNode = new XmlNode(XmlNodeType.Element, currentNode);\n          if (content && !normalizedProps.ignoreAttributes) {\n            childNode.attributes = parseAttributes(content, XmlNodeType.Element, normalizedProps);\n          }\n          childNode.name = tagName;\n          childNode.prefix = prefix;\n          currentNode.addChild(childNode);\n          currentNode = childNode;\n        }\n        textData = \"\";\n        i = endIndex;\n      }\n    }\n  }\n  return rootXmlNode;\n}\nfunction findEndIndexOrThrow(value, search, position, error) {\n  const index = value.indexOf(search, position);\n  if (!~index) {\n    throw new Error(error);\n  }\n  return index + search.length - 1;\n}\nfunction processNodeValue(name, type, value, props) {\n  if (value) {\n    if (props.trimValues) {\n      value = value.trim();\n    }\n    return parseValue(props.valueProcessor(value, type, name), props.parseNodeValue);\n  }\n  return null;\n}\nfunction parseValue(value, shouldParse) {\n  if (shouldParse && isString(value)) {\n    value = value.trim();\n    if (value === \"true\") {\n      return true;\n    } else if (value === \"false\") {\n      return false;\n    } else {\n      return tryToNumber(value);\n    }\n  } else {\n    return isNull(value) ? null : value;\n  }\n}\nfunction tryToNumber(value) {\n  const number = parseFloat(value);\n  return Number.isNaN(number) ? value : number;\n}\nfunction toTextValue(value) {\n  return isNull(value) ? \"\" : isString(value) ? value : String(value);\n}\nvar attributeRE = /[^\\s=]+\\s*(=\\s*['\"][\\s\\S]*?['\"])?/g;\nfunction parseAttributes(content, type, props) {\n  content = content.replace(/\\r?\\n/g, \" \");\n  const matches = content.match(attributeRE) || [];\n  const attributes = {};\n  for (let i = 0; i < matches.length; i++) {\n    const attrString = matches[i];\n    let [name, value] = attrString.split(\"=\");\n    name = name.trim();\n    if (isString(value)) {\n      value = value.substring(1, value.length - 1);\n      if (props.trimValues) {\n        value = value.trim();\n      }\n      attributes[name] = parseValue(props.attributeProcessor(value, name, type), props.parseNodeValue);\n    } else {\n      attributes[name] = true;\n    }\n  }\n  return attributes;\n}\n\n// src/builder.ts\nfunction buildFromJson(json, props = {}) {\n  const normalizedProps = normalizeBuildProps(props);\n  const { nameKey, typeKey, valueKey, attributesKey, childrenKey, selfClosingKey, prefixKey } = normalizedProps;\n  const rootXmlNode = new XmlNode(XmlNodeType.Root);\n  const loopQueue = [];\n  if (normalizedProps.isRoot || json[typeKey] === XmlNodeType.Root) {\n    loopQueue.push(...(json[childrenKey] || []).map((child) => ({ parent: rootXmlNode, child })));\n  } else {\n    const declarationNdoe = new XmlNode(XmlNodeType.Declaration, rootXmlNode);\n    declarationNdoe.attributes = {\n      version: 1,\n      encoding: \"UTF-8\",\n      standalone: \"yes\"\n    };\n    rootXmlNode.addChild(declarationNdoe);\n    loopQueue.push({ parent: rootXmlNode, child: json });\n  }\n  while (loopQueue.length) {\n    const { parent, child } = loopQueue.shift();\n    if (isString(child)) {\n      parent.addChild(new XmlNode(XmlNodeType.Text, parent, processNodeValue2(XmlNodeType.Text, child, \"\", normalizedProps)));\n      continue;\n    }\n    const name = isString(child[nameKey]) ? child[nameKey] : \"\";\n    const type = child[typeKey];\n    const value = isNull(child[valueKey]) ? null : child[valueKey];\n    switch (type) {\n      case XmlNodeType.CDATA: {\n        parent.addChild(new XmlNode(XmlNodeType.CDATA, parent, processNodeValue2(type, value, \"\", normalizedProps)));\n        break;\n      }\n      case XmlNodeType.Text: {\n        parent.addChild(new XmlNode(XmlNodeType.Text, parent, processNodeValue2(type, value, \"\", normalizedProps)));\n        break;\n      }\n      case XmlNodeType.DocumentType: {\n        parent.addChild(new XmlNode(XmlNodeType.DocumentType, parent, processNodeValue2(type, value, \"\", normalizedProps)));\n        break;\n      }\n      case XmlNodeType.Comment: {\n        parent.addChild(new XmlNode(XmlNodeType.Comment, parent, processNodeValue2(type, value, \"\", normalizedProps)));\n        break;\n      }\n      case XmlNodeType.Declaration: {\n        const node = new XmlNode(XmlNodeType.Declaration, parent);\n        const attributes = attributesKey && child[attributesKey];\n        if (attributes && isObject(attributes)) {\n          Object.keys(attributes).forEach((key) => {\n            node.attributes[key] = normalizedProps.attributeProcessor(attributes[key], key, XmlNodeType.Declaration);\n          });\n        }\n        if (!node.attributes.version) {\n          node.attributes.version = 1;\n        } else {\n          const version = parseFloat(node.attributes.version);\n          node.attributes.version = Number.isNaN(version) ? 1 : version;\n        }\n        parent.addChild(node);\n        break;\n      }\n      case XmlNodeType.Instruction: {\n        parent.addChild(new XmlNode(XmlNodeType.Instruction, parent, processNodeValue2(type, value, \"\", normalizedProps)));\n        break;\n      }\n      default: {\n        if (!name)\n          break;\n        let tagName = name;\n        let prefix = \"\";\n        if (normalizedProps.prefixInName) {\n          const prefixIndex = tagName.indexOf(\":\");\n          if (~prefixIndex) {\n            prefix = tagName.substring(0, prefixIndex);\n            tagName = tagName.substring(prefixIndex + 1);\n          }\n        } else if (prefixKey && isString(child[prefixKey])) {\n          prefix = child[prefixKey];\n        }\n        const node = new XmlNode(XmlNodeType.Element, parent);\n        const attributes = attributesKey && child[attributesKey];\n        node.name = tagName;\n        node.prefix = prefix;\n        if (attributes && isObject(attributes)) {\n          Object.keys(attributes).forEach((key) => {\n            node.attributes[key] = normalizedProps.attributeProcessor(attributes[key], key, XmlNodeType.Element);\n          });\n        }\n        parent.addChild(node);\n        const children = child[childrenKey];\n        if (isArray(children) && children.length) {\n          node.selfClosing = false;\n          loopQueue.push(...children.map((child2) => ({ parent: node, child: child2 })));\n        } else {\n          node.selfClosing = Boolean(selfClosingKey && child[selfClosingKey]);\n        }\n      }\n    }\n  }\n  return rootXmlNode;\n}\nfunction processNodeValue2(type, value, name, props) {\n  value = props.valueProcessor(value, type, name);\n  if (isNull(value)) {\n    return null;\n  }\n  if (isString(value)) {\n    return props.trimValues ? value.trim() : value;\n  }\n  return String(value);\n}\nexport {\n  XmlNode,\n  XmlNodeType,\n  buildFromJson,\n  parseXmlString\n};\n"], "mappings": ";;;AAAA,IAAI,YAAY,OAAO;AACvB,IAAI,sBAAsB,OAAO;AACjC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,iBAAiB,CAAC,GAAG,MAAM;AAC7B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,sBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AACpC,MAAI;AACF,aAAS,QAAQ,oBAAoB,CAAC,GAAG;AACvC,UAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,wBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACpC;AACF,SAAO;AACT;AACA,IAAI,YAAY,CAAC,QAAQ,YAAY;AACnC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,aAAa,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC7D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,oBAAoB,MAAM,GAAG;AAC5C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,aAAa,KAAK,QAAQ,IAAI;AAC7D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,IAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,kBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,SAAO;AACT;AAGA,IAAI,WAAW,OAAO,UAAU;AAChC,SAAS,GAAG,OAAO,MAAM;AACvB,SAAO,SAAS,KAAK,KAAK,MAAM,WAAW,IAAI;AACjD;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,UAAU,UAAU,UAAU;AACvC;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,QAAQ,OAAO;AACtB,SAAO,MAAM,QAAQ,KAAK;AAC5B;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,GAAG,OAAO,QAAQ;AAC3B;AAGA,IAAI;AAAA,CACH,SAAS,cAAc;AACtB,eAAa,MAAM,IAAI;AACvB,eAAa,aAAa,IAAI;AAC9B,eAAa,SAAS,IAAI;AAC1B,eAAa,cAAc,IAAI;AAC/B,eAAa,SAAS,IAAI;AAC1B,eAAa,MAAM,IAAI;AACvB,eAAa,aAAa,IAAI;AAC9B,eAAa,OAAO,IAAI;AAC1B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAI,UAAU,MAAM;AAAA,EAClB,YAAY,MAAM,SAAS,MAAM,QAAQ,MAAM;AAC7C,kBAAc,MAAM,MAAM;AAC1B,kBAAc,MAAM,MAAM;AAC1B,kBAAc,MAAM,QAAQ;AAC5B,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,OAAO;AAC3B,kBAAc,MAAM,aAAa;AACjC,kBAAc,MAAM,QAAQ;AAC5B,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,aAAa,CAAC;AACnB,SAAK,cAAc;AACnB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,OAAO;AACZ,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,OAAO;AACZ,WAAO;AAAA,EACT;AAAA,EACA,UAAU,OAAO;AACf,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,WAAW,QAAQ,MAAM,KAAK,KAAK,IAAI;AAC5C,WAAO;AAAA,EACT;AAAA,EACA,cAAc,OAAO;AACnB,SAAK,aAAa,eAAe,CAAC,GAAG,KAAK;AAC1C,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAO;AACd,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA,EACA,UAAU,OAAO;AACf,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AAAA,EACA,aAAa,MAAM,OAAO;AACxB,SAAK,WAAW,IAAI,IAAI;AACxB,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,KAAK,WAAW,IAAI;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,cAAc;AAChB,aAAO;AACT,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW,CAAC;AAAA,IACnB;AACA,SAAK,SAAS,KAAK,SAAS;AAC5B,QAAI,UAAU,WAAW,MAAM;AAC7B,gBAAU,SAAS;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,WAAW;AACrB,QAAI,KAAK,YAAY,KAAK,SAAS,QAAQ;AACzC,YAAM,QAAQ,KAAK,SAAS,UAAU,CAAC,SAAS,SAAS,SAAS;AAClE,UAAI,CAAC,OAAO;AACV,aAAK,SAAS,OAAO,OAAO,CAAC;AAC7B,kBAAU,SAAS;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,WAAO;AAAA,MACL,MAAM,KAAK,QAAQ;AAAA,MACnB,QAAQ,KAAK,UAAU;AAAA,MACvB,MAAM,KAAK;AAAA,MACX,YAAY,OAAO,KAAK,KAAK,UAAU,EAAE,SAAS,KAAK,aAAa;AAAA,MACpE,OAAO,OAAO,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,MAC1C,aAAa,KAAK,eAAe;AAAA,MACjC,WAAW,KAAK,SAAS,YAAY,WAAW,KAAK,SAAS,YAAY,SAAS,KAAK,YAAY,KAAK,SAAS,SAAS,KAAK,SAAS,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,IAAI;AAAA,IAC5K;AAAA,EACF;AAAA,EACA,YAAY,aAAa,MAAM,UAAU,MAAM,cAAc,GAAG;AAC9D,UAAM,SAAS,WAAW,OAAO,WAAW;AAC5C,QAAI,MAAM;AACV,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK,YAAY,MAAM;AACrB,eAAO,KAAK,YAAY,KAAK,SAAS,SAAS,KAAK,SAAS,IAAI,CAAC,SAAS,KAAK,YAAY,YAAY,SAAS,WAAW,CAAC,EAAE,KAAK,OAAO,IAAI;AAC/I;AAAA,MACF;AAAA,MACA,KAAK,YAAY,SAAS;AACxB,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,cAAM,OAAO,KAAK,SAAS,GAAG,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK;AAChE,eAAO,GAAG,MAAM,IAAI,IAAI;AACxB,cAAM,aAAa,qBAAqB,KAAK,cAAc,CAAC,CAAC;AAC7D,YAAI,YAAY;AACd,iBAAO,IAAI,UAAU;AAAA,QACvB;AACA,YAAI,KAAK,YAAY,KAAK,SAAS,QAAQ;AACzC,iBAAO,IAAI,OAAO,GAAG,KAAK,SAAS,IAAI,CAAC,SAAS,KAAK,YAAY,YAAY,SAAS,cAAc,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,GAAG,OAAO,GAAG,MAAM,KAAK,IAAI;AAAA,QACpJ,OAAO;AACL,iBAAO,KAAK,cAAc,QAAQ,MAAM,IAAI;AAAA,QAC9C;AACA;AAAA,MACF;AAAA,MACA,KAAK,YAAY,OAAO;AACtB,eAAO,GAAG,MAAM,YAAY,OAAO,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK;AAChE;AAAA,MACF;AAAA,MACA,KAAK,YAAY,MAAM;AACrB,eAAO,OAAO,KAAK,KAAK,IAAI,KAAK,GAAG,MAAM,GAAG,KAAK,KAAK;AACvD;AAAA,MACF;AAAA,MACA,KAAK,YAAY,cAAc;AAC7B,eAAO,OAAO,KAAK,KAAK,IAAI,KAAK,GAAG,MAAM,aAAa,KAAK,KAAK;AACjE;AAAA,MACF;AAAA,MACA,KAAK,YAAY,SAAS;AACxB,eAAO,GAAG,MAAM,QAAQ,OAAO,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,GAAG;AAClE;AAAA,MACF;AAAA,MACA,KAAK,YAAY,aAAa;AAC5B,eAAO,GAAG,MAAM;AAChB,YAAI,CAAC,KAAK,cAAc,OAAO,KAAK,WAAW,OAAO,GAAG;AACvD,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,UAAU,WAAW,KAAK,WAAW,OAAO;AAClD,iBAAO,YAAY,OAAO,MAAM,OAAO,IAAI,QAAQ,QAAQ,QAAQ,CAAC,CAAC;AAAA,QACvE;AACA,YAAI,KAAK,YAAY;AACnB,gBAAM,KAAK,KAAK,YAAY,EAAE,QAAQ,IAAI,IAAI,aAAa,UAAU,IAAI,CAAC,SAAS,CAAC;AACpF,cAAI,YAAY;AACd,mBAAO,qBAAqB,UAAU;AAAA,UACxC;AAAA,QACF;AACA,eAAO;AACP;AAAA,MACF;AAAA,MACA,KAAK,YAAY,aAAa;AAC5B,eAAO,OAAO,KAAK,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK,KAAK;AACzD;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,WAAW;AACT,WAAO,KAAK,YAAY,IAAI,EAAE;AAAA,EAChC;AACF;AACA,SAAS,qBAAqB,YAAY;AACxC,SAAO,OAAO,KAAK,UAAU,EAAE,IAAI,CAAC,QAAQ;AAC1C,UAAM,QAAQ,WAAW,GAAG;AAC5B,QAAI,OAAO,KAAK,GAAG;AACjB,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK,GAAG;AACpB,aAAO,QAAQ,MAAM;AAAA,IACvB;AACA,WAAO,GAAG,GAAG,KAAK,KAAK;AAAA,EACzB,CAAC,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC7B;AAGA,IAAI,mBAAmB,CAAC,MAAM;AAC9B,IAAI,oBAAoB;AAAA,EACtB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,oBAAoB;AACtB;AACA,IAAI,oBAAoB;AAAA,EACtB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,eAAe;AAAA,EACf,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,oBAAoB;AACtB;AACA,IAAI,gBAAgB,OAAO,KAAK,iBAAiB;AACjD,SAAS,oBAAoB,QAAQ,CAAC,GAAG;AACvC,QAAM,kBAAkB,eAAe,CAAC,GAAG,KAAK;AAChD,gBAAc,QAAQ,CAAC,QAAQ;AAC7B,QAAI,OAAO,gBAAgB,GAAG,CAAC,GAAG;AAChC,sBAAgB,GAAG,IAAI,kBAAkB,GAAG;AAAA,IAC9C;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,IAAI,gBAAgB,OAAO,KAAK,iBAAiB;AACjD,SAAS,oBAAoB,QAAQ,CAAC,GAAG;AACvC,QAAM,kBAAkB,eAAe,CAAC,GAAG,KAAK;AAChD,gBAAc,QAAQ,CAAC,QAAQ;AAC7B,QAAI,OAAO,gBAAgB,GAAG,CAAC,GAAG;AAChC,sBAAgB,GAAG,IAAI,kBAAkB,GAAG;AAAA,IAC9C;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAGA,IAAI,eAAe;AACnB,SAAS,eAAe,WAAW,QAAQ,CAAC,GAAG;AAC7C,QAAM,gBAAgB,UAAU,QAAQ,UAAU,IAAI;AACtD,QAAM,kBAAkB,oBAAoB,KAAK;AACjD,QAAM,YAAY,cAAc;AAChC,QAAM,cAAc,IAAI,QAAQ,YAAY,IAAI;AAChD,MAAI,cAAc;AAClB,MAAI,WAAW;AACf,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAM,OAAO,cAAc,CAAC;AAC5B,QAAI,SAAS,KAAK;AAChB,kBAAY;AAAA,IACd,OAAO;AACL,UAAI,cAAc,IAAI,CAAC,MAAM,KAAK;AAChC,cAAM,WAAW,oBAAoB,eAAe,KAAK,GAAG,eAAe,YAAY,EAAE;AACzF,YAAI,UAAU,cAAc,UAAU,IAAI,GAAG,QAAQ;AACrD,YAAI,SAAS;AACb,YAAI,CAAC,gBAAgB,cAAc;AACjC,gBAAM,cAAc,QAAQ,QAAQ,GAAG;AACvC,cAAI,CAAC,aAAa;AAChB,qBAAS,QAAQ,UAAU,GAAG,WAAW;AACzC,sBAAU,QAAQ,UAAU,cAAc,CAAC;AAAA,UAC7C;AAAA,QACF;AACA,YAAI,YAAY,WAAW,UAAU,YAAY,SAAS,SAAS;AACjE,gBAAM,IAAI,MAAM,uBAAuB;AAAA,QACzC;AACA,YAAI,UAAU;AACZ,gBAAM,YAAY,YAAY,iBAAiB,IAAI,YAAY,MAAM,UAAU,eAAe,CAAC;AAC/F,cAAI,WAAW;AACb,wBAAY,SAAS,IAAI,QAAQ,YAAY,MAAM,aAAa,SAAS,CAAC;AAAA,UAC5E;AAAA,QACF;AACA,sBAAc,YAAY;AAC1B,mBAAW;AACX,YAAI;AAAA,MACN,WAAW,cAAc,IAAI,CAAC,MAAM,KAAK;AACvC,cAAM,WAAW,oBAAoB,eAAe,MAAM,GAAG,0BAA0B,YAAY,EAAE;AACrG,cAAM,UAAU,cAAc,UAAU,IAAI,GAAG,WAAW,CAAC;AAC3D,YAAI,aAAa;AACf,cAAI,QAAQ,WAAW,MAAM,KAAK,QAAQ,SAAS,UAAU,KAAK,YAAY,SAAS,YAAY,MAAM;AACvG,kBAAM,YAAY,IAAI,QAAQ,YAAY,aAAa,WAAW;AAClE,sBAAU,aAAa,gBAAgB,QAAQ,OAAO,CAAC,GAAG,YAAY,aAAa,eAAe;AAClG,wBAAY,SAAS,SAAS;AAAA,UAChC,OAAO;AACL,wBAAY,SAAS,IAAI,QAAQ,YAAY,aAAa,aAAa,iBAAiB,IAAI,YAAY,aAAa,SAAS,eAAe,CAAC,CAAC;AAAA,UACjJ;AAAA,QACF;AACA,YAAI;AAAA,MACN,WAAW,cAAc,OAAO,IAAI,GAAG,CAAC,MAAM,OAAO;AACnD,cAAM,WAAW,oBAAoB,eAAe,OAAO,GAAG,WAAW,YAAY,EAAE;AACvF,cAAM,UAAU,cAAc,UAAU,IAAI,GAAG,WAAW,CAAC;AAC3D,YAAI,aAAa;AACf,sBAAY,SAAS,IAAI,QAAQ,YAAY,SAAS,aAAa,iBAAiB,IAAI,YAAY,SAAS,SAAS,eAAe,CAAC,CAAC;AAAA,QACzI;AACA,YAAI;AAAA,MACN,WAAW,cAAc,OAAO,IAAI,GAAG,CAAC,MAAM,YAAY;AACxD,YAAI,WAAW,oBAAoB,eAAe,KAAK,GAAG,iBAAiB,YAAY,EAAE;AACzF,YAAI,UAAU,cAAc,UAAU,IAAI,GAAG,QAAQ;AACrD,YAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,qBAAW,oBAAoB,eAAe,MAAM,GAAG,iBAAiB,YAAY,EAAE;AACtF,oBAAU,cAAc,UAAU,IAAI,GAAG,QAAQ;AAAA,QACnD;AACA,YAAI,aAAa;AACf,sBAAY,SAAS,IAAI,QAAQ,YAAY,cAAc,aAAa,iBAAiB,IAAI,YAAY,cAAc,SAAS,eAAe,CAAC,CAAC;AAAA,QACnJ;AACA,YAAI;AAAA,MACN,WAAW,cAAc,OAAO,IAAI,GAAG,CAAC,MAAM,YAAY;AACxD,cAAM,WAAW,oBAAoB,eAAe,OAAO,GAAG,iBAAiB,YAAY,EAAE;AAC7F,cAAM,UAAU,cAAc,UAAU,IAAI,GAAG,WAAW,CAAC;AAC3D,YAAI,eAAe,UAAU;AAC3B,gBAAM,YAAY,YAAY,iBAAiB,IAAI,YAAY,MAAM,UAAU,eAAe,CAAC;AAC/F,cAAI,WAAW;AACb,wBAAY,SAAS,IAAI,QAAQ,YAAY,MAAM,aAAa,SAAS,CAAC;AAAA,UAC5E;AAAA,QACF;AACA,oBAAY,SAAS,IAAI,QAAQ,YAAY,OAAO,aAAa,iBAAiB,IAAI,YAAY,OAAO,SAAS,eAAe,CAAC,CAAC;AACnI,mBAAW;AACX,YAAI;AAAA,MACN,OAAO;AACL,YAAI,eAAe;AACnB,YAAI,UAAU;AACd,YAAI,WAAW,IAAI;AACnB,eAAO,YAAY,WAAW;AAC5B,cAAI,QAAQ,cAAc,QAAQ;AAClC,cAAI,cAAc;AAChB,gBAAI,UAAU;AACZ,6BAAe;AAAA,UACnB,WAAW,UAAU,OAAO,UAAU,KAAK;AACzC,2BAAe;AAAA,UACjB,WAAW,UAAU,KAAK;AACxB,oBAAQ;AAAA,UACV,WAAW,UAAU,KAAK;AACxB;AAAA,UACF;AACA,qBAAW;AACX;AAAA,QACF;AACA,YAAI,WAAW,WAAW;AACxB,gBAAM,IAAI,MAAM,WAAW,YAAY,EAAE;AAAA,QAC3C;AACA,kBAAU,QAAQ,KAAK;AACvB,cAAM,iBAAiB,QAAQ,QAAQ,GAAG;AAC1C,YAAI,UAAU;AACd,YAAI,SAAS;AACb,YAAI,CAAC,gBAAgB;AACnB,oBAAU,QAAQ,OAAO,GAAG,cAAc;AAC1C,oBAAU,QAAQ,OAAO,iBAAiB,CAAC;AAAA,QAC7C,OAAO;AACL,oBAAU;AAAA,QACZ;AACA,YAAI,CAAC,gBAAgB,cAAc;AACjC,gBAAM,cAAc,QAAQ,QAAQ,GAAG;AACvC,cAAI,CAAC,aAAa;AAChB,qBAAS,QAAQ,UAAU,GAAG,WAAW;AACzC,sBAAU,QAAQ,UAAU,cAAc,CAAC;AAAA,UAC7C;AAAA,QACF;AACA,YAAI,eAAe,UAAU;AAC3B,gBAAM,YAAY,YAAY,iBAAiB,IAAI,YAAY,MAAM,UAAU,eAAe,CAAC;AAC/F,cAAI,WAAW;AACb,wBAAY,SAAS,IAAI,QAAQ,YAAY,MAAM,aAAa,SAAS,CAAC;AAAA,UAC5E;AAAA,QACF;AACA,YAAI,QAAQ,UAAU,QAAQ,YAAY,GAAG,MAAM,QAAQ,SAAS,GAAG;AACrE,cAAI,QAAQ,QAAQ,SAAS,CAAC,MAAM,KAAK;AACvC,sBAAU,QAAQ,OAAO,GAAG,QAAQ,SAAS,CAAC;AAC9C,sBAAU;AAAA,UACZ,OAAO;AACL,sBAAU,QAAQ,OAAO,GAAG,QAAQ,SAAS,CAAC;AAAA,UAChD;AACA,gBAAM,YAAY,IAAI,QAAQ,YAAY,SAAS,WAAW;AAC9D,oBAAU,OAAO;AACjB,oBAAU,cAAc;AACxB,oBAAU,SAAS;AACnB,cAAI,WAAW,CAAC,gBAAgB,kBAAkB;AAChD,sBAAU,aAAa,gBAAgB,SAAS,YAAY,SAAS,eAAe;AAAA,UACtF;AACA,sBAAY,SAAS,SAAS;AAAA,QAChC,OAAO;AACL,gBAAM,YAAY,IAAI,QAAQ,YAAY,SAAS,WAAW;AAC9D,cAAI,WAAW,CAAC,gBAAgB,kBAAkB;AAChD,sBAAU,aAAa,gBAAgB,SAAS,YAAY,SAAS,eAAe;AAAA,UACtF;AACA,oBAAU,OAAO;AACjB,oBAAU,SAAS;AACnB,sBAAY,SAAS,SAAS;AAC9B,wBAAc;AAAA,QAChB;AACA,mBAAW;AACX,YAAI;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,OAAO,QAAQ,UAAU,OAAO;AAC3D,QAAM,QAAQ,MAAM,QAAQ,QAAQ,QAAQ;AAC5C,MAAI,CAAC,CAAC,OAAO;AACX,UAAM,IAAI,MAAM,KAAK;AAAA,EACvB;AACA,SAAO,QAAQ,OAAO,SAAS;AACjC;AACA,SAAS,iBAAiB,MAAM,MAAM,OAAO,OAAO;AAClD,MAAI,OAAO;AACT,QAAI,MAAM,YAAY;AACpB,cAAQ,MAAM,KAAK;AAAA,IACrB;AACA,WAAO,WAAW,MAAM,eAAe,OAAO,MAAM,IAAI,GAAG,MAAM,cAAc;AAAA,EACjF;AACA,SAAO;AACT;AACA,SAAS,WAAW,OAAO,aAAa;AACtC,MAAI,eAAe,SAAS,KAAK,GAAG;AAClC,YAAQ,MAAM,KAAK;AACnB,QAAI,UAAU,QAAQ;AACpB,aAAO;AAAA,IACT,WAAW,UAAU,SAAS;AAC5B,aAAO;AAAA,IACT,OAAO;AACL,aAAO,YAAY,KAAK;AAAA,IAC1B;AAAA,EACF,OAAO;AACL,WAAO,OAAO,KAAK,IAAI,OAAO;AAAA,EAChC;AACF;AACA,SAAS,YAAY,OAAO;AAC1B,QAAM,SAAS,WAAW,KAAK;AAC/B,SAAO,OAAO,MAAM,MAAM,IAAI,QAAQ;AACxC;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,OAAO,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,QAAQ,OAAO,KAAK;AACpE;AACA,IAAI,cAAc;AAClB,SAAS,gBAAgB,SAAS,MAAM,OAAO;AAC7C,YAAU,QAAQ,QAAQ,UAAU,GAAG;AACvC,QAAM,UAAU,QAAQ,MAAM,WAAW,KAAK,CAAC;AAC/C,QAAM,aAAa,CAAC;AACpB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,aAAa,QAAQ,CAAC;AAC5B,QAAI,CAAC,MAAM,KAAK,IAAI,WAAW,MAAM,GAAG;AACxC,WAAO,KAAK,KAAK;AACjB,QAAI,SAAS,KAAK,GAAG;AACnB,cAAQ,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC;AAC3C,UAAI,MAAM,YAAY;AACpB,gBAAQ,MAAM,KAAK;AAAA,MACrB;AACA,iBAAW,IAAI,IAAI,WAAW,MAAM,mBAAmB,OAAO,MAAM,IAAI,GAAG,MAAM,cAAc;AAAA,IACjG,OAAO;AACL,iBAAW,IAAI,IAAI;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,cAAc,MAAM,QAAQ,CAAC,GAAG;AACvC,QAAM,kBAAkB,oBAAoB,KAAK;AACjD,QAAM,EAAE,SAAS,SAAS,UAAU,eAAe,aAAa,gBAAgB,UAAU,IAAI;AAC9F,QAAM,cAAc,IAAI,QAAQ,YAAY,IAAI;AAChD,QAAM,YAAY,CAAC;AACnB,MAAI,gBAAgB,UAAU,KAAK,OAAO,MAAM,YAAY,MAAM;AAChE,cAAU,KAAK,IAAI,KAAK,WAAW,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,QAAQ,aAAa,MAAM,EAAE,CAAC;AAAA,EAC9F,OAAO;AACL,UAAM,kBAAkB,IAAI,QAAQ,YAAY,aAAa,WAAW;AACxE,oBAAgB,aAAa;AAAA,MAC3B,SAAS;AAAA,MACT,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AACA,gBAAY,SAAS,eAAe;AACpC,cAAU,KAAK,EAAE,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EACrD;AACA,SAAO,UAAU,QAAQ;AACvB,UAAM,EAAE,QAAQ,MAAM,IAAI,UAAU,MAAM;AAC1C,QAAI,SAAS,KAAK,GAAG;AACnB,aAAO,SAAS,IAAI,QAAQ,YAAY,MAAM,QAAQ,kBAAkB,YAAY,MAAM,OAAO,IAAI,eAAe,CAAC,CAAC;AACtH;AAAA,IACF;AACA,UAAM,OAAO,SAAS,MAAM,OAAO,CAAC,IAAI,MAAM,OAAO,IAAI;AACzD,UAAM,OAAO,MAAM,OAAO;AAC1B,UAAM,QAAQ,OAAO,MAAM,QAAQ,CAAC,IAAI,OAAO,MAAM,QAAQ;AAC7D,YAAQ,MAAM;AAAA,MACZ,KAAK,YAAY,OAAO;AACtB,eAAO,SAAS,IAAI,QAAQ,YAAY,OAAO,QAAQ,kBAAkB,MAAM,OAAO,IAAI,eAAe,CAAC,CAAC;AAC3G;AAAA,MACF;AAAA,MACA,KAAK,YAAY,MAAM;AACrB,eAAO,SAAS,IAAI,QAAQ,YAAY,MAAM,QAAQ,kBAAkB,MAAM,OAAO,IAAI,eAAe,CAAC,CAAC;AAC1G;AAAA,MACF;AAAA,MACA,KAAK,YAAY,cAAc;AAC7B,eAAO,SAAS,IAAI,QAAQ,YAAY,cAAc,QAAQ,kBAAkB,MAAM,OAAO,IAAI,eAAe,CAAC,CAAC;AAClH;AAAA,MACF;AAAA,MACA,KAAK,YAAY,SAAS;AACxB,eAAO,SAAS,IAAI,QAAQ,YAAY,SAAS,QAAQ,kBAAkB,MAAM,OAAO,IAAI,eAAe,CAAC,CAAC;AAC7G;AAAA,MACF;AAAA,MACA,KAAK,YAAY,aAAa;AAC5B,cAAM,OAAO,IAAI,QAAQ,YAAY,aAAa,MAAM;AACxD,cAAM,aAAa,iBAAiB,MAAM,aAAa;AACvD,YAAI,cAAc,SAAS,UAAU,GAAG;AACtC,iBAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ;AACvC,iBAAK,WAAW,GAAG,IAAI,gBAAgB,mBAAmB,WAAW,GAAG,GAAG,KAAK,YAAY,WAAW;AAAA,UACzG,CAAC;AAAA,QACH;AACA,YAAI,CAAC,KAAK,WAAW,SAAS;AAC5B,eAAK,WAAW,UAAU;AAAA,QAC5B,OAAO;AACL,gBAAM,UAAU,WAAW,KAAK,WAAW,OAAO;AAClD,eAAK,WAAW,UAAU,OAAO,MAAM,OAAO,IAAI,IAAI;AAAA,QACxD;AACA,eAAO,SAAS,IAAI;AACpB;AAAA,MACF;AAAA,MACA,KAAK,YAAY,aAAa;AAC5B,eAAO,SAAS,IAAI,QAAQ,YAAY,aAAa,QAAQ,kBAAkB,MAAM,OAAO,IAAI,eAAe,CAAC,CAAC;AACjH;AAAA,MACF;AAAA,MACA,SAAS;AACP,YAAI,CAAC;AACH;AACF,YAAI,UAAU;AACd,YAAI,SAAS;AACb,YAAI,gBAAgB,cAAc;AAChC,gBAAM,cAAc,QAAQ,QAAQ,GAAG;AACvC,cAAI,CAAC,aAAa;AAChB,qBAAS,QAAQ,UAAU,GAAG,WAAW;AACzC,sBAAU,QAAQ,UAAU,cAAc,CAAC;AAAA,UAC7C;AAAA,QACF,WAAW,aAAa,SAAS,MAAM,SAAS,CAAC,GAAG;AAClD,mBAAS,MAAM,SAAS;AAAA,QAC1B;AACA,cAAM,OAAO,IAAI,QAAQ,YAAY,SAAS,MAAM;AACpD,cAAM,aAAa,iBAAiB,MAAM,aAAa;AACvD,aAAK,OAAO;AACZ,aAAK,SAAS;AACd,YAAI,cAAc,SAAS,UAAU,GAAG;AACtC,iBAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ;AACvC,iBAAK,WAAW,GAAG,IAAI,gBAAgB,mBAAmB,WAAW,GAAG,GAAG,KAAK,YAAY,OAAO;AAAA,UACrG,CAAC;AAAA,QACH;AACA,eAAO,SAAS,IAAI;AACpB,cAAM,WAAW,MAAM,WAAW;AAClC,YAAI,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACxC,eAAK,cAAc;AACnB,oBAAU,KAAK,GAAG,SAAS,IAAI,CAAC,YAAY,EAAE,QAAQ,MAAM,OAAO,OAAO,EAAE,CAAC;AAAA,QAC/E,OAAO;AACL,eAAK,cAAc,QAAQ,kBAAkB,MAAM,cAAc,CAAC;AAAA,QACpE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,MAAM,OAAO,MAAM,OAAO;AACnD,UAAQ,MAAM,eAAe,OAAO,MAAM,IAAI;AAC9C,MAAI,OAAO,KAAK,GAAG;AACjB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,KAAK,GAAG;AACnB,WAAO,MAAM,aAAa,MAAM,KAAK,IAAI;AAAA,EAC3C;AACA,SAAO,OAAO,KAAK;AACrB;", "names": []}