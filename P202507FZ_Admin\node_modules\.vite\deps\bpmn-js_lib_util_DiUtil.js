import {
  hasCompensateEventDefinition,
  hasErrorEventDefinition,
  hasEscalationEventDefinition,
  hasEventDefinition,
  isEventSubProcess,
  isExpanded,
  isHorizontal,
  isInterrupting
} from "./chunk-7C6J56BH.js";
import "./chunk-FNF472WR.js";
import "./chunk-YTJ5ESGD.js";
import "./chunk-GFT2G5UO.js";
export {
  hasCompensateEventDefinition,
  hasErrorEventDefinition,
  hasEscalationEventDefinition,
  hasEventDefinition,
  isEventSubProcess,
  isExpanded,
  isHorizontal,
  isInterrupting
};
//# sourceMappingURL=bpmn-js_lib_util_DiUtil.js.map
