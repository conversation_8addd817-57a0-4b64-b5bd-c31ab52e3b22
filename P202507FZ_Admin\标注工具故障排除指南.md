# 标注工具故障排除指南

## 🚨 常见错误分析

### 1. 跨域和安全策略错误

#### 错误信息：
```
The Cross-Origin-Opener-Policy header has been ignored, because the URL's origin was untrustworthy
Refused to frame 'https://labelstud.io/' because it violates the following Content Security Policy directive
```

#### 原因：
- 外部系统使用HTTP协议，浏览器认为不安全
- Content Security Policy (CSP) 阻止iframe加载外部内容
- 跨域安全策略限制

#### 解决方案：

**方案1：配置外部系统（推荐）**
```nginx
# 在外部系统的Nginx配置中添加
add_header X-Frame-Options "SAMEORIGIN";
add_header Content-Security-Policy "frame-ancestors 'self' http://localhost:* http://127.0.0.1:*";
```

**方案2：使用HTTPS**
- 将外部系统升级为HTTPS协议
- 更新URL为 `https://***************:19003`

**方案3：本地代理（临时解决）**
```javascript
// 在vite.config.ts中添加代理
server: {
  proxy: {
    '/annotation-api': {
      target: 'http://***************:19003',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/annotation-api/, '')
    }
  }
}
```

### 2. 网络连接超时

#### 错误信息：
```
标注工具加载超时
GET https://cdn.cr-relay.com/v1/site/bbf4914f-17d9-46fd-b5a2-7f24948d400f/signals.js net::ERR_CONNECTION_RESET
```

#### 解决方案：

**检查网络连接：**
```bash
# 测试外部系统是否可访问
curl -I http://***************:19003

# 检查端口是否开放
telnet *************** 19003
```

**防火墙配置：**
```bash
# 在外部系统服务器上开放端口
sudo ufw allow 19003
# 或者
sudo iptables -A INPUT -p tcp --dport 19003 -j ACCEPT
```

### 3. JavaScript执行错误

#### 错误信息：
```
Uncaught TypeError: Illegal invocation
Refused to execute inline script because it violates CSP directive
```

#### 解决方案：
- 外部系统需要配置正确的CSP策略
- 避免使用内联脚本

## 🔧 配置优化建议

### 1. 更新iframe配置

如果外部系统支持，可以尝试更宽松的sandbox设置：

```vue
<iframe
  :src="currentUrl"
  sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-top-navigation allow-downloads"
  referrerpolicy="no-referrer-when-downgrade"
  loading="lazy"
></iframe>
```

### 2. 添加错误重试机制

```javascript
// 在组件中添加自动重试
const retryCount = ref(0)
const maxRetries = 3

const autoRetry = () => {
  if (retryCount.value < maxRetries) {
    retryCount.value++
    setTimeout(() => {
      reloadIframe()
    }, 5000) // 5秒后重试
  }
}
```

### 3. 使用PostMessage通信

如果需要与外部系统交互：

```javascript
// 监听来自iframe的消息
window.addEventListener('message', (event) => {
  if (event.origin === 'http://***************:19003') {
    console.log('收到标注工具消息:', event.data)
  }
})

// 向iframe发送消息
const sendMessage = (data) => {
  if (annotationIframe.value?.contentWindow) {
    annotationIframe.value.contentWindow.postMessage(data, 'http://***************:19003')
  }
}
```

## 🛡️ 安全建议

### 1. 限制iframe权限
```html
<!-- 最小权限原则 -->
<iframe sandbox="allow-scripts allow-same-origin"></iframe>
```

### 2. 验证外部系统
- 确认外部系统的安全性
- 定期检查系统更新
- 监控异常访问

### 3. 设置CSP策略
```html
<meta http-equiv="Content-Security-Policy" 
      content="frame-src 'self' http://***************:19003;">
```

## 🔍 调试工具

### 1. 浏览器开发者工具
- **Network标签**：检查网络请求状态
- **Console标签**：查看JavaScript错误
- **Security标签**：检查安全策略

### 2. 网络诊断命令
```bash
# 检查DNS解析
nslookup ***************

# 检查路由
traceroute ***************

# 检查端口连通性
nc -zv *************** 19003
```

### 3. 服务器日志
检查外部系统的访问日志：
```bash
# Nginx访问日志
tail -f /var/log/nginx/access.log

# 应用程序日志
tail -f /var/log/annotation-tool/app.log
```

## 📋 故障排除清单

### 基础检查
- [ ] 外部系统服务是否运行
- [ ] 网络连接是否正常
- [ ] 防火墙端口是否开放
- [ ] DNS解析是否正确

### 浏览器检查
- [ ] 清除浏览器缓存
- [ ] 禁用浏览器扩展
- [ ] 尝试无痕模式
- [ ] 检查浏览器安全设置

### 系统配置检查
- [ ] CSP策略配置
- [ ] CORS设置
- [ ] X-Frame-Options头部
- [ ] SSL证书状态

### 应用程序检查
- [ ] iframe sandbox设置
- [ ] 错误处理逻辑
- [ ] 超时时间配置
- [ ] 日志记录

## 📞 获取支持

如果以上方案都无法解决问题，请提供以下信息：

1. **浏览器信息**：版本、类型
2. **错误截图**：控制台错误信息
3. **网络环境**：是否使用代理、防火墙设置
4. **系统配置**：操作系统、网络配置
5. **复现步骤**：详细的操作步骤

联系开发团队时，请附上完整的错误日志和环境信息。
