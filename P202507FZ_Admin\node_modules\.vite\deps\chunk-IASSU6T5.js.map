{"version": 3, "sources": ["../../.pnpm/ufo@1.5.4/node_modules/ufo/dist/index.mjs"], "sourcesContent": ["const n = /[^\\0-\\x7E]/;\nconst t = /[\\x2E\\u3002\\uFF0E\\uFF61]/g;\nconst o = {\n  overflow: \"Overflow Error\",\n  \"not-basic\": \"Illegal Input\",\n  \"invalid-input\": \"Invalid Input\"\n};\nconst e = Math.floor;\nconst r = String.fromCharCode;\nfunction s(n2) {\n  throw new RangeError(o[n2]);\n}\nconst c = function(n2, t2) {\n  return n2 + 22 + 75 * (n2 < 26) - ((t2 != 0) << 5);\n};\nconst u = function(n2, t2, o2) {\n  let r2 = 0;\n  for (n2 = o2 ? e(n2 / 700) : n2 >> 1, n2 += e(n2 / t2); n2 > 455; r2 += 36) {\n    n2 = e(n2 / 35);\n  }\n  return e(r2 + 36 * n2 / (n2 + 38));\n};\nfunction toASCII(o2) {\n  return function(n2, o3) {\n    const e2 = n2.split(\"@\");\n    let r2 = \"\";\n    e2.length > 1 && (r2 = e2[0] + \"@\", n2 = e2[1]);\n    const s2 = function(n3, t2) {\n      const o4 = [];\n      let e3 = n3.length;\n      for (; e3--; ) {\n        o4[e3] = t2(n3[e3]);\n      }\n      return o4;\n    }((n2 = n2.replace(t, \".\")).split(\".\"), o3).join(\".\");\n    return r2 + s2;\n  }(o2, function(t2) {\n    return n.test(t2) ? \"xn--\" + function(n2) {\n      const t3 = [];\n      const o3 = (n2 = function(n3) {\n        const t4 = [];\n        let o4 = 0;\n        const e2 = n3.length;\n        for (; o4 < e2; ) {\n          const r2 = n3.charCodeAt(o4++);\n          if (r2 >= 55296 && r2 <= 56319 && o4 < e2) {\n            const e3 = n3.charCodeAt(o4++);\n            (64512 & e3) == 56320 ? t4.push(((1023 & r2) << 10) + (1023 & e3) + 65536) : (t4.push(r2), o4--);\n          } else {\n            t4.push(r2);\n          }\n        }\n        return t4;\n      }(n2)).length;\n      let f = 128;\n      let i = 0;\n      let l = 72;\n      for (const o4 of n2) {\n        o4 < 128 && t3.push(r(o4));\n      }\n      const h = t3.length;\n      let p = h;\n      for (h && t3.push(\"-\"); p < o3; ) {\n        let o4 = 2147483647;\n        for (const t4 of n2) {\n          t4 >= f && t4 < o4 && (o4 = t4);\n        }\n        const a = p + 1;\n        o4 - f > e((2147483647 - i) / a) && s(\"overflow\"), i += (o4 - f) * a, f = o4;\n        for (const o5 of n2) {\n          if (o5 < f && ++i > 2147483647 && s(\"overflow\"), o5 == f) {\n            let n3 = i;\n            for (let o6 = 36; ; o6 += 36) {\n              const s2 = o6 <= l ? 1 : o6 >= l + 26 ? 26 : o6 - l;\n              if (n3 < s2) {\n                break;\n              }\n              const u2 = n3 - s2;\n              const f2 = 36 - s2;\n              t3.push(r(c(s2 + u2 % f2, 0))), n3 = e(u2 / f2);\n            }\n            t3.push(r(c(n3, 0))), l = u(i, a, p == h), i = 0, ++p;\n          }\n        }\n        ++i, ++f;\n      }\n      return t3.join(\"\");\n    }(t2) : t2;\n  });\n}\n\nconst HASH_RE = /#/g;\nconst AMPERSAND_RE = /&/g;\nconst SLASH_RE = /\\//g;\nconst EQUAL_RE = /=/g;\nconst IM_RE = /\\?/g;\nconst PLUS_RE = /\\+/g;\nconst ENC_CARET_RE = /%5e/gi;\nconst ENC_BACKTICK_RE = /%60/gi;\nconst ENC_CURLY_OPEN_RE = /%7b/gi;\nconst ENC_PIPE_RE = /%7c/gi;\nconst ENC_CURLY_CLOSE_RE = /%7d/gi;\nconst ENC_SPACE_RE = /%20/gi;\nconst ENC_SLASH_RE = /%2f/gi;\nconst ENC_ENC_SLASH_RE = /%252f/gi;\nfunction encode(text) {\n  return encodeURI(\"\" + text).replace(ENC_PIPE_RE, \"|\");\n}\nfunction encodeHash(text) {\n  return encode(text).replace(ENC_CURLY_OPEN_RE, \"{\").replace(ENC_CURLY_CLOSE_RE, \"}\").replace(ENC_CARET_RE, \"^\");\n}\nfunction encodeQueryValue(input) {\n  return encode(typeof input === \"string\" ? input : JSON.stringify(input)).replace(PLUS_RE, \"%2B\").replace(ENC_SPACE_RE, \"+\").replace(HASH_RE, \"%23\").replace(AMPERSAND_RE, \"%26\").replace(ENC_BACKTICK_RE, \"`\").replace(ENC_CARET_RE, \"^\").replace(SLASH_RE, \"%2F\");\n}\nfunction encodeQueryKey(text) {\n  return encodeQueryValue(text).replace(EQUAL_RE, \"%3D\");\n}\nfunction encodePath(text) {\n  return encode(text).replace(HASH_RE, \"%23\").replace(IM_RE, \"%3F\").replace(ENC_ENC_SLASH_RE, \"%2F\").replace(AMPERSAND_RE, \"%26\").replace(PLUS_RE, \"%2B\");\n}\nfunction encodeParam(text) {\n  return encodePath(text).replace(SLASH_RE, \"%2F\");\n}\nfunction decode(text = \"\") {\n  try {\n    return decodeURIComponent(\"\" + text);\n  } catch {\n    return \"\" + text;\n  }\n}\nfunction decodePath(text) {\n  return decode(text.replace(ENC_SLASH_RE, \"%252F\"));\n}\nfunction decodeQueryKey(text) {\n  return decode(text.replace(PLUS_RE, \" \"));\n}\nfunction decodeQueryValue(text) {\n  return decode(text.replace(PLUS_RE, \" \"));\n}\nfunction encodeHost(name = \"\") {\n  return toASCII(name);\n}\n\nfunction parseQuery(parametersString = \"\") {\n  const object = {};\n  if (parametersString[0] === \"?\") {\n    parametersString = parametersString.slice(1);\n  }\n  for (const parameter of parametersString.split(\"&\")) {\n    const s = parameter.match(/([^=]+)=?(.*)/) || [];\n    if (s.length < 2) {\n      continue;\n    }\n    const key = decodeQueryKey(s[1]);\n    if (key === \"__proto__\" || key === \"constructor\") {\n      continue;\n    }\n    const value = decodeQueryValue(s[2] || \"\");\n    if (object[key] === void 0) {\n      object[key] = value;\n    } else if (Array.isArray(object[key])) {\n      object[key].push(value);\n    } else {\n      object[key] = [object[key], value];\n    }\n  }\n  return object;\n}\nfunction encodeQueryItem(key, value) {\n  if (typeof value === \"number\" || typeof value === \"boolean\") {\n    value = String(value);\n  }\n  if (!value) {\n    return encodeQueryKey(key);\n  }\n  if (Array.isArray(value)) {\n    return value.map((_value) => `${encodeQueryKey(key)}=${encodeQueryValue(_value)}`).join(\"&\");\n  }\n  return `${encodeQueryKey(key)}=${encodeQueryValue(value)}`;\n}\nfunction stringifyQuery(query) {\n  return Object.keys(query).filter((k) => query[k] !== void 0).map((k) => encodeQueryItem(k, query[k])).filter(Boolean).join(\"&\");\n}\n\nconst PROTOCOL_STRICT_REGEX = /^[\\s\\w\\0+.-]{2,}:([/\\\\]{1,2})/;\nconst PROTOCOL_REGEX = /^[\\s\\w\\0+.-]{2,}:([/\\\\]{2})?/;\nconst PROTOCOL_RELATIVE_REGEX = /^([/\\\\]\\s*){2,}[^/\\\\]/;\nconst PROTOCOL_SCRIPT_RE = /^[\\s\\0]*(blob|data|javascript|vbscript):$/i;\nconst TRAILING_SLASH_RE = /\\/$|\\/\\?|\\/#/;\nconst JOIN_LEADING_SLASH_RE = /^\\.?\\//;\nfunction isRelative(inputString) {\n  return [\"./\", \"../\"].some((string_) => inputString.startsWith(string_));\n}\nfunction hasProtocol(inputString, opts = {}) {\n  if (typeof opts === \"boolean\") {\n    opts = { acceptRelative: opts };\n  }\n  if (opts.strict) {\n    return PROTOCOL_STRICT_REGEX.test(inputString);\n  }\n  return PROTOCOL_REGEX.test(inputString) || (opts.acceptRelative ? PROTOCOL_RELATIVE_REGEX.test(inputString) : false);\n}\nfunction isScriptProtocol(protocol) {\n  return !!protocol && PROTOCOL_SCRIPT_RE.test(protocol);\n}\nfunction hasTrailingSlash(input = \"\", respectQueryAndFragment) {\n  if (!respectQueryAndFragment) {\n    return input.endsWith(\"/\");\n  }\n  return TRAILING_SLASH_RE.test(input);\n}\nfunction withoutTrailingSlash(input = \"\", respectQueryAndFragment) {\n  if (!respectQueryAndFragment) {\n    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || \"/\";\n  }\n  if (!hasTrailingSlash(input, true)) {\n    return input || \"/\";\n  }\n  let path = input;\n  let fragment = \"\";\n  const fragmentIndex = input.indexOf(\"#\");\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n  }\n  const [s0, ...s] = path.split(\"?\");\n  const cleanPath = s0.endsWith(\"/\") ? s0.slice(0, -1) : s0;\n  return (cleanPath || \"/\") + (s.length > 0 ? `?${s.join(\"?\")}` : \"\") + fragment;\n}\nfunction withTrailingSlash(input = \"\", respectQueryAndFragment) {\n  if (!respectQueryAndFragment) {\n    return input.endsWith(\"/\") ? input : input + \"/\";\n  }\n  if (hasTrailingSlash(input, true)) {\n    return input || \"/\";\n  }\n  let path = input;\n  let fragment = \"\";\n  const fragmentIndex = input.indexOf(\"#\");\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n    if (!path) {\n      return fragment;\n    }\n  }\n  const [s0, ...s] = path.split(\"?\");\n  return s0 + \"/\" + (s.length > 0 ? `?${s.join(\"?\")}` : \"\") + fragment;\n}\nfunction hasLeadingSlash(input = \"\") {\n  return input.startsWith(\"/\");\n}\nfunction withoutLeadingSlash(input = \"\") {\n  return (hasLeadingSlash(input) ? input.slice(1) : input) || \"/\";\n}\nfunction withLeadingSlash(input = \"\") {\n  return hasLeadingSlash(input) ? input : \"/\" + input;\n}\nfunction cleanDoubleSlashes(input = \"\") {\n  return input.split(\"://\").map((string_) => string_.replace(/\\/{2,}/g, \"/\")).join(\"://\");\n}\nfunction withBase(input, base) {\n  if (isEmptyURL(base) || hasProtocol(input)) {\n    return input;\n  }\n  const _base = withoutTrailingSlash(base);\n  if (input.startsWith(_base)) {\n    return input;\n  }\n  return joinURL(_base, input);\n}\nfunction withoutBase(input, base) {\n  if (isEmptyURL(base)) {\n    return input;\n  }\n  const _base = withoutTrailingSlash(base);\n  if (!input.startsWith(_base)) {\n    return input;\n  }\n  const trimmed = input.slice(_base.length);\n  return trimmed[0] === \"/\" ? trimmed : \"/\" + trimmed;\n}\nfunction withQuery(input, query) {\n  const parsed = parseURL(input);\n  const mergedQuery = { ...parseQuery(parsed.search), ...query };\n  parsed.search = stringifyQuery(mergedQuery);\n  return stringifyParsedURL(parsed);\n}\nfunction getQuery(input) {\n  return parseQuery(parseURL(input).search);\n}\nfunction isEmptyURL(url) {\n  return !url || url === \"/\";\n}\nfunction isNonEmptyURL(url) {\n  return url && url !== \"/\";\n}\nfunction joinURL(base, ...input) {\n  let url = base || \"\";\n  for (const segment of input.filter((url2) => isNonEmptyURL(url2))) {\n    if (url) {\n      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, \"\");\n      url = withTrailingSlash(url) + _segment;\n    } else {\n      url = segment;\n    }\n  }\n  return url;\n}\nfunction joinRelativeURL(..._input) {\n  const JOIN_SEGMENT_SPLIT_RE = /\\/(?!\\/)/;\n  const input = _input.filter(Boolean);\n  const segments = [];\n  let segmentsDepth = 0;\n  for (const i of input) {\n    if (!i || i === \"/\") {\n      continue;\n    }\n    for (const [sindex, s] of i.split(JOIN_SEGMENT_SPLIT_RE).entries()) {\n      if (!s || s === \".\") {\n        continue;\n      }\n      if (s === \"..\") {\n        if (segments.length === 1 && hasProtocol(segments[0])) {\n          continue;\n        }\n        segments.pop();\n        segmentsDepth--;\n        continue;\n      }\n      if (sindex === 1 && segments[segments.length - 1]?.endsWith(\":/\")) {\n        segments[segments.length - 1] += \"/\" + s;\n        continue;\n      }\n      segments.push(s);\n      segmentsDepth++;\n    }\n  }\n  let url = segments.join(\"/\");\n  if (segmentsDepth >= 0) {\n    if (input[0]?.startsWith(\"/\") && !url.startsWith(\"/\")) {\n      url = \"/\" + url;\n    } else if (input[0]?.startsWith(\"./\") && !url.startsWith(\"./\")) {\n      url = \"./\" + url;\n    }\n  } else {\n    url = \"../\".repeat(-1 * segmentsDepth) + url;\n  }\n  if (input[input.length - 1]?.endsWith(\"/\") && !url.endsWith(\"/\")) {\n    url += \"/\";\n  }\n  return url;\n}\nfunction withHttp(input) {\n  return withProtocol(input, \"http://\");\n}\nfunction withHttps(input) {\n  return withProtocol(input, \"https://\");\n}\nfunction withoutProtocol(input) {\n  return withProtocol(input, \"\");\n}\nfunction withProtocol(input, protocol) {\n  let match = input.match(PROTOCOL_REGEX);\n  if (!match) {\n    match = input.match(/^\\/{2,}/);\n  }\n  if (!match) {\n    return protocol + input;\n  }\n  return protocol + input.slice(match[0].length);\n}\nfunction normalizeURL(input) {\n  const parsed = parseURL(input);\n  parsed.pathname = encodePath(decodePath(parsed.pathname));\n  parsed.hash = encodeHash(decode(parsed.hash));\n  parsed.host = encodeHost(decode(parsed.host));\n  parsed.search = stringifyQuery(parseQuery(parsed.search));\n  return stringifyParsedURL(parsed);\n}\nfunction resolveURL(base = \"\", ...inputs) {\n  if (typeof base !== \"string\") {\n    throw new TypeError(\n      `URL input should be string received ${typeof base} (${base})`\n    );\n  }\n  const filteredInputs = inputs.filter((input) => isNonEmptyURL(input));\n  if (filteredInputs.length === 0) {\n    return base;\n  }\n  const url = parseURL(base);\n  for (const inputSegment of filteredInputs) {\n    const urlSegment = parseURL(inputSegment);\n    if (urlSegment.pathname) {\n      url.pathname = withTrailingSlash(url.pathname) + withoutLeadingSlash(urlSegment.pathname);\n    }\n    if (urlSegment.hash && urlSegment.hash !== \"#\") {\n      url.hash = urlSegment.hash;\n    }\n    if (urlSegment.search && urlSegment.search !== \"?\") {\n      if (url.search && url.search !== \"?\") {\n        const queryString = stringifyQuery({\n          ...parseQuery(url.search),\n          ...parseQuery(urlSegment.search)\n        });\n        url.search = queryString.length > 0 ? \"?\" + queryString : \"\";\n      } else {\n        url.search = urlSegment.search;\n      }\n    }\n  }\n  return stringifyParsedURL(url);\n}\nfunction isSamePath(p1, p2) {\n  return decode(withoutTrailingSlash(p1)) === decode(withoutTrailingSlash(p2));\n}\nfunction isEqual(a, b, options = {}) {\n  if (!options.trailingSlash) {\n    a = withTrailingSlash(a);\n    b = withTrailingSlash(b);\n  }\n  if (!options.leadingSlash) {\n    a = withLeadingSlash(a);\n    b = withLeadingSlash(b);\n  }\n  if (!options.encoding) {\n    a = decode(a);\n    b = decode(b);\n  }\n  return a === b;\n}\nfunction withFragment(input, hash) {\n  if (!hash || hash === \"#\") {\n    return input;\n  }\n  const parsed = parseURL(input);\n  parsed.hash = hash === \"\" ? \"\" : \"#\" + encodeHash(hash);\n  return stringifyParsedURL(parsed);\n}\nfunction withoutFragment(input) {\n  return stringifyParsedURL({ ...parseURL(input), hash: \"\" });\n}\nfunction withoutHost(input) {\n  const parsed = parseURL(input);\n  return (parsed.pathname || \"/\") + parsed.search + parsed.hash;\n}\n\nconst protocolRelative = Symbol.for(\"ufo:protocolRelative\");\nfunction parseURL(input = \"\", defaultProto) {\n  const _specialProtoMatch = input.match(\n    /^[\\s\\0]*(blob:|data:|javascript:|vbscript:)(.*)/i\n  );\n  if (_specialProtoMatch) {\n    const [, _proto, _pathname = \"\"] = _specialProtoMatch;\n    return {\n      protocol: _proto.toLowerCase(),\n      pathname: _pathname,\n      href: _proto + _pathname,\n      auth: \"\",\n      host: \"\",\n      search: \"\",\n      hash: \"\"\n    };\n  }\n  if (!hasProtocol(input, { acceptRelative: true })) {\n    return defaultProto ? parseURL(defaultProto + input) : parsePath(input);\n  }\n  const [, protocol = \"\", auth, hostAndPath = \"\"] = input.replace(/\\\\/g, \"/\").match(/^[\\s\\0]*([\\w+.-]{2,}:)?\\/\\/([^/@]+@)?(.*)/) || [];\n  let [, host = \"\", path = \"\"] = hostAndPath.match(/([^#/?]*)(.*)?/) || [];\n  if (protocol === \"file:\") {\n    path = path.replace(/\\/(?=[A-Za-z]:)/, \"\");\n  }\n  const { pathname, search, hash } = parsePath(path);\n  return {\n    protocol: protocol.toLowerCase(),\n    auth: auth ? auth.slice(0, Math.max(0, auth.length - 1)) : \"\",\n    host,\n    pathname,\n    search,\n    hash,\n    [protocolRelative]: !protocol\n  };\n}\nfunction parsePath(input = \"\") {\n  const [pathname = \"\", search = \"\", hash = \"\"] = (input.match(/([^#?]*)(\\?[^#]*)?(#.*)?/) || []).splice(1);\n  return {\n    pathname,\n    search,\n    hash\n  };\n}\nfunction parseAuth(input = \"\") {\n  const [username, password] = input.split(\":\");\n  return {\n    username: decode(username),\n    password: decode(password)\n  };\n}\nfunction parseHost(input = \"\") {\n  const [hostname, port] = (input.match(/([^/:]*):?(\\d+)?/) || []).splice(1);\n  return {\n    hostname: decode(hostname),\n    port\n  };\n}\nfunction stringifyParsedURL(parsed) {\n  const pathname = parsed.pathname || \"\";\n  const search = parsed.search ? (parsed.search.startsWith(\"?\") ? \"\" : \"?\") + parsed.search : \"\";\n  const hash = parsed.hash || \"\";\n  const auth = parsed.auth ? parsed.auth + \"@\" : \"\";\n  const host = parsed.host || \"\";\n  const proto = parsed.protocol || parsed[protocolRelative] ? (parsed.protocol || \"\") + \"//\" : \"\";\n  return proto + auth + host + pathname + search + hash;\n}\nconst FILENAME_STRICT_REGEX = /\\/([^/]+\\.[^/]+)$/;\nconst FILENAME_REGEX = /\\/([^/]+)$/;\nfunction parseFilename(input = \"\", { strict }) {\n  const { pathname } = parseURL(input);\n  const matches = strict ? pathname.match(FILENAME_STRICT_REGEX) : pathname.match(FILENAME_REGEX);\n  return matches ? matches[1] : void 0;\n}\n\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass $URL {\n  constructor(input = \"\") {\n    __publicField(this, \"protocol\");\n    __publicField(this, \"host\");\n    __publicField(this, \"auth\");\n    __publicField(this, \"pathname\");\n    __publicField(this, \"query\", {});\n    __publicField(this, \"hash\");\n    if (typeof input !== \"string\") {\n      throw new TypeError(\n        `URL input should be string received ${typeof input} (${input})`\n      );\n    }\n    const parsed = parseURL(input);\n    this.protocol = decode(parsed.protocol);\n    this.host = decode(parsed.host);\n    this.auth = decode(parsed.auth);\n    this.pathname = decodePath(parsed.pathname);\n    this.query = parseQuery(parsed.search);\n    this.hash = decode(parsed.hash);\n  }\n  get hostname() {\n    return parseHost(this.host).hostname;\n  }\n  get port() {\n    return parseHost(this.host).port || \"\";\n  }\n  get username() {\n    return parseAuth(this.auth).username;\n  }\n  get password() {\n    return parseAuth(this.auth).password || \"\";\n  }\n  get hasProtocol() {\n    return this.protocol.length;\n  }\n  get isAbsolute() {\n    return this.hasProtocol || this.pathname[0] === \"/\";\n  }\n  get search() {\n    const q = stringifyQuery(this.query);\n    return q.length > 0 ? \"?\" + q : \"\";\n  }\n  get searchParams() {\n    const p = new URLSearchParams();\n    for (const name in this.query) {\n      const value = this.query[name];\n      if (Array.isArray(value)) {\n        for (const v of value) {\n          p.append(name, v);\n        }\n      } else {\n        p.append(\n          name,\n          typeof value === \"string\" ? value : JSON.stringify(value)\n        );\n      }\n    }\n    return p;\n  }\n  get origin() {\n    return (this.protocol ? this.protocol + \"//\" : \"\") + encodeHost(this.host);\n  }\n  get fullpath() {\n    return encodePath(this.pathname) + this.search + encodeHash(this.hash);\n  }\n  get encodedAuth() {\n    if (!this.auth) {\n      return \"\";\n    }\n    const { username, password } = parseAuth(this.auth);\n    return encodeURIComponent(username) + (password ? \":\" + encodeURIComponent(password) : \"\");\n  }\n  get href() {\n    const auth = this.encodedAuth;\n    const originWithAuth = (this.protocol ? this.protocol + \"//\" : \"\") + (auth ? auth + \"@\" : \"\") + encodeHost(this.host);\n    return this.hasProtocol && this.isAbsolute ? originWithAuth + this.fullpath : this.fullpath;\n  }\n  append(url) {\n    if (url.hasProtocol) {\n      throw new Error(\"Cannot append a URL with protocol\");\n    }\n    Object.assign(this.query, url.query);\n    if (url.pathname) {\n      this.pathname = withTrailingSlash(this.pathname) + withoutLeadingSlash(url.pathname);\n    }\n    if (url.hash) {\n      this.hash = url.hash;\n    }\n  }\n  toJSON() {\n    return this.href;\n  }\n  toString() {\n    return this.href;\n  }\n}\nfunction createURL(input) {\n  return new $URL(input);\n}\n\nexport { $URL, cleanDoubleSlashes, createURL, decode, decodePath, decodeQueryKey, decodeQueryValue, encode, encodeHash, encodeHost, encodeParam, encodePath, encodeQueryItem, encodeQueryKey, encodeQueryValue, getQuery, hasLeadingSlash, hasProtocol, hasTrailingSlash, isEmptyURL, isEqual, isNonEmptyURL, isRelative, isSamePath, isScriptProtocol, joinRelativeURL, joinURL, normalizeURL, parseAuth, parseFilename, parseHost, parsePath, parseQuery, parseURL, resolveURL, stringifyParsedURL, stringifyQuery, withBase, withFragment, withHttp, withHttps, withLeadingSlash, withProtocol, withQuery, withTrailingSlash, withoutBase, withoutFragment, withoutHost, withoutLeadingSlash, withoutProtocol, withoutTrailingSlash };\n"], "mappings": ";AAQA,IAAM,IAAI,OAAO;AAmFjB,IAAM,UAAU;AAChB,IAAM,eAAe;AACrB,IAAM,WAAW;AACjB,IAAM,WAAW;AAEjB,IAAM,UAAU;AAChB,IAAM,eAAe;AACrB,IAAM,kBAAkB;AAExB,IAAM,cAAc;AAEpB,IAAM,eAAe;AAGrB,SAAS,OAAO,MAAM;AACpB,SAAO,UAAU,KAAK,IAAI,EAAE,QAAQ,aAAa,GAAG;AACtD;AAIA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,OAAO,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU,KAAK,CAAC,EAAE,QAAQ,SAAS,KAAK,EAAE,QAAQ,cAAc,GAAG,EAAE,QAAQ,SAAS,KAAK,EAAE,QAAQ,cAAc,KAAK,EAAE,QAAQ,iBAAiB,GAAG,EAAE,QAAQ,cAAc,GAAG,EAAE,QAAQ,UAAU,KAAK;AACnQ;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,iBAAiB,IAAI,EAAE,QAAQ,UAAU,KAAK;AACvD;AAOA,SAAS,OAAO,OAAO,IAAI;AACzB,MAAI;AACF,WAAO,mBAAmB,KAAK,IAAI;AAAA,EACrC,QAAQ;AACN,WAAO,KAAK;AAAA,EACd;AACF;AAIA,SAAS,eAAe,MAAM;AAC5B,SAAO,OAAO,KAAK,QAAQ,SAAS,GAAG,CAAC;AAC1C;AACA,SAAS,iBAAiB,MAAM;AAC9B,SAAO,OAAO,KAAK,QAAQ,SAAS,GAAG,CAAC;AAC1C;AAKA,SAAS,WAAW,mBAAmB,IAAI;AACzC,QAAM,SAAS,CAAC;AAChB,MAAI,iBAAiB,CAAC,MAAM,KAAK;AAC/B,uBAAmB,iBAAiB,MAAM,CAAC;AAAA,EAC7C;AACA,aAAW,aAAa,iBAAiB,MAAM,GAAG,GAAG;AACnD,UAAM,IAAI,UAAU,MAAM,eAAe,KAAK,CAAC;AAC/C,QAAI,EAAE,SAAS,GAAG;AAChB;AAAA,IACF;AACA,UAAM,MAAM,eAAe,EAAE,CAAC,CAAC;AAC/B,QAAI,QAAQ,eAAe,QAAQ,eAAe;AAChD;AAAA,IACF;AACA,UAAM,QAAQ,iBAAiB,EAAE,CAAC,KAAK,EAAE;AACzC,QAAI,OAAO,GAAG,MAAM,QAAQ;AAC1B,aAAO,GAAG,IAAI;AAAA,IAChB,WAAW,MAAM,QAAQ,OAAO,GAAG,CAAC,GAAG;AACrC,aAAO,GAAG,EAAE,KAAK,KAAK;AAAA,IACxB,OAAO;AACL,aAAO,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;AAAA,IACnC;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,OAAO;AACnC,MAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;AAC3D,YAAQ,OAAO,KAAK;AAAA,EACtB;AACA,MAAI,CAAC,OAAO;AACV,WAAO,eAAe,GAAG;AAAA,EAC3B;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,IAAI,CAAC,WAAW,GAAG,eAAe,GAAG,CAAC,IAAI,iBAAiB,MAAM,CAAC,EAAE,EAAE,KAAK,GAAG;AAAA,EAC7F;AACA,SAAO,GAAG,eAAe,GAAG,CAAC,IAAI,iBAAiB,KAAK,CAAC;AAC1D;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,MAAM,MAAM,CAAC,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM,gBAAgB,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAChI;AAEA,IAAM,wBAAwB;AAC9B,IAAM,iBAAiB;AACvB,IAAM,0BAA0B;AAEhC,IAAM,oBAAoB;AAC1B,IAAM,wBAAwB;AAI9B,SAAS,YAAY,aAAa,OAAO,CAAC,GAAG;AAC3C,MAAI,OAAO,SAAS,WAAW;AAC7B,WAAO,EAAE,gBAAgB,KAAK;AAAA,EAChC;AACA,MAAI,KAAK,QAAQ;AACf,WAAO,sBAAsB,KAAK,WAAW;AAAA,EAC/C;AACA,SAAO,eAAe,KAAK,WAAW,MAAM,KAAK,iBAAiB,wBAAwB,KAAK,WAAW,IAAI;AAChH;AAIA,SAAS,iBAAiB,QAAQ,IAAI,yBAAyB;AAC7D,MAAI,CAAC,yBAAyB;AAC5B,WAAO,MAAM,SAAS,GAAG;AAAA,EAC3B;AACA,SAAO,kBAAkB,KAAK,KAAK;AACrC;AACA,SAAS,qBAAqB,QAAQ,IAAI,yBAAyB;AACjE,MAAI,CAAC,yBAAyB;AAC5B,YAAQ,iBAAiB,KAAK,IAAI,MAAM,MAAM,GAAG,EAAE,IAAI,UAAU;AAAA,EACnE;AACA,MAAI,CAAC,iBAAiB,OAAO,IAAI,GAAG;AAClC,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,OAAO;AACX,MAAI,WAAW;AACf,QAAM,gBAAgB,MAAM,QAAQ,GAAG;AACvC,MAAI,iBAAiB,GAAG;AACtB,WAAO,MAAM,MAAM,GAAG,aAAa;AACnC,eAAW,MAAM,MAAM,aAAa;AAAA,EACtC;AACA,QAAM,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,GAAG;AACjC,QAAM,YAAY,GAAG,SAAS,GAAG,IAAI,GAAG,MAAM,GAAG,EAAE,IAAI;AACvD,UAAQ,aAAa,QAAQ,EAAE,SAAS,IAAI,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,MAAM;AACxE;AACA,SAAS,kBAAkB,QAAQ,IAAI,yBAAyB;AAC9D,MAAI,CAAC,yBAAyB;AAC5B,WAAO,MAAM,SAAS,GAAG,IAAI,QAAQ,QAAQ;AAAA,EAC/C;AACA,MAAI,iBAAiB,OAAO,IAAI,GAAG;AACjC,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,OAAO;AACX,MAAI,WAAW;AACf,QAAM,gBAAgB,MAAM,QAAQ,GAAG;AACvC,MAAI,iBAAiB,GAAG;AACtB,WAAO,MAAM,MAAM,GAAG,aAAa;AACnC,eAAW,MAAM,MAAM,aAAa;AACpC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,GAAG;AACjC,SAAO,KAAK,OAAO,EAAE,SAAS,IAAI,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,MAAM;AAC9D;AAaA,SAAS,SAAS,OAAO,MAAM;AAC7B,MAAI,WAAW,IAAI,KAAK,YAAY,KAAK,GAAG;AAC1C,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,qBAAqB,IAAI;AACvC,MAAI,MAAM,WAAW,KAAK,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,OAAO,KAAK;AAC7B;AAYA,SAAS,UAAU,OAAO,OAAO;AAC/B,QAAM,SAAS,SAAS,KAAK;AAC7B,QAAM,cAAc,EAAE,GAAG,WAAW,OAAO,MAAM,GAAG,GAAG,MAAM;AAC7D,SAAO,SAAS,eAAe,WAAW;AAC1C,SAAO,mBAAmB,MAAM;AAClC;AAIA,SAAS,WAAW,KAAK;AACvB,SAAO,CAAC,OAAO,QAAQ;AACzB;AACA,SAAS,cAAc,KAAK;AAC1B,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,QAAQ,SAAS,OAAO;AAC/B,MAAI,MAAM,QAAQ;AAClB,aAAW,WAAW,MAAM,OAAO,CAAC,SAAS,cAAc,IAAI,CAAC,GAAG;AACjE,QAAI,KAAK;AACP,YAAM,WAAW,QAAQ,QAAQ,uBAAuB,EAAE;AAC1D,YAAM,kBAAkB,GAAG,IAAI;AAAA,IACjC,OAAO;AACL,YAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO;AACT;AA2IA,IAAM,mBAAmB,OAAO,IAAI,sBAAsB;AAC1D,SAAS,SAAS,QAAQ,IAAI,cAAc;AAC1C,QAAM,qBAAqB,MAAM;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,oBAAoB;AACtB,UAAM,CAAC,EAAE,QAAQ,YAAY,EAAE,IAAI;AACnC,WAAO;AAAA,MACL,UAAU,OAAO,YAAY;AAAA,MAC7B,UAAU;AAAA,MACV,MAAM,SAAS;AAAA,MACf,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AACA,MAAI,CAAC,YAAY,OAAO,EAAE,gBAAgB,KAAK,CAAC,GAAG;AACjD,WAAO,eAAe,SAAS,eAAe,KAAK,IAAI,UAAU,KAAK;AAAA,EACxE;AACA,QAAM,CAAC,EAAE,WAAW,IAAI,MAAM,cAAc,EAAE,IAAI,MAAM,QAAQ,OAAO,GAAG,EAAE,MAAM,2CAA2C,KAAK,CAAC;AACnI,MAAI,CAAC,EAAE,OAAO,IAAI,OAAO,EAAE,IAAI,YAAY,MAAM,gBAAgB,KAAK,CAAC;AACvE,MAAI,aAAa,SAAS;AACxB,WAAO,KAAK,QAAQ,mBAAmB,EAAE;AAAA,EAC3C;AACA,QAAM,EAAE,UAAU,QAAQ,KAAK,IAAI,UAAU,IAAI;AACjD,SAAO;AAAA,IACL,UAAU,SAAS,YAAY;AAAA,IAC/B,MAAM,OAAO,KAAK,MAAM,GAAG,KAAK,IAAI,GAAG,KAAK,SAAS,CAAC,CAAC,IAAI;AAAA,IAC3D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,gBAAgB,GAAG,CAAC;AAAA,EACvB;AACF;AACA,SAAS,UAAU,QAAQ,IAAI;AAC7B,QAAM,CAAC,WAAW,IAAI,SAAS,IAAI,OAAO,EAAE,KAAK,MAAM,MAAM,0BAA0B,KAAK,CAAC,GAAG,OAAO,CAAC;AACxG,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAeA,SAAS,mBAAmB,QAAQ;AAClC,QAAM,WAAW,OAAO,YAAY;AACpC,QAAM,SAAS,OAAO,UAAU,OAAO,OAAO,WAAW,GAAG,IAAI,KAAK,OAAO,OAAO,SAAS;AAC5F,QAAM,OAAO,OAAO,QAAQ;AAC5B,QAAM,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM;AAC/C,QAAM,OAAO,OAAO,QAAQ;AAC5B,QAAM,QAAQ,OAAO,YAAY,OAAO,gBAAgB,KAAK,OAAO,YAAY,MAAM,OAAO;AAC7F,SAAO,QAAQ,OAAO,OAAO,WAAW,SAAS;AACnD;", "names": []}