{"version": 3, "sources": ["../../.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/Cursor.js", "../../.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/ClickTrap.js", "../../.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/PositionUtil.js", "../../.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/navigation/movecanvas/MoveCanvas.js", "../../.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/navigation/movecanvas/index.js"], "sourcesContent": ["import {\n  classes as domClasses\n} from 'min-dom';\n\nvar CURSOR_CLS_PATTERN = /^djs-cursor-.*$/;\n\n/**\n * @param {string} mode\n */\nexport function set(mode) {\n  var classes = domClasses(document.body);\n\n  classes.removeMatching(CURSOR_CLS_PATTERN);\n\n  if (mode) {\n    classes.add('djs-cursor-' + mode);\n  }\n}\n\nexport function unset() {\n  set(null);\n}\n\n/**\n * @param {string} mode\n *\n * @return {boolean}\n */\nexport function has(mode) {\n  var classes = domClasses(document.body);\n\n  return classes.has('djs-cursor-' + mode);\n}\n", "/**\n * @typedef {import('../core/EventBus').EventBus} EventBus\n */\n\nvar TRAP_PRIORITY = 5000;\n\n/**\n * Installs a click trap that prevents a ghost click following a dragging operation.\n *\n * @param {EventBus} eventBus\n * @param {string} [eventName='element.click']\n *\n * @return {() => void} a function to immediately remove the installed trap.\n */\nexport function install(eventBus, eventName) {\n\n  eventName = eventName || 'element.click';\n\n  function trap() {\n    return false;\n  }\n\n  eventBus.once(eventName, TRAP_PRIORITY, trap);\n\n  return function() {\n    eventBus.off(eventName, trap);\n  };\n}", "/**\n * @typedef {import('../util/Types').Point} Point\n * @typedef {import('../util/Types').Rect} Rect\n */\n\n/**\n * @param {Rect} bounds\n * @return {Point}\n */\nexport function center(bounds) {\n  return {\n    x: bounds.x + (bounds.width / 2),\n    y: bounds.y + (bounds.height / 2)\n  };\n}\n\n\n/**\n * @param {Point} a\n * @param {Point} b\n * @return {Point}\n */\nexport function delta(a, b) {\n  return {\n    x: a.x - b.x,\n    y: a.y - b.y\n  };\n}\n", "import {\n  set as cursorSet,\n  unset as cursorUnset\n} from '../../util/Cursor';\n\nimport {\n  install as installClickTrap\n} from '../../util/ClickTrap';\n\nimport {\n  delta as deltaPos\n} from '../../util/PositionUtil';\n\nimport {\n  event as domEvent,\n  closest as domClosest\n} from 'min-dom';\n\nimport {\n  toPoint\n} from '../../util/Event';\n\n/**\n * @typedef {import('../../core/Canvas').default} Canvas\n * @typedef {import('../../core/EventBus').default} EventBus\n */\n\nvar THRESHOLD = 15;\n\n\n/**\n * Move the canvas via mouse.\n *\n * @param {EventBus} eventBus\n * @param {Canvas} canvas\n */\nexport default function MoveCanvas(eventBus, canvas) {\n\n  var context;\n\n\n  // listen for move on element mouse down;\n  // allow others to hook into the event before us though\n  // (dragging / element moving will do this)\n  eventBus.on('element.mousedown', 500, function(e) {\n    return handleStart(e.originalEvent);\n  });\n\n\n  function handleMove(event) {\n\n    var start = context.start,\n        button = context.button,\n        position = toPoint(event),\n        delta = deltaPos(position, start);\n\n    if (!context.dragging && length(delta) > THRESHOLD) {\n      context.dragging = true;\n\n      if (button === 0) {\n        installClickTrap(eventBus);\n      }\n\n      cursorSet('grab');\n    }\n\n    if (context.dragging) {\n\n      var lastPosition = context.last || context.start;\n\n      delta = deltaPos(position, lastPosition);\n\n      canvas.scroll({\n        dx: delta.x,\n        dy: delta.y\n      });\n\n      context.last = position;\n    }\n\n    // prevent select\n    event.preventDefault();\n  }\n\n\n  function handleEnd(event) {\n    domEvent.unbind(document, 'mousemove', handleMove);\n    domEvent.unbind(document, 'mouseup', handleEnd);\n\n    context = null;\n\n    cursorUnset();\n  }\n\n  function handleStart(event) {\n\n    // event is already handled by '.djs-draggable'\n    if (domClosest(event.target, '.djs-draggable')) {\n      return;\n    }\n\n    var button = event.button;\n\n    // reject right mouse button or modifier key\n    if (button >= 2 || event.ctrlKey || event.shiftKey || event.altKey) {\n      return;\n    }\n\n    context = {\n      button: button,\n      start: toPoint(event)\n    };\n\n    domEvent.bind(document, 'mousemove', handleMove);\n    domEvent.bind(document, 'mouseup', handleEnd);\n\n    // we've handled the event\n    return true;\n  }\n\n  this.isActive = function() {\n    return !!context;\n  };\n\n}\n\n\nMoveCanvas.$inject = [\n  'eventBus',\n  'canvas'\n];\n\n\n\n// helpers ///////\n\nfunction length(point) {\n  return Math.sqrt(Math.pow(point.x, 2) + Math.pow(point.y, 2));\n}\n", "import MoveCanvas from './MoveCanvas';\n\n\n/**\n * @type { import('didi').ModuleDeclaration }\n */\nexport default {\n  __init__: [ 'moveCanvas' ],\n  moveCanvas: [ 'type', MoveCanvas ]\n};"], "mappings": ";;;;;;;;;;;AAIA,IAAI,qBAAqB;AAKlB,SAAS,IAAI,MAAM;AACxB,MAAIA,WAAU,QAAW,SAAS,IAAI;AAEtC,EAAAA,SAAQ,eAAe,kBAAkB;AAEzC,MAAI,MAAM;AACR,IAAAA,SAAQ,IAAI,gBAAgB,IAAI;AAAA,EAClC;AACF;AAEO,SAAS,QAAQ;AACtB,MAAI,IAAI;AACV;;;ACjBA,IAAI,gBAAgB;AAUb,SAAS,QAAQ,UAAU,WAAW;AAE3C,cAAY,aAAa;AAEzB,WAAS,OAAO;AACd,WAAO;AAAA,EACT;AAEA,WAAS,KAAK,WAAW,eAAe,IAAI;AAE5C,SAAO,WAAW;AAChB,aAAS,IAAI,WAAW,IAAI;AAAA,EAC9B;AACF;;;ACLO,SAAS,MAAM,GAAG,GAAG;AAC1B,SAAO;AAAA,IACL,GAAG,EAAE,IAAI,EAAE;AAAA,IACX,GAAG,EAAE,IAAI,EAAE;AAAA,EACb;AACF;;;ACAA,IAAI,YAAY;AASD,SAAR,WAA4B,UAAU,QAAQ;AAEnD,MAAI;AAMJ,WAAS,GAAG,qBAAqB,KAAK,SAAS,GAAG;AAChD,WAAO,YAAY,EAAE,aAAa;AAAA,EACpC,CAAC;AAGD,WAAS,WAAWC,QAAO;AAEzB,QAAI,QAAQ,QAAQ,OAChB,SAAS,QAAQ,QACjB,WAAW,QAAQA,MAAK,GACxBC,SAAQ,MAAS,UAAU,KAAK;AAEpC,QAAI,CAAC,QAAQ,YAAY,OAAOA,MAAK,IAAI,WAAW;AAClD,cAAQ,WAAW;AAEnB,UAAI,WAAW,GAAG;AAChB,gBAAiB,QAAQ;AAAA,MAC3B;AAEA,UAAU,MAAM;AAAA,IAClB;AAEA,QAAI,QAAQ,UAAU;AAEpB,UAAI,eAAe,QAAQ,QAAQ,QAAQ;AAE3C,MAAAA,SAAQ,MAAS,UAAU,YAAY;AAEvC,aAAO,OAAO;AAAA,QACZ,IAAIA,OAAM;AAAA,QACV,IAAIA,OAAM;AAAA,MACZ,CAAC;AAED,cAAQ,OAAO;AAAA,IACjB;AAGA,IAAAD,OAAM,eAAe;AAAA,EACvB;AAGA,WAAS,UAAUA,QAAO;AACxB,UAAS,OAAO,UAAU,aAAa,UAAU;AACjD,UAAS,OAAO,UAAU,WAAW,SAAS;AAE9C,cAAU;AAEV,UAAY;AAAA,EACd;AAEA,WAAS,YAAYA,QAAO;AAG1B,QAAI,QAAWA,OAAM,QAAQ,gBAAgB,GAAG;AAC9C;AAAA,IACF;AAEA,QAAI,SAASA,OAAM;AAGnB,QAAI,UAAU,KAAKA,OAAM,WAAWA,OAAM,YAAYA,OAAM,QAAQ;AAClE;AAAA,IACF;AAEA,cAAU;AAAA,MACR;AAAA,MACA,OAAO,QAAQA,MAAK;AAAA,IACtB;AAEA,UAAS,KAAK,UAAU,aAAa,UAAU;AAC/C,UAAS,KAAK,UAAU,WAAW,SAAS;AAG5C,WAAO;AAAA,EACT;AAEA,OAAK,WAAW,WAAW;AACzB,WAAO,CAAC,CAAC;AAAA,EACX;AAEF;AAGA,WAAW,UAAU;AAAA,EACnB;AAAA,EACA;AACF;AAMA,SAAS,OAAO,OAAO;AACrB,SAAO,KAAK,KAAK,KAAK,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC;AAC9D;;;ACpIA,IAAO,qBAAQ;AAAA,EACb,UAAU,CAAE,YAAa;AAAA,EACzB,YAAY,CAAE,QAAQ,UAAW;AACnC;", "names": ["classes", "event", "delta"]}