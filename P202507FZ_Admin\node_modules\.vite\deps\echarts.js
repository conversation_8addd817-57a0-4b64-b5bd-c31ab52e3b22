import {
  installUniversalTransition
} from "./chunk-AR5ZO5IW.js";
import {
  install as install29,
  install10 as install38,
  install11 as install39,
  install12 as install40,
  install13 as install41,
  install14 as install42,
  install15 as install43,
  install16 as install44,
  install17 as install45,
  install18 as install46,
  install19 as install47,
  install2 as install30,
  install20 as install48,
  install21 as install49,
  install22 as install50,
  install3 as install31,
  install4 as install32,
  install5 as install33,
  install6 as install34,
  install7 as install35,
  install8 as install36,
  install9 as install37
} from "./chunk-A74VKSZ3.js";
import {
  extendChartView,
  extendComponentModel,
  extendComponentView,
  extendSeriesModel,
  format_exports,
  graphic_exports,
  helper_exports,
  number_exports,
  time_exports,
  util_exports as util_exports2
} from "./chunk-26G46X43.js";
import {
  installLabelLayout
} from "./chunk-TAHELCKR.js";
import "./chunk-EB5BXDKM.js";
import "./chunk-PKX2G6II.js";
import {
  install,
  install10 as install12,
  install11 as install13,
  install14 as install17,
  install15 as install18,
  install16 as install19,
  install17 as install20,
  install18 as install21,
  install19 as install22,
  install2,
  install20 as install23,
  install21 as install24,
  install22 as install25,
  install3,
  install4 as install5,
  install5 as install7,
  install6 as install8,
  install7 as install9,
  install8 as install10,
  install9 as install11
} from "./chunk-FOS2XZKC.js";
import {
  install as install15
} from "./chunk-6D5TN6IN.js";
import {
  install as install16
} from "./chunk-OORZMOXS.js";
import {
  install as install14
} from "./chunk-SAGDWLW7.js";
import "./chunk-ZR4V7UZP.js";
import {
  install3 as install4,
  install4 as install6
} from "./chunk-L34X75WJ.js";
import "./chunk-ZONBLEOS.js";
import "./chunk-QAR3K42R.js";
import "./chunk-6MK55XBE.js";
import {
  install as install26
} from "./chunk-22WAPDAC.js";
import {
  Axis_default,
  parseGeoJSON
} from "./chunk-BUGQH6DG.js";
import {
  Chart_default,
  Component_default as Component_default2,
  PRIORITY,
  SeriesData_default,
  connect,
  dataTool,
  dependencies,
  disConnect,
  disconnect,
  dispose,
  getCoordinateSystemDimensions,
  getInstanceByDom,
  getInstanceById,
  getMap,
  init,
  registerAction,
  registerCoordinateSystem,
  registerLayout,
  registerLoading,
  registerMap,
  registerPostInit,
  registerPostUpdate,
  registerPreprocessor,
  registerProcessor,
  registerTheme,
  registerTransform,
  registerUpdateLifecycle,
  registerVisual,
  setCanvasCreator,
  throttle,
  use,
  version
} from "./chunk-H732WCN4.js";
import "./chunk-YB2SVXLR.js";
import {
  Component_default,
  Model_default,
  Series_default,
  registerLocale
} from "./chunk-Q47K3BNQ.js";
import {
  install as install27
} from "./chunk-NSSU2UD4.js";
import {
  install as install28
} from "./chunk-2A7SHRFG.js";
import {
  brushSingle,
  zrender_exports
} from "./chunk-OUMNO3IT.js";
import {
  color_exports,
  env_default,
  matrix_exports,
  setPlatformAPI,
  util_exports,
  vector_exports
} from "./chunk-MUBQFVAI.js";
import "./chunk-GFT2G5UO.js";

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/index.js
use([install28]);
use([install27]);
use([install29, install30, install31, install32, install33, install34, install35, install36, install37, install38, install39, install40, install41, install42, install43, install44, install45, install46, install47, install48, install49, install50]);
use(install2);
use(install3);
use(install4);
use(install5);
use(install6);
use(install7);
use(install8);
use(install9);
use(install10);
use(install);
use(install11);
use(install12);
use(install13);
use(install14);
use(install15);
use(install16);
use(install17);
use(install20);
use(install18);
use(install19);
use(install23);
use(install21);
use(install22);
use(install24);
use(install25);
use(install26);
use(installUniversalTransition);
use(installLabelLayout);
export {
  Axis_default as Axis,
  Chart_default as ChartView,
  Component_default as ComponentModel,
  Component_default2 as ComponentView,
  SeriesData_default as List,
  Model_default as Model,
  PRIORITY,
  Series_default as SeriesModel,
  color_exports as color,
  connect,
  dataTool,
  dependencies,
  disConnect,
  disconnect,
  dispose,
  env_default as env,
  extendChartView,
  extendComponentModel,
  extendComponentView,
  extendSeriesModel,
  format_exports as format,
  getCoordinateSystemDimensions,
  getInstanceByDom,
  getInstanceById,
  getMap,
  graphic_exports as graphic,
  helper_exports as helper,
  init,
  brushSingle as innerDrawElementOnCanvas,
  matrix_exports as matrix,
  number_exports as number,
  parseGeoJSON,
  parseGeoJSON as parseGeoJson,
  registerAction,
  registerCoordinateSystem,
  registerLayout,
  registerLoading,
  registerLocale,
  registerMap,
  registerPostInit,
  registerPostUpdate,
  registerPreprocessor,
  registerProcessor,
  registerTheme,
  registerTransform,
  registerUpdateLifecycle,
  registerVisual,
  setCanvasCreator,
  setPlatformAPI,
  throttle,
  time_exports as time,
  use,
  util_exports2 as util,
  vector_exports as vector,
  version,
  util_exports as zrUtil,
  zrender_exports as zrender
};
//# sourceMappingURL=echarts.js.map
