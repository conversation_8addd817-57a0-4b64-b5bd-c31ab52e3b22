{"version": 3, "sources": ["../../.pnpm/markmap-toolbar@0.17.2_markmap-common@0.16.0/node_modules/markmap-toolbar/dist/index.mjs"], "sourcesContent": ["/*! @gera2ld/jsx-dom v2.2.2 | ISC License */\nconst VTYPE_ELEMENT = 1;\nconst VTYPE_FUNCTION = 2;\nconst SVG_NS = \"http://www.w3.org/2000/svg\";\nconst XLINK_NS = \"http://www.w3.org/1999/xlink\";\nconst NS_ATTRS = {\n  show: XLINK_NS,\n  actuate: XLINK_NS,\n  href: XLINK_NS\n};\nconst isLeaf = (c) => typeof c === \"string\" || typeof c === \"number\";\nconst isElement = (c) => (c == null ? void 0 : c.vtype) === VTYPE_ELEMENT;\nconst isRenderFunction = (c) => (c == null ? void 0 : c.vtype) === VTYPE_FUNCTION;\nfunction jsx(type, props) {\n  let vtype;\n  if (typeof type === \"string\")\n    vtype = VTYPE_ELEMENT;\n  else if (typeof type === \"function\")\n    vtype = VTYPE_FUNCTION;\n  else\n    throw new Error(\"Invalid VNode type\");\n  return {\n    vtype,\n    type,\n    props\n  };\n}\nconst jsxs = jsx;\nfunction Fragment(props) {\n  return props.children;\n}\nconst DEFAULT_ENV = {\n  isSvg: false\n};\nfunction insertDom(parent, nodes) {\n  if (!Array.isArray(nodes))\n    nodes = [nodes];\n  nodes = nodes.filter(Boolean);\n  if (nodes.length)\n    parent.append(...nodes);\n}\nfunction mountAttributes(domElement, props, env) {\n  for (const key in props) {\n    if (key === \"key\" || key === \"children\" || key === \"ref\")\n      continue;\n    if (key === \"dangerouslySetInnerHTML\") {\n      domElement.innerHTML = props[key].__html;\n    } else if (key === \"innerHTML\" || key === \"textContent\" || key === \"innerText\" || key === \"value\" && [\"textarea\", \"select\"].includes(domElement.tagName)) {\n      const value = props[key];\n      if (value != null)\n        domElement[key] = value;\n    } else if (key.startsWith(\"on\")) {\n      domElement[key.toLowerCase()] = props[key];\n    } else {\n      setDOMAttribute(domElement, key, props[key], env.isSvg);\n    }\n  }\n}\nconst attrMap = {\n  className: \"class\",\n  labelFor: \"for\"\n};\nfunction setDOMAttribute(el, attr, value, isSVG) {\n  attr = attrMap[attr] || attr;\n  if (value === true) {\n    el.setAttribute(attr, \"\");\n  } else if (value === false) {\n    el.removeAttribute(attr);\n  } else {\n    const namespace = isSVG ? NS_ATTRS[attr] : void 0;\n    if (namespace !== void 0) {\n      el.setAttributeNS(namespace, attr, value);\n    } else {\n      el.setAttribute(attr, value);\n    }\n  }\n}\nfunction flatten(arr) {\n  return arr.reduce((prev, item) => prev.concat(item), []);\n}\nfunction mountChildren(children, env) {\n  return Array.isArray(children) ? flatten(children.map((child) => mountChildren(child, env))) : mount(children, env);\n}\nfunction mount(vnode, env = DEFAULT_ENV) {\n  if (vnode == null || typeof vnode === \"boolean\") {\n    return null;\n  }\n  if (vnode instanceof Node) {\n    return vnode;\n  }\n  if (isRenderFunction(vnode)) {\n    const {\n      type,\n      props\n    } = vnode;\n    if (type === Fragment) {\n      const node = document.createDocumentFragment();\n      if (props.children) {\n        const children = mountChildren(props.children, env);\n        insertDom(node, children);\n      }\n      return node;\n    }\n    const childVNode = type(props);\n    return mount(childVNode, env);\n  }\n  if (isLeaf(vnode)) {\n    return document.createTextNode(`${vnode}`);\n  }\n  if (isElement(vnode)) {\n    let node;\n    const {\n      type,\n      props\n    } = vnode;\n    if (!env.isSvg && type === \"svg\") {\n      env = Object.assign({}, env, {\n        isSvg: true\n      });\n    }\n    if (!env.isSvg) {\n      node = document.createElement(type);\n    } else {\n      node = document.createElementNS(SVG_NS, type);\n    }\n    mountAttributes(node, props, env);\n    if (props.children) {\n      let childEnv = env;\n      if (env.isSvg && type === \"foreignObject\") {\n        childEnv = Object.assign({}, childEnv, {\n          isSvg: false\n        });\n      }\n      const children = mountChildren(props.children, childEnv);\n      if (children != null)\n        insertDom(node, children);\n    }\n    const {\n      ref\n    } = props;\n    if (typeof ref === \"function\")\n      ref(node);\n    return node;\n  }\n  throw new Error(\"mount: Invalid Vnode!\");\n}\nfunction mountDom(vnode) {\n  return mount(vnode);\n}\nconst clsToolbarItem = \"mm-toolbar-item\";\nconst clsActive = \"active\";\nfunction renderBrand() {\n  return /* @__PURE__ */ jsxs(\"a\", { className: \"mm-toolbar-brand\", href: \"https://markmap.js.org/\", children: [\n    /* @__PURE__ */ jsx(\n      \"img\",\n      {\n        alt: \"markmap\",\n        src: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAACoFBMVEUAAAAAAAD//wAAAACAgAD//wAAAABVVQCqqgBAQACAQACAgABmZgBtbQAAAABgQABgYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAaFQAAAAAAAAAAAAAAAAAHAAARBQIdGAIYEwI/OgJYUQUfHQI+OgJDPgJJRARBPQRJQgRRSwRRTQRIQQRUTgRUUARZUgRSTQRPSQRjWgZORQRfWQZsZAhTTQRNRwRWUAZkXAZOSARUTgZPRwRRSQRoYwZWUQZWTgRbUwZmXQZoXghmXwdqYwdsYwdfVwVmXQdqYgdiWgVpYAl3bgl6cgl4cAqLggw8OAOWjA2Uig1OSAR2bQihlg55cAh5cAh6cQmMgwyOhAyUjA2QhQ2Uiw2Viw2soBCflA+voxGwpRGhlg+hlg+snxGroBGjmBCpnBC0pxKyphKxpRG2qhK0qBK5rBK5rBP/7h3/8B7/8R3/8h3/8R7/8h786x397B3+7R3EtxT66Rz66hz76hz86xz96xz97Bz+7Rz45xz56Bz76hz97Bz97B3MvRX15Rv25Rv45xz66Rz76hz97B3+7R3IuxX05Bv15Bv25Rz56Bz66Ry/sxPAsxPCtRTCthTNvxbZyxfczxfi0xjl1Rnn2Bnr2xrr3Brs3Rru3Rru3xrv3hrw3xrx4Bvx4Rvy4hvz4hvz4xv04xv05Bv14xv15Bv15Rv25Bv25Rv25Rz25hv35hv35xv45xv45xz55xz56Bv56Bz66Rv66Rz76Rv76Rz76hz86hv86xz+7h3/7R3/7h3/7x3/8B3/8B7/8R3/8R4Yqhj5AAAAq3RSTlMAAQECAgIDAwMEBAQFBwgICAwQERITFRYXGBkbHB0eHyQlJyguNTg8RUZISU5PV2FiY2RlZmdqa2xubnJzc3R2d3d3eXl5eXp7fH1+gIGCgoKDg4SEhIWGh4eHiYmJjIyMjZSUlJ+sra+zt7i4uru8ztHV1tbW2d7g4OHi4uPk5ufp7Ozv9fX29/f3+Pj6+vr7+/v7+/v7+/z8/Pz8/f39/f39/f3+/v7+/v7K6J1dAAACHklEQVQ4y2NgwAoYWdi5uLm5GXHIcrLCmMzYpDmAhKCKjoGtp40MFhVsDAwSxmmVEzZu2XvqSLkchjw3g0h445Ybd24vmTN1Usd5X3R5DgaNqgN35sycP2/GxMkTMRVwMOivvtO3YsWUm3duX790EcMKdgbNNXdnnJh1+9T6ipzU+FB0RzIyiFYB5WdfaElUF8TmTQ6GwH39J2bvypMHcpg4MAKKkUGo5s6KWRfyGRh4WJClGEGBCgS8DLobliy/3abMwM8NBYwQjXDgf3ryxOspyKYyg+RFTFwdnYDAzbrw+oLFm9Ot3J3AwNHFTBykQrhg++GDh48cOXzk4P6VZy8s230MyAGCwwcP7iyRBJpiur1n8hQIWHX27NkLi6bAwOSuow5ABeY7OydOhoCFIAULe6E8YFCf8QAqEC86evniZTA4tfLsuRXHr0E4ly9ePF0uC3KnpH1MZBQQxPoVgxyZ5RMdBQaRMc6yIEcihWbQGaA3k9G8CfQoN0pAtSoxCMACihk9qGtBQZ2LHtRIkRUMiqwd2TJADiswsrjQlAGju/o+MLrPNkWo8mFN1ewMWmvBCebQ0rKMJG87QzF0FRwMRuvugpLcrXu3rp7Zs61UCtMZ2nVHbk+fMX/+jMmTp3Sf9MLiULG45q237txaPG3yxPYrYQzYMo60RWbD3E27Ll68Uq+AK+uJqOlZBiSEKGLNnMA0iDfzwrI/NKgBOivk9piPdtUAAAAASUVORK5CYII=\"\n      }\n    ),\n    /* @__PURE__ */ jsx(\"span\", { children: \"markmap\" })\n  ] });\n}\nfunction renderItem({ title, content, onClick }) {\n  return /* @__PURE__ */ jsx(\"div\", { className: clsToolbarItem, title, onClick, children: content });\n}\nlet promise;\nfunction safeCaller(fn) {\n  return async (...args) => {\n    if (promise)\n      return;\n    promise = fn(...args);\n    try {\n      await promise;\n    } finally {\n      promise = void 0;\n    }\n  };\n}\nconst _Toolbar = class _Toolbar {\n  constructor() {\n    this.showBrand = true;\n    this.registry = {};\n    this.el = mountDom(/* @__PURE__ */ jsx(\"div\", { className: \"mm-toolbar\" }));\n    this.items = [..._Toolbar.defaultItems];\n    this.register({\n      id: \"zoomIn\",\n      title: \"Zoom in\",\n      content: _Toolbar.icon(\"M9 5v4h-4v2h4v4h2v-4h4v-2h-4v-4z\"),\n      onClick: this.getHandler((mm) => mm.rescale(1.25))\n    });\n    this.register({\n      id: \"zoomOut\",\n      title: \"Zoom out\",\n      content: _Toolbar.icon(\"M5 9h10v2h-10z\"),\n      onClick: this.getHandler((mm) => mm.rescale(0.8))\n    });\n    this.register({\n      id: \"fit\",\n      title: \"Fit window size\",\n      content: _Toolbar.icon(\n        \"M4 7h2v-2h2v4h-4zM4 13h2v2h2v-4h-4zM16 7h-2v-2h-2v4h4zM16 13h-2v2h-2v-4h4z\"\n      ),\n      onClick: this.getHandler((mm) => mm.fit())\n    });\n    this.register({\n      id: \"recurse\",\n      title: \"Toggle recursively\",\n      content: _Toolbar.icon(\"M16 4h-12v12h12v-8h-8v4h2v-2h4v4h-8v-8h10z\"),\n      onClick: (e) => {\n        var _a;\n        const button = e.target.closest(\n          `.${clsToolbarItem}`\n        );\n        const active = button == null ? void 0 : button.classList.toggle(clsActive);\n        (_a = this.markmap) == null ? void 0 : _a.setOptions({\n          toggleRecursively: active\n        });\n      }\n    });\n    this.render();\n  }\n  static create(mm) {\n    const toolbar = new _Toolbar();\n    toolbar.attach(mm);\n    return toolbar;\n  }\n  static icon(path, attrs = {}) {\n    attrs = {\n      stroke: \"none\",\n      fill: \"currentColor\",\n      \"fill-rule\": \"evenodd\",\n      ...attrs\n    };\n    return /* @__PURE__ */ jsx(\"svg\", { width: \"20\", height: \"20\", viewBox: \"0 0 20 20\", children: /* @__PURE__ */ jsx(\"path\", { ...attrs, d: path }) });\n  }\n  setBrand(show) {\n    this.showBrand = show;\n    return this.render();\n  }\n  register(data) {\n    this.registry[data.id] = data;\n  }\n  getHandler(handle) {\n    handle = safeCaller(handle);\n    return () => {\n      if (this.markmap)\n        handle(this.markmap);\n    };\n  }\n  setItems(items) {\n    this.items = [...items];\n    return this.render();\n  }\n  attach(mm) {\n    this.markmap = mm;\n  }\n  render() {\n    const items = this.items.map((item) => {\n      if (typeof item === \"string\") {\n        const data = this.registry[item];\n        if (!data)\n          console.warn(`[markmap-toolbar] ${item} not found`);\n        return data;\n      }\n      return item;\n    }).filter(Boolean);\n    while (this.el.firstChild) {\n      this.el.firstChild.remove();\n    }\n    this.el.append(\n      mountDom(\n        /* @__PURE__ */ jsxs(Fragment, { children: [\n          this.showBrand && renderBrand(),\n          items.map(renderItem)\n        ] })\n      )\n    );\n    return this.el;\n  }\n};\n_Toolbar.defaultItems = [\n  \"zoomIn\",\n  \"zoomOut\",\n  \"fit\",\n  \"recurse\"\n];\nlet Toolbar = _Toolbar;\nexport {\n  Toolbar\n};\n"], "mappings": ";;;AACA,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,SAAS;AACf,IAAM,WAAW;AACjB,IAAM,WAAW;AAAA,EACf,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM;AACR;AACA,IAAM,SAAS,CAAC,MAAM,OAAO,MAAM,YAAY,OAAO,MAAM;AAC5D,IAAM,YAAY,CAAC,OAAO,KAAK,OAAO,SAAS,EAAE,WAAW;AAC5D,IAAM,mBAAmB,CAAC,OAAO,KAAK,OAAO,SAAS,EAAE,WAAW;AACnE,SAAS,IAAI,MAAM,OAAO;AACxB,MAAI;AACJ,MAAI,OAAO,SAAS;AAClB,YAAQ;AAAA,WACD,OAAO,SAAS;AACvB,YAAQ;AAAA;AAER,UAAM,IAAI,MAAM,oBAAoB;AACtC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,OAAO;AACb,SAAS,SAAS,OAAO;AACvB,SAAO,MAAM;AACf;AACA,IAAM,cAAc;AAAA,EAClB,OAAO;AACT;AACA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,CAAC,MAAM,QAAQ,KAAK;AACtB,YAAQ,CAAC,KAAK;AAChB,UAAQ,MAAM,OAAO,OAAO;AAC5B,MAAI,MAAM;AACR,WAAO,OAAO,GAAG,KAAK;AAC1B;AACA,SAAS,gBAAgB,YAAY,OAAO,KAAK;AAC/C,aAAW,OAAO,OAAO;AACvB,QAAI,QAAQ,SAAS,QAAQ,cAAc,QAAQ;AACjD;AACF,QAAI,QAAQ,2BAA2B;AACrC,iBAAW,YAAY,MAAM,GAAG,EAAE;AAAA,IACpC,WAAW,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ,eAAe,QAAQ,WAAW,CAAC,YAAY,QAAQ,EAAE,SAAS,WAAW,OAAO,GAAG;AACxJ,YAAM,QAAQ,MAAM,GAAG;AACvB,UAAI,SAAS;AACX,mBAAW,GAAG,IAAI;AAAA,IACtB,WAAW,IAAI,WAAW,IAAI,GAAG;AAC/B,iBAAW,IAAI,YAAY,CAAC,IAAI,MAAM,GAAG;AAAA,IAC3C,OAAO;AACL,sBAAgB,YAAY,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK;AAAA,IACxD;AAAA,EACF;AACF;AACA,IAAM,UAAU;AAAA,EACd,WAAW;AAAA,EACX,UAAU;AACZ;AACA,SAAS,gBAAgB,IAAI,MAAM,OAAO,OAAO;AAC/C,SAAO,QAAQ,IAAI,KAAK;AACxB,MAAI,UAAU,MAAM;AAClB,OAAG,aAAa,MAAM,EAAE;AAAA,EAC1B,WAAW,UAAU,OAAO;AAC1B,OAAG,gBAAgB,IAAI;AAAA,EACzB,OAAO;AACL,UAAM,YAAY,QAAQ,SAAS,IAAI,IAAI;AAC3C,QAAI,cAAc,QAAQ;AACxB,SAAG,eAAe,WAAW,MAAM,KAAK;AAAA,IAC1C,OAAO;AACL,SAAG,aAAa,MAAM,KAAK;AAAA,IAC7B;AAAA,EACF;AACF;AACA,SAAS,QAAQ,KAAK;AACpB,SAAO,IAAI,OAAO,CAAC,MAAM,SAAS,KAAK,OAAO,IAAI,GAAG,CAAC,CAAC;AACzD;AACA,SAAS,cAAc,UAAU,KAAK;AACpC,SAAO,MAAM,QAAQ,QAAQ,IAAI,QAAQ,SAAS,IAAI,CAAC,UAAU,cAAc,OAAO,GAAG,CAAC,CAAC,IAAI,MAAM,UAAU,GAAG;AACpH;AACA,SAAS,MAAM,OAAO,MAAM,aAAa;AACvC,MAAI,SAAS,QAAQ,OAAO,UAAU,WAAW;AAC/C,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,MAAM;AACzB,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,KAAK,GAAG;AAC3B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS,UAAU;AACrB,YAAM,OAAO,SAAS,uBAAuB;AAC7C,UAAI,MAAM,UAAU;AAClB,cAAM,WAAW,cAAc,MAAM,UAAU,GAAG;AAClD,kBAAU,MAAM,QAAQ;AAAA,MAC1B;AACA,aAAO;AAAA,IACT;AACA,UAAM,aAAa,KAAK,KAAK;AAC7B,WAAO,MAAM,YAAY,GAAG;AAAA,EAC9B;AACA,MAAI,OAAO,KAAK,GAAG;AACjB,WAAO,SAAS,eAAe,GAAG,KAAK,EAAE;AAAA,EAC3C;AACA,MAAI,UAAU,KAAK,GAAG;AACpB,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,IAAI,SAAS,SAAS,OAAO;AAChC,YAAM,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,QAC3B,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,QAAI,CAAC,IAAI,OAAO;AACd,aAAO,SAAS,cAAc,IAAI;AAAA,IACpC,OAAO;AACL,aAAO,SAAS,gBAAgB,QAAQ,IAAI;AAAA,IAC9C;AACA,oBAAgB,MAAM,OAAO,GAAG;AAChC,QAAI,MAAM,UAAU;AAClB,UAAI,WAAW;AACf,UAAI,IAAI,SAAS,SAAS,iBAAiB;AACzC,mBAAW,OAAO,OAAO,CAAC,GAAG,UAAU;AAAA,UACrC,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,YAAM,WAAW,cAAc,MAAM,UAAU,QAAQ;AACvD,UAAI,YAAY;AACd,kBAAU,MAAM,QAAQ;AAAA,IAC5B;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,QAAQ;AACjB,UAAI,IAAI;AACV,WAAO;AAAA,EACT;AACA,QAAM,IAAI,MAAM,uBAAuB;AACzC;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,MAAM,KAAK;AACpB;AACA,IAAM,iBAAiB;AACvB,IAAM,YAAY;AAClB,SAAS,cAAc;AACrB,SAAuB,KAAK,KAAK,EAAE,WAAW,oBAAoB,MAAM,2BAA2B,UAAU;AAAA,IAC3F;AAAA,MACd;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IACgB,IAAI,QAAQ,EAAE,UAAU,UAAU,CAAC;AAAA,EACrD,EAAE,CAAC;AACL;AACA,SAAS,WAAW,EAAE,OAAO,SAAS,QAAQ,GAAG;AAC/C,SAAuB,IAAI,OAAO,EAAE,WAAW,gBAAgB,OAAO,SAAS,UAAU,QAAQ,CAAC;AACpG;AACA,IAAI;AACJ,SAAS,WAAW,IAAI;AACtB,SAAO,UAAU,SAAS;AACxB,QAAI;AACF;AACF,cAAU,GAAG,GAAG,IAAI;AACpB,QAAI;AACF,YAAM;AAAA,IACR,UAAE;AACA,gBAAU;AAAA,IACZ;AAAA,EACF;AACF;AACA,IAAM,WAAW,MAAMA,UAAS;AAAA,EAC9B,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,WAAW,CAAC;AACjB,SAAK,KAAK,SAAyB,IAAI,OAAO,EAAE,WAAW,aAAa,CAAC,CAAC;AAC1E,SAAK,QAAQ,CAAC,GAAGA,UAAS,YAAY;AACtC,SAAK,SAAS;AAAA,MACZ,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,SAASA,UAAS,KAAK,kCAAkC;AAAA,MACzD,SAAS,KAAK,WAAW,CAAC,OAAO,GAAG,QAAQ,IAAI,CAAC;AAAA,IACnD,CAAC;AACD,SAAK,SAAS;AAAA,MACZ,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,SAASA,UAAS,KAAK,gBAAgB;AAAA,MACvC,SAAS,KAAK,WAAW,CAAC,OAAO,GAAG,QAAQ,GAAG,CAAC;AAAA,IAClD,CAAC;AACD,SAAK,SAAS;AAAA,MACZ,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,SAASA,UAAS;AAAA,QAChB;AAAA,MACF;AAAA,MACA,SAAS,KAAK,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;AAAA,IAC3C,CAAC;AACD,SAAK,SAAS;AAAA,MACZ,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,SAASA,UAAS,KAAK,4CAA4C;AAAA,MACnE,SAAS,CAAC,MAAM;AACd,YAAI;AACJ,cAAM,SAAS,EAAE,OAAO;AAAA,UACtB,IAAI,cAAc;AAAA,QACpB;AACA,cAAM,SAAS,UAAU,OAAO,SAAS,OAAO,UAAU,OAAO,SAAS;AAC1E,SAAC,KAAK,KAAK,YAAY,OAAO,SAAS,GAAG,WAAW;AAAA,UACnD,mBAAmB;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO,OAAO,IAAI;AAChB,UAAM,UAAU,IAAIA,UAAS;AAC7B,YAAQ,OAAO,EAAE;AACjB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK,MAAM,QAAQ,CAAC,GAAG;AAC5B,YAAQ;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,aAAa;AAAA,MACb,GAAG;AAAA,IACL;AACA,WAAuB,IAAI,OAAO,EAAE,OAAO,MAAM,QAAQ,MAAM,SAAS,aAAa,UAA0B,IAAI,QAAQ,EAAE,GAAG,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC;AAAA,EACrJ;AAAA,EACA,SAAS,MAAM;AACb,SAAK,YAAY;AACjB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,SAAS,MAAM;AACb,SAAK,SAAS,KAAK,EAAE,IAAI;AAAA,EAC3B;AAAA,EACA,WAAW,QAAQ;AACjB,aAAS,WAAW,MAAM;AAC1B,WAAO,MAAM;AACX,UAAI,KAAK;AACP,eAAO,KAAK,OAAO;AAAA,IACvB;AAAA,EACF;AAAA,EACA,SAAS,OAAO;AACd,SAAK,QAAQ,CAAC,GAAG,KAAK;AACtB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,OAAO,IAAI;AACT,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,SAAS;AACP,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,SAAS;AACrC,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,OAAO,KAAK,SAAS,IAAI;AAC/B,YAAI,CAAC;AACH,kBAAQ,KAAK,qBAAqB,IAAI,YAAY;AACpD,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC,EAAE,OAAO,OAAO;AACjB,WAAO,KAAK,GAAG,YAAY;AACzB,WAAK,GAAG,WAAW,OAAO;AAAA,IAC5B;AACA,SAAK,GAAG;AAAA,MACN;AAAA,QACkB,KAAK,UAAU,EAAE,UAAU;AAAA,UACzC,KAAK,aAAa,YAAY;AAAA,UAC9B,MAAM,IAAI,UAAU;AAAA,QACtB,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AACF;AACA,SAAS,eAAe;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,UAAU;", "names": ["_Too<PERSON>bar"]}