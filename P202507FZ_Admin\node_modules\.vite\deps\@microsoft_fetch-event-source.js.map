{"version": 3, "sources": ["../../.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/src/parse.ts", "../../.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/src/fetch.ts"], "sourcesContent": [null, null], "mappings": ";;;AAqBA,eAAsB,SAAS,QAAoC,SAAkC;AACjG,QAAM,SAAS,OAAO,UAAS;AAC/B,MAAI;AACJ,SAAO,EAAE,SAAS,MAAM,OAAO,KAAI,GAAI,MAAM;AACzC,YAAQ,OAAO,KAAK;;AAE5B;AAeM,SAAU,SAAS,QAAuD;AAC5E,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,yBAAyB;AAG7B,SAAO,SAAS,QAAQ,KAAe;AACnC,QAAI,WAAW,QAAW;AACtB,eAAS;AACT,iBAAW;AACX,oBAAc;WACX;AAEH,eAAS,OAAO,QAAQ,GAAG;;AAG/B,UAAM,YAAY,OAAO;AACzB,QAAI,YAAY;AAChB,WAAO,WAAW,WAAW;AACzB,UAAI,wBAAwB;AACxB,YAAI,OAAO,QAAQ,MAAC,IAA2B;AAC3C,sBAAY,EAAE;;AAGlB,iCAAyB;;AAI7B,UAAI,UAAU;AACd,aAAO,WAAW,aAAa,YAAY,IAAI,EAAE,UAAU;AACvD,gBAAQ,OAAO,QAAQ,GAAG;UACtB,KAAA;AACI,gBAAI,gBAAgB,IAAI;AACpB,4BAAc,WAAW;;AAE7B;UAEJ,KAAA;AACI,qCAAyB;UAC7B,KAAA;AACI,sBAAU;AACV;;;AAIZ,UAAI,YAAY,IAAI;AAGhB;;AAIJ,aAAO,OAAO,SAAS,WAAW,OAAO,GAAG,WAAW;AACvD,kBAAY;AACZ,oBAAc;;AAGlB,QAAI,cAAc,WAAW;AACzB,eAAS;eACF,cAAc,GAAG;AAGxB,eAAS,OAAO,SAAS,SAAS;AAClC,kBAAY;;EAEpB;AACJ;AASM,SAAU,YACZ,MACA,SACA,WAA6C;AAE7C,MAAI,UAAU,WAAU;AACxB,QAAM,UAAU,IAAI,YAAW;AAG/B,SAAO,SAAS,OAAO,MAAkB,aAAmB;AACxD,QAAI,KAAK,WAAW,GAAG;AAEnB,oBAAS,QAAT,cAAS,SAAA,SAAT,UAAY,OAAO;AACnB,gBAAU,WAAU;eACb,cAAc,GAAG;AAGxB,YAAM,QAAQ,QAAQ,OAAO,KAAK,SAAS,GAAG,WAAW,CAAC;AAC1D,YAAM,cAAc,eAAe,KAAK,cAAc,CAAC,MAAC,KAA0B,IAAI;AACtF,YAAM,QAAQ,QAAQ,OAAO,KAAK,SAAS,WAAW,CAAC;AAEvD,cAAQ,OAAO;QACX,KAAK;AAGD,kBAAQ,OAAO,QAAQ,OACjB,QAAQ,OAAO,OAAO,QACtB;AACN;QACJ,KAAK;AACD,kBAAQ,QAAQ;AAChB;QACJ,KAAK;AACD,eAAK,QAAQ,KAAK,KAAK;AACvB;QACJ,KAAK;AACD,gBAAM,QAAQ,SAAS,OAAO,EAAE;AAChC,cAAI,CAAC,MAAM,KAAK,GAAG;AACf,oBAAQ,QAAQ,QAAQ,KAAK;;AAEjC;;;EAGhB;AACJ;AAEA,SAAS,OAAO,GAAe,GAAa;AACxC,QAAM,MAAM,IAAI,WAAW,EAAE,SAAS,EAAE,MAAM;AAC9C,MAAI,IAAI,CAAC;AACT,MAAI,IAAI,GAAG,EAAE,MAAM;AACnB,SAAO;AACX;AAEA,SAAS,aAAU;AAKf,SAAO;IACH,MAAM;IACN,OAAO;IACP,IAAI;IACJ,OAAO;;AAEf;;;;;;;;;;;;;;;ACpLO,IAAM,yBAAyB;AAEtC,IAAM,uBAAuB;AAC7B,IAAM,cAAc;AAkDd,SAAU,iBAAiB,OAAoB,IAU9B;MAV8B,EACjD,QAAQ,aACR,SAAS,cACT,QAAQ,aACR,WACA,SACA,SACA,gBACA,OAAO,WAAU,IAAA,IACd,OAAI,OAAA,IAT0C,CAAA,UAAA,WAAA,UAAA,aAAA,WAAA,WAAA,kBAAA,OAAA,CAUpD;AACG,SAAO,IAAI,QAAc,CAAC,SAAS,WAAU;AAEzC,UAAM,UAAO,OAAA,OAAA,CAAA,GAAQ,YAAY;AACjC,QAAI,CAAC,QAAQ,QAAQ;AACjB,cAAQ,SAAS;;AAGrB,QAAI;AACJ,aAAS,qBAAkB;AACvB,2BAAqB,MAAK;AAC1B,UAAI,CAAC,SAAS,QAAQ;AAClB,eAAM;;IAEd;AAEA,QAAI,CAAC,gBAAgB;AACjB,eAAS,iBAAiB,oBAAoB,kBAAkB;;AAGpE,QAAI,gBAAgB;AACpB,QAAI,aAAa;AACjB,aAAS,UAAO;AACZ,eAAS,oBAAoB,oBAAoB,kBAAkB;AACnE,aAAO,aAAa,UAAU;AAC9B,2BAAqB,MAAK;IAC9B;AAGA,oBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,iBAAiB,SAAS,MAAK;AACxC,cAAO;AACP,cAAO;IACX,CAAC;AAED,UAAM,QAAQ,eAAU,QAAV,eAAU,SAAV,aAAc,OAAO;AACnC,UAAM,SAAS,gBAAW,QAAX,gBAAW,SAAX,cAAe;AAC9B,mBAAe,SAAM;;AACjB,6BAAuB,IAAI,gBAAe;AAC1C,UAAI;AACA,cAAM,WAAW,MAAM,MAAM,OAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GAC3B,IAAI,GAAA,EACP,SACA,QAAQ,qBAAqB,OAAM,CAAA,CAAA;AAGvC,cAAM,OAAO,QAAQ;AAErB,cAAM,SAAS,SAAS,MAAM,SAAS,YAAY,QAAK;AACpD,cAAI,IAAI;AAEJ,oBAAQ,WAAW,IAAI;iBACpB;AAEH,mBAAO,QAAQ,WAAW;;QAElC,GAAG,WAAQ;AACP,0BAAgB;QACpB,GAAG,SAAS,CAAC,CAAC;AAEd,oBAAO,QAAP,YAAO,SAAA,SAAP,QAAO;AACP,gBAAO;AACP,gBAAO;eACF,KAAK;AACV,YAAI,CAAC,qBAAqB,OAAO,SAAS;AAEtC,cAAI;AAEA,kBAAM,YAAgBA,MAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAU,GAAG,OAAC,QAAAA,QAAA,SAAAA,MAAI;AACxC,mBAAO,aAAa,UAAU;AAC9B,yBAAa,OAAO,WAAW,QAAQ,QAAQ;mBAC1C,UAAU;AAEf,oBAAO;AACP,mBAAO,QAAQ;;;;IAI/B;AAEA,WAAM;EACV,CAAC;AACL;AAEA,SAAS,cAAc,UAAkB;AACrC,QAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AACvD,MAAI,EAAC,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,WAAW,sBAAsB,IAAG;AAClD,UAAM,IAAI,MAAM,+BAA+B,sBAAsB,aAAa,WAAW,EAAE;;AAEvG;", "names": ["_a"]}