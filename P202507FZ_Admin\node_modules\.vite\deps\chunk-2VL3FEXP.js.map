{"version": 3, "sources": ["../../.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/features/palette/PaletteProvider.js"], "sourcesContent": ["import {\n  assign\n} from 'min-dash';\n\n/**\n * @typedef {import('diagram-js/lib/features/palette/Palette').default} Palette\n * @typedef {import('diagram-js/lib/features/create/Create').default} Create\n * @typedef {import('diagram-js/lib/core/ElementFactory').default} ElementFactory\n * @typedef {import('../space-tool/BpmnSpaceTool').default} SpaceTool\n * @typedef {import('diagram-js/lib/features/lasso-tool/LassoTool').default} LassoTool\n * @typedef {import('diagram-js/lib/features/hand-tool/HandTool').default} HandTool\n * @typedef {import('diagram-js/lib/features/global-connect/GlobalConnect').default} GlobalConnect\n * @typedef {import('diagram-js/lib/i18n/translate/translate').default} Translate\n *\n * @typedef {import('diagram-js/lib/features/palette/Palette').PaletteEntries} PaletteEntries\n */\n\n/**\n * A palette provider for BPMN 2.0 elements.\n *\n * @param {Palette} palette\n * @param {Create} create\n * @param {ElementFactory} elementFactory\n * @param {SpaceTool} spaceTool\n * @param {LassoTool} lassoTool\n * @param {HandTool} handTool\n * @param {GlobalConnect} globalConnect\n * @param {Translate} translate\n */\nexport default function PaletteProvider(\n    palette, create, elementFactory,\n    spaceTool, lassoTool, handTool,\n    globalConnect, translate) {\n\n  this._palette = palette;\n  this._create = create;\n  this._elementFactory = elementFactory;\n  this._spaceTool = spaceTool;\n  this._lassoTool = lassoTool;\n  this._handTool = handTool;\n  this._globalConnect = globalConnect;\n  this._translate = translate;\n\n  palette.registerProvider(this);\n}\n\nPaletteProvider.$inject = [\n  'palette',\n  'create',\n  'elementFactory',\n  'spaceTool',\n  'lassoTool',\n  'handTool',\n  'globalConnect',\n  'translate'\n];\n\n/**\n * @return {PaletteEntries}\n */\nPaletteProvider.prototype.getPaletteEntries = function() {\n\n  var actions = {},\n      create = this._create,\n      elementFactory = this._elementFactory,\n      spaceTool = this._spaceTool,\n      lassoTool = this._lassoTool,\n      handTool = this._handTool,\n      globalConnect = this._globalConnect,\n      translate = this._translate;\n\n  function createAction(type, group, className, title, options) {\n\n    function createListener(event) {\n      var shape = elementFactory.createShape(assign({ type: type }, options));\n      create.start(event, shape);\n    }\n\n    return {\n      group: group,\n      className: className,\n      title: title,\n      action: {\n        dragstart: createListener,\n        click: createListener\n      }\n    };\n  }\n\n  function createSubprocess(event) {\n    var subProcess = elementFactory.createShape({\n      type: 'bpmn:SubProcess',\n      x: 0,\n      y: 0,\n      isExpanded: true\n    });\n\n    var startEvent = elementFactory.createShape({\n      type: 'bpmn:StartEvent',\n      x: 40,\n      y: 82,\n      parent: subProcess\n    });\n\n    create.start(event, [ subProcess, startEvent ], {\n      hints: {\n        autoSelect: [ subProcess ]\n      }\n    });\n  }\n\n  function createParticipant(event) {\n    create.start(event, elementFactory.createParticipantShape());\n  }\n\n  assign(actions, {\n    'hand-tool': {\n      group: 'tools',\n      className: 'bpmn-icon-hand-tool',\n      title: translate('Activate hand tool'),\n      action: {\n        click: function(event) {\n          handTool.activateHand(event);\n        }\n      }\n    },\n    'lasso-tool': {\n      group: 'tools',\n      className: 'bpmn-icon-lasso-tool',\n      title: translate('Activate lasso tool'),\n      action: {\n        click: function(event) {\n          lassoTool.activateSelection(event);\n        }\n      }\n    },\n    'space-tool': {\n      group: 'tools',\n      className: 'bpmn-icon-space-tool',\n      title: translate('Activate create/remove space tool'),\n      action: {\n        click: function(event) {\n          spaceTool.activateSelection(event);\n        }\n      }\n    },\n    'global-connect-tool': {\n      group: 'tools',\n      className: 'bpmn-icon-connection-multi',\n      title: translate('Activate global connect tool'),\n      action: {\n        click: function(event) {\n          globalConnect.start(event);\n        }\n      }\n    },\n    'tool-separator': {\n      group: 'tools',\n      separator: true\n    },\n    'create.start-event': createAction(\n      'bpmn:StartEvent', 'event', 'bpmn-icon-start-event-none',\n      translate('Create start event')\n    ),\n    'create.intermediate-event': createAction(\n      'bpmn:IntermediateThrowEvent', 'event', 'bpmn-icon-intermediate-event-none',\n      translate('Create intermediate/boundary event')\n    ),\n    'create.end-event': createAction(\n      'bpmn:EndEvent', 'event', 'bpmn-icon-end-event-none',\n      translate('Create end event')\n    ),\n    'create.exclusive-gateway': createAction(\n      'bpmn:ExclusiveGateway', 'gateway', 'bpmn-icon-gateway-none',\n      translate('Create gateway')\n    ),\n    'create.task': createAction(\n      'bpmn:Task', 'activity', 'bpmn-icon-task',\n      translate('Create task')\n    ),\n    'create.data-object': createAction(\n      'bpmn:DataObjectReference', 'data-object', 'bpmn-icon-data-object',\n      translate('Create data object reference')\n    ),\n    'create.data-store': createAction(\n      'bpmn:DataStoreReference', 'data-store', 'bpmn-icon-data-store',\n      translate('Create data store reference')\n    ),\n    'create.subprocess-expanded': {\n      group: 'activity',\n      className: 'bpmn-icon-subprocess-expanded',\n      title: translate('Create expanded sub-process'),\n      action: {\n        dragstart: createSubprocess,\n        click: createSubprocess\n      }\n    },\n    'create.participant-expanded': {\n      group: 'collaboration',\n      className: 'bpmn-icon-participant',\n      title: translate('Create pool/participant'),\n      action: {\n        dragstart: createParticipant,\n        click: createParticipant\n      }\n    },\n    'create.group': createAction(\n      'bpmn:Group', 'artifact', 'bpmn-icon-group',\n      translate('Create group')\n    ),\n  });\n\n  return actions;\n};\n"], "mappings": ";;;;;AA6Be,SAAR,gBACH,SAAS,QAAQ,gBACjB,WAAW,WAAW,UACtB,eAAe,WAAW;AAE5B,OAAK,WAAW;AAChB,OAAK,UAAU;AACf,OAAK,kBAAkB;AACvB,OAAK,aAAa;AAClB,OAAK,aAAa;AAClB,OAAK,YAAY;AACjB,OAAK,iBAAiB;AACtB,OAAK,aAAa;AAElB,UAAQ,iBAAiB,IAAI;AAC/B;AAEA,gBAAgB,UAAU;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAKA,gBAAgB,UAAU,oBAAoB,WAAW;AAEvD,MAAI,UAAU,CAAC,GACX,SAAS,KAAK,SACd,iBAAiB,KAAK,iBACtB,YAAY,KAAK,YACjB,YAAY,KAAK,YACjB,WAAW,KAAK,WAChB,gBAAgB,KAAK,gBACrB,YAAY,KAAK;AAErB,WAAS,aAAa,MAAM,OAAO,WAAW,OAAO,SAAS;AAE5D,aAAS,eAAe,OAAO;AAC7B,UAAI,QAAQ,eAAe,YAAY,OAAO,EAAE,KAAW,GAAG,OAAO,CAAC;AACtE,aAAO,MAAM,OAAO,KAAK;AAAA,IAC3B;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,WAAS,iBAAiB,OAAO;AAC/B,QAAI,aAAa,eAAe,YAAY;AAAA,MAC1C,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,YAAY;AAAA,IACd,CAAC;AAED,QAAI,aAAa,eAAe,YAAY;AAAA,MAC1C,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,QAAQ;AAAA,IACV,CAAC;AAED,WAAO,MAAM,OAAO,CAAE,YAAY,UAAW,GAAG;AAAA,MAC9C,OAAO;AAAA,QACL,YAAY,CAAE,UAAW;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,kBAAkB,OAAO;AAChC,WAAO,MAAM,OAAO,eAAe,uBAAuB,CAAC;AAAA,EAC7D;AAEA,SAAO,SAAS;AAAA,IACd,aAAa;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,MACX,OAAO,UAAU,oBAAoB;AAAA,MACrC,QAAQ;AAAA,QACN,OAAO,SAAS,OAAO;AACrB,mBAAS,aAAa,KAAK;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,OAAO,UAAU,qBAAqB;AAAA,MACtC,QAAQ;AAAA,QACN,OAAO,SAAS,OAAO;AACrB,oBAAU,kBAAkB,KAAK;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,OAAO,UAAU,mCAAmC;AAAA,MACpD,QAAQ;AAAA,QACN,OAAO,SAAS,OAAO;AACrB,oBAAU,kBAAkB,KAAK;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,OAAO,UAAU,8BAA8B;AAAA,MAC/C,QAAQ;AAAA,QACN,OAAO,SAAS,OAAO;AACrB,wBAAc,MAAM,KAAK;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,MAAmB;AAAA,MAAS;AAAA,MAC5B,UAAU,oBAAoB;AAAA,IAChC;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,MAA+B;AAAA,MAAS;AAAA,MACxC,UAAU,oCAAoC;AAAA,IAChD;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,MAAiB;AAAA,MAAS;AAAA,MAC1B,UAAU,kBAAkB;AAAA,IAC9B;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,MAAyB;AAAA,MAAW;AAAA,MACpC,UAAU,gBAAgB;AAAA,IAC5B;AAAA,IACA,eAAe;AAAA,MACb;AAAA,MAAa;AAAA,MAAY;AAAA,MACzB,UAAU,aAAa;AAAA,IACzB;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,MAA4B;AAAA,MAAe;AAAA,MAC3C,UAAU,8BAA8B;AAAA,IAC1C;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,MAA2B;AAAA,MAAc;AAAA,MACzC,UAAU,6BAA6B;AAAA,IACzC;AAAA,IACA,8BAA8B;AAAA,MAC5B,OAAO;AAAA,MACP,WAAW;AAAA,MACX,OAAO,UAAU,6BAA6B;AAAA,MAC9C,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B,OAAO;AAAA,MACP,WAAW;AAAA,MACX,OAAO,UAAU,yBAAyB;AAAA,MAC1C,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,MAAc;AAAA,MAAY;AAAA,MAC1B,UAAU,cAAc;AAAA,IAC1B;AAAA,EACF,CAAC;AAED,SAAO;AACT;", "names": []}