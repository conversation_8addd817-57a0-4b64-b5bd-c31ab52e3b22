import "./chunk-ULX5FOVL.js";
import {
  createBaseVNode,
  createElementBlock,
  defineComponent,
  normalizeStyle,
  onMounted,
  openBlock,
  reactive,
  unref,
  watch,
  withModifiers
} from "./chunk-GTWINWNV.js";
import "./chunk-GFT2G5UO.js";

// node_modules/.pnpm/vue3-signature@0.2.4_vue@3.5.12_typescript@5.3.3_/node_modules/vue3-signature/dist/vue3-signature.es.js
var t = Object.defineProperty;
var e = Object.prototype.hasOwnProperty;
var i = Object.getOwnPropertySymbols;
var o = Object.prototype.propertyIsEnumerable;
var s = (e2, i2, o2) => i2 in e2 ? t(e2, i2, { enumerable: true, configurable: true, writable: true, value: o2 }) : e2[i2] = o2;
var n = (t2, n2) => {
  for (var h in n2 || (n2 = {}))
    e.call(n2, h) && s(t2, h, n2[h]);
  if (i)
    for (var h of i(n2))
      o.call(n2, h) && s(t2, h, n2[h]);
  return t2;
};
var g = class {
  constructor(t2, e2, i2) {
    this.x = t2, this.y = e2, this.time = i2 || Date.now();
  }
  distanceTo(t2) {
    return Math.sqrt(Math.pow(this.x - t2.x, 2) + Math.pow(this.y - t2.y, 2));
  }
  equals(t2) {
    return this.x === t2.x && this.y === t2.y && this.time === t2.time;
  }
  velocityFrom(t2) {
    return this.time !== t2.time ? this.distanceTo(t2) / (this.time - t2.time) : 0;
  }
};
var _ = class __ {
  constructor(t2, e2, i2, o2, s2, n2) {
    this.startPoint = t2, this.control2 = e2, this.control1 = i2, this.endPoint = o2, this.startWidth = s2, this.endWidth = n2;
  }
  static fromPoints(t2, e2) {
    const i2 = this.calculateControlPoints(t2[0], t2[1], t2[2]).c2, o2 = this.calculateControlPoints(t2[1], t2[2], t2[3]).c1;
    return new __(t2[1], i2, o2, t2[2], e2.start, e2.end);
  }
  static calculateControlPoints(t2, e2, i2) {
    const o2 = t2.x - e2.x, s2 = t2.y - e2.y, n2 = e2.x - i2.x, h = e2.y - i2.y, a = (t2.x + e2.x) / 2, r = (t2.y + e2.y) / 2, l = (e2.x + i2.x) / 2, c = (e2.y + i2.y) / 2, d = Math.sqrt(o2 * o2 + s2 * s2), u = Math.sqrt(n2 * n2 + h * h), v = u / (d + u), m = l + (a - l) * v, p = c + (r - c) * v, _2 = e2.x - m, y2 = e2.y - p;
    return { c1: new g(a + _2, r + y2), c2: new g(l + _2, c + y2) };
  }
  length() {
    let t2, e2, i2 = 0;
    for (let o2 = 0; o2 <= 10; o2 += 1) {
      const s2 = o2 / 10, n2 = this.point(s2, this.startPoint.x, this.control1.x, this.control2.x, this.endPoint.x), h = this.point(s2, this.startPoint.y, this.control1.y, this.control2.y, this.endPoint.y);
      if (o2 > 0) {
        const o3 = n2 - t2, s3 = h - e2;
        i2 += Math.sqrt(o3 * o3 + s3 * s3);
      }
      t2 = n2, e2 = h;
    }
    return i2;
  }
  point(t2, e2, i2, o2, s2) {
    return e2 * (1 - t2) * (1 - t2) * (1 - t2) + 3 * i2 * (1 - t2) * (1 - t2) * t2 + 3 * o2 * (1 - t2) * t2 * t2 + s2 * t2 * t2 * t2;
  }
};
var y = class _y {
  constructor(t2, e2 = {}) {
    this.canvas = t2, this.options = e2, this._handleMouseDown = (t3) => {
      1 === t3.which && (this._mouseButtonDown = true, this._strokeBegin(t3));
    }, this._handleMouseMove = (t3) => {
      this._mouseButtonDown && this._strokeMoveUpdate(t3);
    }, this._handleMouseUp = (t3) => {
      1 === t3.which && this._mouseButtonDown && (this._mouseButtonDown = false, this._strokeEnd(t3));
    }, this._handleTouchStart = (t3) => {
      if (t3.preventDefault(), 1 === t3.targetTouches.length) {
        const e3 = t3.changedTouches[0];
        this._strokeBegin(e3);
      }
    }, this._handleTouchMove = (t3) => {
      t3.preventDefault();
      const e3 = t3.targetTouches[0];
      this._strokeMoveUpdate(e3);
    }, this._handleTouchEnd = (t3) => {
      if (t3.target === this.canvas) {
        t3.preventDefault();
        const e3 = t3.changedTouches[0];
        this._strokeEnd(e3);
      }
    }, this.velocityFilterWeight = e2.velocityFilterWeight || 0.7, this.minWidth = e2.minWidth || 0.5, this.maxWidth = e2.maxWidth || 2.5, this.throttle = "throttle" in e2 ? e2.throttle : 16, this.minDistance = "minDistance" in e2 ? e2.minDistance : 5, this.dotSize = e2.dotSize || function() {
      return (this.minWidth + this.maxWidth) / 2;
    }, this.penColor = e2.penColor || "black", this.backgroundColor = e2.backgroundColor || "rgba(0,0,0,0)", this.onBegin = e2.onBegin, this.onEnd = e2.onEnd, this._strokeMoveUpdate = this.throttle ? /* @__PURE__ */ function(t3, e3 = 250) {
      let i2, o2, s2, n2 = 0, h = null;
      const a = () => {
        n2 = Date.now(), h = null, i2 = t3.apply(o2, s2), h || (o2 = null, s2 = []);
      };
      return function(...r) {
        const l = Date.now(), c = e3 - (l - n2);
        return o2 = this, s2 = r, c <= 0 || c > e3 ? (h && (clearTimeout(h), h = null), n2 = l, i2 = t3.apply(o2, s2), h || (o2 = null, s2 = [])) : h || (h = window.setTimeout(a, c)), i2;
      };
    }(_y.prototype._strokeUpdate, this.throttle) : _y.prototype._strokeUpdate, this._ctx = t2.getContext("2d"), this.clear(), this.on();
  }
  clear() {
    const { _ctx: t2, canvas: e2 } = this;
    t2.fillStyle = this.backgroundColor, t2.clearRect(0, 0, e2.width, e2.height), t2.fillRect(0, 0, e2.width, e2.height), this._data = [], this._reset(), this._isEmpty = true;
  }
  fromDataURL(t2, e2 = {}, i2) {
    const o2 = new Image(), s2 = e2.ratio || window.devicePixelRatio || 1, n2 = e2.width || this.canvas.width / s2, h = e2.height || this.canvas.height / s2;
    this._reset(), o2.onload = () => {
      this._ctx.drawImage(o2, 0, 0, n2, h), i2 && i2();
    }, o2.onerror = (t3) => {
      i2 && i2(t3);
    }, o2.src = t2, this._isEmpty = false;
  }
  toDataURL(t2 = "image/png", e2) {
    switch (t2) {
      case "image/svg+xml":
        return this._toSVG();
      default:
        return this.canvas.toDataURL(t2, e2);
    }
  }
  on() {
    this.canvas.style.touchAction = "none", this.canvas.style.msTouchAction = "none", window.PointerEvent ? this._handlePointerEvents() : (this._handleMouseEvents(), "ontouchstart" in window && this._handleTouchEvents());
  }
  off() {
    this.canvas.style.touchAction = "auto", this.canvas.style.msTouchAction = "auto", this.canvas.removeEventListener("pointerdown", this._handleMouseDown), this.canvas.removeEventListener("pointermove", this._handleMouseMove), document.removeEventListener("pointerup", this._handleMouseUp), this.canvas.removeEventListener("mousedown", this._handleMouseDown), this.canvas.removeEventListener("mousemove", this._handleMouseMove), document.removeEventListener("mouseup", this._handleMouseUp), this.canvas.removeEventListener("touchstart", this._handleTouchStart), this.canvas.removeEventListener("touchmove", this._handleTouchMove), this.canvas.removeEventListener("touchend", this._handleTouchEnd);
  }
  isEmpty() {
    return this._isEmpty;
  }
  fromData(t2) {
    this.clear(), this._fromData(t2, ({ color: t3, curve: e2 }) => this._drawCurve({ color: t3, curve: e2 }), ({ color: t3, point: e2 }) => this._drawDot({ color: t3, point: e2 })), this._data = t2;
  }
  toData() {
    return this._data;
  }
  _strokeBegin(t2) {
    const e2 = { color: this.penColor, points: [] };
    "function" == typeof this.onBegin && this.onBegin(t2), this._data.push(e2), this._reset(), this._strokeUpdate(t2);
  }
  _strokeUpdate(t2) {
    if (0 === this._data.length)
      return void this._strokeBegin(t2);
    const e2 = t2.clientX, i2 = t2.clientY, o2 = this._createPoint(e2, i2), s2 = this._data[this._data.length - 1], n2 = s2.points, h = n2.length > 0 && n2[n2.length - 1], a = !!h && o2.distanceTo(h) <= this.minDistance, r = s2.color;
    if (!h || !h || !a) {
      const t3 = this._addPoint(o2);
      h ? t3 && this._drawCurve({ color: r, curve: t3 }) : this._drawDot({ color: r, point: o2 }), n2.push({ time: o2.time, x: o2.x, y: o2.y });
    }
  }
  _strokeEnd(t2) {
    this._strokeUpdate(t2), "function" == typeof this.onEnd && this.onEnd(t2);
  }
  _handlePointerEvents() {
    this._mouseButtonDown = false, this.canvas.addEventListener("pointerdown", this._handleMouseDown), this.canvas.addEventListener("pointermove", this._handleMouseMove), document.addEventListener("pointerup", this._handleMouseUp);
  }
  _handleMouseEvents() {
    this._mouseButtonDown = false, this.canvas.addEventListener("mousedown", this._handleMouseDown), this.canvas.addEventListener("mousemove", this._handleMouseMove), document.addEventListener("mouseup", this._handleMouseUp);
  }
  _handleTouchEvents() {
    this.canvas.addEventListener("touchstart", this._handleTouchStart), this.canvas.addEventListener("touchmove", this._handleTouchMove), this.canvas.addEventListener("touchend", this._handleTouchEnd);
  }
  _reset() {
    this._lastPoints = [], this._lastVelocity = 0, this._lastWidth = (this.minWidth + this.maxWidth) / 2, this._ctx.fillStyle = this.penColor;
  }
  _createPoint(t2, e2) {
    const i2 = this.canvas.getBoundingClientRect();
    return new g(t2 - i2.left, e2 - i2.top, (/* @__PURE__ */ new Date()).getTime());
  }
  _addPoint(t2) {
    const { _lastPoints: e2 } = this;
    if (e2.push(t2), e2.length > 2) {
      3 === e2.length && e2.unshift(e2[0]);
      const t3 = this._calculateCurveWidths(e2[1], e2[2]), i2 = _.fromPoints(e2, t3);
      return e2.shift(), i2;
    }
    return null;
  }
  _calculateCurveWidths(t2, e2) {
    const i2 = this.velocityFilterWeight * e2.velocityFrom(t2) + (1 - this.velocityFilterWeight) * this._lastVelocity, o2 = this._strokeWidth(i2), s2 = { end: o2, start: this._lastWidth };
    return this._lastVelocity = i2, this._lastWidth = o2, s2;
  }
  _strokeWidth(t2) {
    return Math.max(this.maxWidth / (t2 + 1), this.minWidth);
  }
  _drawCurveSegment(t2, e2, i2) {
    const o2 = this._ctx;
    o2.moveTo(t2, e2), o2.arc(t2, e2, i2, 0, 2 * Math.PI, false), this._isEmpty = false;
  }
  _drawCurve({ color: t2, curve: e2 }) {
    const i2 = this._ctx, o2 = e2.endWidth - e2.startWidth, s2 = 2 * Math.floor(e2.length());
    i2.beginPath(), i2.fillStyle = t2;
    for (let n2 = 0; n2 < s2; n2 += 1) {
      const t3 = n2 / s2, i3 = t3 * t3, h = i3 * t3, a = 1 - t3, r = a * a, l = r * a;
      let c = l * e2.startPoint.x;
      c += 3 * r * t3 * e2.control1.x, c += 3 * a * i3 * e2.control2.x, c += h * e2.endPoint.x;
      let d = l * e2.startPoint.y;
      d += 3 * r * t3 * e2.control1.y, d += 3 * a * i3 * e2.control2.y, d += h * e2.endPoint.y;
      const u = Math.min(e2.startWidth + h * o2, this.maxWidth);
      this._drawCurveSegment(c, d, u);
    }
    i2.closePath(), i2.fill();
  }
  _drawDot({ color: t2, point: e2 }) {
    const i2 = this._ctx, o2 = "function" == typeof this.dotSize ? this.dotSize() : this.dotSize;
    i2.beginPath(), this._drawCurveSegment(e2.x, e2.y, o2), i2.closePath(), i2.fillStyle = t2, i2.fill();
  }
  _fromData(t2, e2, i2) {
    for (const o2 of t2) {
      const { color: t3, points: s2 } = o2;
      if (s2.length > 1)
        for (let i3 = 0; i3 < s2.length; i3 += 1) {
          const o3 = s2[i3], n2 = new g(o3.x, o3.y, o3.time);
          this.penColor = t3, 0 === i3 && this._reset();
          const h = this._addPoint(n2);
          h && e2({ color: t3, curve: h });
        }
      else
        this._reset(), i2({ color: t3, point: s2[0] });
    }
  }
  _toSVG() {
    const t2 = this._data, e2 = Math.max(window.devicePixelRatio || 1, 1), i2 = this.canvas.width / e2, o2 = this.canvas.height / e2, s2 = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    s2.setAttribute("width", this.canvas.width.toString()), s2.setAttribute("height", this.canvas.height.toString()), this._fromData(t2, ({ color: t3, curve: e3 }) => {
      const i3 = document.createElement("path");
      if (!(isNaN(e3.control1.x) || isNaN(e3.control1.y) || isNaN(e3.control2.x) || isNaN(e3.control2.y))) {
        const o3 = `M ${e3.startPoint.x.toFixed(3)},${e3.startPoint.y.toFixed(3)} C ${e3.control1.x.toFixed(3)},${e3.control1.y.toFixed(3)} ${e3.control2.x.toFixed(3)},${e3.control2.y.toFixed(3)} ${e3.endPoint.x.toFixed(3)},${e3.endPoint.y.toFixed(3)}`;
        i3.setAttribute("d", o3), i3.setAttribute("stroke-width", (2.25 * e3.endWidth).toFixed(3)), i3.setAttribute("stroke", t3), i3.setAttribute("fill", "none"), i3.setAttribute("stroke-linecap", "round"), s2.appendChild(i3);
      }
    }, ({ color: t3, point: e3 }) => {
      const i3 = document.createElement("circle"), o3 = "function" == typeof this.dotSize ? this.dotSize() : this.dotSize;
      i3.setAttribute("r", o3.toString()), i3.setAttribute("cx", e3.x.toString()), i3.setAttribute("cy", e3.y.toString()), i3.setAttribute("fill", t3), s2.appendChild(i3);
    });
    const n2 = `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 ${i2} ${o2}" width="${i2}" height="${o2}">`;
    let h = s2.innerHTML;
    if (void 0 === h) {
      const t3 = document.createElement("dummy"), e3 = s2.childNodes;
      t3.innerHTML = "";
      for (let i3 = 0; i3 < e3.length; i3 += 1)
        t3.appendChild(e3[i3].cloneNode(true));
      h = t3.innerHTML;
    }
    return "data:image/svg+xml;base64," + btoa(n2 + h + "</svg>");
  }
};
var x = ["id", "data-uid", "disabled"];
var w = defineComponent(n(n({}, { name: "Vue3Signature" }), { props: { sigOption: { type: Object, default: () => ({ backgroundColor: "rgb(255,255,255)", penColor: "rgb(0, 0, 0)" }) }, w: { type: String, default: "100%" }, h: { type: String, default: "100%" }, clearOnResize: { type: Boolean, default: false }, waterMark: { type: Object, default: () => ({}) }, disabled: { type: Boolean, default: false }, defaultUrl: { type: String, default: "" } }, emits: ["begin", "end"], setup(t2, { expose: e2, emit: i2 }) {
  const o2 = t2, s2 = { width: "100%", height: "100%" };
  let h = reactive({ sig: void 0, option: n({ backgroundColor: "rgb(255,255,255)", penColor: "rgb(0, 0, 0)" }, o2.sigOption), uid: "canvas" + Math.random() });
  watch(() => o2.disabled, (t3) => {
    t3 ? h.sig.off() : h.sig.on();
  });
  const g2 = () => {
    h.sig.clear();
  }, _2 = (t3) => t3 ? h.sig.toDataURL(t3) : h.sig.toDataURL(), w2 = (t3) => {
    h.sig.fromDataURL(t3);
  }, f2 = () => h.sig.isEmpty(), E2 = (t3) => {
    if ("[object Object]" != Object.prototype.toString.call(t3))
      throw new Error("Expected Object, got " + typeof t3 + ".");
    {
      let e3 = document.getElementById(h.uid), i3 = { text: t3.text || "", x: t3.x || 20, y: t3.y || 20, sx: t3.sx || 40, sy: t3.sy || 40 }, o3 = e3.getContext("2d");
      o3.font = t3.font || "20px sans-serif", o3.fillStyle = t3.fillStyle || "#333", o3.strokeStyle = t3.strokeStyle || "#333", "all" == t3.style ? (o3.fillText(i3.text, i3.x, i3.y), o3.strokeText(i3.text, i3.sx, i3.sy)) : "stroke" == t3.style ? o3.strokeText(i3.text, i3.sx, i3.sy) : o3.fillText(i3.text, i3.x, i3.y), h.sig._isEmpty = false;
    }
  };
  return onMounted(() => {
    (() => {
      let t3 = document.getElementById(h.uid);
      function e3(t4) {
        let e4;
        f2() || (e4 = _2());
        let i3 = Math.max(window.devicePixelRatio || 1, 1);
        const s3 = RegExp(/px/);
        t4.width = s3.test(o2.w) ? Number(o2.w.replace(/px/g, "")) * i3 : t4.offsetWidth * i3, t4.height = s3.test(o2.h) ? Number(o2.h.replace(/px/g, "")) * i3 : t4.offsetHeight * i3, t4.getContext("2d").scale(i3, i3), g2(), !o2.clearOnResize && void 0 !== e4 && w2(e4), Object.keys(o2.waterMark).length && E2(o2.waterMark);
      }
      h.sig = new y(t3, h.option), h.sig.onBegin = (t4) => i2("begin"), h.sig.onEnd = (t4) => i2("end"), window.addEventListener("resize", () => e3(t3)), e3(t3), "" !== o2.defaultUrl && w2(o2.defaultUrl), o2.disabled ? h.sig.off() : h.sig.on();
    })();
  }), e2({ save: _2, clear: g2, isEmpty: f2, undo: () => {
    let t3 = h.sig.toData();
    t3 && (t3.pop(), h.sig.fromData(t3));
  }, addWaterMark: E2, fromDataURL: w2 }), (e3, i3) => (openBlock(), createElementBlock("div", { style: normalizeStyle({ width: t2.w, height: t2.h }), onTouchmove: i3[0] || (i3[0] = withModifiers(() => {
  }, ["prevent"])) }, [createBaseVNode("canvas", { id: unref(h).uid, "data-uid": unref(h).uid, disabled: unref(h).disabled, style: s2 }, null, 8, x)], 36));
} }));
var f;
var E = ((f = w).install = (t2) => {
  t2.component(f.name, f);
}, f);
var vue3_signature_es_default = E;
export {
  vue3_signature_es_default as default
};
/*! Bundled license information:

vue3-signature/dist/vue3-signature.es.js:
  (*!
   * Signature Pad v3.0.0-beta.4 | https://github.com/szimek/signature_pad
   * (c) 2020 Szymon Nowak | Released under the MIT license
   *)
*/
//# sourceMappingURL=vue3-signature.js.map
