{"version": 3, "sources": ["../../.pnpm/vue3-signature@0.2.4_vue@3.5.12_typescript@5.3.3_/node_modules/vue3-signature/dist/vue3-signature.es.js"], "sourcesContent": ["var t=Object.defineProperty,e=Object.prototype.hasOwnProperty,i=Object.getOwnPropertySymbols,o=Object.prototype.propertyIsEnumerable,s=(e,i,o)=>i in e?t(e,i,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[i]=o,n=(t,n)=>{for(var h in n||(n={}))e.call(n,h)&&s(t,h,n[h]);if(i)for(var h of i(n))o.call(n,h)&&s(t,h,n[h]);return t};import{defineComponent as h,reactive as a,watch as r,onMounted as l,openBlock as c,createElementBlock as d,normalizeStyle as u,withModifiers as v,createElementVNode as m,unref as p}from\"vue\";\n/*!\n * Signature Pad v3.0.0-beta.4 | https://github.com/szimek/signature_pad\n * (c) 2020 <PERSON><PERSON><PERSON> | Released under the MIT license\n */class g{constructor(t,e,i){this.x=t,this.y=e,this.time=i||Date.now()}distanceTo(t){return Math.sqrt(Math.pow(this.x-t.x,2)+Math.pow(this.y-t.y,2))}equals(t){return this.x===t.x&&this.y===t.y&&this.time===t.time}velocityFrom(t){return this.time!==t.time?this.distanceTo(t)/(this.time-t.time):0}}class _{constructor(t,e,i,o,s,n){this.startPoint=t,this.control2=e,this.control1=i,this.endPoint=o,this.startWidth=s,this.endWidth=n}static fromPoints(t,e){const i=this.calculateControlPoints(t[0],t[1],t[2]).c2,o=this.calculateControlPoints(t[1],t[2],t[3]).c1;return new _(t[1],i,o,t[2],e.start,e.end)}static calculateControlPoints(t,e,i){const o=t.x-e.x,s=t.y-e.y,n=e.x-i.x,h=e.y-i.y,a=(t.x+e.x)/2,r=(t.y+e.y)/2,l=(e.x+i.x)/2,c=(e.y+i.y)/2,d=Math.sqrt(o*o+s*s),u=Math.sqrt(n*n+h*h),v=u/(d+u),m=l+(a-l)*v,p=c+(r-c)*v,_=e.x-m,y=e.y-p;return{c1:new g(a+_,r+y),c2:new g(l+_,c+y)}}length(){let t,e,i=0;for(let o=0;o<=10;o+=1){const s=o/10,n=this.point(s,this.startPoint.x,this.control1.x,this.control2.x,this.endPoint.x),h=this.point(s,this.startPoint.y,this.control1.y,this.control2.y,this.endPoint.y);if(o>0){const o=n-t,s=h-e;i+=Math.sqrt(o*o+s*s)}t=n,e=h}return i}point(t,e,i,o,s){return e*(1-t)*(1-t)*(1-t)+3*i*(1-t)*(1-t)*t+3*o*(1-t)*t*t+s*t*t*t}}class y{constructor(t,e={}){this.canvas=t,this.options=e,this._handleMouseDown=t=>{1===t.which&&(this._mouseButtonDown=!0,this._strokeBegin(t))},this._handleMouseMove=t=>{this._mouseButtonDown&&this._strokeMoveUpdate(t)},this._handleMouseUp=t=>{1===t.which&&this._mouseButtonDown&&(this._mouseButtonDown=!1,this._strokeEnd(t))},this._handleTouchStart=t=>{if(t.preventDefault(),1===t.targetTouches.length){const e=t.changedTouches[0];this._strokeBegin(e)}},this._handleTouchMove=t=>{t.preventDefault();const e=t.targetTouches[0];this._strokeMoveUpdate(e)},this._handleTouchEnd=t=>{if(t.target===this.canvas){t.preventDefault();const e=t.changedTouches[0];this._strokeEnd(e)}},this.velocityFilterWeight=e.velocityFilterWeight||.7,this.minWidth=e.minWidth||.5,this.maxWidth=e.maxWidth||2.5,this.throttle=\"throttle\"in e?e.throttle:16,this.minDistance=\"minDistance\"in e?e.minDistance:5,this.dotSize=e.dotSize||function(){return(this.minWidth+this.maxWidth)/2},this.penColor=e.penColor||\"black\",this.backgroundColor=e.backgroundColor||\"rgba(0,0,0,0)\",this.onBegin=e.onBegin,this.onEnd=e.onEnd,this._strokeMoveUpdate=this.throttle?function(t,e=250){let i,o,s,n=0,h=null;const a=()=>{n=Date.now(),h=null,i=t.apply(o,s),h||(o=null,s=[])};return function(...r){const l=Date.now(),c=e-(l-n);return o=this,s=r,c<=0||c>e?(h&&(clearTimeout(h),h=null),n=l,i=t.apply(o,s),h||(o=null,s=[])):h||(h=window.setTimeout(a,c)),i}}(y.prototype._strokeUpdate,this.throttle):y.prototype._strokeUpdate,this._ctx=t.getContext(\"2d\"),this.clear(),this.on()}clear(){const{_ctx:t,canvas:e}=this;t.fillStyle=this.backgroundColor,t.clearRect(0,0,e.width,e.height),t.fillRect(0,0,e.width,e.height),this._data=[],this._reset(),this._isEmpty=!0}fromDataURL(t,e={},i){const o=new Image,s=e.ratio||window.devicePixelRatio||1,n=e.width||this.canvas.width/s,h=e.height||this.canvas.height/s;this._reset(),o.onload=()=>{this._ctx.drawImage(o,0,0,n,h),i&&i()},o.onerror=t=>{i&&i(t)},o.src=t,this._isEmpty=!1}toDataURL(t=\"image/png\",e){switch(t){case\"image/svg+xml\":return this._toSVG();default:return this.canvas.toDataURL(t,e)}}on(){this.canvas.style.touchAction=\"none\",this.canvas.style.msTouchAction=\"none\",window.PointerEvent?this._handlePointerEvents():(this._handleMouseEvents(),\"ontouchstart\"in window&&this._handleTouchEvents())}off(){this.canvas.style.touchAction=\"auto\",this.canvas.style.msTouchAction=\"auto\",this.canvas.removeEventListener(\"pointerdown\",this._handleMouseDown),this.canvas.removeEventListener(\"pointermove\",this._handleMouseMove),document.removeEventListener(\"pointerup\",this._handleMouseUp),this.canvas.removeEventListener(\"mousedown\",this._handleMouseDown),this.canvas.removeEventListener(\"mousemove\",this._handleMouseMove),document.removeEventListener(\"mouseup\",this._handleMouseUp),this.canvas.removeEventListener(\"touchstart\",this._handleTouchStart),this.canvas.removeEventListener(\"touchmove\",this._handleTouchMove),this.canvas.removeEventListener(\"touchend\",this._handleTouchEnd)}isEmpty(){return this._isEmpty}fromData(t){this.clear(),this._fromData(t,(({color:t,curve:e})=>this._drawCurve({color:t,curve:e})),(({color:t,point:e})=>this._drawDot({color:t,point:e}))),this._data=t}toData(){return this._data}_strokeBegin(t){const e={color:this.penColor,points:[]};\"function\"==typeof this.onBegin&&this.onBegin(t),this._data.push(e),this._reset(),this._strokeUpdate(t)}_strokeUpdate(t){if(0===this._data.length)return void this._strokeBegin(t);const e=t.clientX,i=t.clientY,o=this._createPoint(e,i),s=this._data[this._data.length-1],n=s.points,h=n.length>0&&n[n.length-1],a=!!h&&o.distanceTo(h)<=this.minDistance,r=s.color;if(!h||!h||!a){const t=this._addPoint(o);h?t&&this._drawCurve({color:r,curve:t}):this._drawDot({color:r,point:o}),n.push({time:o.time,x:o.x,y:o.y})}}_strokeEnd(t){this._strokeUpdate(t),\"function\"==typeof this.onEnd&&this.onEnd(t)}_handlePointerEvents(){this._mouseButtonDown=!1,this.canvas.addEventListener(\"pointerdown\",this._handleMouseDown),this.canvas.addEventListener(\"pointermove\",this._handleMouseMove),document.addEventListener(\"pointerup\",this._handleMouseUp)}_handleMouseEvents(){this._mouseButtonDown=!1,this.canvas.addEventListener(\"mousedown\",this._handleMouseDown),this.canvas.addEventListener(\"mousemove\",this._handleMouseMove),document.addEventListener(\"mouseup\",this._handleMouseUp)}_handleTouchEvents(){this.canvas.addEventListener(\"touchstart\",this._handleTouchStart),this.canvas.addEventListener(\"touchmove\",this._handleTouchMove),this.canvas.addEventListener(\"touchend\",this._handleTouchEnd)}_reset(){this._lastPoints=[],this._lastVelocity=0,this._lastWidth=(this.minWidth+this.maxWidth)/2,this._ctx.fillStyle=this.penColor}_createPoint(t,e){const i=this.canvas.getBoundingClientRect();return new g(t-i.left,e-i.top,(new Date).getTime())}_addPoint(t){const{_lastPoints:e}=this;if(e.push(t),e.length>2){3===e.length&&e.unshift(e[0]);const t=this._calculateCurveWidths(e[1],e[2]),i=_.fromPoints(e,t);return e.shift(),i}return null}_calculateCurveWidths(t,e){const i=this.velocityFilterWeight*e.velocityFrom(t)+(1-this.velocityFilterWeight)*this._lastVelocity,o=this._strokeWidth(i),s={end:o,start:this._lastWidth};return this._lastVelocity=i,this._lastWidth=o,s}_strokeWidth(t){return Math.max(this.maxWidth/(t+1),this.minWidth)}_drawCurveSegment(t,e,i){const o=this._ctx;o.moveTo(t,e),o.arc(t,e,i,0,2*Math.PI,!1),this._isEmpty=!1}_drawCurve({color:t,curve:e}){const i=this._ctx,o=e.endWidth-e.startWidth,s=2*Math.floor(e.length());i.beginPath(),i.fillStyle=t;for(let n=0;n<s;n+=1){const t=n/s,i=t*t,h=i*t,a=1-t,r=a*a,l=r*a;let c=l*e.startPoint.x;c+=3*r*t*e.control1.x,c+=3*a*i*e.control2.x,c+=h*e.endPoint.x;let d=l*e.startPoint.y;d+=3*r*t*e.control1.y,d+=3*a*i*e.control2.y,d+=h*e.endPoint.y;const u=Math.min(e.startWidth+h*o,this.maxWidth);this._drawCurveSegment(c,d,u)}i.closePath(),i.fill()}_drawDot({color:t,point:e}){const i=this._ctx,o=\"function\"==typeof this.dotSize?this.dotSize():this.dotSize;i.beginPath(),this._drawCurveSegment(e.x,e.y,o),i.closePath(),i.fillStyle=t,i.fill()}_fromData(t,e,i){for(const o of t){const{color:t,points:s}=o;if(s.length>1)for(let i=0;i<s.length;i+=1){const o=s[i],n=new g(o.x,o.y,o.time);this.penColor=t,0===i&&this._reset();const h=this._addPoint(n);h&&e({color:t,curve:h})}else this._reset(),i({color:t,point:s[0]})}}_toSVG(){const t=this._data,e=Math.max(window.devicePixelRatio||1,1),i=this.canvas.width/e,o=this.canvas.height/e,s=document.createElementNS(\"http://www.w3.org/2000/svg\",\"svg\");s.setAttribute(\"width\",this.canvas.width.toString()),s.setAttribute(\"height\",this.canvas.height.toString()),this._fromData(t,(({color:t,curve:e})=>{const i=document.createElement(\"path\");if(!(isNaN(e.control1.x)||isNaN(e.control1.y)||isNaN(e.control2.x)||isNaN(e.control2.y))){const o=`M ${e.startPoint.x.toFixed(3)},${e.startPoint.y.toFixed(3)} C ${e.control1.x.toFixed(3)},${e.control1.y.toFixed(3)} ${e.control2.x.toFixed(3)},${e.control2.y.toFixed(3)} ${e.endPoint.x.toFixed(3)},${e.endPoint.y.toFixed(3)}`;i.setAttribute(\"d\",o),i.setAttribute(\"stroke-width\",(2.25*e.endWidth).toFixed(3)),i.setAttribute(\"stroke\",t),i.setAttribute(\"fill\",\"none\"),i.setAttribute(\"stroke-linecap\",\"round\"),s.appendChild(i)}}),(({color:t,point:e})=>{const i=document.createElement(\"circle\"),o=\"function\"==typeof this.dotSize?this.dotSize():this.dotSize;i.setAttribute(\"r\",o.toString()),i.setAttribute(\"cx\",e.x.toString()),i.setAttribute(\"cy\",e.y.toString()),i.setAttribute(\"fill\",t),s.appendChild(i)}));const n=`<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"0 0 ${i} ${o}\" width=\"${i}\" height=\"${o}\">`;let h=s.innerHTML;if(void 0===h){const t=document.createElement(\"dummy\"),e=s.childNodes;t.innerHTML=\"\";for(let i=0;i<e.length;i+=1)t.appendChild(e[i].cloneNode(!0));h=t.innerHTML}return\"data:image/svg+xml;base64,\"+btoa(n+h+\"</svg>\")}}const x=[\"id\",\"data-uid\",\"disabled\"];var w=h(n(n({},{name:\"Vue3Signature\"}),{props:{sigOption:{type:Object,default:()=>({backgroundColor:\"rgb(255,255,255)\",penColor:\"rgb(0, 0, 0)\"})},w:{type:String,default:\"100%\"},h:{type:String,default:\"100%\"},clearOnResize:{type:Boolean,default:!1},waterMark:{type:Object,default:()=>({})},disabled:{type:Boolean,default:!1},defaultUrl:{type:String,default:\"\"}},emits:[\"begin\",\"end\"],setup(t,{expose:e,emit:i}){const o=t,s={width:\"100%\",height:\"100%\"};let h=a({sig:void 0,option:n({backgroundColor:\"rgb(255,255,255)\",penColor:\"rgb(0, 0, 0)\"},o.sigOption),uid:\"canvas\"+Math.random()});r((()=>o.disabled),(t=>{t?h.sig.off():h.sig.on()}));const g=()=>{h.sig.clear()},_=t=>t?h.sig.toDataURL(t):h.sig.toDataURL(),w=t=>{h.sig.fromDataURL(t)},f=()=>h.sig.isEmpty(),E=t=>{if(\"[object Object]\"!=Object.prototype.toString.call(t))throw new Error(\"Expected Object, got \"+typeof t+\".\");{let e=document.getElementById(h.uid),i={text:t.text||\"\",x:t.x||20,y:t.y||20,sx:t.sx||40,sy:t.sy||40},o=e.getContext(\"2d\");o.font=t.font||\"20px sans-serif\",o.fillStyle=t.fillStyle||\"#333\",o.strokeStyle=t.strokeStyle||\"#333\",\"all\"==t.style?(o.fillText(i.text,i.x,i.y),o.strokeText(i.text,i.sx,i.sy)):\"stroke\"==t.style?o.strokeText(i.text,i.sx,i.sy):o.fillText(i.text,i.x,i.y),h.sig._isEmpty=!1}};return l((()=>{(()=>{let t=document.getElementById(h.uid);function e(t){let e;f()||(e=_());let i=Math.max(window.devicePixelRatio||1,1);const s=RegExp(/px/);t.width=s.test(o.w)?Number(o.w.replace(/px/g,\"\"))*i:t.offsetWidth*i,t.height=s.test(o.h)?Number(o.h.replace(/px/g,\"\"))*i:t.offsetHeight*i,t.getContext(\"2d\").scale(i,i),g(),!o.clearOnResize&&void 0!==e&&w(e),Object.keys(o.waterMark).length&&E(o.waterMark)}h.sig=new y(t,h.option),h.sig.onBegin=t=>i(\"begin\"),h.sig.onEnd=t=>i(\"end\"),window.addEventListener(\"resize\",(()=>e(t))),e(t),\"\"!==o.defaultUrl&&w(o.defaultUrl),o.disabled?h.sig.off():h.sig.on()})()})),e({save:_,clear:g,isEmpty:f,undo:()=>{let t=h.sig.toData();t&&(t.pop(),h.sig.fromData(t))},addWaterMark:E,fromDataURL:w}),(e,i)=>(c(),d(\"div\",{style:u({width:t.w,height:t.h}),onTouchmove:i[0]||(i[0]=v((()=>{}),[\"prevent\"]))},[m(\"canvas\",{id:p(h).uid,\"data-uid\":p(h).uid,disabled:p(h).disabled,style:s},null,8,x)],36))}}));var f,E=((f=w).install=t=>{t.component(f.name,f)},f);export default E;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAI,IAAE,OAAO;AAAb,IAA4B,IAAE,OAAO,UAAU;AAA/C,IAA8D,IAAE,OAAO;AAAvE,IAA6F,IAAE,OAAO,UAAU;AAAhH,IAAqI,IAAE,CAACA,IAAEC,IAAEC,OAAID,MAAKD,KAAE,EAAEA,IAAEC,IAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAMC,GAAC,CAAC,IAAEF,GAAEC,EAAC,IAAEC;AAAvN,IAAyN,IAAE,CAACC,IAAEC,OAAI;AAAC,WAAQ,KAAKA,OAAIA,KAAE,CAAC;AAAG,MAAE,KAAKA,IAAE,CAAC,KAAG,EAAED,IAAE,GAAEC,GAAE,CAAC,CAAC;AAAE,MAAG;AAAE,aAAQ,KAAK,EAAEA,EAAC;AAAE,QAAE,KAAKA,IAAE,CAAC,KAAG,EAAED,IAAE,GAAEC,GAAE,CAAC,CAAC;AAAE,SAAOD;AAAC;AAIxU,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEH,IAAEC,IAAE;AAAC,SAAK,IAAEE,IAAE,KAAK,IAAEH,IAAE,KAAK,OAAKC,MAAG,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,WAAWE,IAAE;AAAC,WAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAEA,GAAE,GAAE,CAAC,IAAE,KAAK,IAAI,KAAK,IAAEA,GAAE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,WAAO,KAAK,MAAIA,GAAE,KAAG,KAAK,MAAIA,GAAE,KAAG,KAAK,SAAOA,GAAE;AAAA,EAAI;AAAA,EAAC,aAAaA,IAAE;AAAC,WAAO,KAAK,SAAOA,GAAE,OAAK,KAAK,WAAWA,EAAC,KAAG,KAAK,OAAKA,GAAE,QAAM;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,YAAYA,IAAEH,IAAEC,IAAEC,IAAEG,IAAED,IAAE;AAAC,SAAK,aAAWD,IAAE,KAAK,WAASH,IAAE,KAAK,WAASC,IAAE,KAAK,WAASC,IAAE,KAAK,aAAWG,IAAE,KAAK,WAASD;AAAA,EAAC;AAAA,EAAC,OAAO,WAAWD,IAAEH,IAAE;AAAC,UAAMC,KAAE,KAAK,uBAAuBE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAE,IAAGD,KAAE,KAAK,uBAAuBC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAE;AAAG,WAAO,IAAI,GAAEA,GAAE,CAAC,GAAEF,IAAEC,IAAEC,GAAE,CAAC,GAAEH,GAAE,OAAMA,GAAE,GAAG;AAAA,EAAC;AAAA,EAAC,OAAO,uBAAuBG,IAAEH,IAAEC,IAAE;AAAC,UAAMC,KAAEC,GAAE,IAAEH,GAAE,GAAEK,KAAEF,GAAE,IAAEH,GAAE,GAAEI,KAAEJ,GAAE,IAAEC,GAAE,GAAE,IAAED,GAAE,IAAEC,GAAE,GAAE,KAAGE,GAAE,IAAEH,GAAE,KAAG,GAAE,KAAGG,GAAE,IAAEH,GAAE,KAAG,GAAE,KAAGA,GAAE,IAAEC,GAAE,KAAG,GAAE,KAAGD,GAAE,IAAEC,GAAE,KAAG,GAAE,IAAE,KAAK,KAAKC,KAAEA,KAAEG,KAAEA,EAAC,GAAE,IAAE,KAAK,KAAKD,KAAEA,KAAE,IAAE,CAAC,GAAE,IAAE,KAAG,IAAE,IAAG,IAAE,KAAG,IAAE,KAAG,GAAE,IAAE,KAAG,IAAE,KAAG,GAAEE,KAAEN,GAAE,IAAE,GAAEO,KAAEP,GAAE,IAAE;AAAE,WAAM,EAAC,IAAG,IAAI,EAAE,IAAEM,IAAE,IAAEC,EAAC,GAAE,IAAG,IAAI,EAAE,IAAED,IAAE,IAAEC,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAIJ,IAAEH,IAAEC,KAAE;AAAE,aAAQC,KAAE,GAAEA,MAAG,IAAGA,MAAG,GAAE;AAAC,YAAMG,KAAEH,KAAE,IAAGE,KAAE,KAAK,MAAMC,IAAE,KAAK,WAAW,GAAE,KAAK,SAAS,GAAE,KAAK,SAAS,GAAE,KAAK,SAAS,CAAC,GAAE,IAAE,KAAK,MAAMA,IAAE,KAAK,WAAW,GAAE,KAAK,SAAS,GAAE,KAAK,SAAS,GAAE,KAAK,SAAS,CAAC;AAAE,UAAGH,KAAE,GAAE;AAAC,cAAMA,KAAEE,KAAED,IAAEE,KAAE,IAAEL;AAAE,QAAAC,MAAG,KAAK,KAAKC,KAAEA,KAAEG,KAAEA,EAAC;AAAA,MAAC;AAAC,MAAAF,KAAEC,IAAEJ,KAAE;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,MAAME,IAAEH,IAAEC,IAAEC,IAAEG,IAAE;AAAC,WAAOL,MAAG,IAAEG,OAAI,IAAEA,OAAI,IAAEA,MAAG,IAAEF,MAAG,IAAEE,OAAI,IAAEA,MAAGA,KAAE,IAAED,MAAG,IAAEC,MAAGA,KAAEA,KAAEE,KAAEF,KAAEA,KAAEA;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,YAAYA,IAAEH,KAAE,CAAC,GAAE;AAAC,SAAK,SAAOG,IAAE,KAAK,UAAQH,IAAE,KAAK,mBAAiB,CAAAG,OAAG;AAAC,YAAIA,GAAE,UAAQ,KAAK,mBAAiB,MAAG,KAAK,aAAaA,EAAC;AAAA,IAAE,GAAE,KAAK,mBAAiB,CAAAA,OAAG;AAAC,WAAK,oBAAkB,KAAK,kBAAkBA,EAAC;AAAA,IAAC,GAAE,KAAK,iBAAe,CAAAA,OAAG;AAAC,YAAIA,GAAE,SAAO,KAAK,qBAAmB,KAAK,mBAAiB,OAAG,KAAK,WAAWA,EAAC;AAAA,IAAE,GAAE,KAAK,oBAAkB,CAAAA,OAAG;AAAC,UAAGA,GAAE,eAAe,GAAE,MAAIA,GAAE,cAAc,QAAO;AAAC,cAAMH,KAAEG,GAAE,eAAe,CAAC;AAAE,aAAK,aAAaH,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,KAAK,mBAAiB,CAAAG,OAAG;AAAC,MAAAA,GAAE,eAAe;AAAE,YAAMH,KAAEG,GAAE,cAAc,CAAC;AAAE,WAAK,kBAAkBH,EAAC;AAAA,IAAC,GAAE,KAAK,kBAAgB,CAAAG,OAAG;AAAC,UAAGA,GAAE,WAAS,KAAK,QAAO;AAAC,QAAAA,GAAE,eAAe;AAAE,cAAMH,KAAEG,GAAE,eAAe,CAAC;AAAE,aAAK,WAAWH,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,KAAK,uBAAqBA,GAAE,wBAAsB,KAAG,KAAK,WAASA,GAAE,YAAU,KAAG,KAAK,WAASA,GAAE,YAAU,KAAI,KAAK,WAAS,cAAaA,KAAEA,GAAE,WAAS,IAAG,KAAK,cAAY,iBAAgBA,KAAEA,GAAE,cAAY,GAAE,KAAK,UAAQA,GAAE,WAAS,WAAU;AAAC,cAAO,KAAK,WAAS,KAAK,YAAU;AAAA,IAAC,GAAE,KAAK,WAASA,GAAE,YAAU,SAAQ,KAAK,kBAAgBA,GAAE,mBAAiB,iBAAgB,KAAK,UAAQA,GAAE,SAAQ,KAAK,QAAMA,GAAE,OAAM,KAAK,oBAAkB,KAAK,WAAS,yBAASG,IAAEH,KAAE,KAAI;AAAC,UAAIC,IAAEC,IAAEG,IAAED,KAAE,GAAE,IAAE;AAAK,YAAM,IAAE,MAAI;AAAC,QAAAA,KAAE,KAAK,IAAI,GAAE,IAAE,MAAKH,KAAEE,GAAE,MAAMD,IAAEG,EAAC,GAAE,MAAIH,KAAE,MAAKG,KAAE,CAAC;AAAA,MAAE;AAAE,aAAO,YAAY,GAAE;AAAC,cAAM,IAAE,KAAK,IAAI,GAAE,IAAEL,MAAG,IAAEI;AAAG,eAAOF,KAAE,MAAKG,KAAE,GAAE,KAAG,KAAG,IAAEL,MAAG,MAAI,aAAa,CAAC,GAAE,IAAE,OAAMI,KAAE,GAAEH,KAAEE,GAAE,MAAMD,IAAEG,EAAC,GAAE,MAAIH,KAAE,MAAKG,KAAE,CAAC,MAAI,MAAI,IAAE,OAAO,WAAW,GAAE,CAAC,IAAGJ;AAAA,MAAC;AAAA,IAAC,EAAE,GAAE,UAAU,eAAc,KAAK,QAAQ,IAAE,GAAE,UAAU,eAAc,KAAK,OAAKE,GAAE,WAAW,IAAI,GAAE,KAAK,MAAM,GAAE,KAAK,GAAG;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAK,EAAC,MAAKA,IAAE,QAAOH,GAAC,IAAE;AAAK,IAAAG,GAAE,YAAU,KAAK,iBAAgBA,GAAE,UAAU,GAAE,GAAEH,GAAE,OAAMA,GAAE,MAAM,GAAEG,GAAE,SAAS,GAAE,GAAEH,GAAE,OAAMA,GAAE,MAAM,GAAE,KAAK,QAAM,CAAC,GAAE,KAAK,OAAO,GAAE,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,YAAYG,IAAEH,KAAE,CAAC,GAAEC,IAAE;AAAC,UAAMC,KAAE,IAAI,SAAMG,KAAEL,GAAE,SAAO,OAAO,oBAAkB,GAAEI,KAAEJ,GAAE,SAAO,KAAK,OAAO,QAAMK,IAAE,IAAEL,GAAE,UAAQ,KAAK,OAAO,SAAOK;AAAE,SAAK,OAAO,GAAEH,GAAE,SAAO,MAAI;AAAC,WAAK,KAAK,UAAUA,IAAE,GAAE,GAAEE,IAAE,CAAC,GAAEH,MAAGA,GAAE;AAAA,IAAC,GAAEC,GAAE,UAAQ,CAAAC,OAAG;AAAC,MAAAF,MAAGA,GAAEE,EAAC;AAAA,IAAC,GAAED,GAAE,MAAIC,IAAE,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,UAAUA,KAAE,aAAYH,IAAE;AAAC,YAAOG,IAAE;AAAA,MAAC,KAAI;AAAgB,eAAO,KAAK,OAAO;AAAA,MAAE;AAAQ,eAAO,KAAK,OAAO,UAAUA,IAAEH,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,SAAK,OAAO,MAAM,cAAY,QAAO,KAAK,OAAO,MAAM,gBAAc,QAAO,OAAO,eAAa,KAAK,qBAAqB,KAAG,KAAK,mBAAmB,GAAE,kBAAiB,UAAQ,KAAK,mBAAmB;AAAA,EAAE;AAAA,EAAC,MAAK;AAAC,SAAK,OAAO,MAAM,cAAY,QAAO,KAAK,OAAO,MAAM,gBAAc,QAAO,KAAK,OAAO,oBAAoB,eAAc,KAAK,gBAAgB,GAAE,KAAK,OAAO,oBAAoB,eAAc,KAAK,gBAAgB,GAAE,SAAS,oBAAoB,aAAY,KAAK,cAAc,GAAE,KAAK,OAAO,oBAAoB,aAAY,KAAK,gBAAgB,GAAE,KAAK,OAAO,oBAAoB,aAAY,KAAK,gBAAgB,GAAE,SAAS,oBAAoB,WAAU,KAAK,cAAc,GAAE,KAAK,OAAO,oBAAoB,cAAa,KAAK,iBAAiB,GAAE,KAAK,OAAO,oBAAoB,aAAY,KAAK,gBAAgB,GAAE,KAAK,OAAO,oBAAoB,YAAW,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,SAASG,IAAE;AAAC,SAAK,MAAM,GAAE,KAAK,UAAUA,IAAG,CAAC,EAAC,OAAMA,IAAE,OAAMH,GAAC,MAAI,KAAK,WAAW,EAAC,OAAMG,IAAE,OAAMH,GAAC,CAAC,GAAI,CAAC,EAAC,OAAMG,IAAE,OAAMH,GAAC,MAAI,KAAK,SAAS,EAAC,OAAMG,IAAE,OAAMH,GAAC,CAAC,CAAE,GAAE,KAAK,QAAMG;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAK;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAMH,KAAE,EAAC,OAAM,KAAK,UAAS,QAAO,CAAC,EAAC;AAAE,kBAAY,OAAO,KAAK,WAAS,KAAK,QAAQG,EAAC,GAAE,KAAK,MAAM,KAAKH,EAAC,GAAE,KAAK,OAAO,GAAE,KAAK,cAAcG,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,QAAG,MAAI,KAAK,MAAM;AAAO,aAAO,KAAK,KAAK,aAAaA,EAAC;AAAE,UAAMH,KAAEG,GAAE,SAAQF,KAAEE,GAAE,SAAQD,KAAE,KAAK,aAAaF,IAAEC,EAAC,GAAEI,KAAE,KAAK,MAAM,KAAK,MAAM,SAAO,CAAC,GAAED,KAAEC,GAAE,QAAO,IAAED,GAAE,SAAO,KAAGA,GAAEA,GAAE,SAAO,CAAC,GAAE,IAAE,CAAC,CAAC,KAAGF,GAAE,WAAW,CAAC,KAAG,KAAK,aAAY,IAAEG,GAAE;AAAM,QAAG,CAAC,KAAG,CAAC,KAAG,CAAC,GAAE;AAAC,YAAMF,KAAE,KAAK,UAAUD,EAAC;AAAE,UAAEC,MAAG,KAAK,WAAW,EAAC,OAAM,GAAE,OAAMA,GAAC,CAAC,IAAE,KAAK,SAAS,EAAC,OAAM,GAAE,OAAMD,GAAC,CAAC,GAAEE,GAAE,KAAK,EAAC,MAAKF,GAAE,MAAK,GAAEA,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAWC,IAAE;AAAC,SAAK,cAAcA,EAAC,GAAE,cAAY,OAAO,KAAK,SAAO,KAAK,MAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,SAAK,mBAAiB,OAAG,KAAK,OAAO,iBAAiB,eAAc,KAAK,gBAAgB,GAAE,KAAK,OAAO,iBAAiB,eAAc,KAAK,gBAAgB,GAAE,SAAS,iBAAiB,aAAY,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,SAAK,mBAAiB,OAAG,KAAK,OAAO,iBAAiB,aAAY,KAAK,gBAAgB,GAAE,KAAK,OAAO,iBAAiB,aAAY,KAAK,gBAAgB,GAAE,SAAS,iBAAiB,WAAU,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,SAAK,OAAO,iBAAiB,cAAa,KAAK,iBAAiB,GAAE,KAAK,OAAO,iBAAiB,aAAY,KAAK,gBAAgB,GAAE,KAAK,OAAO,iBAAiB,YAAW,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,cAAY,CAAC,GAAE,KAAK,gBAAc,GAAE,KAAK,cAAY,KAAK,WAAS,KAAK,YAAU,GAAE,KAAK,KAAK,YAAU,KAAK;AAAA,EAAQ;AAAA,EAAC,aAAaA,IAAEH,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAO,sBAAsB;AAAE,WAAO,IAAI,EAAEE,KAAEF,GAAE,MAAKD,KAAEC,GAAE,MAAK,oBAAI,QAAM,QAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUE,IAAE;AAAC,UAAK,EAAC,aAAYH,GAAC,IAAE;AAAK,QAAGA,GAAE,KAAKG,EAAC,GAAEH,GAAE,SAAO,GAAE;AAAC,YAAIA,GAAE,UAAQA,GAAE,QAAQA,GAAE,CAAC,CAAC;AAAE,YAAMG,KAAE,KAAK,sBAAsBH,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAEC,KAAE,EAAE,WAAWD,IAAEG,EAAC;AAAE,aAAOH,GAAE,MAAM,GAAEC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,sBAAsBE,IAAEH,IAAE;AAAC,UAAMC,KAAE,KAAK,uBAAqBD,GAAE,aAAaG,EAAC,KAAG,IAAE,KAAK,wBAAsB,KAAK,eAAcD,KAAE,KAAK,aAAaD,EAAC,GAAEI,KAAE,EAAC,KAAIH,IAAE,OAAM,KAAK,WAAU;AAAE,WAAO,KAAK,gBAAcD,IAAE,KAAK,aAAWC,IAAEG;AAAA,EAAC;AAAA,EAAC,aAAaF,IAAE;AAAC,WAAO,KAAK,IAAI,KAAK,YAAUA,KAAE,IAAG,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEH,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAK,IAAAA,GAAE,OAAOC,IAAEH,EAAC,GAAEE,GAAE,IAAIC,IAAEH,IAAEC,IAAE,GAAE,IAAE,KAAK,IAAG,KAAE,GAAE,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,WAAW,EAAC,OAAME,IAAE,OAAMH,GAAC,GAAE;AAAC,UAAMC,KAAE,KAAK,MAAKC,KAAEF,GAAE,WAASA,GAAE,YAAWK,KAAE,IAAE,KAAK,MAAML,GAAE,OAAO,CAAC;AAAE,IAAAC,GAAE,UAAU,GAAEA,GAAE,YAAUE;AAAE,aAAQC,KAAE,GAAEA,KAAEC,IAAED,MAAG,GAAE;AAAC,YAAMD,KAAEC,KAAEC,IAAEJ,KAAEE,KAAEA,IAAE,IAAEF,KAAEE,IAAE,IAAE,IAAEA,IAAE,IAAE,IAAE,GAAE,IAAE,IAAE;AAAE,UAAI,IAAE,IAAEH,GAAE,WAAW;AAAE,WAAG,IAAE,IAAEG,KAAEH,GAAE,SAAS,GAAE,KAAG,IAAE,IAAEC,KAAED,GAAE,SAAS,GAAE,KAAG,IAAEA,GAAE,SAAS;AAAE,UAAI,IAAE,IAAEA,GAAE,WAAW;AAAE,WAAG,IAAE,IAAEG,KAAEH,GAAE,SAAS,GAAE,KAAG,IAAE,IAAEC,KAAED,GAAE,SAAS,GAAE,KAAG,IAAEA,GAAE,SAAS;AAAE,YAAM,IAAE,KAAK,IAAIA,GAAE,aAAW,IAAEE,IAAE,KAAK,QAAQ;AAAE,WAAK,kBAAkB,GAAE,GAAE,CAAC;AAAA,IAAC;AAAC,IAAAD,GAAE,UAAU,GAAEA,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,SAAS,EAAC,OAAME,IAAE,OAAMH,GAAC,GAAE;AAAC,UAAMC,KAAE,KAAK,MAAKC,KAAE,cAAY,OAAO,KAAK,UAAQ,KAAK,QAAQ,IAAE,KAAK;AAAQ,IAAAD,GAAE,UAAU,GAAE,KAAK,kBAAkBD,GAAE,GAAEA,GAAE,GAAEE,EAAC,GAAED,GAAE,UAAU,GAAEA,GAAE,YAAUE,IAAEF,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,UAAUE,IAAEH,IAAEC,IAAE;AAAC,eAAUC,MAAKC,IAAE;AAAC,YAAK,EAAC,OAAMA,IAAE,QAAOE,GAAC,IAAEH;AAAE,UAAGG,GAAE,SAAO;AAAE,iBAAQJ,KAAE,GAAEA,KAAEI,GAAE,QAAOJ,MAAG,GAAE;AAAC,gBAAMC,KAAEG,GAAEJ,EAAC,GAAEG,KAAE,IAAI,EAAEF,GAAE,GAAEA,GAAE,GAAEA,GAAE,IAAI;AAAE,eAAK,WAASC,IAAE,MAAIF,MAAG,KAAK,OAAO;AAAE,gBAAM,IAAE,KAAK,UAAUG,EAAC;AAAE,eAAGJ,GAAE,EAAC,OAAMG,IAAE,OAAM,EAAC,CAAC;AAAA,QAAC;AAAA;AAAM,aAAK,OAAO,GAAEF,GAAE,EAAC,OAAME,IAAE,OAAME,GAAE,CAAC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAMF,KAAE,KAAK,OAAMH,KAAE,KAAK,IAAI,OAAO,oBAAkB,GAAE,CAAC,GAAEC,KAAE,KAAK,OAAO,QAAMD,IAAEE,KAAE,KAAK,OAAO,SAAOF,IAAEK,KAAE,SAAS,gBAAgB,8BAA6B,KAAK;AAAE,IAAAA,GAAE,aAAa,SAAQ,KAAK,OAAO,MAAM,SAAS,CAAC,GAAEA,GAAE,aAAa,UAAS,KAAK,OAAO,OAAO,SAAS,CAAC,GAAE,KAAK,UAAUF,IAAG,CAAC,EAAC,OAAMA,IAAE,OAAMH,GAAC,MAAI;AAAC,YAAMC,KAAE,SAAS,cAAc,MAAM;AAAE,UAAG,EAAE,MAAMD,GAAE,SAAS,CAAC,KAAG,MAAMA,GAAE,SAAS,CAAC,KAAG,MAAMA,GAAE,SAAS,CAAC,KAAG,MAAMA,GAAE,SAAS,CAAC,IAAG;AAAC,cAAME,KAAE,KAAKF,GAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,IAAIA,GAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,MAAMA,GAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAIA,GAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAIA,GAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAIA,GAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAIA,GAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAIA,GAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;AAAG,QAAAC,GAAE,aAAa,KAAIC,EAAC,GAAED,GAAE,aAAa,iBAAgB,OAAKD,GAAE,UAAU,QAAQ,CAAC,CAAC,GAAEC,GAAE,aAAa,UAASE,EAAC,GAAEF,GAAE,aAAa,QAAO,MAAM,GAAEA,GAAE,aAAa,kBAAiB,OAAO,GAAEI,GAAE,YAAYJ,EAAC;AAAA,MAAC;AAAA,IAAC,GAAI,CAAC,EAAC,OAAME,IAAE,OAAMH,GAAC,MAAI;AAAC,YAAMC,KAAE,SAAS,cAAc,QAAQ,GAAEC,KAAE,cAAY,OAAO,KAAK,UAAQ,KAAK,QAAQ,IAAE,KAAK;AAAQ,MAAAD,GAAE,aAAa,KAAIC,GAAE,SAAS,CAAC,GAAED,GAAE,aAAa,MAAKD,GAAE,EAAE,SAAS,CAAC,GAAEC,GAAE,aAAa,MAAKD,GAAE,EAAE,SAAS,CAAC,GAAEC,GAAE,aAAa,QAAOE,EAAC,GAAEE,GAAE,YAAYJ,EAAC;AAAA,IAAC,CAAE;AAAE,UAAMG,KAAE,mGAAmGH,EAAC,IAAIC,EAAC,YAAYD,EAAC,aAAaC,EAAC;AAAK,QAAI,IAAEG,GAAE;AAAU,QAAG,WAAS,GAAE;AAAC,YAAMF,KAAE,SAAS,cAAc,OAAO,GAAEH,KAAEK,GAAE;AAAW,MAAAF,GAAE,YAAU;AAAG,eAAQF,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAG;AAAE,QAAAE,GAAE,YAAYH,GAAEC,EAAC,EAAE,UAAU,IAAE,CAAC;AAAE,UAAEE,GAAE;AAAA,IAAS;AAAC,WAAM,+BAA6B,KAAKC,KAAE,IAAE,QAAQ;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,CAAC,MAAK,YAAW,UAAU;AAAE,IAAI,IAAE,gBAAE,EAAE,EAAE,CAAC,GAAE,EAAC,MAAK,gBAAe,CAAC,GAAE,EAAC,OAAM,EAAC,WAAU,EAAC,MAAK,QAAO,SAAQ,OAAK,EAAC,iBAAgB,oBAAmB,UAAS,eAAc,GAAE,GAAE,GAAE,EAAC,MAAK,QAAO,SAAQ,OAAM,GAAE,GAAE,EAAC,MAAK,QAAO,SAAQ,OAAM,GAAE,eAAc,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,WAAU,EAAC,MAAK,QAAO,SAAQ,OAAK,CAAC,GAAE,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,YAAW,EAAC,MAAK,QAAO,SAAQ,GAAE,EAAC,GAAE,OAAM,CAAC,SAAQ,KAAK,GAAE,MAAMD,IAAE,EAAC,QAAOH,IAAE,MAAKC,GAAC,GAAE;AAAC,QAAMC,KAAEC,IAAEE,KAAE,EAAC,OAAM,QAAO,QAAO,OAAM;AAAE,MAAI,IAAE,SAAE,EAAC,KAAI,QAAO,QAAO,EAAE,EAAC,iBAAgB,oBAAmB,UAAS,eAAc,GAAEH,GAAE,SAAS,GAAE,KAAI,WAAS,KAAK,OAAO,EAAC,CAAC;AAAE,QAAG,MAAIA,GAAE,UAAW,CAAAC,OAAG;AAAC,IAAAA,KAAE,EAAE,IAAI,IAAI,IAAE,EAAE,IAAI,GAAG;AAAA,EAAC,CAAE;AAAE,QAAMK,KAAE,MAAI;AAAC,MAAE,IAAI,MAAM;AAAA,EAAC,GAAEF,KAAE,CAAAH,OAAGA,KAAE,EAAE,IAAI,UAAUA,EAAC,IAAE,EAAE,IAAI,UAAU,GAAEM,KAAE,CAAAN,OAAG;AAAC,MAAE,IAAI,YAAYA,EAAC;AAAA,EAAC,GAAEO,KAAE,MAAI,EAAE,IAAI,QAAQ,GAAEC,KAAE,CAAAR,OAAG;AAAC,QAAG,qBAAmB,OAAO,UAAU,SAAS,KAAKA,EAAC;AAAE,YAAM,IAAI,MAAM,0BAAwB,OAAOA,KAAE,GAAG;AAAE;AAAC,UAAIH,KAAE,SAAS,eAAe,EAAE,GAAG,GAAEC,KAAE,EAAC,MAAKE,GAAE,QAAM,IAAG,GAAEA,GAAE,KAAG,IAAG,GAAEA,GAAE,KAAG,IAAG,IAAGA,GAAE,MAAI,IAAG,IAAGA,GAAE,MAAI,GAAE,GAAED,KAAEF,GAAE,WAAW,IAAI;AAAE,MAAAE,GAAE,OAAKC,GAAE,QAAM,mBAAkBD,GAAE,YAAUC,GAAE,aAAW,QAAOD,GAAE,cAAYC,GAAE,eAAa,QAAO,SAAOA,GAAE,SAAOD,GAAE,SAASD,GAAE,MAAKA,GAAE,GAAEA,GAAE,CAAC,GAAEC,GAAE,WAAWD,GAAE,MAAKA,GAAE,IAAGA,GAAE,EAAE,KAAG,YAAUE,GAAE,QAAMD,GAAE,WAAWD,GAAE,MAAKA,GAAE,IAAGA,GAAE,EAAE,IAAEC,GAAE,SAASD,GAAE,MAAKA,GAAE,GAAEA,GAAE,CAAC,GAAE,EAAE,IAAI,WAAS;AAAA,IAAE;AAAA,EAAC;AAAE,SAAO,UAAG,MAAI;AAAC,KAAC,MAAI;AAAC,UAAIE,KAAE,SAAS,eAAe,EAAE,GAAG;AAAE,eAASH,GAAEG,IAAE;AAAC,YAAIH;AAAE,QAAAU,GAAE,MAAIV,KAAEM,GAAE;AAAG,YAAIL,KAAE,KAAK,IAAI,OAAO,oBAAkB,GAAE,CAAC;AAAE,cAAMI,KAAE,OAAO,IAAI;AAAE,QAAAF,GAAE,QAAME,GAAE,KAAKH,GAAE,CAAC,IAAE,OAAOA,GAAE,EAAE,QAAQ,OAAM,EAAE,CAAC,IAAED,KAAEE,GAAE,cAAYF,IAAEE,GAAE,SAAOE,GAAE,KAAKH,GAAE,CAAC,IAAE,OAAOA,GAAE,EAAE,QAAQ,OAAM,EAAE,CAAC,IAAED,KAAEE,GAAE,eAAaF,IAAEE,GAAE,WAAW,IAAI,EAAE,MAAMF,IAAEA,EAAC,GAAEO,GAAE,GAAE,CAACN,GAAE,iBAAe,WAASF,MAAGS,GAAET,EAAC,GAAE,OAAO,KAAKE,GAAE,SAAS,EAAE,UAAQS,GAAET,GAAE,SAAS;AAAA,MAAC;AAAC,QAAE,MAAI,IAAI,EAAEC,IAAE,EAAE,MAAM,GAAE,EAAE,IAAI,UAAQ,CAAAA,OAAGF,GAAE,OAAO,GAAE,EAAE,IAAI,QAAM,CAAAE,OAAGF,GAAE,KAAK,GAAE,OAAO,iBAAiB,UAAU,MAAID,GAAEG,EAAC,CAAE,GAAEH,GAAEG,EAAC,GAAE,OAAKD,GAAE,cAAYO,GAAEP,GAAE,UAAU,GAAEA,GAAE,WAAS,EAAE,IAAI,IAAI,IAAE,EAAE,IAAI,GAAG;AAAA,IAAC,GAAG;AAAA,EAAC,CAAE,GAAEF,GAAE,EAAC,MAAKM,IAAE,OAAME,IAAE,SAAQE,IAAE,MAAK,MAAI;AAAC,QAAIP,KAAE,EAAE,IAAI,OAAO;AAAE,IAAAA,OAAIA,GAAE,IAAI,GAAE,EAAE,IAAI,SAASA,EAAC;AAAA,EAAE,GAAE,cAAaQ,IAAE,aAAYF,GAAC,CAAC,GAAE,CAACT,IAAEC,QAAK,UAAE,GAAE,mBAAE,OAAM,EAAC,OAAM,eAAE,EAAC,OAAME,GAAE,GAAE,QAAOA,GAAE,EAAC,CAAC,GAAE,aAAYF,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAE,cAAG,MAAI;AAAA,EAAC,GAAG,CAAC,SAAS,CAAC,GAAE,GAAE,CAAC,gBAAE,UAAS,EAAC,IAAG,MAAE,CAAC,EAAE,KAAI,YAAW,MAAE,CAAC,EAAE,KAAI,UAAS,MAAE,CAAC,EAAE,UAAS,OAAMI,GAAC,GAAE,MAAK,GAAE,CAAC,CAAC,GAAE,EAAE;AAAE,EAAC,CAAC,CAAC;AAAE,IAAI;AAAJ,IAAM,MAAI,IAAE,GAAG,UAAQ,CAAAF,OAAG;AAAC,EAAAA,GAAE,UAAU,EAAE,MAAK,CAAC;AAAC,GAAE;AAAG,IAAO,4BAAQ;", "names": ["e", "i", "o", "t", "n", "s", "_", "y", "g", "w", "f", "E"]}