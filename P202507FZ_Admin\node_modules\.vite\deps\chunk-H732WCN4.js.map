{"version": 3, "sources": ["../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/view/Component.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/chart/helper/createRenderPlanner.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/view/Chart.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/util/throttle.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/model/globalDefault.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/model/internalComponentCreator.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/model/Global.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/core/ExtensionAPI.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/core/CoordinateSystem.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/model/OptionManager.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/preprocessor/helper/compatStyle.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/preprocessor/backwardCompat.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/processor/dataStack.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/visual/style.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/loading/default.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/core/Scheduler.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/theme/light.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/theme/dark.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/util/ECEventProcessor.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/visual/symbol.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/visual/helper.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/legacy/dataSelectAction.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/util/event.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/core/WeakMap.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/util/symbol.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/util/decal.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/visual/decal.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/core/lifecycle.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/core/impl.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/core/echarts.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/data/helper/dimensionHelper.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/data/SeriesDimensionDefine.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/data/helper/SeriesDataSchema.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/data/SeriesData.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/extension.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/data/helper/dataStackHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport Group from 'zrender/lib/graphic/Group.js';\nimport * as componentUtil from '../util/component.js';\nimport * as clazzUtil from '../util/clazz.js';\nvar ComponentView = /** @class */function () {\n  function ComponentView() {\n    this.group = new Group();\n    this.uid = componentUtil.getUID('viewComponent');\n  }\n  ComponentView.prototype.init = function (ecModel, api) {};\n  ComponentView.prototype.render = function (model, ecModel, api, payload) {};\n  ComponentView.prototype.dispose = function (ecModel, api) {};\n  ComponentView.prototype.updateView = function (model, ecModel, api, payload) {\n    // Do nothing;\n  };\n  ComponentView.prototype.updateLayout = function (model, ecModel, api, payload) {\n    // Do nothing;\n  };\n  ComponentView.prototype.updateVisual = function (model, ecModel, api, payload) {\n    // Do nothing;\n  };\n  /**\n   * Hook for toggle blur target series.\n   * Can be used in marker for blur or leave blur the markers\n   */\n  ComponentView.prototype.toggleBlurSeries = function (seriesModels, isBlur, ecModel) {\n    // Do nothing;\n  };\n  /**\n   * Traverse the new rendered elements.\n   *\n   * It will traverse the new added element in progressive rendering.\n   * And traverse all in normal rendering.\n   */\n  ComponentView.prototype.eachRendered = function (cb) {\n    var group = this.group;\n    if (group) {\n      group.traverse(cb);\n    }\n  };\n  return ComponentView;\n}();\n;\nclazzUtil.enableClassExtend(ComponentView);\nclazzUtil.enableClassManagement(ComponentView);\nexport default ComponentView;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { makeInner } from '../../util/model.js';\n/**\n * @return {string} If large mode changed, return string 'reset';\n */\nexport default function createRenderPlanner() {\n  var inner = makeInner();\n  return function (seriesModel) {\n    var fields = inner(seriesModel);\n    var pipelineContext = seriesModel.pipelineContext;\n    var originalLarge = !!fields.large;\n    var originalProgressive = !!fields.progressiveRender;\n    // FIXME: if the planner works on a filtered series, `pipelineContext` does not\n    // exists. See #11611 . Probably we need to modify this structure, see the comment\n    // on `performRawSeries` in `Schedular.js`.\n    var large = fields.large = !!(pipelineContext && pipelineContext.large);\n    var progressive = fields.progressiveRender = !!(pipelineContext && pipelineContext.progressiveRender);\n    return !!(originalLarge !== large || originalProgressive !== progressive) && 'reset';\n  };\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { each } from 'zrender/lib/core/util.js';\nimport Group from 'zrender/lib/graphic/Group.js';\nimport * as componentUtil from '../util/component.js';\nimport * as clazzUtil from '../util/clazz.js';\nimport * as modelUtil from '../util/model.js';\nimport { enterEmphasis, leaveEmphasis, getHighlightDigit, isHighDownDispatcher } from '../util/states.js';\nimport { createTask } from '../core/task.js';\nimport createRenderPlanner from '../chart/helper/createRenderPlanner.js';\nimport { traverseElements } from '../util/graphic.js';\nimport { error } from '../util/log.js';\nvar inner = modelUtil.makeInner();\nvar renderPlanner = createRenderPlanner();\nvar ChartView = /** @class */function () {\n  function ChartView() {\n    this.group = new Group();\n    this.uid = componentUtil.getUID('viewChart');\n    this.renderTask = createTask({\n      plan: renderTaskPlan,\n      reset: renderTaskReset\n    });\n    this.renderTask.context = {\n      view: this\n    };\n  }\n  ChartView.prototype.init = function (ecModel, api) {};\n  ChartView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error('render method must been implemented');\n    }\n  };\n  /**\n   * Highlight series or specified data item.\n   */\n  ChartView.prototype.highlight = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData(payload && payload.dataType);\n    if (!data) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(\"Unknown dataType \" + payload.dataType);\n      }\n      return;\n    }\n    toggleHighlight(data, payload, 'emphasis');\n  };\n  /**\n   * Downplay series or specified data item.\n   */\n  ChartView.prototype.downplay = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData(payload && payload.dataType);\n    if (!data) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(\"Unknown dataType \" + payload.dataType);\n      }\n      return;\n    }\n    toggleHighlight(data, payload, 'normal');\n  };\n  /**\n   * Remove self.\n   */\n  ChartView.prototype.remove = function (ecModel, api) {\n    this.group.removeAll();\n  };\n  /**\n   * Dispose self.\n   */\n  ChartView.prototype.dispose = function (ecModel, api) {};\n  ChartView.prototype.updateView = function (seriesModel, ecModel, api, payload) {\n    this.render(seriesModel, ecModel, api, payload);\n  };\n  // FIXME never used?\n  ChartView.prototype.updateLayout = function (seriesModel, ecModel, api, payload) {\n    this.render(seriesModel, ecModel, api, payload);\n  };\n  // FIXME never used?\n  ChartView.prototype.updateVisual = function (seriesModel, ecModel, api, payload) {\n    this.render(seriesModel, ecModel, api, payload);\n  };\n  /**\n   * Traverse the new rendered elements.\n   *\n   * It will traverse the new added element in progressive rendering.\n   * And traverse all in normal rendering.\n   */\n  ChartView.prototype.eachRendered = function (cb) {\n    traverseElements(this.group, cb);\n  };\n  ChartView.markUpdateMethod = function (payload, methodName) {\n    inner(payload).updateMethod = methodName;\n  };\n  ChartView.protoInitialize = function () {\n    var proto = ChartView.prototype;\n    proto.type = 'chart';\n  }();\n  return ChartView;\n}();\n;\n/**\n * Set state of single element\n */\nfunction elSetState(el, state, highlightDigit) {\n  if (el && isHighDownDispatcher(el)) {\n    (state === 'emphasis' ? enterEmphasis : leaveEmphasis)(el, highlightDigit);\n  }\n}\nfunction toggleHighlight(data, payload, state) {\n  var dataIndex = modelUtil.queryDataIndex(data, payload);\n  var highlightDigit = payload && payload.highlightKey != null ? getHighlightDigit(payload.highlightKey) : null;\n  if (dataIndex != null) {\n    each(modelUtil.normalizeToArray(dataIndex), function (dataIdx) {\n      elSetState(data.getItemGraphicEl(dataIdx), state, highlightDigit);\n    });\n  } else {\n    data.eachItemGraphicEl(function (el) {\n      elSetState(el, state, highlightDigit);\n    });\n  }\n}\nclazzUtil.enableClassExtend(ChartView, ['dispose']);\nclazzUtil.enableClassManagement(ChartView);\nfunction renderTaskPlan(context) {\n  return renderPlanner(context.model);\n}\nfunction renderTaskReset(context) {\n  var seriesModel = context.model;\n  var ecModel = context.ecModel;\n  var api = context.api;\n  var payload = context.payload;\n  // FIXME: remove updateView updateVisual\n  var progressiveRender = seriesModel.pipelineContext.progressiveRender;\n  var view = context.view;\n  var updateMethod = payload && inner(payload).updateMethod;\n  var methodName = progressiveRender ? 'incrementalPrepareRender' : updateMethod && view[updateMethod] ? updateMethod\n  // `appendData` is also supported when data amount\n  // is less than progressive threshold.\n  : 'render';\n  if (methodName !== 'render') {\n    view[methodName](seriesModel, ecModel, api, payload);\n  }\n  return progressMethodMap[methodName];\n}\nvar progressMethodMap = {\n  incrementalPrepareRender: {\n    progress: function (params, context) {\n      context.view.incrementalRender(params, context.model, context.ecModel, context.api, context.payload);\n    }\n  },\n  render: {\n    // Put view.render in `progress` to support appendData. But in this case\n    // view.render should not be called in reset, otherwise it will be called\n    // twise. Use `forceFirstProgress` to make sure that view.render is called\n    // in any cases.\n    forceFirstProgress: true,\n    progress: function (params, context) {\n      context.view.render(context.model, context.ecModel, context.api, context.payload);\n    }\n  }\n};\nexport default ChartView;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nvar ORIGIN_METHOD = '\\0__throttleOriginMethod';\nvar RATE = '\\0__throttleRate';\nvar THROTTLE_TYPE = '\\0__throttleType';\n;\n/**\n * @public\n * @param {(Function)} fn\n * @param {number} [delay=0] Unit: ms.\n * @param {boolean} [debounce=false]\n *        true: If call interval less than `delay`, only the last call works.\n *        false: If call interval less than `delay, call works on fixed rate.\n * @return {(Function)} throttled fn.\n */\nexport function throttle(fn, delay, debounce) {\n  var currCall;\n  var lastCall = 0;\n  var lastExec = 0;\n  var timer = null;\n  var diff;\n  var scope;\n  var args;\n  var debounceNextCall;\n  delay = delay || 0;\n  function exec() {\n    lastExec = new Date().getTime();\n    timer = null;\n    fn.apply(scope, args || []);\n  }\n  var cb = function () {\n    var cbArgs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      cbArgs[_i] = arguments[_i];\n    }\n    currCall = new Date().getTime();\n    scope = this;\n    args = cbArgs;\n    var thisDelay = debounceNextCall || delay;\n    var thisDebounce = debounceNextCall || debounce;\n    debounceNextCall = null;\n    diff = currCall - (thisDebounce ? lastCall : lastExec) - thisDelay;\n    clearTimeout(timer);\n    // Here we should make sure that: the `exec` SHOULD NOT be called later\n    // than a new call of `cb`, that is, preserving the command order. Consider\n    // calculating \"scale rate\" when roaming as an example. When a call of `cb`\n    // happens, either the `exec` is called dierectly, or the call is delayed.\n    // But the delayed call should never be later than next call of `cb`. Under\n    // this assurance, we can simply update view state each time `dispatchAction`\n    // triggered by user roaming, but not need to add extra code to avoid the\n    // state being \"rolled-back\".\n    if (thisDebounce) {\n      timer = setTimeout(exec, thisDelay);\n    } else {\n      if (diff >= 0) {\n        exec();\n      } else {\n        timer = setTimeout(exec, -diff);\n      }\n    }\n    lastCall = currCall;\n  };\n  /**\n   * Clear throttle.\n   * @public\n   */\n  cb.clear = function () {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  };\n  /**\n   * Enable debounce once.\n   */\n  cb.debounceNextCall = function (debounceDelay) {\n    debounceNextCall = debounceDelay;\n  };\n  return cb;\n}\n/**\n * Create throttle method or update throttle rate.\n *\n * @example\n * ComponentView.prototype.render = function () {\n *     ...\n *     throttle.createOrUpdate(\n *         this,\n *         '_dispatchAction',\n *         this.model.get('throttle'),\n *         'fixRate'\n *     );\n * };\n * ComponentView.prototype.remove = function () {\n *     throttle.clear(this, '_dispatchAction');\n * };\n * ComponentView.prototype.dispose = function () {\n *     throttle.clear(this, '_dispatchAction');\n * };\n *\n */\nexport function createOrUpdate(obj, fnAttr, rate, throttleType) {\n  var fn = obj[fnAttr];\n  if (!fn) {\n    return;\n  }\n  var originFn = fn[ORIGIN_METHOD] || fn;\n  var lastThrottleType = fn[THROTTLE_TYPE];\n  var lastRate = fn[RATE];\n  if (lastRate !== rate || lastThrottleType !== throttleType) {\n    if (rate == null || !throttleType) {\n      return obj[fnAttr] = originFn;\n    }\n    fn = obj[fnAttr] = throttle(originFn, rate, throttleType === 'debounce');\n    fn[ORIGIN_METHOD] = originFn;\n    fn[THROTTLE_TYPE] = throttleType;\n    fn[RATE] = rate;\n  }\n  return fn;\n}\n/**\n * Clear throttle. Example see throttle.createOrUpdate.\n */\nexport function clear(obj, fnAttr) {\n  var fn = obj[fnAttr];\n  if (fn && fn[ORIGIN_METHOD]) {\n    // Clear throttle\n    fn.clear && fn.clear();\n    obj[fnAttr] = fn[ORIGIN_METHOD];\n  }\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nvar platform = '';\n// Navigator not exists in node\nif (typeof navigator !== 'undefined') {\n  /* global navigator */\n  platform = navigator.platform || '';\n}\nvar decalColor = 'rgba(0, 0, 0, 0.2)';\nexport default {\n  darkMode: 'auto',\n  // backgroundColor: 'rgba(0,0,0,0)',\n  colorBy: 'series',\n  color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],\n  gradientColor: ['#f6efa6', '#d88273', '#bf444c'],\n  aria: {\n    decal: {\n      decals: [{\n        color: decalColor,\n        dashArrayX: [1, 0],\n        dashArrayY: [2, 5],\n        symbolSize: 1,\n        rotation: Math.PI / 6\n      }, {\n        color: decalColor,\n        symbol: 'circle',\n        dashArrayX: [[8, 8], [0, 8, 8, 0]],\n        dashArrayY: [6, 0],\n        symbolSize: 0.8\n      }, {\n        color: decalColor,\n        dashArrayX: [1, 0],\n        dashArrayY: [4, 3],\n        rotation: -Math.PI / 4\n      }, {\n        color: decalColor,\n        dashArrayX: [[6, 6], [0, 6, 6, 0]],\n        dashArrayY: [6, 0]\n      }, {\n        color: decalColor,\n        dashArrayX: [[1, 0], [1, 6]],\n        dashArrayY: [1, 0, 6, 0],\n        rotation: Math.PI / 4\n      }, {\n        color: decalColor,\n        symbol: 'triangle',\n        dashArrayX: [[9, 9], [0, 9, 9, 0]],\n        dashArrayY: [7, 2],\n        symbolSize: 0.75\n      }]\n    }\n  },\n  // If xAxis and yAxis declared, grid is created by default.\n  // grid: {},\n  textStyle: {\n    // color: '#000',\n    // decoration: 'none',\n    // PENDING\n    fontFamily: platform.match(/^Win/) ? 'Microsoft YaHei' : 'sans-serif',\n    // fontFamily: 'Arial, Verdana, sans-serif',\n    fontSize: 12,\n    fontStyle: 'normal',\n    fontWeight: 'normal'\n  },\n  // http://blogs.adobe.com/webplatform/2014/02/24/using-blend-modes-in-html-canvas/\n  // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalCompositeOperation\n  // Default is source-over\n  blendMode: null,\n  stateAnimation: {\n    duration: 300,\n    easing: 'cubicOut'\n  },\n  animation: 'auto',\n  animationDuration: 1000,\n  animationDurationUpdate: 500,\n  animationEasing: 'cubicInOut',\n  animationEasingUpdate: 'cubicInOut',\n  animationThreshold: 2000,\n  // Configuration for progressive/incremental rendering\n  progressiveThreshold: 3000,\n  progressive: 400,\n  // Threshold of if use single hover layer to optimize.\n  // It is recommended that `hoverLayerThreshold` is equivalent to or less than\n  // `progressiveThreshold`, otherwise hover will cause restart of progressive,\n  // which is unexpected.\n  // see example <echarts/test/heatmap-large.html>.\n  hoverLayerThreshold: 3000,\n  // See: module:echarts/scale/Time\n  useUTC: false\n};", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, assert } from 'zrender/lib/core/util.js';\nimport { isComponentIdInternal } from '../util/model.js';\nvar internalOptionCreatorMap = createHashMap();\nexport function registerInternalOptionCreator(mainType, creator) {\n  assert(internalOptionCreatorMap.get(mainType) == null && creator);\n  internalOptionCreatorMap.set(mainType, creator);\n}\nexport function concatInternalOptions(ecModel, mainType, newCmptOptionList) {\n  var internalOptionCreator = internalOptionCreatorMap.get(mainType);\n  if (!internalOptionCreator) {\n    return newCmptOptionList;\n  }\n  var internalOptions = internalOptionCreator(ecModel);\n  if (!internalOptions) {\n    return newCmptOptionList;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    for (var i = 0; i < internalOptions.length; i++) {\n      assert(isComponentIdInternal(internalOptions[i]));\n    }\n  }\n  return newCmptOptionList.concat(internalOptions);\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\n/**\n * Caution: If the mechanism should be changed some day, these cases\n * should be considered:\n *\n * (1) In `merge option` mode, if using the same option to call `setOption`\n * many times, the result should be the same (try our best to ensure that).\n * (2) In `merge option` mode, if a component has no id/name specified, it\n * will be merged by index, and the result sequence of the components is\n * consistent to the original sequence.\n * (3) In `replaceMerge` mode, keep the result sequence of the components is\n * consistent to the original sequence, even though there might result in \"hole\".\n * (4) `reset` feature (in toolbox). Find detailed info in comments about\n * `mergeOption` in module:echarts/model/OptionManager.\n */\nimport { each, filter, isArray, isObject, isString, createHashMap, assert, clone, merge, extend, mixin, isFunction } from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../util/model.js';\nimport Model from './Model.js';\nimport ComponentModel from './Component.js';\nimport globalDefault from './globalDefault.js';\nimport { resetSourceDefaulter } from '../data/helper/sourceHelper.js';\nimport { concatInternalOptions } from './internalComponentCreator.js';\nimport { PaletteMixin } from './mixin/palette.js';\nimport { error, warn } from '../util/log.js';\n// -----------------------\n// Internal method names:\n// -----------------------\nvar reCreateSeriesIndices;\nvar assertSeriesInitialized;\nvar initBase;\nvar OPTION_INNER_KEY = '\\0_ec_inner';\nvar OPTION_INNER_VALUE = 1;\nvar BUITIN_COMPONENTS_MAP = {\n  grid: 'GridComponent',\n  polar: 'PolarComponent',\n  geo: 'GeoComponent',\n  singleAxis: 'SingleAxisComponent',\n  parallel: 'ParallelComponent',\n  calendar: 'CalendarComponent',\n  graphic: 'GraphicComponent',\n  toolbox: 'ToolboxComponent',\n  tooltip: 'TooltipComponent',\n  axisPointer: 'AxisPointerComponent',\n  brush: 'BrushComponent',\n  title: 'TitleComponent',\n  timeline: 'TimelineComponent',\n  markPoint: 'MarkPointComponent',\n  markLine: 'MarkLineComponent',\n  markArea: 'MarkAreaComponent',\n  legend: 'LegendComponent',\n  dataZoom: 'DataZoomComponent',\n  visualMap: 'VisualMapComponent',\n  // aria: 'AriaComponent',\n  // dataset: 'DatasetComponent',\n  // Dependencies\n  xAxis: 'GridComponent',\n  yAxis: 'GridComponent',\n  angleAxis: 'PolarComponent',\n  radiusAxis: 'PolarComponent'\n};\nvar BUILTIN_CHARTS_MAP = {\n  line: 'LineChart',\n  bar: 'BarChart',\n  pie: 'PieChart',\n  scatter: 'ScatterChart',\n  radar: 'RadarChart',\n  map: 'MapChart',\n  tree: 'TreeChart',\n  treemap: 'TreemapChart',\n  graph: 'GraphChart',\n  gauge: 'GaugeChart',\n  funnel: 'FunnelChart',\n  parallel: 'ParallelChart',\n  sankey: 'SankeyChart',\n  boxplot: 'BoxplotChart',\n  candlestick: 'CandlestickChart',\n  effectScatter: 'EffectScatterChart',\n  lines: 'LinesChart',\n  heatmap: 'HeatmapChart',\n  pictorialBar: 'PictorialBarChart',\n  themeRiver: 'ThemeRiverChart',\n  sunburst: 'SunburstChart',\n  custom: 'CustomChart'\n};\nvar componetsMissingLogPrinted = {};\nfunction checkMissingComponents(option) {\n  each(option, function (componentOption, mainType) {\n    if (!ComponentModel.hasClass(mainType)) {\n      var componentImportName = BUITIN_COMPONENTS_MAP[mainType];\n      if (componentImportName && !componetsMissingLogPrinted[componentImportName]) {\n        error(\"Component \" + mainType + \" is used but not imported.\\nimport { \" + componentImportName + \" } from 'echarts/components';\\necharts.use([\" + componentImportName + \"]);\");\n        componetsMissingLogPrinted[componentImportName] = true;\n      }\n    }\n  });\n}\nvar GlobalModel = /** @class */function (_super) {\n  __extends(GlobalModel, _super);\n  function GlobalModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  GlobalModel.prototype.init = function (option, parentModel, ecModel, theme, locale, optionManager) {\n    theme = theme || {};\n    this.option = null; // Mark as not initialized.\n    this._theme = new Model(theme);\n    this._locale = new Model(locale);\n    this._optionManager = optionManager;\n  };\n  GlobalModel.prototype.setOption = function (option, opts, optionPreprocessorFuncs) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(option != null, 'option is null/undefined');\n      assert(option[OPTION_INNER_KEY] !== OPTION_INNER_VALUE, 'please use chart.getOption()');\n    }\n    var innerOpt = normalizeSetOptionInput(opts);\n    this._optionManager.setOption(option, optionPreprocessorFuncs, innerOpt);\n    this._resetOption(null, innerOpt);\n  };\n  /**\n   * @param type null/undefined: reset all.\n   *        'recreate': force recreate all.\n   *        'timeline': only reset timeline option\n   *        'media': only reset media query option\n   * @return Whether option changed.\n   */\n  GlobalModel.prototype.resetOption = function (type, opt) {\n    return this._resetOption(type, normalizeSetOptionInput(opt));\n  };\n  GlobalModel.prototype._resetOption = function (type, opt) {\n    var optionChanged = false;\n    var optionManager = this._optionManager;\n    if (!type || type === 'recreate') {\n      var baseOption = optionManager.mountOption(type === 'recreate');\n      if (process.env.NODE_ENV !== 'production') {\n        checkMissingComponents(baseOption);\n      }\n      if (!this.option || type === 'recreate') {\n        initBase(this, baseOption);\n      } else {\n        this.restoreData();\n        this._mergeOption(baseOption, opt);\n      }\n      optionChanged = true;\n    }\n    if (type === 'timeline' || type === 'media') {\n      this.restoreData();\n    }\n    // By design, if `setOption(option2)` at the second time, and `option2` is a `ECUnitOption`,\n    // it should better not have the same props with `MediaUnit['option']`.\n    // Because either `option2` or `MediaUnit['option']` will be always merged to \"current option\"\n    // rather than original \"baseOption\". If they both override a prop, the result might be\n    // unexpected when media state changed after `setOption` called.\n    // If we really need to modify a props in each `MediaUnit['option']`, use the full version\n    // (`{baseOption, media}`) in `setOption`.\n    // For `timeline`, the case is the same.\n    if (!type || type === 'recreate' || type === 'timeline') {\n      var timelineOption = optionManager.getTimelineOption(this);\n      if (timelineOption) {\n        optionChanged = true;\n        this._mergeOption(timelineOption, opt);\n      }\n    }\n    if (!type || type === 'recreate' || type === 'media') {\n      var mediaOptions = optionManager.getMediaOption(this);\n      if (mediaOptions.length) {\n        each(mediaOptions, function (mediaOption) {\n          optionChanged = true;\n          this._mergeOption(mediaOption, opt);\n        }, this);\n      }\n    }\n    return optionChanged;\n  };\n  GlobalModel.prototype.mergeOption = function (option) {\n    this._mergeOption(option, null);\n  };\n  GlobalModel.prototype._mergeOption = function (newOption, opt) {\n    var option = this.option;\n    var componentsMap = this._componentsMap;\n    var componentsCount = this._componentsCount;\n    var newCmptTypes = [];\n    var newCmptTypeMap = createHashMap();\n    var replaceMergeMainTypeMap = opt && opt.replaceMergeMainTypeMap;\n    resetSourceDefaulter(this);\n    // If no component class, merge directly.\n    // For example: color, animaiton options, etc.\n    each(newOption, function (componentOption, mainType) {\n      if (componentOption == null) {\n        return;\n      }\n      if (!ComponentModel.hasClass(mainType)) {\n        // globalSettingTask.dirty();\n        option[mainType] = option[mainType] == null ? clone(componentOption) : merge(option[mainType], componentOption, true);\n      } else if (mainType) {\n        newCmptTypes.push(mainType);\n        newCmptTypeMap.set(mainType, true);\n      }\n    });\n    if (replaceMergeMainTypeMap) {\n      // If there is a mainType `xxx` in `replaceMerge` but not declared in option,\n      // we trade it as it is declared in option as `{xxx: []}`. Because:\n      // (1) for normal merge, `{xxx: null/undefined}` are the same meaning as `{xxx: []}`.\n      // (2) some preprocessor may convert some of `{xxx: null/undefined}` to `{xxx: []}`.\n      replaceMergeMainTypeMap.each(function (val, mainTypeInReplaceMerge) {\n        if (ComponentModel.hasClass(mainTypeInReplaceMerge) && !newCmptTypeMap.get(mainTypeInReplaceMerge)) {\n          newCmptTypes.push(mainTypeInReplaceMerge);\n          newCmptTypeMap.set(mainTypeInReplaceMerge, true);\n        }\n      });\n    }\n    ComponentModel.topologicalTravel(newCmptTypes, ComponentModel.getAllClassMainTypes(), visitComponent, this);\n    function visitComponent(mainType) {\n      var newCmptOptionList = concatInternalOptions(this, mainType, modelUtil.normalizeToArray(newOption[mainType]));\n      var oldCmptList = componentsMap.get(mainType);\n      var mergeMode =\n      // `!oldCmptList` means init. See the comment in `mappingToExists`\n      !oldCmptList ? 'replaceAll' : replaceMergeMainTypeMap && replaceMergeMainTypeMap.get(mainType) ? 'replaceMerge' : 'normalMerge';\n      var mappingResult = modelUtil.mappingToExists(oldCmptList, newCmptOptionList, mergeMode);\n      // Set mainType and complete subType.\n      modelUtil.setComponentTypeToKeyInfo(mappingResult, mainType, ComponentModel);\n      // Empty it before the travel, in order to prevent `this._componentsMap`\n      // from being used in the `init`/`mergeOption`/`optionUpdated` of some\n      // components, which is probably incorrect logic.\n      option[mainType] = null;\n      componentsMap.set(mainType, null);\n      componentsCount.set(mainType, 0);\n      var optionsByMainType = [];\n      var cmptsByMainType = [];\n      var cmptsCountByMainType = 0;\n      var tooltipExists;\n      var tooltipWarningLogged;\n      each(mappingResult, function (resultItem, index) {\n        var componentModel = resultItem.existing;\n        var newCmptOption = resultItem.newOption;\n        if (!newCmptOption) {\n          if (componentModel) {\n            // Consider where is no new option and should be merged using {},\n            // see removeEdgeAndAdd in topologicalTravel and\n            // ComponentModel.getAllClassMainTypes.\n            componentModel.mergeOption({}, this);\n            componentModel.optionUpdated({}, false);\n          }\n          // If no both `resultItem.exist` and `resultItem.option`,\n          // either it is in `replaceMerge` and not matched by any id,\n          // or it has been removed in previous `replaceMerge` and left a \"hole\" in this component index.\n        } else {\n          var isSeriesType = mainType === 'series';\n          var ComponentModelClass = ComponentModel.getClass(mainType, resultItem.keyInfo.subType, !isSeriesType // Give a more detailed warn later if series don't exists\n          );\n\n          if (!ComponentModelClass) {\n            if (process.env.NODE_ENV !== 'production') {\n              var subType = resultItem.keyInfo.subType;\n              var seriesImportName = BUILTIN_CHARTS_MAP[subType];\n              if (!componetsMissingLogPrinted[subType]) {\n                componetsMissingLogPrinted[subType] = true;\n                if (seriesImportName) {\n                  error(\"Series \" + subType + \" is used but not imported.\\nimport { \" + seriesImportName + \" } from 'echarts/charts';\\necharts.use([\" + seriesImportName + \"]);\");\n                } else {\n                  error(\"Unknown series \" + subType);\n                }\n              }\n            }\n            return;\n          }\n          // TODO Before multiple tooltips get supported, we do this check to avoid unexpected exception.\n          if (mainType === 'tooltip') {\n            if (tooltipExists) {\n              if (process.env.NODE_ENV !== 'production') {\n                if (!tooltipWarningLogged) {\n                  warn('Currently only one tooltip component is allowed.');\n                  tooltipWarningLogged = true;\n                }\n              }\n              return;\n            }\n            tooltipExists = true;\n          }\n          if (componentModel && componentModel.constructor === ComponentModelClass) {\n            componentModel.name = resultItem.keyInfo.name;\n            // componentModel.settingTask && componentModel.settingTask.dirty();\n            componentModel.mergeOption(newCmptOption, this);\n            componentModel.optionUpdated(newCmptOption, false);\n          } else {\n            // PENDING Global as parent ?\n            var extraOpt = extend({\n              componentIndex: index\n            }, resultItem.keyInfo);\n            componentModel = new ComponentModelClass(newCmptOption, this, this, extraOpt);\n            // Assign `keyInfo`\n            extend(componentModel, extraOpt);\n            if (resultItem.brandNew) {\n              componentModel.__requireNewView = true;\n            }\n            componentModel.init(newCmptOption, this, this);\n            // Call optionUpdated after init.\n            // newCmptOption has been used as componentModel.option\n            // and may be merged with theme and default, so pass null\n            // to avoid confusion.\n            componentModel.optionUpdated(null, true);\n          }\n        }\n        if (componentModel) {\n          optionsByMainType.push(componentModel.option);\n          cmptsByMainType.push(componentModel);\n          cmptsCountByMainType++;\n        } else {\n          // Always do assign to avoid elided item in array.\n          optionsByMainType.push(void 0);\n          cmptsByMainType.push(void 0);\n        }\n      }, this);\n      option[mainType] = optionsByMainType;\n      componentsMap.set(mainType, cmptsByMainType);\n      componentsCount.set(mainType, cmptsCountByMainType);\n      // Backup series for filtering.\n      if (mainType === 'series') {\n        reCreateSeriesIndices(this);\n      }\n    }\n    // If no series declared, ensure `_seriesIndices` initialized.\n    if (!this._seriesIndices) {\n      reCreateSeriesIndices(this);\n    }\n  };\n  /**\n   * Get option for output (cloned option and inner info removed)\n   */\n  GlobalModel.prototype.getOption = function () {\n    var option = clone(this.option);\n    each(option, function (optInMainType, mainType) {\n      if (ComponentModel.hasClass(mainType)) {\n        var opts = modelUtil.normalizeToArray(optInMainType);\n        // Inner cmpts need to be removed.\n        // Inner cmpts might not be at last since ec5.0, but still\n        // compatible for users: if inner cmpt at last, splice the returned array.\n        var realLen = opts.length;\n        var metNonInner = false;\n        for (var i = realLen - 1; i >= 0; i--) {\n          // Remove options with inner id.\n          if (opts[i] && !modelUtil.isComponentIdInternal(opts[i])) {\n            metNonInner = true;\n          } else {\n            opts[i] = null;\n            !metNonInner && realLen--;\n          }\n        }\n        opts.length = realLen;\n        option[mainType] = opts;\n      }\n    });\n    delete option[OPTION_INNER_KEY];\n    return option;\n  };\n  GlobalModel.prototype.getTheme = function () {\n    return this._theme;\n  };\n  GlobalModel.prototype.getLocaleModel = function () {\n    return this._locale;\n  };\n  GlobalModel.prototype.setUpdatePayload = function (payload) {\n    this._payload = payload;\n  };\n  GlobalModel.prototype.getUpdatePayload = function () {\n    return this._payload;\n  };\n  /**\n   * @param idx If not specified, return the first one.\n   */\n  GlobalModel.prototype.getComponent = function (mainType, idx) {\n    var list = this._componentsMap.get(mainType);\n    if (list) {\n      var cmpt = list[idx || 0];\n      if (cmpt) {\n        return cmpt;\n      } else if (idx == null) {\n        for (var i = 0; i < list.length; i++) {\n          if (list[i]) {\n            return list[i];\n          }\n        }\n      }\n    }\n  };\n  /**\n   * @return Never be null/undefined.\n   */\n  GlobalModel.prototype.queryComponents = function (condition) {\n    var mainType = condition.mainType;\n    if (!mainType) {\n      return [];\n    }\n    var index = condition.index;\n    var id = condition.id;\n    var name = condition.name;\n    var cmpts = this._componentsMap.get(mainType);\n    if (!cmpts || !cmpts.length) {\n      return [];\n    }\n    var result;\n    if (index != null) {\n      result = [];\n      each(modelUtil.normalizeToArray(index), function (idx) {\n        cmpts[idx] && result.push(cmpts[idx]);\n      });\n    } else if (id != null) {\n      result = queryByIdOrName('id', id, cmpts);\n    } else if (name != null) {\n      result = queryByIdOrName('name', name, cmpts);\n    } else {\n      // Return all non-empty components in that mainType\n      result = filter(cmpts, function (cmpt) {\n        return !!cmpt;\n      });\n    }\n    return filterBySubType(result, condition);\n  };\n  /**\n   * The interface is different from queryComponents,\n   * which is convenient for inner usage.\n   *\n   * @usage\n   * let result = findComponents(\n   *     {mainType: 'dataZoom', query: {dataZoomId: 'abc'}}\n   * );\n   * let result = findComponents(\n   *     {mainType: 'series', subType: 'pie', query: {seriesName: 'uio'}}\n   * );\n   * let result = findComponents(\n   *     {mainType: 'series',\n   *     filter: function (model, index) {...}}\n   * );\n   * // result like [component0, componnet1, ...]\n   */\n  GlobalModel.prototype.findComponents = function (condition) {\n    var query = condition.query;\n    var mainType = condition.mainType;\n    var queryCond = getQueryCond(query);\n    var result = queryCond ? this.queryComponents(queryCond)\n    // Retrieve all non-empty components.\n    : filter(this._componentsMap.get(mainType), function (cmpt) {\n      return !!cmpt;\n    });\n    return doFilter(filterBySubType(result, condition));\n    function getQueryCond(q) {\n      var indexAttr = mainType + 'Index';\n      var idAttr = mainType + 'Id';\n      var nameAttr = mainType + 'Name';\n      return q && (q[indexAttr] != null || q[idAttr] != null || q[nameAttr] != null) ? {\n        mainType: mainType,\n        // subType will be filtered finally.\n        index: q[indexAttr],\n        id: q[idAttr],\n        name: q[nameAttr]\n      } : null;\n    }\n    function doFilter(res) {\n      return condition.filter ? filter(res, condition.filter) : res;\n    }\n  };\n  GlobalModel.prototype.eachComponent = function (mainType, cb, context) {\n    var componentsMap = this._componentsMap;\n    if (isFunction(mainType)) {\n      var ctxForAll_1 = cb;\n      var cbForAll_1 = mainType;\n      componentsMap.each(function (cmpts, componentType) {\n        for (var i = 0; cmpts && i < cmpts.length; i++) {\n          var cmpt = cmpts[i];\n          cmpt && cbForAll_1.call(ctxForAll_1, componentType, cmpt, cmpt.componentIndex);\n        }\n      });\n    } else {\n      var cmpts = isString(mainType) ? componentsMap.get(mainType) : isObject(mainType) ? this.findComponents(mainType) : null;\n      for (var i = 0; cmpts && i < cmpts.length; i++) {\n        var cmpt = cmpts[i];\n        cmpt && cb.call(context, cmpt, cmpt.componentIndex);\n      }\n    }\n  };\n  /**\n   * Get series list before filtered by name.\n   */\n  GlobalModel.prototype.getSeriesByName = function (name) {\n    var nameStr = modelUtil.convertOptionIdName(name, null);\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries && nameStr != null && oneSeries.name === nameStr;\n    });\n  };\n  /**\n   * Get series list before filtered by index.\n   */\n  GlobalModel.prototype.getSeriesByIndex = function (seriesIndex) {\n    return this._componentsMap.get('series')[seriesIndex];\n  };\n  /**\n   * Get series list before filtered by type.\n   * FIXME: rename to getRawSeriesByType?\n   */\n  GlobalModel.prototype.getSeriesByType = function (subType) {\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries && oneSeries.subType === subType;\n    });\n  };\n  /**\n   * Get all series before filtered.\n   */\n  GlobalModel.prototype.getSeries = function () {\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries;\n    });\n  };\n  /**\n   * Count series before filtered.\n   */\n  GlobalModel.prototype.getSeriesCount = function () {\n    return this._componentsCount.get('series');\n  };\n  /**\n   * After filtering, series may be different\n   * from raw series.\n   */\n  GlobalModel.prototype.eachSeries = function (cb, context) {\n    assertSeriesInitialized(this);\n    each(this._seriesIndices, function (rawSeriesIndex) {\n      var series = this._componentsMap.get('series')[rawSeriesIndex];\n      cb.call(context, series, rawSeriesIndex);\n    }, this);\n  };\n  /**\n   * Iterate raw series before filtered.\n   *\n   * @param {Function} cb\n   * @param {*} context\n   */\n  GlobalModel.prototype.eachRawSeries = function (cb, context) {\n    each(this._componentsMap.get('series'), function (series) {\n      series && cb.call(context, series, series.componentIndex);\n    });\n  };\n  /**\n   * After filtering, series may be different.\n   * from raw series.\n   */\n  GlobalModel.prototype.eachSeriesByType = function (subType, cb, context) {\n    assertSeriesInitialized(this);\n    each(this._seriesIndices, function (rawSeriesIndex) {\n      var series = this._componentsMap.get('series')[rawSeriesIndex];\n      if (series.subType === subType) {\n        cb.call(context, series, rawSeriesIndex);\n      }\n    }, this);\n  };\n  /**\n   * Iterate raw series before filtered of given type.\n   */\n  GlobalModel.prototype.eachRawSeriesByType = function (subType, cb, context) {\n    return each(this.getSeriesByType(subType), cb, context);\n  };\n  GlobalModel.prototype.isSeriesFiltered = function (seriesModel) {\n    assertSeriesInitialized(this);\n    return this._seriesIndicesMap.get(seriesModel.componentIndex) == null;\n  };\n  GlobalModel.prototype.getCurrentSeriesIndices = function () {\n    return (this._seriesIndices || []).slice();\n  };\n  GlobalModel.prototype.filterSeries = function (cb, context) {\n    assertSeriesInitialized(this);\n    var newSeriesIndices = [];\n    each(this._seriesIndices, function (seriesRawIdx) {\n      var series = this._componentsMap.get('series')[seriesRawIdx];\n      cb.call(context, series, seriesRawIdx) && newSeriesIndices.push(seriesRawIdx);\n    }, this);\n    this._seriesIndices = newSeriesIndices;\n    this._seriesIndicesMap = createHashMap(newSeriesIndices);\n  };\n  GlobalModel.prototype.restoreData = function (payload) {\n    reCreateSeriesIndices(this);\n    var componentsMap = this._componentsMap;\n    var componentTypes = [];\n    componentsMap.each(function (components, componentType) {\n      if (ComponentModel.hasClass(componentType)) {\n        componentTypes.push(componentType);\n      }\n    });\n    ComponentModel.topologicalTravel(componentTypes, ComponentModel.getAllClassMainTypes(), function (componentType) {\n      each(componentsMap.get(componentType), function (component) {\n        if (component && (componentType !== 'series' || !isNotTargetSeries(component, payload))) {\n          component.restoreData();\n        }\n      });\n    });\n  };\n  GlobalModel.internalField = function () {\n    reCreateSeriesIndices = function (ecModel) {\n      var seriesIndices = ecModel._seriesIndices = [];\n      each(ecModel._componentsMap.get('series'), function (series) {\n        // series may have been removed by `replaceMerge`.\n        series && seriesIndices.push(series.componentIndex);\n      });\n      ecModel._seriesIndicesMap = createHashMap(seriesIndices);\n    };\n    assertSeriesInitialized = function (ecModel) {\n      // Components that use _seriesIndices should depends on series component,\n      // which make sure that their initialization is after series.\n      if (process.env.NODE_ENV !== 'production') {\n        if (!ecModel._seriesIndices) {\n          throw new Error('Option should contains series.');\n        }\n      }\n    };\n    initBase = function (ecModel, baseOption) {\n      // Using OPTION_INNER_KEY to mark that this option cannot be used outside,\n      // i.e. `chart.setOption(chart.getModel().option);` is forbidden.\n      ecModel.option = {};\n      ecModel.option[OPTION_INNER_KEY] = OPTION_INNER_VALUE;\n      // Init with series: [], in case of calling findSeries method\n      // before series initialized.\n      ecModel._componentsMap = createHashMap({\n        series: []\n      });\n      ecModel._componentsCount = createHashMap();\n      // If user spefied `option.aria`, aria will be enable. This detection should be\n      // performed before theme and globalDefault merge.\n      var airaOption = baseOption.aria;\n      if (isObject(airaOption) && airaOption.enabled == null) {\n        airaOption.enabled = true;\n      }\n      mergeTheme(baseOption, ecModel._theme.option);\n      // TODO Needs clone when merging to the unexisted property\n      merge(baseOption, globalDefault, false);\n      ecModel._mergeOption(baseOption, null);\n    };\n  }();\n  return GlobalModel;\n}(Model);\nfunction isNotTargetSeries(seriesModel, payload) {\n  if (payload) {\n    var index = payload.seriesIndex;\n    var id = payload.seriesId;\n    var name_1 = payload.seriesName;\n    return index != null && seriesModel.componentIndex !== index || id != null && seriesModel.id !== id || name_1 != null && seriesModel.name !== name_1;\n  }\n}\nfunction mergeTheme(option, theme) {\n  // PENDING\n  // NOT use `colorLayer` in theme if option has `color`\n  var notMergeColorLayer = option.color && !option.colorLayer;\n  each(theme, function (themeItem, name) {\n    if (name === 'colorLayer' && notMergeColorLayer) {\n      return;\n    }\n    // If it is component model mainType, the model handles that merge later.\n    // otherwise, merge them here.\n    if (!ComponentModel.hasClass(name)) {\n      if (typeof themeItem === 'object') {\n        option[name] = !option[name] ? clone(themeItem) : merge(option[name], themeItem, false);\n      } else {\n        if (option[name] == null) {\n          option[name] = themeItem;\n        }\n      }\n    }\n  });\n}\nfunction queryByIdOrName(attr, idOrName, cmpts) {\n  // Here is a break from echarts4: string and number are\n  // treated as equal.\n  if (isArray(idOrName)) {\n    var keyMap_1 = createHashMap();\n    each(idOrName, function (idOrNameItem) {\n      if (idOrNameItem != null) {\n        var idName = modelUtil.convertOptionIdName(idOrNameItem, null);\n        idName != null && keyMap_1.set(idOrNameItem, true);\n      }\n    });\n    return filter(cmpts, function (cmpt) {\n      return cmpt && keyMap_1.get(cmpt[attr]);\n    });\n  } else {\n    var idName_1 = modelUtil.convertOptionIdName(idOrName, null);\n    return filter(cmpts, function (cmpt) {\n      return cmpt && idName_1 != null && cmpt[attr] === idName_1;\n    });\n  }\n}\nfunction filterBySubType(components, condition) {\n  // Using hasOwnProperty for restrict. Consider\n  // subType is undefined in user payload.\n  return condition.hasOwnProperty('subType') ? filter(components, function (cmpt) {\n    return cmpt && cmpt.subType === condition.subType;\n  }) : components;\n}\nfunction normalizeSetOptionInput(opts) {\n  var replaceMergeMainTypeMap = createHashMap();\n  opts && each(modelUtil.normalizeToArray(opts.replaceMerge), function (mainType) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(ComponentModel.hasClass(mainType), '\"' + mainType + '\" is not valid component main type in \"replaceMerge\"');\n    }\n    replaceMergeMainTypeMap.set(mainType, true);\n  });\n  return {\n    replaceMergeMainTypeMap: replaceMergeMainTypeMap\n  };\n}\nmixin(GlobalModel, PaletteMixin);\nexport default GlobalModel;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar availableMethods = ['getDom', 'getZr', 'getWidth', 'getHeight', 'getDevicePixelRatio', 'dispatchAction', 'isSSR', 'isDisposed', 'on', 'off', 'getDataURL', 'getConnectedDataURL',\n// 'getModel',\n'getOption',\n// 'getViewOfComponentModel',\n// 'getViewOfSeriesModel',\n'getId', 'updateLabelLayout'];\nvar ExtensionAPI = /** @class */function () {\n  function ExtensionAPI(ecInstance) {\n    zrUtil.each(availableMethods, function (methodName) {\n      this[methodName] = zrUtil.bind(ecInstance[methodName], ecInstance);\n    }, this);\n  }\n  return ExtensionAPI;\n}();\nexport default ExtensionAPI;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar coordinateSystemCreators = {};\nvar CoordinateSystemManager = /** @class */function () {\n  function CoordinateSystemManager() {\n    this._coordinateSystems = [];\n  }\n  CoordinateSystemManager.prototype.create = function (ecModel, api) {\n    var coordinateSystems = [];\n    zrUtil.each(coordinateSystemCreators, function (creator, type) {\n      var list = creator.create(ecModel, api);\n      coordinateSystems = coordinateSystems.concat(list || []);\n    });\n    this._coordinateSystems = coordinateSystems;\n  };\n  CoordinateSystemManager.prototype.update = function (ecModel, api) {\n    zrUtil.each(this._coordinateSystems, function (coordSys) {\n      coordSys.update && coordSys.update(ecModel, api);\n    });\n  };\n  CoordinateSystemManager.prototype.getCoordinateSystems = function () {\n    return this._coordinateSystems.slice();\n  };\n  CoordinateSystemManager.register = function (type, creator) {\n    coordinateSystemCreators[type] = creator;\n  };\n  CoordinateSystemManager.get = function (type) {\n    return coordinateSystemCreators[type];\n  };\n  return CoordinateSystemManager;\n}();\nexport default CoordinateSystemManager;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { normalizeToArray\n// , MappingExistingItem, setComponentTypeToKeyInfo, mappingToExists\n} from '../util/model.js';\nimport { each, clone, map, isTypedArray, setAsPrimitive, isArray, isObject\n// , HashMap , createHashMap, extend, merge,\n} from 'zrender/lib/core/util.js';\nimport { error } from '../util/log.js';\nvar QUERY_REG = /^(min|max)?(.+)$/;\n// Key: mainType\n// type FakeComponentsMap = HashMap<(MappingExistingItem & { subType: string })[]>;\n/**\n * TERM EXPLANATIONS:\n * See `ECOption` and `ECUnitOption` in `src/util/types.ts`.\n */\nvar OptionManager = /** @class */function () {\n  // timeline.notMerge is not supported in ec3. Firstly there is rearly\n  // case that notMerge is needed. Secondly supporting 'notMerge' requires\n  // rawOption cloned and backuped when timeline changed, which does no\n  // good to performance. What's more, that both timeline and setOption\n  // method supply 'notMerge' brings complex and some problems.\n  // Consider this case:\n  // (step1) chart.setOption({timeline: {notMerge: false}, ...}, false);\n  // (step2) chart.setOption({timeline: {notMerge: true}, ...}, false);\n  function OptionManager(api) {\n    this._timelineOptions = [];\n    this._mediaList = [];\n    /**\n     * -1, means default.\n     * empty means no media.\n     */\n    this._currentMediaIndices = [];\n    this._api = api;\n  }\n  OptionManager.prototype.setOption = function (rawOption, optionPreprocessorFuncs, opt) {\n    if (rawOption) {\n      // That set dat primitive is dangerous if user reuse the data when setOption again.\n      each(normalizeToArray(rawOption.series), function (series) {\n        series && series.data && isTypedArray(series.data) && setAsPrimitive(series.data);\n      });\n      each(normalizeToArray(rawOption.dataset), function (dataset) {\n        dataset && dataset.source && isTypedArray(dataset.source) && setAsPrimitive(dataset.source);\n      });\n    }\n    // Caution: some series modify option data, if do not clone,\n    // it should ensure that the repeat modify correctly\n    // (create a new object when modify itself).\n    rawOption = clone(rawOption);\n    // FIXME\n    // If some property is set in timeline options or media option but\n    // not set in baseOption, a warning should be given.\n    var optionBackup = this._optionBackup;\n    var newParsedOption = parseRawOption(rawOption, optionPreprocessorFuncs, !optionBackup);\n    this._newBaseOption = newParsedOption.baseOption;\n    // For setOption at second time (using merge mode);\n    if (optionBackup) {\n      // FIXME\n      // the restore merge solution is essentially incorrect.\n      // the mapping can not be 100% consistent with ecModel, which probably brings\n      // potential bug!\n      // The first merge is delayed, because in most cases, users do not call `setOption` twice.\n      // let fakeCmptsMap = this._fakeCmptsMap;\n      // if (!fakeCmptsMap) {\n      //     fakeCmptsMap = this._fakeCmptsMap = createHashMap();\n      //     mergeToBackupOption(fakeCmptsMap, null, optionBackup.baseOption, null);\n      // }\n      // mergeToBackupOption(\n      //     fakeCmptsMap, optionBackup.baseOption, newParsedOption.baseOption, opt\n      // );\n      // For simplicity, timeline options and media options do not support merge,\n      // that is, if you `setOption` twice and both has timeline options, the latter\n      // timeline options will not be merged to the former, but just substitute them.\n      if (newParsedOption.timelineOptions.length) {\n        optionBackup.timelineOptions = newParsedOption.timelineOptions;\n      }\n      if (newParsedOption.mediaList.length) {\n        optionBackup.mediaList = newParsedOption.mediaList;\n      }\n      if (newParsedOption.mediaDefault) {\n        optionBackup.mediaDefault = newParsedOption.mediaDefault;\n      }\n    } else {\n      this._optionBackup = newParsedOption;\n    }\n  };\n  OptionManager.prototype.mountOption = function (isRecreate) {\n    var optionBackup = this._optionBackup;\n    this._timelineOptions = optionBackup.timelineOptions;\n    this._mediaList = optionBackup.mediaList;\n    this._mediaDefault = optionBackup.mediaDefault;\n    this._currentMediaIndices = [];\n    return clone(isRecreate\n    // this._optionBackup.baseOption, which is created at the first `setOption`\n    // called, and is merged into every new option by inner method `mergeToBackupOption`\n    // each time `setOption` called, can be only used in `isRecreate`, because\n    // its reliability is under suspicion. In other cases option merge is\n    // performed by `model.mergeOption`.\n    ? optionBackup.baseOption : this._newBaseOption);\n  };\n  OptionManager.prototype.getTimelineOption = function (ecModel) {\n    var option;\n    var timelineOptions = this._timelineOptions;\n    if (timelineOptions.length) {\n      // getTimelineOption can only be called after ecModel inited,\n      // so we can get currentIndex from timelineModel.\n      var timelineModel = ecModel.getComponent('timeline');\n      if (timelineModel) {\n        option = clone(\n        // FIXME:TS as TimelineModel or quivlant interface\n        timelineOptions[timelineModel.getCurrentIndex()]);\n      }\n    }\n    return option;\n  };\n  OptionManager.prototype.getMediaOption = function (ecModel) {\n    var ecWidth = this._api.getWidth();\n    var ecHeight = this._api.getHeight();\n    var mediaList = this._mediaList;\n    var mediaDefault = this._mediaDefault;\n    var indices = [];\n    var result = [];\n    // No media defined.\n    if (!mediaList.length && !mediaDefault) {\n      return result;\n    }\n    // Multi media may be applied, the latter defined media has higher priority.\n    for (var i = 0, len = mediaList.length; i < len; i++) {\n      if (applyMediaQuery(mediaList[i].query, ecWidth, ecHeight)) {\n        indices.push(i);\n      }\n    }\n    // FIXME\n    // Whether mediaDefault should force users to provide? Otherwise\n    // the change by media query can not be recorvered.\n    if (!indices.length && mediaDefault) {\n      indices = [-1];\n    }\n    if (indices.length && !indicesEquals(indices, this._currentMediaIndices)) {\n      result = map(indices, function (index) {\n        return clone(index === -1 ? mediaDefault.option : mediaList[index].option);\n      });\n    }\n    // Otherwise return nothing.\n    this._currentMediaIndices = indices;\n    return result;\n  };\n  return OptionManager;\n}();\n/**\n * [RAW_OPTION_PATTERNS]\n * (Note: \"series: []\" represents all other props in `ECUnitOption`)\n *\n * (1) No prop \"baseOption\" declared:\n * Root option is used as \"baseOption\" (except prop \"options\" and \"media\").\n * ```js\n * option = {\n *     series: [],\n *     timeline: {},\n *     options: [],\n * };\n * option = {\n *     series: [],\n *     media: {},\n * };\n * option = {\n *     series: [],\n *     timeline: {},\n *     options: [],\n *     media: {},\n * }\n * ```\n *\n * (2) Prop \"baseOption\" declared:\n * If \"baseOption\" declared, `ECUnitOption` props can only be declared\n * inside \"baseOption\" except prop \"timeline\" (compat ec2).\n * ```js\n * option = {\n *     baseOption: {\n *         timeline: {},\n *         series: [],\n *     },\n *     options: []\n * };\n * option = {\n *     baseOption: {\n *         series: [],\n *     },\n *     media: []\n * };\n * option = {\n *     baseOption: {\n *         timeline: {},\n *         series: [],\n *     },\n *     options: []\n *     media: []\n * };\n * option = {\n *     // ec3 compat ec2: allow (only) `timeline` declared\n *     // outside baseOption. Keep this setting for compat.\n *     timeline: {},\n *     baseOption: {\n *         series: [],\n *     },\n *     options: [],\n *     media: []\n * };\n * ```\n */\nfunction parseRawOption(\n// `rawOption` May be modified\nrawOption, optionPreprocessorFuncs, isNew) {\n  var mediaList = [];\n  var mediaDefault;\n  var baseOption;\n  var declaredBaseOption = rawOption.baseOption;\n  // Compatible with ec2, [RAW_OPTION_PATTERNS] above.\n  var timelineOnRoot = rawOption.timeline;\n  var timelineOptionsOnRoot = rawOption.options;\n  var mediaOnRoot = rawOption.media;\n  var hasMedia = !!rawOption.media;\n  var hasTimeline = !!(timelineOptionsOnRoot || timelineOnRoot || declaredBaseOption && declaredBaseOption.timeline);\n  if (declaredBaseOption) {\n    baseOption = declaredBaseOption;\n    // For merge option.\n    if (!baseOption.timeline) {\n      baseOption.timeline = timelineOnRoot;\n    }\n  }\n  // For convenience, enable to use the root option as the `baseOption`:\n  // `{ ...normalOptionProps, media: [{ ... }, { ... }] }`\n  else {\n    if (hasTimeline || hasMedia) {\n      rawOption.options = rawOption.media = null;\n    }\n    baseOption = rawOption;\n  }\n  if (hasMedia) {\n    if (isArray(mediaOnRoot)) {\n      each(mediaOnRoot, function (singleMedia) {\n        if (process.env.NODE_ENV !== 'production') {\n          // Real case of wrong config.\n          if (singleMedia && !singleMedia.option && isObject(singleMedia.query) && isObject(singleMedia.query.option)) {\n            error('Illegal media option. Must be like { media: [ { query: {}, option: {} } ] }');\n          }\n        }\n        if (singleMedia && singleMedia.option) {\n          if (singleMedia.query) {\n            mediaList.push(singleMedia);\n          } else if (!mediaDefault) {\n            // Use the first media default.\n            mediaDefault = singleMedia;\n          }\n        }\n      });\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        // Real case of wrong config.\n        error('Illegal media option. Must be an array. Like { media: [ {...}, {...} ] }');\n      }\n    }\n  }\n  doPreprocess(baseOption);\n  each(timelineOptionsOnRoot, function (option) {\n    return doPreprocess(option);\n  });\n  each(mediaList, function (media) {\n    return doPreprocess(media.option);\n  });\n  function doPreprocess(option) {\n    each(optionPreprocessorFuncs, function (preProcess) {\n      preProcess(option, isNew);\n    });\n  }\n  return {\n    baseOption: baseOption,\n    timelineOptions: timelineOptionsOnRoot || [],\n    mediaDefault: mediaDefault,\n    mediaList: mediaList\n  };\n}\n/**\n * @see <http://www.w3.org/TR/css3-mediaqueries/#media1>\n * Support: width, height, aspectRatio\n * Can use max or min as prefix.\n */\nfunction applyMediaQuery(query, ecWidth, ecHeight) {\n  var realMap = {\n    width: ecWidth,\n    height: ecHeight,\n    aspectratio: ecWidth / ecHeight // lower case for convenience.\n  };\n\n  var applicable = true;\n  each(query, function (value, attr) {\n    var matched = attr.match(QUERY_REG);\n    if (!matched || !matched[1] || !matched[2]) {\n      return;\n    }\n    var operator = matched[1];\n    var realAttr = matched[2].toLowerCase();\n    if (!compare(realMap[realAttr], value, operator)) {\n      applicable = false;\n    }\n  });\n  return applicable;\n}\nfunction compare(real, expect, operator) {\n  if (operator === 'min') {\n    return real >= expect;\n  } else if (operator === 'max') {\n    return real <= expect;\n  } else {\n    // Equals\n    return real === expect;\n  }\n}\nfunction indicesEquals(indices1, indices2) {\n  // indices is always order by asc and has only finite number.\n  return indices1.join(',') === indices2.join(',');\n}\n/**\n * Consider case:\n * `chart.setOption(opt1);`\n * Then user do some interaction like dataZoom, dataView changing.\n * `chart.setOption(opt2);`\n * Then user press 'reset button' in toolbox.\n *\n * After doing that all of the interaction effects should be reset, the\n * chart should be the same as the result of invoke\n * `chart.setOption(opt1); chart.setOption(opt2);`.\n *\n * Although it is not able ensure that\n * `chart.setOption(opt1); chart.setOption(opt2);` is equivalents to\n * `chart.setOption(merge(opt1, opt2));` exactly,\n * this might be the only simple way to implement that feature.\n *\n * MEMO: We've considered some other approaches:\n * 1. Each model handles its self restoration but not uniform treatment.\n *     (Too complex in logic and error-prone)\n * 2. Use a shadow ecModel. (Performance expensive)\n *\n * FIXME: A possible solution:\n * Add a extra level of model for each component model. The inheritance chain would be:\n * ecModel <- componentModel <- componentActionModel <- dataItemModel\n * And all of the actions can only modify the `componentActionModel` rather than\n * `componentModel`. `setOption` will only modify the `ecModel` and `componentModel`.\n * When \"resotre\" action triggered, model from `componentActionModel` will be discarded\n * instead of recreating the \"ecModel\" from the \"_optionBackup\".\n */\n// function mergeToBackupOption(\n//     fakeCmptsMap: FakeComponentsMap,\n//     // `tarOption` Can be null/undefined, means init\n//     tarOption: ECUnitOption,\n//     newOption: ECUnitOption,\n//     // Can be null/undefined\n//     opt: InnerSetOptionOpts\n// ): void {\n//     newOption = newOption || {} as ECUnitOption;\n//     const notInit = !!tarOption;\n//     each(newOption, function (newOptsInMainType, mainType) {\n//         if (newOptsInMainType == null) {\n//             return;\n//         }\n//         if (!ComponentModel.hasClass(mainType)) {\n//             if (tarOption) {\n//                 tarOption[mainType] = merge(tarOption[mainType], newOptsInMainType, true);\n//             }\n//         }\n//         else {\n//             const oldTarOptsInMainType = notInit ? normalizeToArray(tarOption[mainType]) : null;\n//             const oldFakeCmptsInMainType = fakeCmptsMap.get(mainType) || [];\n//             const resultTarOptsInMainType = notInit ? (tarOption[mainType] = [] as ComponentOption[]) : null;\n//             const resultFakeCmptsInMainType = fakeCmptsMap.set(mainType, []);\n//             const mappingResult = mappingToExists(\n//                 oldFakeCmptsInMainType,\n//                 normalizeToArray(newOptsInMainType),\n//                 (opt && opt.replaceMergeMainTypeMap.get(mainType)) ? 'replaceMerge' : 'normalMerge'\n//             );\n//             setComponentTypeToKeyInfo(mappingResult, mainType, ComponentModel as ComponentModelConstructor);\n//             each(mappingResult, function (resultItem, index) {\n//                 // The same logic as `Global.ts#_mergeOption`.\n//                 let fakeCmpt = resultItem.existing;\n//                 const newOption = resultItem.newOption;\n//                 const keyInfo = resultItem.keyInfo;\n//                 let fakeCmptOpt;\n//                 if (!newOption) {\n//                     fakeCmptOpt = oldTarOptsInMainType[index];\n//                 }\n//                 else {\n//                     if (fakeCmpt && fakeCmpt.subType === keyInfo.subType) {\n//                         fakeCmpt.name = keyInfo.name;\n//                         if (notInit) {\n//                             fakeCmptOpt = merge(oldTarOptsInMainType[index], newOption, true);\n//                         }\n//                     }\n//                     else {\n//                         fakeCmpt = extend({}, keyInfo);\n//                         if (notInit) {\n//                             fakeCmptOpt = clone(newOption);\n//                         }\n//                     }\n//                 }\n//                 if (fakeCmpt) {\n//                     notInit && resultTarOptsInMainType.push(fakeCmptOpt);\n//                     resultFakeCmptsInMainType.push(fakeCmpt);\n//                 }\n//                 else {\n//                     notInit && resultTarOptsInMainType.push(void 0);\n//                     resultFakeCmptsInMainType.push(void 0);\n//                 }\n//             });\n//         }\n//     });\n// }\nexport default OptionManager;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport { deprecateLog, deprecateReplaceLog } from '../../util/log.js';\nvar each = zrUtil.each;\nvar isObject = zrUtil.isObject;\nvar POSSIBLE_STYLES = ['areaStyle', 'lineStyle', 'nodeStyle', 'linkStyle', 'chordStyle', 'label', 'labelLine'];\nfunction compatEC2ItemStyle(opt) {\n  var itemStyleOpt = opt && opt.itemStyle;\n  if (!itemStyleOpt) {\n    return;\n  }\n  for (var i = 0, len = POSSIBLE_STYLES.length; i < len; i++) {\n    var styleName = POSSIBLE_STYLES[i];\n    var normalItemStyleOpt = itemStyleOpt.normal;\n    var emphasisItemStyleOpt = itemStyleOpt.emphasis;\n    if (normalItemStyleOpt && normalItemStyleOpt[styleName]) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(\"itemStyle.normal.\" + styleName, styleName);\n      }\n      opt[styleName] = opt[styleName] || {};\n      if (!opt[styleName].normal) {\n        opt[styleName].normal = normalItemStyleOpt[styleName];\n      } else {\n        zrUtil.merge(opt[styleName].normal, normalItemStyleOpt[styleName]);\n      }\n      normalItemStyleOpt[styleName] = null;\n    }\n    if (emphasisItemStyleOpt && emphasisItemStyleOpt[styleName]) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(\"itemStyle.emphasis.\" + styleName, \"emphasis.\" + styleName);\n      }\n      opt[styleName] = opt[styleName] || {};\n      if (!opt[styleName].emphasis) {\n        opt[styleName].emphasis = emphasisItemStyleOpt[styleName];\n      } else {\n        zrUtil.merge(opt[styleName].emphasis, emphasisItemStyleOpt[styleName]);\n      }\n      emphasisItemStyleOpt[styleName] = null;\n    }\n  }\n}\nfunction convertNormalEmphasis(opt, optType, useExtend) {\n  if (opt && opt[optType] && (opt[optType].normal || opt[optType].emphasis)) {\n    var normalOpt = opt[optType].normal;\n    var emphasisOpt = opt[optType].emphasis;\n    if (normalOpt) {\n      if (process.env.NODE_ENV !== 'production') {\n        // eslint-disable-next-line max-len\n        deprecateLog(\"'normal' hierarchy in \" + optType + \" has been removed since 4.0. All style properties are configured in \" + optType + \" directly now.\");\n      }\n      // Timeline controlStyle has other properties besides normal and emphasis\n      if (useExtend) {\n        opt[optType].normal = opt[optType].emphasis = null;\n        zrUtil.defaults(opt[optType], normalOpt);\n      } else {\n        opt[optType] = normalOpt;\n      }\n    }\n    if (emphasisOpt) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog(optType + \".emphasis has been changed to emphasis.\" + optType + \" since 4.0\");\n      }\n      opt.emphasis = opt.emphasis || {};\n      opt.emphasis[optType] = emphasisOpt;\n      // Also compat the case user mix the style and focus together in ec3 style\n      // for example: { itemStyle: { normal: {}, emphasis: {focus, shadowBlur} } }\n      if (emphasisOpt.focus) {\n        opt.emphasis.focus = emphasisOpt.focus;\n      }\n      if (emphasisOpt.blurScope) {\n        opt.emphasis.blurScope = emphasisOpt.blurScope;\n      }\n    }\n  }\n}\nfunction removeEC3NormalStatus(opt) {\n  convertNormalEmphasis(opt, 'itemStyle');\n  convertNormalEmphasis(opt, 'lineStyle');\n  convertNormalEmphasis(opt, 'areaStyle');\n  convertNormalEmphasis(opt, 'label');\n  convertNormalEmphasis(opt, 'labelLine');\n  // treemap\n  convertNormalEmphasis(opt, 'upperLabel');\n  // graph\n  convertNormalEmphasis(opt, 'edgeLabel');\n}\nfunction compatTextStyle(opt, propName) {\n  // Check whether is not object (string\\null\\undefined ...)\n  var labelOptSingle = isObject(opt) && opt[propName];\n  var textStyle = isObject(labelOptSingle) && labelOptSingle.textStyle;\n  if (textStyle) {\n    if (process.env.NODE_ENV !== 'production') {\n      // eslint-disable-next-line max-len\n      deprecateLog(\"textStyle hierarchy in \" + propName + \" has been removed since 4.0. All textStyle properties are configured in \" + propName + \" directly now.\");\n    }\n    for (var i = 0, len = modelUtil.TEXT_STYLE_OPTIONS.length; i < len; i++) {\n      var textPropName = modelUtil.TEXT_STYLE_OPTIONS[i];\n      if (textStyle.hasOwnProperty(textPropName)) {\n        labelOptSingle[textPropName] = textStyle[textPropName];\n      }\n    }\n  }\n}\nfunction compatEC3CommonStyles(opt) {\n  if (opt) {\n    removeEC3NormalStatus(opt);\n    compatTextStyle(opt, 'label');\n    opt.emphasis && compatTextStyle(opt.emphasis, 'label');\n  }\n}\nfunction processSeries(seriesOpt) {\n  if (!isObject(seriesOpt)) {\n    return;\n  }\n  compatEC2ItemStyle(seriesOpt);\n  removeEC3NormalStatus(seriesOpt);\n  compatTextStyle(seriesOpt, 'label');\n  // treemap\n  compatTextStyle(seriesOpt, 'upperLabel');\n  // graph\n  compatTextStyle(seriesOpt, 'edgeLabel');\n  if (seriesOpt.emphasis) {\n    compatTextStyle(seriesOpt.emphasis, 'label');\n    // treemap\n    compatTextStyle(seriesOpt.emphasis, 'upperLabel');\n    // graph\n    compatTextStyle(seriesOpt.emphasis, 'edgeLabel');\n  }\n  var markPoint = seriesOpt.markPoint;\n  if (markPoint) {\n    compatEC2ItemStyle(markPoint);\n    compatEC3CommonStyles(markPoint);\n  }\n  var markLine = seriesOpt.markLine;\n  if (markLine) {\n    compatEC2ItemStyle(markLine);\n    compatEC3CommonStyles(markLine);\n  }\n  var markArea = seriesOpt.markArea;\n  if (markArea) {\n    compatEC3CommonStyles(markArea);\n  }\n  var data = seriesOpt.data;\n  // Break with ec3: if `setOption` again, there may be no `type` in option,\n  // then the backward compat based on option type will not be performed.\n  if (seriesOpt.type === 'graph') {\n    data = data || seriesOpt.nodes;\n    var edgeData = seriesOpt.links || seriesOpt.edges;\n    if (edgeData && !zrUtil.isTypedArray(edgeData)) {\n      for (var i = 0; i < edgeData.length; i++) {\n        compatEC3CommonStyles(edgeData[i]);\n      }\n    }\n    zrUtil.each(seriesOpt.categories, function (opt) {\n      removeEC3NormalStatus(opt);\n    });\n  }\n  if (data && !zrUtil.isTypedArray(data)) {\n    for (var i = 0; i < data.length; i++) {\n      compatEC3CommonStyles(data[i]);\n    }\n  }\n  // mark point data\n  markPoint = seriesOpt.markPoint;\n  if (markPoint && markPoint.data) {\n    var mpData = markPoint.data;\n    for (var i = 0; i < mpData.length; i++) {\n      compatEC3CommonStyles(mpData[i]);\n    }\n  }\n  // mark line data\n  markLine = seriesOpt.markLine;\n  if (markLine && markLine.data) {\n    var mlData = markLine.data;\n    for (var i = 0; i < mlData.length; i++) {\n      if (zrUtil.isArray(mlData[i])) {\n        compatEC3CommonStyles(mlData[i][0]);\n        compatEC3CommonStyles(mlData[i][1]);\n      } else {\n        compatEC3CommonStyles(mlData[i]);\n      }\n    }\n  }\n  // Series\n  if (seriesOpt.type === 'gauge') {\n    compatTextStyle(seriesOpt, 'axisLabel');\n    compatTextStyle(seriesOpt, 'title');\n    compatTextStyle(seriesOpt, 'detail');\n  } else if (seriesOpt.type === 'treemap') {\n    convertNormalEmphasis(seriesOpt.breadcrumb, 'itemStyle');\n    zrUtil.each(seriesOpt.levels, function (opt) {\n      removeEC3NormalStatus(opt);\n    });\n  } else if (seriesOpt.type === 'tree') {\n    removeEC3NormalStatus(seriesOpt.leaves);\n  }\n  // sunburst starts from ec4, so it does not need to compat levels.\n}\n\nfunction toArr(o) {\n  return zrUtil.isArray(o) ? o : o ? [o] : [];\n}\nfunction toObj(o) {\n  return (zrUtil.isArray(o) ? o[0] : o) || {};\n}\nexport default function globalCompatStyle(option, isTheme) {\n  each(toArr(option.series), function (seriesOpt) {\n    isObject(seriesOpt) && processSeries(seriesOpt);\n  });\n  var axes = ['xAxis', 'yAxis', 'radiusAxis', 'angleAxis', 'singleAxis', 'parallelAxis', 'radar'];\n  isTheme && axes.push('valueAxis', 'categoryAxis', 'logAxis', 'timeAxis');\n  each(axes, function (axisName) {\n    each(toArr(option[axisName]), function (axisOpt) {\n      if (axisOpt) {\n        compatTextStyle(axisOpt, 'axisLabel');\n        compatTextStyle(axisOpt.axisPointer, 'label');\n      }\n    });\n  });\n  each(toArr(option.parallel), function (parallelOpt) {\n    var parallelAxisDefault = parallelOpt && parallelOpt.parallelAxisDefault;\n    compatTextStyle(parallelAxisDefault, 'axisLabel');\n    compatTextStyle(parallelAxisDefault && parallelAxisDefault.axisPointer, 'label');\n  });\n  each(toArr(option.calendar), function (calendarOpt) {\n    convertNormalEmphasis(calendarOpt, 'itemStyle');\n    compatTextStyle(calendarOpt, 'dayLabel');\n    compatTextStyle(calendarOpt, 'monthLabel');\n    compatTextStyle(calendarOpt, 'yearLabel');\n  });\n  // radar.name.textStyle\n  each(toArr(option.radar), function (radarOpt) {\n    compatTextStyle(radarOpt, 'name');\n    // Use axisName instead of name because component has name property\n    if (radarOpt.name && radarOpt.axisName == null) {\n      radarOpt.axisName = radarOpt.name;\n      delete radarOpt.name;\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog('name property in radar component has been changed to axisName');\n      }\n    }\n    if (radarOpt.nameGap != null && radarOpt.axisNameGap == null) {\n      radarOpt.axisNameGap = radarOpt.nameGap;\n      delete radarOpt.nameGap;\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog('nameGap property in radar component has been changed to axisNameGap');\n      }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      each(radarOpt.indicator, function (indicatorOpt) {\n        if (indicatorOpt.text) {\n          deprecateReplaceLog('text', 'name', 'radar.indicator');\n        }\n      });\n    }\n  });\n  each(toArr(option.geo), function (geoOpt) {\n    if (isObject(geoOpt)) {\n      compatEC3CommonStyles(geoOpt);\n      each(toArr(geoOpt.regions), function (regionObj) {\n        compatEC3CommonStyles(regionObj);\n      });\n    }\n  });\n  each(toArr(option.timeline), function (timelineOpt) {\n    compatEC3CommonStyles(timelineOpt);\n    convertNormalEmphasis(timelineOpt, 'label');\n    convertNormalEmphasis(timelineOpt, 'itemStyle');\n    convertNormalEmphasis(timelineOpt, 'controlStyle', true);\n    var data = timelineOpt.data;\n    zrUtil.isArray(data) && zrUtil.each(data, function (item) {\n      if (zrUtil.isObject(item)) {\n        convertNormalEmphasis(item, 'label');\n        convertNormalEmphasis(item, 'itemStyle');\n      }\n    });\n  });\n  each(toArr(option.toolbox), function (toolboxOpt) {\n    convertNormalEmphasis(toolboxOpt, 'iconStyle');\n    each(toolboxOpt.feature, function (featureOpt) {\n      convertNormalEmphasis(featureOpt, 'iconStyle');\n    });\n  });\n  compatTextStyle(toObj(option.axisPointer), 'label');\n  compatTextStyle(toObj(option.tooltip).axisPointer, 'label');\n  // Clean logs\n  // storedLogs = {};\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { each, isArray, isObject, isTypedArray, defaults } from 'zrender/lib/core/util.js';\nimport compatStyle from './helper/compatStyle.js';\nimport { normalizeToArray } from '../util/model.js';\nimport { deprecateLog, deprecateReplaceLog } from '../util/log.js';\nfunction get(opt, path) {\n  var pathArr = path.split(',');\n  var obj = opt;\n  for (var i = 0; i < pathArr.length; i++) {\n    obj = obj && obj[pathArr[i]];\n    if (obj == null) {\n      break;\n    }\n  }\n  return obj;\n}\nfunction set(opt, path, val, overwrite) {\n  var pathArr = path.split(',');\n  var obj = opt;\n  var key;\n  var i = 0;\n  for (; i < pathArr.length - 1; i++) {\n    key = pathArr[i];\n    if (obj[key] == null) {\n      obj[key] = {};\n    }\n    obj = obj[key];\n  }\n  if (overwrite || obj[pathArr[i]] == null) {\n    obj[pathArr[i]] = val;\n  }\n}\nfunction compatLayoutProperties(option) {\n  option && each(LAYOUT_PROPERTIES, function (prop) {\n    if (prop[0] in option && !(prop[1] in option)) {\n      option[prop[1]] = option[prop[0]];\n    }\n  });\n}\nvar LAYOUT_PROPERTIES = [['x', 'left'], ['y', 'top'], ['x2', 'right'], ['y2', 'bottom']];\nvar COMPATITABLE_COMPONENTS = ['grid', 'geo', 'parallel', 'legend', 'toolbox', 'title', 'visualMap', 'dataZoom', 'timeline'];\nvar BAR_ITEM_STYLE_MAP = [['borderRadius', 'barBorderRadius'], ['borderColor', 'barBorderColor'], ['borderWidth', 'barBorderWidth']];\nfunction compatBarItemStyle(option) {\n  var itemStyle = option && option.itemStyle;\n  if (itemStyle) {\n    for (var i = 0; i < BAR_ITEM_STYLE_MAP.length; i++) {\n      var oldName = BAR_ITEM_STYLE_MAP[i][1];\n      var newName = BAR_ITEM_STYLE_MAP[i][0];\n      if (itemStyle[oldName] != null) {\n        itemStyle[newName] = itemStyle[oldName];\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog(oldName, newName);\n        }\n      }\n    }\n  }\n}\nfunction compatPieLabel(option) {\n  if (!option) {\n    return;\n  }\n  if (option.alignTo === 'edge' && option.margin != null && option.edgeDistance == null) {\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('label.margin', 'label.edgeDistance', 'pie');\n    }\n    option.edgeDistance = option.margin;\n  }\n}\nfunction compatSunburstState(option) {\n  if (!option) {\n    return;\n  }\n  if (option.downplay && !option.blur) {\n    option.blur = option.downplay;\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('downplay', 'blur', 'sunburst');\n    }\n  }\n}\nfunction compatGraphFocus(option) {\n  if (!option) {\n    return;\n  }\n  if (option.focusNodeAdjacency != null) {\n    option.emphasis = option.emphasis || {};\n    if (option.emphasis.focus == null) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog('focusNodeAdjacency', 'emphasis: { focus: \\'adjacency\\'}', 'graph/sankey');\n      }\n      option.emphasis.focus = 'adjacency';\n    }\n  }\n}\nfunction traverseTree(data, cb) {\n  if (data) {\n    for (var i = 0; i < data.length; i++) {\n      cb(data[i]);\n      data[i] && traverseTree(data[i].children, cb);\n    }\n  }\n}\nexport default function globalBackwardCompat(option, isTheme) {\n  compatStyle(option, isTheme);\n  // Make sure series array for model initialization.\n  option.series = normalizeToArray(option.series);\n  each(option.series, function (seriesOpt) {\n    if (!isObject(seriesOpt)) {\n      return;\n    }\n    var seriesType = seriesOpt.type;\n    if (seriesType === 'line') {\n      if (seriesOpt.clipOverflow != null) {\n        seriesOpt.clip = seriesOpt.clipOverflow;\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('clipOverflow', 'clip', 'line');\n        }\n      }\n    } else if (seriesType === 'pie' || seriesType === 'gauge') {\n      if (seriesOpt.clockWise != null) {\n        seriesOpt.clockwise = seriesOpt.clockWise;\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('clockWise', 'clockwise');\n        }\n      }\n      compatPieLabel(seriesOpt.label);\n      var data = seriesOpt.data;\n      if (data && !isTypedArray(data)) {\n        for (var i = 0; i < data.length; i++) {\n          compatPieLabel(data[i]);\n        }\n      }\n      if (seriesOpt.hoverOffset != null) {\n        seriesOpt.emphasis = seriesOpt.emphasis || {};\n        if (seriesOpt.emphasis.scaleSize = null) {\n          if (process.env.NODE_ENV !== 'production') {\n            deprecateReplaceLog('hoverOffset', 'emphasis.scaleSize');\n          }\n          seriesOpt.emphasis.scaleSize = seriesOpt.hoverOffset;\n        }\n      }\n    } else if (seriesType === 'gauge') {\n      var pointerColor = get(seriesOpt, 'pointer.color');\n      pointerColor != null && set(seriesOpt, 'itemStyle.color', pointerColor);\n    } else if (seriesType === 'bar') {\n      compatBarItemStyle(seriesOpt);\n      compatBarItemStyle(seriesOpt.backgroundStyle);\n      compatBarItemStyle(seriesOpt.emphasis);\n      var data = seriesOpt.data;\n      if (data && !isTypedArray(data)) {\n        for (var i = 0; i < data.length; i++) {\n          if (typeof data[i] === 'object') {\n            compatBarItemStyle(data[i]);\n            compatBarItemStyle(data[i] && data[i].emphasis);\n          }\n        }\n      }\n    } else if (seriesType === 'sunburst') {\n      var highlightPolicy = seriesOpt.highlightPolicy;\n      if (highlightPolicy) {\n        seriesOpt.emphasis = seriesOpt.emphasis || {};\n        if (!seriesOpt.emphasis.focus) {\n          seriesOpt.emphasis.focus = highlightPolicy;\n          if (process.env.NODE_ENV !== 'production') {\n            deprecateReplaceLog('highlightPolicy', 'emphasis.focus', 'sunburst');\n          }\n        }\n      }\n      compatSunburstState(seriesOpt);\n      traverseTree(seriesOpt.data, compatSunburstState);\n    } else if (seriesType === 'graph' || seriesType === 'sankey') {\n      compatGraphFocus(seriesOpt);\n      // TODO nodes, edges?\n    } else if (seriesType === 'map') {\n      if (seriesOpt.mapType && !seriesOpt.map) {\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('mapType', 'map', 'map');\n        }\n        seriesOpt.map = seriesOpt.mapType;\n      }\n      if (seriesOpt.mapLocation) {\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateLog('`mapLocation` is not used anymore.');\n        }\n        defaults(seriesOpt, seriesOpt.mapLocation);\n      }\n    }\n    if (seriesOpt.hoverAnimation != null) {\n      seriesOpt.emphasis = seriesOpt.emphasis || {};\n      if (seriesOpt.emphasis && seriesOpt.emphasis.scale == null) {\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('hoverAnimation', 'emphasis.scale');\n        }\n        seriesOpt.emphasis.scale = seriesOpt.hoverAnimation;\n      }\n    }\n    compatLayoutProperties(seriesOpt);\n  });\n  // dataRange has changed to visualMap\n  if (option.dataRange) {\n    option.visualMap = option.dataRange;\n  }\n  each(COMPATITABLE_COMPONENTS, function (componentName) {\n    var options = option[componentName];\n    if (options) {\n      if (!isArray(options)) {\n        options = [options];\n      }\n      each(options, function (option) {\n        compatLayoutProperties(option);\n      });\n    }\n  });\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, each } from 'zrender/lib/core/util.js';\nimport { addSafe } from '../util/number.js';\n// (1) [Caution]: the logic is correct based on the premises:\n//     data processing stage is blocked in stream.\n//     See <module:echarts/stream/Scheduler#performDataProcessorTasks>\n// (2) Only register once when import repeatedly.\n//     Should be executed after series is filtered and before stack calculation.\nexport default function dataStack(ecModel) {\n  var stackInfoMap = createHashMap();\n  ecModel.eachSeries(function (seriesModel) {\n    var stack = seriesModel.get('stack');\n    // Compatible: when `stack` is set as '', do not stack.\n    if (stack) {\n      var stackInfoList = stackInfoMap.get(stack) || stackInfoMap.set(stack, []);\n      var data = seriesModel.getData();\n      var stackInfo = {\n        // Used for calculate axis extent automatically.\n        // TODO: Type getCalculationInfo return more specific type?\n        stackResultDimension: data.getCalculationInfo('stackResultDimension'),\n        stackedOverDimension: data.getCalculationInfo('stackedOverDimension'),\n        stackedDimension: data.getCalculationInfo('stackedDimension'),\n        stackedByDimension: data.getCalculationInfo('stackedByDimension'),\n        isStackedByIndex: data.getCalculationInfo('isStackedByIndex'),\n        data: data,\n        seriesModel: seriesModel\n      };\n      // If stacked on axis that do not support data stack.\n      if (!stackInfo.stackedDimension || !(stackInfo.isStackedByIndex || stackInfo.stackedByDimension)) {\n        return;\n      }\n      stackInfoList.length && data.setCalculationInfo('stackedOnSeries', stackInfoList[stackInfoList.length - 1].seriesModel);\n      stackInfoList.push(stackInfo);\n    }\n  });\n  stackInfoMap.each(calculateStack);\n}\nfunction calculateStack(stackInfoList) {\n  each(stackInfoList, function (targetStackInfo, idxInStack) {\n    var resultVal = [];\n    var resultNaN = [NaN, NaN];\n    var dims = [targetStackInfo.stackResultDimension, targetStackInfo.stackedOverDimension];\n    var targetData = targetStackInfo.data;\n    var isStackedByIndex = targetStackInfo.isStackedByIndex;\n    var stackStrategy = targetStackInfo.seriesModel.get('stackStrategy') || 'samesign';\n    // Should not write on raw data, because stack series model list changes\n    // depending on legend selection.\n    targetData.modify(dims, function (v0, v1, dataIndex) {\n      var sum = targetData.get(targetStackInfo.stackedDimension, dataIndex);\n      // Consider `connectNulls` of line area, if value is NaN, stackedOver\n      // should also be NaN, to draw a appropriate belt area.\n      if (isNaN(sum)) {\n        return resultNaN;\n      }\n      var byValue;\n      var stackedDataRawIndex;\n      if (isStackedByIndex) {\n        stackedDataRawIndex = targetData.getRawIndex(dataIndex);\n      } else {\n        byValue = targetData.get(targetStackInfo.stackedByDimension, dataIndex);\n      }\n      // If stackOver is NaN, chart view will render point on value start.\n      var stackedOver = NaN;\n      for (var j = idxInStack - 1; j >= 0; j--) {\n        var stackInfo = stackInfoList[j];\n        // Has been optimized by inverted indices on `stackedByDimension`.\n        if (!isStackedByIndex) {\n          stackedDataRawIndex = stackInfo.data.rawIndexOf(stackInfo.stackedByDimension, byValue);\n        }\n        if (stackedDataRawIndex >= 0) {\n          var val = stackInfo.data.getByRawIndex(stackInfo.stackResultDimension, stackedDataRawIndex);\n          // Considering positive stack, negative stack and empty data\n          if (stackStrategy === 'all' // single stack group\n          || stackStrategy === 'positive' && val > 0 || stackStrategy === 'negative' && val < 0 || stackStrategy === 'samesign' && sum >= 0 && val > 0 // All positive stack\n          || stackStrategy === 'samesign' && sum <= 0 && val < 0 // All negative stack\n          ) {\n            // The sum has to be very small to be affected by the\n            // floating arithmetic problem. An incorrect result will probably\n            // cause axis min/max to be filtered incorrectly.\n            sum = addSafe(sum, val);\n            stackedOver = val;\n            break;\n          }\n        }\n      }\n      resultVal[0] = sum;\n      resultVal[1] = stackedOver;\n      return resultVal;\n    });\n  });\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { isFunction, extend, createHashMap } from 'zrender/lib/core/util.js';\nimport makeStyleMapper from '../model/mixin/makeStyleMapper.js';\nimport { ITEM_STYLE_KEY_MAP } from '../model/mixin/itemStyle.js';\nimport { LINE_STYLE_KEY_MAP } from '../model/mixin/lineStyle.js';\nimport Model from '../model/Model.js';\nimport { makeInner } from '../util/model.js';\nvar inner = makeInner();\nvar defaultStyleMappers = {\n  itemStyle: makeStyleMapper(ITEM_STYLE_KEY_MAP, true),\n  lineStyle: makeStyleMapper(LINE_STYLE_KEY_MAP, true)\n};\nvar defaultColorKey = {\n  lineStyle: 'stroke',\n  itemStyle: 'fill'\n};\nfunction getStyleMapper(seriesModel, stylePath) {\n  var styleMapper = seriesModel.visualStyleMapper || defaultStyleMappers[stylePath];\n  if (!styleMapper) {\n    console.warn(\"Unknown style type '\" + stylePath + \"'.\");\n    return defaultStyleMappers.itemStyle;\n  }\n  return styleMapper;\n}\nfunction getDefaultColorKey(seriesModel, stylePath) {\n  // return defaultColorKey[stylePath] ||\n  var colorKey = seriesModel.visualDrawType || defaultColorKey[stylePath];\n  if (!colorKey) {\n    console.warn(\"Unknown style type '\" + stylePath + \"'.\");\n    return 'fill';\n  }\n  return colorKey;\n}\nvar seriesStyleTask = {\n  createOnAllSeries: true,\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    var stylePath = seriesModel.visualStyleAccessPath || 'itemStyle';\n    // Set in itemStyle\n    var styleModel = seriesModel.getModel(stylePath);\n    var getStyle = getStyleMapper(seriesModel, stylePath);\n    var globalStyle = getStyle(styleModel);\n    var decalOption = styleModel.getShallow('decal');\n    if (decalOption) {\n      data.setVisual('decal', decalOption);\n      decalOption.dirty = true;\n    }\n    // TODO\n    var colorKey = getDefaultColorKey(seriesModel, stylePath);\n    var color = globalStyle[colorKey];\n    // TODO style callback\n    var colorCallback = isFunction(color) ? color : null;\n    var hasAutoColor = globalStyle.fill === 'auto' || globalStyle.stroke === 'auto';\n    // Get from color palette by default.\n    if (!globalStyle[colorKey] || colorCallback || hasAutoColor) {\n      // Note: If some series has color specified (e.g., by itemStyle.color), we DO NOT\n      // make it effect palette. Because some scenarios users need to make some series\n      // transparent or as background, which should better not effect the palette.\n      var colorPalette = seriesModel.getColorFromPalette(\n      // TODO series count changed.\n      seriesModel.name, null, ecModel.getSeriesCount());\n      if (!globalStyle[colorKey]) {\n        globalStyle[colorKey] = colorPalette;\n        data.setVisual('colorFromPalette', true);\n      }\n      globalStyle.fill = globalStyle.fill === 'auto' || isFunction(globalStyle.fill) ? colorPalette : globalStyle.fill;\n      globalStyle.stroke = globalStyle.stroke === 'auto' || isFunction(globalStyle.stroke) ? colorPalette : globalStyle.stroke;\n    }\n    data.setVisual('style', globalStyle);\n    data.setVisual('drawType', colorKey);\n    // Only visible series has each data be visual encoded\n    if (!ecModel.isSeriesFiltered(seriesModel) && colorCallback) {\n      data.setVisual('colorFromPalette', false);\n      return {\n        dataEach: function (data, idx) {\n          var dataParams = seriesModel.getDataParams(idx);\n          var itemStyle = extend({}, globalStyle);\n          itemStyle[colorKey] = colorCallback(dataParams);\n          data.setItemVisual(idx, 'style', itemStyle);\n        }\n      };\n    }\n  }\n};\nvar sharedModel = new Model();\nvar dataStyleTask = {\n  createOnAllSeries: true,\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    if (seriesModel.ignoreStyleOnData || ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var data = seriesModel.getData();\n    var stylePath = seriesModel.visualStyleAccessPath || 'itemStyle';\n    // Set in itemStyle\n    var getStyle = getStyleMapper(seriesModel, stylePath);\n    var colorKey = data.getVisual('drawType');\n    return {\n      dataEach: data.hasItemOption ? function (data, idx) {\n        // Not use getItemModel for performance considuration\n        var rawItem = data.getRawDataItem(idx);\n        if (rawItem && rawItem[stylePath]) {\n          sharedModel.option = rawItem[stylePath];\n          var style = getStyle(sharedModel);\n          var existsStyle = data.ensureUniqueItemVisual(idx, 'style');\n          extend(existsStyle, style);\n          if (sharedModel.option.decal) {\n            data.setItemVisual(idx, 'decal', sharedModel.option.decal);\n            sharedModel.option.decal.dirty = true;\n          }\n          if (colorKey in style) {\n            data.setItemVisual(idx, 'colorFromPalette', false);\n          }\n        }\n      } : null\n    };\n  }\n};\n// Pick color from palette for the data which has not been set with color yet.\n// Note: do not support stream rendering. No such cases yet.\nvar dataColorPaletteTask = {\n  performRawSeries: true,\n  overallReset: function (ecModel) {\n    // Each type of series uses one scope.\n    // Pie and funnel are using different scopes.\n    var paletteScopeGroupByType = createHashMap();\n    ecModel.eachSeries(function (seriesModel) {\n      var colorBy = seriesModel.getColorBy();\n      if (seriesModel.isColorBySeries()) {\n        return;\n      }\n      var key = seriesModel.type + '-' + colorBy;\n      var colorScope = paletteScopeGroupByType.get(key);\n      if (!colorScope) {\n        colorScope = {};\n        paletteScopeGroupByType.set(key, colorScope);\n      }\n      inner(seriesModel).scope = colorScope;\n    });\n    ecModel.eachSeries(function (seriesModel) {\n      if (seriesModel.isColorBySeries() || ecModel.isSeriesFiltered(seriesModel)) {\n        return;\n      }\n      var dataAll = seriesModel.getRawData();\n      var idxMap = {};\n      var data = seriesModel.getData();\n      var colorScope = inner(seriesModel).scope;\n      var stylePath = seriesModel.visualStyleAccessPath || 'itemStyle';\n      var colorKey = getDefaultColorKey(seriesModel, stylePath);\n      data.each(function (idx) {\n        var rawIdx = data.getRawIndex(idx);\n        idxMap[rawIdx] = idx;\n      });\n      // Iterate on data before filtered. To make sure color from palette can be\n      // Consistent when toggling legend.\n      dataAll.each(function (rawIdx) {\n        var idx = idxMap[rawIdx];\n        var fromPalette = data.getItemVisual(idx, 'colorFromPalette');\n        // Get color from palette for each data only when the color is inherited from series color, which is\n        // also picked from color palette. So following situation is not in the case:\n        // 1. series.itemStyle.color is set\n        // 2. color is encoded by visualMap\n        if (fromPalette) {\n          var itemStyle = data.ensureUniqueItemVisual(idx, 'style');\n          var name_1 = dataAll.getName(rawIdx) || rawIdx + '';\n          var dataCount = dataAll.count();\n          itemStyle[colorKey] = seriesModel.getColorFromPalette(name_1, colorScope, dataCount);\n        }\n      });\n    });\n  }\n};\nexport { seriesStyleTask, dataStyleTask, dataColorPaletteTask };", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../util/graphic.js';\nvar PI = Math.PI;\n/**\n * @param {module:echarts/ExtensionAPI} api\n * @param {Object} [opts]\n * @param {string} [opts.text]\n * @param {string} [opts.color]\n * @param {string} [opts.textColor]\n * @return {module:zrender/Element}\n */\nexport default function defaultLoading(api, opts) {\n  opts = opts || {};\n  zrUtil.defaults(opts, {\n    text: 'loading',\n    textColor: '#000',\n    fontSize: 12,\n    fontWeight: 'normal',\n    fontStyle: 'normal',\n    fontFamily: 'sans-serif',\n    maskColor: 'rgba(255, 255, 255, 0.8)',\n    showSpinner: true,\n    color: '#5470c6',\n    spinnerRadius: 10,\n    lineWidth: 5,\n    zlevel: 0\n  });\n  var group = new graphic.Group();\n  var mask = new graphic.Rect({\n    style: {\n      fill: opts.maskColor\n    },\n    zlevel: opts.zlevel,\n    z: 10000\n  });\n  group.add(mask);\n  var textContent = new graphic.Text({\n    style: {\n      text: opts.text,\n      fill: opts.textColor,\n      fontSize: opts.fontSize,\n      fontWeight: opts.fontWeight,\n      fontStyle: opts.fontStyle,\n      fontFamily: opts.fontFamily\n    },\n    zlevel: opts.zlevel,\n    z: 10001\n  });\n  var labelRect = new graphic.Rect({\n    style: {\n      fill: 'none'\n    },\n    textContent: textContent,\n    textConfig: {\n      position: 'right',\n      distance: 10\n    },\n    zlevel: opts.zlevel,\n    z: 10001\n  });\n  group.add(labelRect);\n  var arc;\n  if (opts.showSpinner) {\n    arc = new graphic.Arc({\n      shape: {\n        startAngle: -PI / 2,\n        endAngle: -PI / 2 + 0.1,\n        r: opts.spinnerRadius\n      },\n      style: {\n        stroke: opts.color,\n        lineCap: 'round',\n        lineWidth: opts.lineWidth\n      },\n      zlevel: opts.zlevel,\n      z: 10001\n    });\n    arc.animateShape(true).when(1000, {\n      endAngle: PI * 3 / 2\n    }).start('circularInOut');\n    arc.animateShape(true).when(1000, {\n      startAngle: PI * 3 / 2\n    }).delay(300).start('circularInOut');\n    group.add(arc);\n  }\n  // Inject resize\n  group.resize = function () {\n    var textWidth = textContent.getBoundingRect().width;\n    var r = opts.showSpinner ? opts.spinnerRadius : 0;\n    // cx = (containerWidth - arcDiameter - textDistance - textWidth) / 2\n    // textDistance needs to be calculated when both animation and text exist\n    var cx = (api.getWidth() - r * 2 - (opts.showSpinner && textWidth ? 10 : 0) - textWidth) / 2 - (opts.showSpinner && textWidth ? 0 : 5 + textWidth / 2)\n    // only show the text\n    + (opts.showSpinner ? 0 : textWidth / 2)\n    // only show the spinner\n    + (textWidth ? 0 : r);\n    var cy = api.getHeight() / 2;\n    opts.showSpinner && arc.setShape({\n      cx: cx,\n      cy: cy\n    });\n    labelRect.setShape({\n      x: cx - r,\n      y: cy - r,\n      width: r * 2,\n      height: r * 2\n    });\n    mask.setShape({\n      x: 0,\n      y: 0,\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n  };\n  group.resize();\n  return group;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { each, map, isFunction, createHashMap, noop, assert } from 'zrender/lib/core/util.js';\nimport { createTask } from './task.js';\nimport { getUID } from '../util/component.js';\nimport GlobalModel from '../model/Global.js';\nimport ExtensionAPI from './ExtensionAPI.js';\nimport { normalizeToArray } from '../util/model.js';\n;\nvar Scheduler = /** @class */function () {\n  function Scheduler(ecInstance, api, dataProcessorHandlers, visualHandlers) {\n    // key: handlerUID\n    this._stageTaskMap = createHashMap();\n    this.ecInstance = ecInstance;\n    this.api = api;\n    // Fix current processors in case that in some rear cases that\n    // processors might be registered after echarts instance created.\n    // Register processors incrementally for a echarts instance is\n    // not supported by this stream architecture.\n    dataProcessorHandlers = this._dataProcessorHandlers = dataProcessorHandlers.slice();\n    visualHandlers = this._visualHandlers = visualHandlers.slice();\n    this._allHandlers = dataProcessorHandlers.concat(visualHandlers);\n  }\n  Scheduler.prototype.restoreData = function (ecModel, payload) {\n    // TODO: Only restore needed series and components, but not all components.\n    // Currently `restoreData` of all of the series and component will be called.\n    // But some independent components like `title`, `legend`, `graphic`, `toolbox`,\n    // `tooltip`, `axisPointer`, etc, do not need series refresh when `setOption`,\n    // and some components like coordinate system, axes, dataZoom, visualMap only\n    // need their target series refresh.\n    // (1) If we are implementing this feature some day, we should consider these cases:\n    // if a data processor depends on a component (e.g., dataZoomProcessor depends\n    // on the settings of `dataZoom`), it should be re-performed if the component\n    // is modified by `setOption`.\n    // (2) If a processor depends on sevral series, speicified by its `getTargetSeries`,\n    // it should be re-performed when the result array of `getTargetSeries` changed.\n    // We use `dependencies` to cover these issues.\n    // (3) How to update target series when coordinate system related components modified.\n    // TODO: simply the dirty mechanism? Check whether only the case here can set tasks dirty,\n    // and this case all of the tasks will be set as dirty.\n    ecModel.restoreData(payload);\n    // Theoretically an overall task not only depends on each of its target series, but also\n    // depends on all of the series.\n    // The overall task is not in pipeline, and `ecModel.restoreData` only set pipeline tasks\n    // dirty. If `getTargetSeries` of an overall task returns nothing, we should also ensure\n    // that the overall task is set as dirty and to be performed, otherwise it probably cause\n    // state chaos. So we have to set dirty of all of the overall tasks manually, otherwise it\n    // probably cause state chaos (consider `dataZoomProcessor`).\n    this._stageTaskMap.each(function (taskRecord) {\n      var overallTask = taskRecord.overallTask;\n      overallTask && overallTask.dirty();\n    });\n  };\n  // If seriesModel provided, incremental threshold is check by series data.\n  Scheduler.prototype.getPerformArgs = function (task, isBlock) {\n    // For overall task\n    if (!task.__pipeline) {\n      return;\n    }\n    var pipeline = this._pipelineMap.get(task.__pipeline.id);\n    var pCtx = pipeline.context;\n    var incremental = !isBlock && pipeline.progressiveEnabled && (!pCtx || pCtx.progressiveRender) && task.__idxInPipeline > pipeline.blockIndex;\n    var step = incremental ? pipeline.step : null;\n    var modDataCount = pCtx && pCtx.modDataCount;\n    var modBy = modDataCount != null ? Math.ceil(modDataCount / step) : null;\n    return {\n      step: step,\n      modBy: modBy,\n      modDataCount: modDataCount\n    };\n  };\n  Scheduler.prototype.getPipeline = function (pipelineId) {\n    return this._pipelineMap.get(pipelineId);\n  };\n  /**\n   * Current, progressive rendering starts from visual and layout.\n   * Always detect render mode in the same stage, avoiding that incorrect\n   * detection caused by data filtering.\n   * Caution:\n   * `updateStreamModes` use `seriesModel.getData()`.\n   */\n  Scheduler.prototype.updateStreamModes = function (seriesModel, view) {\n    var pipeline = this._pipelineMap.get(seriesModel.uid);\n    var data = seriesModel.getData();\n    var dataLen = data.count();\n    // `progressiveRender` means that can render progressively in each\n    // animation frame. Note that some types of series do not provide\n    // `view.incrementalPrepareRender` but support `chart.appendData`. We\n    // use the term `incremental` but not `progressive` to describe the\n    // case that `chart.appendData`.\n    var progressiveRender = pipeline.progressiveEnabled && view.incrementalPrepareRender && dataLen >= pipeline.threshold;\n    var large = seriesModel.get('large') && dataLen >= seriesModel.get('largeThreshold');\n    // TODO: modDataCount should not updated if `appendData`, otherwise cause whole repaint.\n    // see `test/candlestick-large3.html`\n    var modDataCount = seriesModel.get('progressiveChunkMode') === 'mod' ? dataLen : null;\n    seriesModel.pipelineContext = pipeline.context = {\n      progressiveRender: progressiveRender,\n      modDataCount: modDataCount,\n      large: large\n    };\n  };\n  Scheduler.prototype.restorePipelines = function (ecModel) {\n    var scheduler = this;\n    var pipelineMap = scheduler._pipelineMap = createHashMap();\n    ecModel.eachSeries(function (seriesModel) {\n      var progressive = seriesModel.getProgressive();\n      var pipelineId = seriesModel.uid;\n      pipelineMap.set(pipelineId, {\n        id: pipelineId,\n        head: null,\n        tail: null,\n        threshold: seriesModel.getProgressiveThreshold(),\n        progressiveEnabled: progressive && !(seriesModel.preventIncremental && seriesModel.preventIncremental()),\n        blockIndex: -1,\n        step: Math.round(progressive || 700),\n        count: 0\n      });\n      scheduler._pipe(seriesModel, seriesModel.dataTask);\n    });\n  };\n  Scheduler.prototype.prepareStageTasks = function () {\n    var stageTaskMap = this._stageTaskMap;\n    var ecModel = this.api.getModel();\n    var api = this.api;\n    each(this._allHandlers, function (handler) {\n      var record = stageTaskMap.get(handler.uid) || stageTaskMap.set(handler.uid, {});\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        // Currently do not need to support to sepecify them both.\n        errMsg = '\"reset\" and \"overallReset\" must not be both specified.';\n      }\n      assert(!(handler.reset && handler.overallReset), errMsg);\n      handler.reset && this._createSeriesStageTask(handler, record, ecModel, api);\n      handler.overallReset && this._createOverallStageTask(handler, record, ecModel, api);\n    }, this);\n  };\n  Scheduler.prototype.prepareView = function (view, model, ecModel, api) {\n    var renderTask = view.renderTask;\n    var context = renderTask.context;\n    context.model = model;\n    context.ecModel = ecModel;\n    context.api = api;\n    renderTask.__block = !view.incrementalPrepareRender;\n    this._pipe(model, renderTask);\n  };\n  Scheduler.prototype.performDataProcessorTasks = function (ecModel, payload) {\n    // If we do not use `block` here, it should be considered when to update modes.\n    this._performStageTasks(this._dataProcessorHandlers, ecModel, payload, {\n      block: true\n    });\n  };\n  Scheduler.prototype.performVisualTasks = function (ecModel, payload, opt) {\n    this._performStageTasks(this._visualHandlers, ecModel, payload, opt);\n  };\n  Scheduler.prototype._performStageTasks = function (stageHandlers, ecModel, payload, opt) {\n    opt = opt || {};\n    var unfinished = false;\n    var scheduler = this;\n    each(stageHandlers, function (stageHandler, idx) {\n      if (opt.visualType && opt.visualType !== stageHandler.visualType) {\n        return;\n      }\n      var stageHandlerRecord = scheduler._stageTaskMap.get(stageHandler.uid);\n      var seriesTaskMap = stageHandlerRecord.seriesTaskMap;\n      var overallTask = stageHandlerRecord.overallTask;\n      if (overallTask) {\n        var overallNeedDirty_1;\n        var agentStubMap = overallTask.agentStubMap;\n        agentStubMap.each(function (stub) {\n          if (needSetDirty(opt, stub)) {\n            stub.dirty();\n            overallNeedDirty_1 = true;\n          }\n        });\n        overallNeedDirty_1 && overallTask.dirty();\n        scheduler.updatePayload(overallTask, payload);\n        var performArgs_1 = scheduler.getPerformArgs(overallTask, opt.block);\n        // Execute stubs firstly, which may set the overall task dirty,\n        // then execute the overall task. And stub will call seriesModel.setData,\n        // which ensures that in the overallTask seriesModel.getData() will not\n        // return incorrect data.\n        agentStubMap.each(function (stub) {\n          stub.perform(performArgs_1);\n        });\n        if (overallTask.perform(performArgs_1)) {\n          unfinished = true;\n        }\n      } else if (seriesTaskMap) {\n        seriesTaskMap.each(function (task, pipelineId) {\n          if (needSetDirty(opt, task)) {\n            task.dirty();\n          }\n          var performArgs = scheduler.getPerformArgs(task, opt.block);\n          // FIXME\n          // if intending to declare `performRawSeries` in handlers, only\n          // stream-independent (specifically, data item independent) operations can be\n          // performed. Because if a series is filtered, most of the tasks will not\n          // be performed. A stream-dependent operation probably cause wrong biz logic.\n          // Perhaps we should not provide a separate callback for this case instead\n          // of providing the config `performRawSeries`. The stream-dependent operations\n          // and stream-independent operations should better not be mixed.\n          performArgs.skip = !stageHandler.performRawSeries && ecModel.isSeriesFiltered(task.context.model);\n          scheduler.updatePayload(task, payload);\n          if (task.perform(performArgs)) {\n            unfinished = true;\n          }\n        });\n      }\n    });\n    function needSetDirty(opt, task) {\n      return opt.setDirty && (!opt.dirtyMap || opt.dirtyMap.get(task.__pipeline.id));\n    }\n    this.unfinished = unfinished || this.unfinished;\n  };\n  Scheduler.prototype.performSeriesTasks = function (ecModel) {\n    var unfinished;\n    ecModel.eachSeries(function (seriesModel) {\n      // Progress to the end for dataInit and dataRestore.\n      unfinished = seriesModel.dataTask.perform() || unfinished;\n    });\n    this.unfinished = unfinished || this.unfinished;\n  };\n  Scheduler.prototype.plan = function () {\n    // Travel pipelines, check block.\n    this._pipelineMap.each(function (pipeline) {\n      var task = pipeline.tail;\n      do {\n        if (task.__block) {\n          pipeline.blockIndex = task.__idxInPipeline;\n          break;\n        }\n        task = task.getUpstream();\n      } while (task);\n    });\n  };\n  Scheduler.prototype.updatePayload = function (task, payload) {\n    payload !== 'remain' && (task.context.payload = payload);\n  };\n  Scheduler.prototype._createSeriesStageTask = function (stageHandler, stageHandlerRecord, ecModel, api) {\n    var scheduler = this;\n    var oldSeriesTaskMap = stageHandlerRecord.seriesTaskMap;\n    // The count of stages are totally about only several dozen, so\n    // do not need to reuse the map.\n    var newSeriesTaskMap = stageHandlerRecord.seriesTaskMap = createHashMap();\n    var seriesType = stageHandler.seriesType;\n    var getTargetSeries = stageHandler.getTargetSeries;\n    // If a stageHandler should cover all series, `createOnAllSeries` should be declared mandatorily,\n    // to avoid some typo or abuse. Otherwise if an extension do not specify a `seriesType`,\n    // it works but it may cause other irrelevant charts blocked.\n    if (stageHandler.createOnAllSeries) {\n      ecModel.eachRawSeries(create);\n    } else if (seriesType) {\n      ecModel.eachRawSeriesByType(seriesType, create);\n    } else if (getTargetSeries) {\n      getTargetSeries(ecModel, api).each(create);\n    }\n    function create(seriesModel) {\n      var pipelineId = seriesModel.uid;\n      // Init tasks for each seriesModel only once.\n      // Reuse original task instance.\n      var task = newSeriesTaskMap.set(pipelineId, oldSeriesTaskMap && oldSeriesTaskMap.get(pipelineId) || createTask({\n        plan: seriesTaskPlan,\n        reset: seriesTaskReset,\n        count: seriesTaskCount\n      }));\n      task.context = {\n        model: seriesModel,\n        ecModel: ecModel,\n        api: api,\n        // PENDING: `useClearVisual` not used?\n        useClearVisual: stageHandler.isVisual && !stageHandler.isLayout,\n        plan: stageHandler.plan,\n        reset: stageHandler.reset,\n        scheduler: scheduler\n      };\n      scheduler._pipe(seriesModel, task);\n    }\n  };\n  Scheduler.prototype._createOverallStageTask = function (stageHandler, stageHandlerRecord, ecModel, api) {\n    var scheduler = this;\n    var overallTask = stageHandlerRecord.overallTask = stageHandlerRecord.overallTask\n    // For overall task, the function only be called on reset stage.\n    || createTask({\n      reset: overallTaskReset\n    });\n    overallTask.context = {\n      ecModel: ecModel,\n      api: api,\n      overallReset: stageHandler.overallReset,\n      scheduler: scheduler\n    };\n    var oldAgentStubMap = overallTask.agentStubMap;\n    // The count of stages are totally about only several dozen, so\n    // do not need to reuse the map.\n    var newAgentStubMap = overallTask.agentStubMap = createHashMap();\n    var seriesType = stageHandler.seriesType;\n    var getTargetSeries = stageHandler.getTargetSeries;\n    var overallProgress = true;\n    var shouldOverallTaskDirty = false;\n    // FIXME:TS never used, so comment it\n    // let modifyOutputEnd = stageHandler.modifyOutputEnd;\n    // An overall task with seriesType detected or has `getTargetSeries`, we add\n    // stub in each pipelines, it will set the overall task dirty when the pipeline\n    // progress. Moreover, to avoid call the overall task each frame (too frequent),\n    // we set the pipeline block.\n    var errMsg = '';\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '\"createOnAllSeries\" is not supported for \"overallReset\", ' + 'because it will block all streams.';\n    }\n    assert(!stageHandler.createOnAllSeries, errMsg);\n    if (seriesType) {\n      ecModel.eachRawSeriesByType(seriesType, createStub);\n    } else if (getTargetSeries) {\n      getTargetSeries(ecModel, api).each(createStub);\n    }\n    // Otherwise, (usually it is legacy case), the overall task will only be\n    // executed when upstream is dirty. Otherwise the progressive rendering of all\n    // pipelines will be disabled unexpectedly. But it still needs stubs to receive\n    // dirty info from upstream.\n    else {\n      overallProgress = false;\n      each(ecModel.getSeries(), createStub);\n    }\n    function createStub(seriesModel) {\n      var pipelineId = seriesModel.uid;\n      var stub = newAgentStubMap.set(pipelineId, oldAgentStubMap && oldAgentStubMap.get(pipelineId) || (\n      // When the result of `getTargetSeries` changed, the overallTask\n      // should be set as dirty and re-performed.\n      shouldOverallTaskDirty = true, createTask({\n        reset: stubReset,\n        onDirty: stubOnDirty\n      })));\n      stub.context = {\n        model: seriesModel,\n        overallProgress: overallProgress\n        // FIXME:TS never used, so comment it\n        // modifyOutputEnd: modifyOutputEnd\n      };\n\n      stub.agent = overallTask;\n      stub.__block = overallProgress;\n      scheduler._pipe(seriesModel, stub);\n    }\n    if (shouldOverallTaskDirty) {\n      overallTask.dirty();\n    }\n  };\n  Scheduler.prototype._pipe = function (seriesModel, task) {\n    var pipelineId = seriesModel.uid;\n    var pipeline = this._pipelineMap.get(pipelineId);\n    !pipeline.head && (pipeline.head = task);\n    pipeline.tail && pipeline.tail.pipe(task);\n    pipeline.tail = task;\n    task.__idxInPipeline = pipeline.count++;\n    task.__pipeline = pipeline;\n  };\n  Scheduler.wrapStageHandler = function (stageHandler, visualType) {\n    if (isFunction(stageHandler)) {\n      stageHandler = {\n        overallReset: stageHandler,\n        seriesType: detectSeriseType(stageHandler)\n      };\n    }\n    stageHandler.uid = getUID('stageHandler');\n    visualType && (stageHandler.visualType = visualType);\n    return stageHandler;\n  };\n  ;\n  return Scheduler;\n}();\nfunction overallTaskReset(context) {\n  context.overallReset(context.ecModel, context.api, context.payload);\n}\nfunction stubReset(context) {\n  return context.overallProgress && stubProgress;\n}\nfunction stubProgress() {\n  this.agent.dirty();\n  this.getDownstream().dirty();\n}\nfunction stubOnDirty() {\n  this.agent && this.agent.dirty();\n}\nfunction seriesTaskPlan(context) {\n  return context.plan ? context.plan(context.model, context.ecModel, context.api, context.payload) : null;\n}\nfunction seriesTaskReset(context) {\n  if (context.useClearVisual) {\n    context.data.clearAllVisual();\n  }\n  var resetDefines = context.resetDefines = normalizeToArray(context.reset(context.model, context.ecModel, context.api, context.payload));\n  return resetDefines.length > 1 ? map(resetDefines, function (v, idx) {\n    return makeSeriesTaskProgress(idx);\n  }) : singleSeriesTaskProgress;\n}\nvar singleSeriesTaskProgress = makeSeriesTaskProgress(0);\nfunction makeSeriesTaskProgress(resetDefineIdx) {\n  return function (params, context) {\n    var data = context.data;\n    var resetDefine = context.resetDefines[resetDefineIdx];\n    if (resetDefine && resetDefine.dataEach) {\n      for (var i = params.start; i < params.end; i++) {\n        resetDefine.dataEach(data, i);\n      }\n    } else if (resetDefine && resetDefine.progress) {\n      resetDefine.progress(params, data);\n    }\n  };\n}\nfunction seriesTaskCount(context) {\n  return context.data.count();\n}\n/**\n * Only some legacy stage handlers (usually in echarts extensions) are pure function.\n * To ensure that they can work normally, they should work in block mode, that is,\n * they should not be started util the previous tasks finished. So they cause the\n * progressive rendering disabled. We try to detect the series type, to narrow down\n * the block range to only the series type they concern, but not all series.\n */\nfunction detectSeriseType(legacyFunc) {\n  seriesType = null;\n  try {\n    // Assume there is no async when calling `eachSeriesByType`.\n    legacyFunc(ecModelMock, apiMock);\n  } catch (e) {}\n  return seriesType;\n}\nvar ecModelMock = {};\nvar apiMock = {};\nvar seriesType;\nmockMethods(ecModelMock, GlobalModel);\nmockMethods(apiMock, ExtensionAPI);\necModelMock.eachSeriesByType = ecModelMock.eachRawSeriesByType = function (type) {\n  seriesType = type;\n};\necModelMock.eachComponent = function (cond) {\n  if (cond.mainType === 'series' && cond.subType) {\n    seriesType = cond.subType;\n  }\n};\nfunction mockMethods(target, Clz) {\n  /* eslint-disable */\n  for (var name_1 in Clz.prototype) {\n    // Do not use hasOwnProperty\n    target[name_1] = noop;\n  }\n  /* eslint-enable */\n}\n\nexport default Scheduler;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nvar colorAll = ['#37A2DA', '#32C5E9', '#67E0E3', '#9FE6B8', '#FFDB5C', '#ff9f7f', '#fb7293', '#E062AE', '#E690D1', '#e7bcf3', '#9d96f5', '#8378EA', '#96BFFF'];\nexport default {\n  color: colorAll,\n  colorLayer: [['#37A2DA', '#ffd85c', '#fd7b5f'], ['#37A2DA', '#67E0E3', '#FFDB5C', '#ff9f7f', '#E062AE', '#9d96f5'], ['#37A2DA', '#32C5E9', '#9FE6B8', '#FFDB5C', '#ff9f7f', '#fb7293', '#e7bcf3', '#8378EA', '#96BFFF'], colorAll]\n};", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nvar contrastColor = '#B9B8CE';\nvar backgroundColor = '#100C2A';\nvar axisCommon = function () {\n  return {\n    axisLine: {\n      lineStyle: {\n        color: contrastColor\n      }\n    },\n    splitLine: {\n      lineStyle: {\n        color: '#484753'\n      }\n    },\n    splitArea: {\n      areaStyle: {\n        color: ['rgba(255,255,255,0.02)', 'rgba(255,255,255,0.05)']\n      }\n    },\n    minorSplitLine: {\n      lineStyle: {\n        color: '#20203B'\n      }\n    }\n  };\n};\nvar colorPalette = ['#4992ff', '#7cffb2', '#fddd60', '#ff6e76', '#58d9f9', '#05c091', '#ff8a45', '#8d48e3', '#dd79ff'];\nvar theme = {\n  darkMode: true,\n  color: colorPalette,\n  backgroundColor: backgroundColor,\n  axisPointer: {\n    lineStyle: {\n      color: '#817f91'\n    },\n    crossStyle: {\n      color: '#817f91'\n    },\n    label: {\n      // TODO Contrast of label backgorundColor\n      color: '#fff'\n    }\n  },\n  legend: {\n    textStyle: {\n      color: contrastColor\n    }\n  },\n  textStyle: {\n    color: contrastColor\n  },\n  title: {\n    textStyle: {\n      color: '#EEF1FA'\n    },\n    subtextStyle: {\n      color: '#B9B8CE'\n    }\n  },\n  toolbox: {\n    iconStyle: {\n      borderColor: contrastColor\n    }\n  },\n  dataZoom: {\n    borderColor: '#71708A',\n    textStyle: {\n      color: contrastColor\n    },\n    brushStyle: {\n      color: 'rgba(135,163,206,0.3)'\n    },\n    handleStyle: {\n      color: '#353450',\n      borderColor: '#C5CBE3'\n    },\n    moveHandleStyle: {\n      color: '#B0B6C3',\n      opacity: 0.3\n    },\n    fillerColor: 'rgba(135,163,206,0.2)',\n    emphasis: {\n      handleStyle: {\n        borderColor: '#91B7F2',\n        color: '#4D587D'\n      },\n      moveHandleStyle: {\n        color: '#636D9A',\n        opacity: 0.7\n      }\n    },\n    dataBackground: {\n      lineStyle: {\n        color: '#71708A',\n        width: 1\n      },\n      areaStyle: {\n        color: '#71708A'\n      }\n    },\n    selectedDataBackground: {\n      lineStyle: {\n        color: '#87A3CE'\n      },\n      areaStyle: {\n        color: '#87A3CE'\n      }\n    }\n  },\n  visualMap: {\n    textStyle: {\n      color: contrastColor\n    }\n  },\n  timeline: {\n    lineStyle: {\n      color: contrastColor\n    },\n    label: {\n      color: contrastColor\n    },\n    controlStyle: {\n      color: contrastColor,\n      borderColor: contrastColor\n    }\n  },\n  calendar: {\n    itemStyle: {\n      color: backgroundColor\n    },\n    dayLabel: {\n      color: contrastColor\n    },\n    monthLabel: {\n      color: contrastColor\n    },\n    yearLabel: {\n      color: contrastColor\n    }\n  },\n  timeAxis: axisCommon(),\n  logAxis: axisCommon(),\n  valueAxis: axisCommon(),\n  categoryAxis: axisCommon(),\n  line: {\n    symbol: 'circle'\n  },\n  graph: {\n    color: colorPalette\n  },\n  gauge: {\n    title: {\n      color: contrastColor\n    },\n    axisLine: {\n      lineStyle: {\n        color: [[1, 'rgba(207,212,219,0.2)']]\n      }\n    },\n    axisLabel: {\n      color: contrastColor\n    },\n    detail: {\n      color: '#EEF1FA'\n    }\n  },\n  candlestick: {\n    itemStyle: {\n      color: '#f64e56',\n      color0: '#54ea92',\n      borderColor: '#f64e56',\n      borderColor0: '#54ea92'\n      // borderColor: '#ca2824',\n      // borderColor0: '#09a443'\n    }\n  }\n};\n\ntheme.categoryAxis.splitLine.show = false;\nexport default theme;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parseClassType } from './clazz.js';\n/**\n * Usage of query:\n * `chart.on('click', query, handler);`\n * The `query` can be:\n * + The component type query string, only `mainType` or `mainType.subType`,\n *   like: 'xAxis', 'series', 'xAxis.category' or 'series.line'.\n * + The component query object, like:\n *   `{seriesIndex: 2}`, `{seriesName: 'xx'}`, `{seriesId: 'some'}`,\n *   `{xAxisIndex: 2}`, `{xAxisName: 'xx'}`, `{xAxisId: 'some'}`.\n * + The data query object, like:\n *   `{dataIndex: 123}`, `{dataType: 'link'}`, `{name: 'some'}`.\n * + The other query object (cmponent customized query), like:\n *   `{element: 'some'}` (only available in custom series).\n *\n * Caveat: If a prop in the `query` object is `null/undefined`, it is the\n * same as there is no such prop in the `query` object.\n */\nvar ECEventProcessor = /** @class */function () {\n  function ECEventProcessor() {}\n  ECEventProcessor.prototype.normalizeQuery = function (query) {\n    var cptQuery = {};\n    var dataQuery = {};\n    var otherQuery = {};\n    // `query` is `mainType` or `mainType.subType` of component.\n    if (zrUtil.isString(query)) {\n      var condCptType = parseClassType(query);\n      // `.main` and `.sub` may be ''.\n      cptQuery.mainType = condCptType.main || null;\n      cptQuery.subType = condCptType.sub || null;\n    }\n    // `query` is an object, convert to {mainType, index, name, id}.\n    else {\n      // `xxxIndex`, `xxxName`, `xxxId`, `name`, `dataIndex`, `dataType` is reserved,\n      // can not be used in `compomentModel.filterForExposedEvent`.\n      var suffixes_1 = ['Index', 'Name', 'Id'];\n      var dataKeys_1 = {\n        name: 1,\n        dataIndex: 1,\n        dataType: 1\n      };\n      zrUtil.each(query, function (val, key) {\n        var reserved = false;\n        for (var i = 0; i < suffixes_1.length; i++) {\n          var propSuffix = suffixes_1[i];\n          var suffixPos = key.lastIndexOf(propSuffix);\n          if (suffixPos > 0 && suffixPos === key.length - propSuffix.length) {\n            var mainType = key.slice(0, suffixPos);\n            // Consider `dataIndex`.\n            if (mainType !== 'data') {\n              cptQuery.mainType = mainType;\n              cptQuery[propSuffix.toLowerCase()] = val;\n              reserved = true;\n            }\n          }\n        }\n        if (dataKeys_1.hasOwnProperty(key)) {\n          dataQuery[key] = val;\n          reserved = true;\n        }\n        if (!reserved) {\n          otherQuery[key] = val;\n        }\n      });\n    }\n    return {\n      cptQuery: cptQuery,\n      dataQuery: dataQuery,\n      otherQuery: otherQuery\n    };\n  };\n  ECEventProcessor.prototype.filter = function (eventType, query) {\n    // They should be assigned before each trigger call.\n    var eventInfo = this.eventInfo;\n    if (!eventInfo) {\n      return true;\n    }\n    var targetEl = eventInfo.targetEl;\n    var packedEvent = eventInfo.packedEvent;\n    var model = eventInfo.model;\n    var view = eventInfo.view;\n    // For event like 'globalout'.\n    if (!model || !view) {\n      return true;\n    }\n    var cptQuery = query.cptQuery;\n    var dataQuery = query.dataQuery;\n    return check(cptQuery, model, 'mainType') && check(cptQuery, model, 'subType') && check(cptQuery, model, 'index', 'componentIndex') && check(cptQuery, model, 'name') && check(cptQuery, model, 'id') && check(dataQuery, packedEvent, 'name') && check(dataQuery, packedEvent, 'dataIndex') && check(dataQuery, packedEvent, 'dataType') && (!view.filterForExposedEvent || view.filterForExposedEvent(eventType, query.otherQuery, targetEl, packedEvent));\n    function check(query, host, prop, propOnHost) {\n      return query[prop] == null || host[propOnHost || prop] === query[prop];\n    }\n  };\n  ECEventProcessor.prototype.afterTrigger = function () {\n    // Make sure the eventInfo won't be used in next trigger.\n    this.eventInfo = null;\n  };\n  return ECEventProcessor;\n}();\nexport { ECEventProcessor };\n;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { extend, isFunction, keys } from 'zrender/lib/core/util.js';\nvar SYMBOL_PROPS_WITH_CB = ['symbol', 'symbolSize', 'symbolRotate', 'symbolOffset'];\nvar SYMBOL_PROPS = SYMBOL_PROPS_WITH_CB.concat(['symbolKeepAspect']);\n// Encoding visual for all series include which is filtered for legend drawing\nvar seriesSymbolTask = {\n  createOnAllSeries: true,\n  // For legend.\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    if (seriesModel.legendIcon) {\n      data.setVisual('legendIcon', seriesModel.legendIcon);\n    }\n    if (!seriesModel.hasSymbolVisual) {\n      return;\n    }\n    var symbolOptions = {};\n    var symbolOptionsCb = {};\n    var hasCallback = false;\n    for (var i = 0; i < SYMBOL_PROPS_WITH_CB.length; i++) {\n      var symbolPropName = SYMBOL_PROPS_WITH_CB[i];\n      var val = seriesModel.get(symbolPropName);\n      if (isFunction(val)) {\n        hasCallback = true;\n        symbolOptionsCb[symbolPropName] = val;\n      } else {\n        symbolOptions[symbolPropName] = val;\n      }\n    }\n    symbolOptions.symbol = symbolOptions.symbol || seriesModel.defaultSymbol;\n    data.setVisual(extend({\n      legendIcon: seriesModel.legendIcon || symbolOptions.symbol,\n      symbolKeepAspect: seriesModel.get('symbolKeepAspect')\n    }, symbolOptions));\n    // Only visible series has each data be visual encoded\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var symbolPropsCb = keys(symbolOptionsCb);\n    function dataEach(data, idx) {\n      var rawValue = seriesModel.getRawValue(idx);\n      var params = seriesModel.getDataParams(idx);\n      for (var i = 0; i < symbolPropsCb.length; i++) {\n        var symbolPropName = symbolPropsCb[i];\n        data.setItemVisual(idx, symbolPropName, symbolOptionsCb[symbolPropName](rawValue, params));\n      }\n    }\n    return {\n      dataEach: hasCallback ? dataEach : null\n    };\n  }\n};\nvar dataSymbolTask = {\n  createOnAllSeries: true,\n  // For legend.\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    if (!seriesModel.hasSymbolVisual) {\n      return;\n    }\n    // Only visible series has each data be visual encoded\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var data = seriesModel.getData();\n    function dataEach(data, idx) {\n      var itemModel = data.getItemModel(idx);\n      for (var i = 0; i < SYMBOL_PROPS.length; i++) {\n        var symbolPropName = SYMBOL_PROPS[i];\n        var val = itemModel.getShallow(symbolPropName, true);\n        if (val != null) {\n          data.setItemVisual(idx, symbolPropName, val);\n        }\n      }\n    }\n    return {\n      dataEach: data.hasItemOption ? dataEach : null\n    };\n  }\n};\nexport { seriesSymbolTask, dataSymbolTask };", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport function getItemVisualFromData(data, dataIndex, key) {\n  switch (key) {\n    case 'color':\n      var style = data.getItemVisual(dataIndex, 'style');\n      return style[data.getVisual('drawType')];\n    case 'opacity':\n      return data.getItemVisual(dataIndex, 'style').opacity;\n    case 'symbol':\n    case 'symbolSize':\n    case 'liftZ':\n      return data.getItemVisual(dataIndex, key);\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\"Unknown visual type \" + key);\n      }\n  }\n}\nexport function getVisualFromData(data, key) {\n  switch (key) {\n    case 'color':\n      var style = data.getVisual('style');\n      return style[data.getVisual('drawType')];\n    case 'opacity':\n      return data.getVisual('style').opacity;\n    case 'symbol':\n    case 'symbolSize':\n    case 'liftZ':\n      return data.getVisual(key);\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\"Unknown visual type \" + key);\n      }\n  }\n}\nexport function setItemVisualFromData(data, dataIndex, key, value) {\n  switch (key) {\n    case 'color':\n      // Make sure not sharing style object.\n      var style = data.ensureUniqueItemVisual(dataIndex, 'style');\n      style[data.getVisual('drawType')] = value;\n      // Mark the color has been changed, not from palette anymore\n      data.setItemVisual(dataIndex, 'colorFromPalette', false);\n      break;\n    case 'opacity':\n      data.ensureUniqueItemVisual(dataIndex, 'style').opacity = value;\n      break;\n    case 'symbol':\n    case 'symbolSize':\n    case 'liftZ':\n      data.setItemVisual(dataIndex, key, value);\n      break;\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\"Unknown visual type \" + key);\n      }\n  }\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { extend, each, isArray, isString } from 'zrender/lib/core/util.js';\nimport { deprecateReplaceLog, deprecateLog } from '../util/log.js';\nimport { queryDataIndex } from '../util/model.js';\n// Legacy data selection action.\n// Includes: pieSelect, pieUnSelect, pieToggleSelect, mapSelect, mapUnSelect, mapToggleSelect\nexport function createLegacyDataSelectAction(seriesType, ecRegisterAction) {\n  function getSeriesIndices(ecModel, payload) {\n    var seriesIndices = [];\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: seriesType,\n      query: payload\n    }, function (seriesModel) {\n      seriesIndices.push(seriesModel.seriesIndex);\n    });\n    return seriesIndices;\n  }\n  each([[seriesType + 'ToggleSelect', 'toggleSelect'], [seriesType + 'Select', 'select'], [seriesType + 'UnSelect', 'unselect']], function (eventsMap) {\n    ecRegisterAction(eventsMap[0], function (payload, ecModel, api) {\n      payload = extend({}, payload);\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(payload.type, eventsMap[1]);\n      }\n      api.dispatchAction(extend(payload, {\n        type: eventsMap[1],\n        seriesIndex: getSeriesIndices(ecModel, payload)\n      }));\n    });\n  });\n}\nfunction handleSeriesLegacySelectEvents(type, eventPostfix, ecIns, ecModel, payload) {\n  var legacyEventName = type + eventPostfix;\n  if (!ecIns.isSilent(legacyEventName)) {\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateLog(\"event \" + legacyEventName + \" is deprecated.\");\n    }\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'pie'\n    }, function (seriesModel) {\n      var seriesIndex = seriesModel.seriesIndex;\n      var selectedMap = seriesModel.option.selectedMap;\n      var selected = payload.selected;\n      for (var i = 0; i < selected.length; i++) {\n        if (selected[i].seriesIndex === seriesIndex) {\n          var data = seriesModel.getData();\n          var dataIndex = queryDataIndex(data, payload.fromActionPayload);\n          ecIns.trigger(legacyEventName, {\n            type: legacyEventName,\n            seriesId: seriesModel.id,\n            name: isArray(dataIndex) ? data.getName(dataIndex[0]) : data.getName(dataIndex),\n            selected: isString(selectedMap) ? selectedMap : extend({}, selectedMap)\n          });\n        }\n      }\n    });\n  }\n}\nexport function handleLegacySelectEvents(messageCenter, ecIns, api) {\n  messageCenter.on('selectchanged', function (params) {\n    var ecModel = api.getModel();\n    if (params.isFromClick) {\n      handleSeriesLegacySelectEvents('map', 'selectchanged', ecIns, ecModel, params);\n      handleSeriesLegacySelectEvents('pie', 'selectchanged', ecIns, ecModel, params);\n    } else if (params.fromAction === 'select') {\n      handleSeriesLegacySelectEvents('map', 'selected', ecIns, ecModel, params);\n      handleSeriesLegacySelectEvents('pie', 'selected', ecIns, ecModel, params);\n    } else if (params.fromAction === 'unselect') {\n      handleSeriesLegacySelectEvents('map', 'unselected', ecIns, ecModel, params);\n      handleSeriesLegacySelectEvents('pie', 'unselected', ecIns, ecModel, params);\n    }\n  });\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport function findEventDispatcher(target, det, returnFirstMatch) {\n  var found;\n  while (target) {\n    if (det(target)) {\n      found = target;\n      if (returnFirstMatch) {\n        break;\n      }\n    }\n    target = target.__hostTarget || target.parent;\n  }\n  return found;\n}", "var wmUniqueIndex = Math.round(Math.random() * 9);\nvar supportDefineProperty = typeof Object.defineProperty === 'function';\nvar WeakMap = (function () {\n    function WeakMap() {\n        this._id = '__ec_inner_' + wmUniqueIndex++;\n    }\n    WeakMap.prototype.get = function (key) {\n        return this._guard(key)[this._id];\n    };\n    WeakMap.prototype.set = function (key, value) {\n        var target = this._guard(key);\n        if (supportDefineProperty) {\n            Object.defineProperty(target, this._id, {\n                value: value,\n                enumerable: false,\n                configurable: true\n            });\n        }\n        else {\n            target[this._id] = value;\n        }\n        return this;\n    };\n    WeakMap.prototype[\"delete\"] = function (key) {\n        if (this.has(key)) {\n            delete this._guard(key)[this._id];\n            return true;\n        }\n        return false;\n    };\n    WeakMap.prototype.has = function (key) {\n        return !!this._guard(key)[this._id];\n    };\n    WeakMap.prototype._guard = function (key) {\n        if (key !== Object(key)) {\n            throw TypeError('Value of WeakMap is not a non-null object.');\n        }\n        return key;\n    };\n    return WeakMap;\n}());\nexport default WeakMap;\n", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// Symbol factory\nimport { each, isArray, retrieve2 } from 'zrender/lib/core/util.js';\nimport * as graphic from './graphic.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { calculateTextPosition } from 'zrender/lib/contain/text.js';\nimport { parsePercent } from './number.js';\n/**\n * Triangle shape\n * @inner\n */\nvar Triangle = graphic.Path.extend({\n  type: 'triangle',\n  shape: {\n    cx: 0,\n    cy: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var cx = shape.cx;\n    var cy = shape.cy;\n    var width = shape.width / 2;\n    var height = shape.height / 2;\n    path.moveTo(cx, cy - height);\n    path.lineTo(cx + width, cy + height);\n    path.lineTo(cx - width, cy + height);\n    path.closePath();\n  }\n});\n/**\n * Diamond shape\n * @inner\n */\nvar Diamond = graphic.Path.extend({\n  type: 'diamond',\n  shape: {\n    cx: 0,\n    cy: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var cx = shape.cx;\n    var cy = shape.cy;\n    var width = shape.width / 2;\n    var height = shape.height / 2;\n    path.moveTo(cx, cy - height);\n    path.lineTo(cx + width, cy);\n    path.lineTo(cx, cy + height);\n    path.lineTo(cx - width, cy);\n    path.closePath();\n  }\n});\n/**\n * Pin shape\n * @inner\n */\nvar Pin = graphic.Path.extend({\n  type: 'pin',\n  shape: {\n    // x, y on the cusp\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var x = shape.x;\n    var y = shape.y;\n    var w = shape.width / 5 * 3;\n    // Height must be larger than width\n    var h = Math.max(w, shape.height);\n    var r = w / 2;\n    // Dist on y with tangent point and circle center\n    var dy = r * r / (h - r);\n    var cy = y - h + r + dy;\n    var angle = Math.asin(dy / r);\n    // Dist on x with tangent point and circle center\n    var dx = Math.cos(angle) * r;\n    var tanX = Math.sin(angle);\n    var tanY = Math.cos(angle);\n    var cpLen = r * 0.6;\n    var cpLen2 = r * 0.7;\n    path.moveTo(x - dx, cy + dy);\n    path.arc(x, cy, r, Math.PI - angle, Math.PI * 2 + angle);\n    path.bezierCurveTo(x + dx - tanX * cpLen, cy + dy + tanY * cpLen, x, y - cpLen2, x, y);\n    path.bezierCurveTo(x, y - cpLen2, x - dx + tanX * cpLen, cy + dy + tanY * cpLen, x - dx, cy + dy);\n    path.closePath();\n  }\n});\n/**\n * Arrow shape\n * @inner\n */\nvar Arrow = graphic.Path.extend({\n  type: 'arrow',\n  shape: {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (ctx, shape) {\n    var height = shape.height;\n    var width = shape.width;\n    var x = shape.x;\n    var y = shape.y;\n    var dx = width / 3 * 2;\n    ctx.moveTo(x, y);\n    ctx.lineTo(x + dx, y + height);\n    ctx.lineTo(x, y + height / 4 * 3);\n    ctx.lineTo(x - dx, y + height);\n    ctx.lineTo(x, y);\n    ctx.closePath();\n  }\n});\n/**\n * Map of path constructors\n */\n// TODO Use function to build symbol path.\nvar symbolCtors = {\n  line: graphic.Line,\n  rect: graphic.Rect,\n  roundRect: graphic.Rect,\n  square: graphic.Rect,\n  circle: graphic.Circle,\n  diamond: Diamond,\n  pin: Pin,\n  arrow: Arrow,\n  triangle: Triangle\n};\nvar symbolShapeMakers = {\n  line: function (x, y, w, h, shape) {\n    shape.x1 = x;\n    shape.y1 = y + h / 2;\n    shape.x2 = x + w;\n    shape.y2 = y + h / 2;\n  },\n  rect: function (x, y, w, h, shape) {\n    shape.x = x;\n    shape.y = y;\n    shape.width = w;\n    shape.height = h;\n  },\n  roundRect: function (x, y, w, h, shape) {\n    shape.x = x;\n    shape.y = y;\n    shape.width = w;\n    shape.height = h;\n    shape.r = Math.min(w, h) / 4;\n  },\n  square: function (x, y, w, h, shape) {\n    var size = Math.min(w, h);\n    shape.x = x;\n    shape.y = y;\n    shape.width = size;\n    shape.height = size;\n  },\n  circle: function (x, y, w, h, shape) {\n    // Put circle in the center of square\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.r = Math.min(w, h) / 2;\n  },\n  diamond: function (x, y, w, h, shape) {\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  pin: function (x, y, w, h, shape) {\n    shape.x = x + w / 2;\n    shape.y = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  arrow: function (x, y, w, h, shape) {\n    shape.x = x + w / 2;\n    shape.y = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  triangle: function (x, y, w, h, shape) {\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  }\n};\nexport var symbolBuildProxies = {};\neach(symbolCtors, function (Ctor, name) {\n  symbolBuildProxies[name] = new Ctor();\n});\nvar SymbolClz = graphic.Path.extend({\n  type: 'symbol',\n  shape: {\n    symbolType: '',\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  calculateTextPosition: function (out, config, rect) {\n    var res = calculateTextPosition(out, config, rect);\n    var shape = this.shape;\n    if (shape && shape.symbolType === 'pin' && config.position === 'inside') {\n      res.y = rect.y + rect.height * 0.4;\n    }\n    return res;\n  },\n  buildPath: function (ctx, shape, inBundle) {\n    var symbolType = shape.symbolType;\n    if (symbolType !== 'none') {\n      var proxySymbol = symbolBuildProxies[symbolType];\n      if (!proxySymbol) {\n        // Default rect\n        symbolType = 'rect';\n        proxySymbol = symbolBuildProxies[symbolType];\n      }\n      symbolShapeMakers[symbolType](shape.x, shape.y, shape.width, shape.height, proxySymbol.shape);\n      proxySymbol.buildPath(ctx, proxySymbol.shape, inBundle);\n    }\n  }\n});\n// Provide setColor helper method to avoid determine if set the fill or stroke outside\nfunction symbolPathSetColor(color, innerColor) {\n  if (this.type !== 'image') {\n    var symbolStyle = this.style;\n    if (this.__isEmptyBrush) {\n      symbolStyle.stroke = color;\n      symbolStyle.fill = innerColor || '#fff';\n      // TODO Same width with lineStyle in LineView\n      symbolStyle.lineWidth = 2;\n    } else if (this.shape.symbolType === 'line') {\n      symbolStyle.stroke = color;\n    } else {\n      symbolStyle.fill = color;\n    }\n    this.markRedraw();\n  }\n}\n/**\n * Create a symbol element with given symbol configuration: shape, x, y, width, height, color\n */\nexport function createSymbol(symbolType, x, y, w, h, color,\n// whether to keep the ratio of w/h,\nkeepAspect) {\n  // TODO Support image object, DynamicImage.\n  var isEmpty = symbolType.indexOf('empty') === 0;\n  if (isEmpty) {\n    symbolType = symbolType.substr(5, 1).toLowerCase() + symbolType.substr(6);\n  }\n  var symbolPath;\n  if (symbolType.indexOf('image://') === 0) {\n    symbolPath = graphic.makeImage(symbolType.slice(8), new BoundingRect(x, y, w, h), keepAspect ? 'center' : 'cover');\n  } else if (symbolType.indexOf('path://') === 0) {\n    symbolPath = graphic.makePath(symbolType.slice(7), {}, new BoundingRect(x, y, w, h), keepAspect ? 'center' : 'cover');\n  } else {\n    symbolPath = new SymbolClz({\n      shape: {\n        symbolType: symbolType,\n        x: x,\n        y: y,\n        width: w,\n        height: h\n      }\n    });\n  }\n  symbolPath.__isEmptyBrush = isEmpty;\n  // TODO Should deprecate setColor\n  symbolPath.setColor = symbolPathSetColor;\n  if (color) {\n    symbolPath.setColor(color);\n  }\n  return symbolPath;\n}\nexport function normalizeSymbolSize(symbolSize) {\n  if (!isArray(symbolSize)) {\n    symbolSize = [+symbolSize, +symbolSize];\n  }\n  return [symbolSize[0] || 0, symbolSize[1] || 0];\n}\nexport function normalizeSymbolOffset(symbolOffset, symbolSize) {\n  if (symbolOffset == null) {\n    return;\n  }\n  if (!isArray(symbolOffset)) {\n    symbolOffset = [symbolOffset, symbolOffset];\n  }\n  return [parsePercent(symbolOffset[0], symbolSize[0]) || 0, parsePercent(retrieve2(symbolOffset[1], symbolOffset[0]), symbolSize[1]) || 0];\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport WeakMap from 'zrender/lib/core/WeakMap.js';\nimport LRU from 'zrender/lib/core/LRU.js';\nimport { defaults, map, isArray, isString, isNumber } from 'zrender/lib/core/util.js';\nimport { getLeastCommonMultiple } from './number.js';\nimport { createSymbol } from './symbol.js';\nimport { brushSingle } from 'zrender/lib/canvas/graphic.js';\nimport { platformApi } from 'zrender/lib/core/platform.js';\nvar decalMap = new WeakMap();\nvar decalCache = new LRU(100);\nvar decalKeys = ['symbol', 'symbolSize', 'symbolKeepAspect', 'color', 'backgroundColor', 'dashArrayX', 'dashArrayY', 'maxTileWidth', 'maxTileHeight'];\n/**\n * Create or update pattern image from decal options\n *\n * @param {InnerDecalObject | 'none'} decalObject decal options, 'none' if no decal\n * @return {Pattern} pattern with generated image, null if no decal\n */\nexport function createOrUpdatePatternFromDecal(decalObject, api) {\n  if (decalObject === 'none') {\n    return null;\n  }\n  var dpr = api.getDevicePixelRatio();\n  var zr = api.getZr();\n  var isSVG = zr.painter.type === 'svg';\n  if (decalObject.dirty) {\n    decalMap[\"delete\"](decalObject);\n  }\n  var oldPattern = decalMap.get(decalObject);\n  if (oldPattern) {\n    return oldPattern;\n  }\n  var decalOpt = defaults(decalObject, {\n    symbol: 'rect',\n    symbolSize: 1,\n    symbolKeepAspect: true,\n    color: 'rgba(0, 0, 0, 0.2)',\n    backgroundColor: null,\n    dashArrayX: 5,\n    dashArrayY: 5,\n    rotation: 0,\n    maxTileWidth: 512,\n    maxTileHeight: 512\n  });\n  if (decalOpt.backgroundColor === 'none') {\n    decalOpt.backgroundColor = null;\n  }\n  var pattern = {\n    repeat: 'repeat'\n  };\n  setPatternnSource(pattern);\n  pattern.rotation = decalOpt.rotation;\n  pattern.scaleX = pattern.scaleY = isSVG ? 1 : 1 / dpr;\n  decalMap.set(decalObject, pattern);\n  decalObject.dirty = false;\n  return pattern;\n  function setPatternnSource(pattern) {\n    var keys = [dpr];\n    var isValidKey = true;\n    for (var i = 0; i < decalKeys.length; ++i) {\n      var value = decalOpt[decalKeys[i]];\n      if (value != null && !isArray(value) && !isString(value) && !isNumber(value) && typeof value !== 'boolean') {\n        isValidKey = false;\n        break;\n      }\n      keys.push(value);\n    }\n    var cacheKey;\n    if (isValidKey) {\n      cacheKey = keys.join(',') + (isSVG ? '-svg' : '');\n      var cache = decalCache.get(cacheKey);\n      if (cache) {\n        isSVG ? pattern.svgElement = cache : pattern.image = cache;\n      }\n    }\n    var dashArrayX = normalizeDashArrayX(decalOpt.dashArrayX);\n    var dashArrayY = normalizeDashArrayY(decalOpt.dashArrayY);\n    var symbolArray = normalizeSymbolArray(decalOpt.symbol);\n    var lineBlockLengthsX = getLineBlockLengthX(dashArrayX);\n    var lineBlockLengthY = getLineBlockLengthY(dashArrayY);\n    var canvas = !isSVG && platformApi.createCanvas();\n    var svgRoot = isSVG && {\n      tag: 'g',\n      attrs: {},\n      key: 'dcl',\n      children: []\n    };\n    var pSize = getPatternSize();\n    var ctx;\n    if (canvas) {\n      canvas.width = pSize.width * dpr;\n      canvas.height = pSize.height * dpr;\n      ctx = canvas.getContext('2d');\n    }\n    brushDecal();\n    if (isValidKey) {\n      decalCache.put(cacheKey, canvas || svgRoot);\n    }\n    pattern.image = canvas;\n    pattern.svgElement = svgRoot;\n    pattern.svgWidth = pSize.width;\n    pattern.svgHeight = pSize.height;\n    /**\n     * Get minimum length that can make a repeatable pattern.\n     *\n     * @return {Object} pattern width and height\n     */\n    function getPatternSize() {\n      /**\n       * For example, if dash is [[3, 2], [2, 1]] for X, it looks like\n       * |---  ---  ---  ---  --- ...\n       * |-- -- -- -- -- -- -- -- ...\n       * |---  ---  ---  ---  --- ...\n       * |-- -- -- -- -- -- -- -- ...\n       * So the minimum length of X is 15,\n       * which is the least common multiple of `3 + 2` and `2 + 1`\n       * |---  ---  ---  |---  --- ...\n       * |-- -- -- -- -- |-- -- -- ...\n       */\n      var width = 1;\n      for (var i = 0, xlen = lineBlockLengthsX.length; i < xlen; ++i) {\n        width = getLeastCommonMultiple(width, lineBlockLengthsX[i]);\n      }\n      var symbolRepeats = 1;\n      for (var i = 0, xlen = symbolArray.length; i < xlen; ++i) {\n        symbolRepeats = getLeastCommonMultiple(symbolRepeats, symbolArray[i].length);\n      }\n      width *= symbolRepeats;\n      var height = lineBlockLengthY * lineBlockLengthsX.length * symbolArray.length;\n      if (process.env.NODE_ENV !== 'production') {\n        var warn = function (attrName) {\n          /* eslint-disable-next-line */\n          console.warn(\"Calculated decal size is greater than \" + attrName + \" due to decal option settings so \" + attrName + \" is used for the decal size. Please consider changing the decal option to make a smaller decal or set \" + attrName + \" to be larger to avoid incontinuity.\");\n        };\n        if (width > decalOpt.maxTileWidth) {\n          warn('maxTileWidth');\n        }\n        if (height > decalOpt.maxTileHeight) {\n          warn('maxTileHeight');\n        }\n      }\n      return {\n        width: Math.max(1, Math.min(width, decalOpt.maxTileWidth)),\n        height: Math.max(1, Math.min(height, decalOpt.maxTileHeight))\n      };\n    }\n    function brushDecal() {\n      if (ctx) {\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        if (decalOpt.backgroundColor) {\n          ctx.fillStyle = decalOpt.backgroundColor;\n          ctx.fillRect(0, 0, canvas.width, canvas.height);\n        }\n      }\n      var ySum = 0;\n      for (var i = 0; i < dashArrayY.length; ++i) {\n        ySum += dashArrayY[i];\n      }\n      if (ySum <= 0) {\n        // dashArrayY is 0, draw nothing\n        return;\n      }\n      var y = -lineBlockLengthY;\n      var yId = 0;\n      var yIdTotal = 0;\n      var xId0 = 0;\n      while (y < pSize.height) {\n        if (yId % 2 === 0) {\n          var symbolYId = yIdTotal / 2 % symbolArray.length;\n          var x = 0;\n          var xId1 = 0;\n          var xId1Total = 0;\n          while (x < pSize.width * 2) {\n            var xSum = 0;\n            for (var i = 0; i < dashArrayX[xId0].length; ++i) {\n              xSum += dashArrayX[xId0][i];\n            }\n            if (xSum <= 0) {\n              // Skip empty line\n              break;\n            }\n            // E.g., [15, 5, 20, 5] draws only for 15 and 20\n            if (xId1 % 2 === 0) {\n              var size = (1 - decalOpt.symbolSize) * 0.5;\n              var left = x + dashArrayX[xId0][xId1] * size;\n              var top_1 = y + dashArrayY[yId] * size;\n              var width = dashArrayX[xId0][xId1] * decalOpt.symbolSize;\n              var height = dashArrayY[yId] * decalOpt.symbolSize;\n              var symbolXId = xId1Total / 2 % symbolArray[symbolYId].length;\n              brushSymbol(left, top_1, width, height, symbolArray[symbolYId][symbolXId]);\n            }\n            x += dashArrayX[xId0][xId1];\n            ++xId1Total;\n            ++xId1;\n            if (xId1 === dashArrayX[xId0].length) {\n              xId1 = 0;\n            }\n          }\n          ++xId0;\n          if (xId0 === dashArrayX.length) {\n            xId0 = 0;\n          }\n        }\n        y += dashArrayY[yId];\n        ++yIdTotal;\n        ++yId;\n        if (yId === dashArrayY.length) {\n          yId = 0;\n        }\n      }\n      function brushSymbol(x, y, width, height, symbolType) {\n        var scale = isSVG ? 1 : dpr;\n        var symbol = createSymbol(symbolType, x * scale, y * scale, width * scale, height * scale, decalOpt.color, decalOpt.symbolKeepAspect);\n        if (isSVG) {\n          var symbolVNode = zr.painter.renderOneToVNode(symbol);\n          if (symbolVNode) {\n            svgRoot.children.push(symbolVNode);\n          }\n        } else {\n          // Paint to canvas for all other renderers.\n          brushSingle(ctx, symbol);\n        }\n      }\n    }\n  }\n}\n/**\n * Convert symbol array into normalized array\n *\n * @param {string | (string | string[])[]} symbol symbol input\n * @return {string[][]} normolized symbol array\n */\nfunction normalizeSymbolArray(symbol) {\n  if (!symbol || symbol.length === 0) {\n    return [['rect']];\n  }\n  if (isString(symbol)) {\n    return [[symbol]];\n  }\n  var isAllString = true;\n  for (var i = 0; i < symbol.length; ++i) {\n    if (!isString(symbol[i])) {\n      isAllString = false;\n      break;\n    }\n  }\n  if (isAllString) {\n    return normalizeSymbolArray([symbol]);\n  }\n  var result = [];\n  for (var i = 0; i < symbol.length; ++i) {\n    if (isString(symbol[i])) {\n      result.push([symbol[i]]);\n    } else {\n      result.push(symbol[i]);\n    }\n  }\n  return result;\n}\n/**\n * Convert dash input into dashArray\n *\n * @param {DecalDashArrayX} dash dash input\n * @return {number[][]} normolized dash array\n */\nfunction normalizeDashArrayX(dash) {\n  if (!dash || dash.length === 0) {\n    return [[0, 0]];\n  }\n  if (isNumber(dash)) {\n    var dashValue = Math.ceil(dash);\n    return [[dashValue, dashValue]];\n  }\n  /**\n   * [20, 5] should be normalized into [[20, 5]],\n   * while [20, [5, 10]] should be normalized into [[20, 20], [5, 10]]\n   */\n  var isAllNumber = true;\n  for (var i = 0; i < dash.length; ++i) {\n    if (!isNumber(dash[i])) {\n      isAllNumber = false;\n      break;\n    }\n  }\n  if (isAllNumber) {\n    return normalizeDashArrayX([dash]);\n  }\n  var result = [];\n  for (var i = 0; i < dash.length; ++i) {\n    if (isNumber(dash[i])) {\n      var dashValue = Math.ceil(dash[i]);\n      result.push([dashValue, dashValue]);\n    } else {\n      var dashValue = map(dash[i], function (n) {\n        return Math.ceil(n);\n      });\n      if (dashValue.length % 2 === 1) {\n        // [4, 2, 1] means |----  -    -- |----  -    -- |\n        // so normalize it to be [4, 2, 1, 4, 2, 1]\n        result.push(dashValue.concat(dashValue));\n      } else {\n        result.push(dashValue);\n      }\n    }\n  }\n  return result;\n}\n/**\n * Convert dash input into dashArray\n *\n * @param {DecalDashArrayY} dash dash input\n * @return {number[]} normolized dash array\n */\nfunction normalizeDashArrayY(dash) {\n  if (!dash || typeof dash === 'object' && dash.length === 0) {\n    return [0, 0];\n  }\n  if (isNumber(dash)) {\n    var dashValue_1 = Math.ceil(dash);\n    return [dashValue_1, dashValue_1];\n  }\n  var dashValue = map(dash, function (n) {\n    return Math.ceil(n);\n  });\n  return dash.length % 2 ? dashValue.concat(dashValue) : dashValue;\n}\n/**\n * Get block length of each line. A block is the length of dash line and space.\n * For example, a line with [4, 1] has a dash line of 4 and a space of 1 after\n * that, so the block length of this line is 5.\n *\n * @param {number[][]} dash dash array of X or Y\n * @return {number[]} block length of each line\n */\nfunction getLineBlockLengthX(dash) {\n  return map(dash, function (line) {\n    return getLineBlockLengthY(line);\n  });\n}\nfunction getLineBlockLengthY(dash) {\n  var blockLength = 0;\n  for (var i = 0; i < dash.length; ++i) {\n    blockLength += dash[i];\n  }\n  if (dash.length % 2 === 1) {\n    // [4, 2, 1] means |----  -    -- |----  -    -- |\n    // So total length is (4 + 2 + 1) * 2\n    return blockLength * 2;\n  }\n  return blockLength;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createOrUpdatePatternFromDecal } from '../util/decal.js';\nexport default function decalVisual(ecModel, api) {\n  ecModel.eachRawSeries(function (seriesModel) {\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var data = seriesModel.getData();\n    if (data.hasItemVisual()) {\n      data.each(function (idx) {\n        var decal = data.getItemVisual(idx, 'decal');\n        if (decal) {\n          var itemStyle = data.ensureUniqueItemVisual(idx, 'style');\n          itemStyle.decal = createOrUpdatePatternFromDecal(decal, api);\n        }\n      });\n    }\n    var decal = data.getVisual('decal');\n    if (decal) {\n      var style = data.getVisual('style');\n      style.decal = createOrUpdatePatternFromDecal(decal, api);\n    }\n  });\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport Eventful from 'zrender/lib/core/Eventful.js';\n;\nvar lifecycle = new Eventful();\nexport default lifecycle;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { error } from '../util/log.js';\n// Implementation of exported APIs. For example registerMap, getMap.\n// The implementations will be registered when installing the component.\n// Avoid these code being bundled to the core module.\nvar implsStore = {};\n// TODO Type\nexport function registerImpl(name, impl) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (implsStore[name]) {\n      error(\"Already has an implementation of \" + name + \".\");\n    }\n  }\n  implsStore[name] = impl;\n}\nexport function getImpl(name) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!implsStore[name]) {\n      error(\"Implementation of \" + name + \" doesn't exists.\");\n    }\n  }\n  return implsStore[name];\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport { __extends } from \"tslib\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrender from 'zrender/lib/zrender.js';\nimport { assert, each, isFunction, isObject, indexOf, bind, clone, setAsPrimitive, extend, createHashMap, map, defaults, isDom, isArray, noop, isString, retrieve2 } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport timsort from 'zrender/lib/core/timsort.js';\nimport Eventful from 'zrender/lib/core/Eventful.js';\nimport GlobalModel from '../model/Global.js';\nimport ExtensionAPI from './ExtensionAPI.js';\nimport CoordinateSystemManager from './CoordinateSystem.js';\nimport OptionManager from '../model/OptionManager.js';\nimport backwardCompat from '../preprocessor/backwardCompat.js';\nimport dataStack from '../processor/dataStack.js';\nimport SeriesModel from '../model/Series.js';\nimport ComponentView from '../view/Component.js';\nimport ChartView from '../view/Chart.js';\nimport * as graphic from '../util/graphic.js';\nimport { getECData } from '../util/innerStore.js';\nimport { isHighDownDispatcher, HOVER_STATE_EMPHASIS, HOVER_STATE_BLUR, blurSeriesFromHighlightPayload, toggleSelectionFromPayload, updateSeriesElementSelection, getAllSelectedIndices, isSelectChangePayload, isHighDownPayload, HIGHLIGHT_ACTION_TYPE, DOWNPLAY_ACTION_TYPE, SELECT_ACTION_TYPE, UNSELECT_ACTION_TYPE, TOGGLE_SELECT_ACTION_TYPE, savePathStates, enterEmphasis, leaveEmphasis, leaveBlur, enterSelect, leaveSelect, enterBlur, allLeaveBlur, findComponentHighDownDispatchers, blurComponent, handleGlobalMouseOverForHighDown, handleGlobalMouseOutForHighDown } from '../util/states.js';\nimport * as modelUtil from '../util/model.js';\nimport { throttle } from '../util/throttle.js';\nimport { seriesStyleTask, dataStyleTask, dataColorPaletteTask } from '../visual/style.js';\nimport loadingDefault from '../loading/default.js';\nimport Scheduler from './Scheduler.js';\nimport lightTheme from '../theme/light.js';\nimport darkTheme from '../theme/dark.js';\nimport { parseClassType } from '../util/clazz.js';\nimport { ECEventProcessor } from '../util/ECEventProcessor.js';\nimport { seriesSymbolTask, dataSymbolTask } from '../visual/symbol.js';\nimport { getVisualFromData, getItemVisualFromData } from '../visual/helper.js';\nimport { deprecateLog, deprecateReplaceLog, error, warn } from '../util/log.js';\nimport { handleLegacySelectEvents } from '../legacy/dataSelectAction.js';\nimport { registerExternalTransform } from '../data/helper/transform.js';\nimport { createLocaleObject, SYSTEM_LANG } from './locale.js';\nimport { findEventDispatcher } from '../util/event.js';\nimport decal from '../visual/decal.js';\nimport lifecycle from './lifecycle.js';\nimport { platformApi, setPlatformAPI } from 'zrender/lib/core/platform.js';\nimport { getImpl } from './impl.js';\nexport var version = '5.5.1';\nexport var dependencies = {\n  zrender: '5.6.0'\n};\nvar TEST_FRAME_REMAIN_TIME = 1;\nvar PRIORITY_PROCESSOR_SERIES_FILTER = 800;\n// Some data processors depends on the stack result dimension (to calculate data extent).\n// So data stack stage should be in front of data processing stage.\nvar PRIORITY_PROCESSOR_DATASTACK = 900;\n// \"Data filter\" will block the stream, so it should be\n// put at the beginning of data processing.\nvar PRIORITY_PROCESSOR_FILTER = 1000;\nvar PRIORITY_PROCESSOR_DEFAULT = 2000;\nvar PRIORITY_PROCESSOR_STATISTIC = 5000;\nvar PRIORITY_VISUAL_LAYOUT = 1000;\nvar PRIORITY_VISUAL_PROGRESSIVE_LAYOUT = 1100;\nvar PRIORITY_VISUAL_GLOBAL = 2000;\nvar PRIORITY_VISUAL_CHART = 3000;\nvar PRIORITY_VISUAL_COMPONENT = 4000;\n// Visual property in data. Greater than `PRIORITY_VISUAL_COMPONENT` to enable to\n// overwrite the viusal result of component (like `visualMap`)\n// using data item specific setting (like itemStyle.xxx on data item)\nvar PRIORITY_VISUAL_CHART_DATA_CUSTOM = 4500;\n// Greater than `PRIORITY_VISUAL_CHART_DATA_CUSTOM` to enable to layout based on\n// visual result like `symbolSize`.\nvar PRIORITY_VISUAL_POST_CHART_LAYOUT = 4600;\nvar PRIORITY_VISUAL_BRUSH = 5000;\nvar PRIORITY_VISUAL_ARIA = 6000;\nvar PRIORITY_VISUAL_DECAL = 7000;\nexport var PRIORITY = {\n  PROCESSOR: {\n    FILTER: PRIORITY_PROCESSOR_FILTER,\n    SERIES_FILTER: PRIORITY_PROCESSOR_SERIES_FILTER,\n    STATISTIC: PRIORITY_PROCESSOR_STATISTIC\n  },\n  VISUAL: {\n    LAYOUT: PRIORITY_VISUAL_LAYOUT,\n    PROGRESSIVE_LAYOUT: PRIORITY_VISUAL_PROGRESSIVE_LAYOUT,\n    GLOBAL: PRIORITY_VISUAL_GLOBAL,\n    CHART: PRIORITY_VISUAL_CHART,\n    POST_CHART_LAYOUT: PRIORITY_VISUAL_POST_CHART_LAYOUT,\n    COMPONENT: PRIORITY_VISUAL_COMPONENT,\n    BRUSH: PRIORITY_VISUAL_BRUSH,\n    CHART_ITEM: PRIORITY_VISUAL_CHART_DATA_CUSTOM,\n    ARIA: PRIORITY_VISUAL_ARIA,\n    DECAL: PRIORITY_VISUAL_DECAL\n  }\n};\n// Main process have three entries: `setOption`, `dispatchAction` and `resize`,\n// where they must not be invoked nestedly, except the only case: invoke\n// dispatchAction with updateMethod \"none\" in main process.\n// This flag is used to carry out this rule.\n// All events will be triggered out side main process (i.e. when !this[IN_MAIN_PROCESS]).\nvar IN_MAIN_PROCESS_KEY = '__flagInMainProcess';\nvar PENDING_UPDATE = '__pendingUpdate';\nvar STATUS_NEEDS_UPDATE_KEY = '__needsUpdateStatus';\nvar ACTION_REG = /^[a-zA-Z0-9_]+$/;\nvar CONNECT_STATUS_KEY = '__connectUpdateStatus';\nvar CONNECT_STATUS_PENDING = 0;\nvar CONNECT_STATUS_UPDATING = 1;\nvar CONNECT_STATUS_UPDATED = 2;\n;\n;\nfunction createRegisterEventWithLowercaseECharts(method) {\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (this.isDisposed()) {\n      disposedWarning(this.id);\n      return;\n    }\n    return toLowercaseNameAndCallEventful(this, method, args);\n  };\n}\nfunction createRegisterEventWithLowercaseMessageCenter(method) {\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    return toLowercaseNameAndCallEventful(this, method, args);\n  };\n}\nfunction toLowercaseNameAndCallEventful(host, method, args) {\n  // `args[0]` is event name. Event name is all lowercase.\n  args[0] = args[0] && args[0].toLowerCase();\n  return Eventful.prototype[method].apply(host, args);\n}\nvar MessageCenter = /** @class */function (_super) {\n  __extends(MessageCenter, _super);\n  function MessageCenter() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return MessageCenter;\n}(Eventful);\nvar messageCenterProto = MessageCenter.prototype;\nmessageCenterProto.on = createRegisterEventWithLowercaseMessageCenter('on');\nmessageCenterProto.off = createRegisterEventWithLowercaseMessageCenter('off');\n// ---------------------------------------\n// Internal method names for class ECharts\n// ---------------------------------------\nvar prepare;\nvar prepareView;\nvar updateDirectly;\nvar updateMethods;\nvar doConvertPixel;\nvar updateStreamModes;\nvar doDispatchAction;\nvar flushPendingActions;\nvar triggerUpdatedEvent;\nvar bindRenderedEvent;\nvar bindMouseEvent;\nvar render;\nvar renderComponents;\nvar renderSeries;\nvar createExtensionAPI;\nvar enableConnect;\nvar markStatusToUpdate;\nvar applyChangedStates;\nvar ECharts = /** @class */function (_super) {\n  __extends(ECharts, _super);\n  function ECharts(dom,\n  // Theme name or themeOption.\n  theme, opts) {\n    var _this = _super.call(this, new ECEventProcessor()) || this;\n    _this._chartsViews = [];\n    _this._chartsMap = {};\n    _this._componentsViews = [];\n    _this._componentsMap = {};\n    // Can't dispatch action during rendering procedure\n    _this._pendingActions = [];\n    opts = opts || {};\n    // Get theme by name\n    if (isString(theme)) {\n      theme = themeStorage[theme];\n    }\n    _this._dom = dom;\n    var defaultRenderer = 'canvas';\n    var defaultCoarsePointer = 'auto';\n    var defaultUseDirtyRect = false;\n    if (process.env.NODE_ENV !== 'production') {\n      var root = /* eslint-disable-next-line */\n      env.hasGlobalWindow ? window : global;\n      if (root) {\n        defaultRenderer = retrieve2(root.__ECHARTS__DEFAULT__RENDERER__, defaultRenderer);\n        defaultCoarsePointer = retrieve2(root.__ECHARTS__DEFAULT__COARSE_POINTER, defaultCoarsePointer);\n        defaultUseDirtyRect = retrieve2(root.__ECHARTS__DEFAULT__USE_DIRTY_RECT__, defaultUseDirtyRect);\n      }\n    }\n    if (opts.ssr) {\n      zrender.registerSSRDataGetter(function (el) {\n        var ecData = getECData(el);\n        var dataIndex = ecData.dataIndex;\n        if (dataIndex == null) {\n          return;\n        }\n        var hashMap = createHashMap();\n        hashMap.set('series_index', ecData.seriesIndex);\n        hashMap.set('data_index', dataIndex);\n        ecData.ssrType && hashMap.set('ssr_type', ecData.ssrType);\n        return hashMap;\n      });\n    }\n    var zr = _this._zr = zrender.init(dom, {\n      renderer: opts.renderer || defaultRenderer,\n      devicePixelRatio: opts.devicePixelRatio,\n      width: opts.width,\n      height: opts.height,\n      ssr: opts.ssr,\n      useDirtyRect: retrieve2(opts.useDirtyRect, defaultUseDirtyRect),\n      useCoarsePointer: retrieve2(opts.useCoarsePointer, defaultCoarsePointer),\n      pointerSize: opts.pointerSize\n    });\n    _this._ssr = opts.ssr;\n    // Expect 60 fps.\n    _this._throttledZrFlush = throttle(bind(zr.flush, zr), 17);\n    theme = clone(theme);\n    theme && backwardCompat(theme, true);\n    _this._theme = theme;\n    _this._locale = createLocaleObject(opts.locale || SYSTEM_LANG);\n    _this._coordSysMgr = new CoordinateSystemManager();\n    var api = _this._api = createExtensionAPI(_this);\n    // Sort on demand\n    function prioritySortFunc(a, b) {\n      return a.__prio - b.__prio;\n    }\n    timsort(visualFuncs, prioritySortFunc);\n    timsort(dataProcessorFuncs, prioritySortFunc);\n    _this._scheduler = new Scheduler(_this, api, dataProcessorFuncs, visualFuncs);\n    _this._messageCenter = new MessageCenter();\n    // Init mouse events\n    _this._initEvents();\n    // In case some people write `window.onresize = chart.resize`\n    _this.resize = bind(_this.resize, _this);\n    zr.animation.on('frame', _this._onframe, _this);\n    bindRenderedEvent(zr, _this);\n    bindMouseEvent(zr, _this);\n    // ECharts instance can be used as value.\n    setAsPrimitive(_this);\n    return _this;\n  }\n  ECharts.prototype._onframe = function () {\n    if (this._disposed) {\n      return;\n    }\n    applyChangedStates(this);\n    var scheduler = this._scheduler;\n    // Lazy update\n    if (this[PENDING_UPDATE]) {\n      var silent = this[PENDING_UPDATE].silent;\n      this[IN_MAIN_PROCESS_KEY] = true;\n      try {\n        prepare(this);\n        updateMethods.update.call(this, null, this[PENDING_UPDATE].updateParams);\n      } catch (e) {\n        this[IN_MAIN_PROCESS_KEY] = false;\n        this[PENDING_UPDATE] = null;\n        throw e;\n      }\n      // At present, in each frame, zrender performs:\n      //   (1) animation step forward.\n      //   (2) trigger('frame') (where this `_onframe` is called)\n      //   (3) zrender flush (render).\n      // If we do nothing here, since we use `setToFinal: true`, the step (3) above\n      // will render the final state of the elements before the real animation started.\n      this._zr.flush();\n      this[IN_MAIN_PROCESS_KEY] = false;\n      this[PENDING_UPDATE] = null;\n      flushPendingActions.call(this, silent);\n      triggerUpdatedEvent.call(this, silent);\n    }\n    // Avoid do both lazy update and progress in one frame.\n    else if (scheduler.unfinished) {\n      // Stream progress.\n      var remainTime = TEST_FRAME_REMAIN_TIME;\n      var ecModel = this._model;\n      var api = this._api;\n      scheduler.unfinished = false;\n      do {\n        var startTime = +new Date();\n        scheduler.performSeriesTasks(ecModel);\n        // Currently dataProcessorFuncs do not check threshold.\n        scheduler.performDataProcessorTasks(ecModel);\n        updateStreamModes(this, ecModel);\n        // Do not update coordinate system here. Because that coord system update in\n        // each frame is not a good user experience. So we follow the rule that\n        // the extent of the coordinate system is determined in the first frame (the\n        // frame is executed immediately after task reset.\n        // this._coordSysMgr.update(ecModel, api);\n        // console.log('--- ec frame visual ---', remainTime);\n        scheduler.performVisualTasks(ecModel);\n        renderSeries(this, this._model, api, 'remain', {});\n        remainTime -= +new Date() - startTime;\n      } while (remainTime > 0 && scheduler.unfinished);\n      // Call flush explicitly for trigger finished event.\n      if (!scheduler.unfinished) {\n        this._zr.flush();\n      }\n      // Else, zr flushing be ensue within the same frame,\n      // because zr flushing is after onframe event.\n    }\n  };\n\n  ECharts.prototype.getDom = function () {\n    return this._dom;\n  };\n  ECharts.prototype.getId = function () {\n    return this.id;\n  };\n  ECharts.prototype.getZr = function () {\n    return this._zr;\n  };\n  ECharts.prototype.isSSR = function () {\n    return this._ssr;\n  };\n  /* eslint-disable-next-line */\n  ECharts.prototype.setOption = function (option, notMerge, lazyUpdate) {\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      if (process.env.NODE_ENV !== 'production') {\n        error('`setOption` should not be called during main process.');\n      }\n      return;\n    }\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var silent;\n    var replaceMerge;\n    var transitionOpt;\n    if (isObject(notMerge)) {\n      lazyUpdate = notMerge.lazyUpdate;\n      silent = notMerge.silent;\n      replaceMerge = notMerge.replaceMerge;\n      transitionOpt = notMerge.transition;\n      notMerge = notMerge.notMerge;\n    }\n    this[IN_MAIN_PROCESS_KEY] = true;\n    if (!this._model || notMerge) {\n      var optionManager = new OptionManager(this._api);\n      var theme = this._theme;\n      var ecModel = this._model = new GlobalModel();\n      ecModel.scheduler = this._scheduler;\n      ecModel.ssr = this._ssr;\n      ecModel.init(null, null, null, theme, this._locale, optionManager);\n    }\n    this._model.setOption(option, {\n      replaceMerge: replaceMerge\n    }, optionPreprocessorFuncs);\n    var updateParams = {\n      seriesTransition: transitionOpt,\n      optionChanged: true\n    };\n    if (lazyUpdate) {\n      this[PENDING_UPDATE] = {\n        silent: silent,\n        updateParams: updateParams\n      };\n      this[IN_MAIN_PROCESS_KEY] = false;\n      // `setOption(option, {lazyMode: true})` may be called when zrender has been slept.\n      // It should wake it up to make sure zrender start to render at the next frame.\n      this.getZr().wakeUp();\n    } else {\n      try {\n        prepare(this);\n        updateMethods.update.call(this, null, updateParams);\n      } catch (e) {\n        this[PENDING_UPDATE] = null;\n        this[IN_MAIN_PROCESS_KEY] = false;\n        throw e;\n      }\n      // Ensure zr refresh sychronously, and then pixel in canvas can be\n      // fetched after `setOption`.\n      if (!this._ssr) {\n        // not use flush when using ssr mode.\n        this._zr.flush();\n      }\n      this[PENDING_UPDATE] = null;\n      this[IN_MAIN_PROCESS_KEY] = false;\n      flushPendingActions.call(this, silent);\n      triggerUpdatedEvent.call(this, silent);\n    }\n  };\n  /**\n   * @deprecated\n   */\n  ECharts.prototype.setTheme = function () {\n    deprecateLog('ECharts#setTheme() is DEPRECATED in ECharts 3.0');\n  };\n  // We don't want developers to use getModel directly.\n  ECharts.prototype.getModel = function () {\n    return this._model;\n  };\n  ECharts.prototype.getOption = function () {\n    return this._model && this._model.getOption();\n  };\n  ECharts.prototype.getWidth = function () {\n    return this._zr.getWidth();\n  };\n  ECharts.prototype.getHeight = function () {\n    return this._zr.getHeight();\n  };\n  ECharts.prototype.getDevicePixelRatio = function () {\n    return this._zr.painter.dpr\n    /* eslint-disable-next-line */ || env.hasGlobalWindow && window.devicePixelRatio || 1;\n  };\n  /**\n   * Get canvas which has all thing rendered\n   * @deprecated Use renderToCanvas instead.\n   */\n  ECharts.prototype.getRenderedCanvas = function (opts) {\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('getRenderedCanvas', 'renderToCanvas');\n    }\n    return this.renderToCanvas(opts);\n  };\n  ECharts.prototype.renderToCanvas = function (opts) {\n    opts = opts || {};\n    var painter = this._zr.painter;\n    if (process.env.NODE_ENV !== 'production') {\n      if (painter.type !== 'canvas') {\n        throw new Error('renderToCanvas can only be used in the canvas renderer.');\n      }\n    }\n    return painter.getRenderedCanvas({\n      backgroundColor: opts.backgroundColor || this._model.get('backgroundColor'),\n      pixelRatio: opts.pixelRatio || this.getDevicePixelRatio()\n    });\n  };\n  ECharts.prototype.renderToSVGString = function (opts) {\n    opts = opts || {};\n    var painter = this._zr.painter;\n    if (process.env.NODE_ENV !== 'production') {\n      if (painter.type !== 'svg') {\n        throw new Error('renderToSVGString can only be used in the svg renderer.');\n      }\n    }\n    return painter.renderToString({\n      useViewBox: opts.useViewBox\n    });\n  };\n  /**\n   * Get svg data url\n   */\n  ECharts.prototype.getSvgDataURL = function () {\n    if (!env.svgSupported) {\n      return;\n    }\n    var zr = this._zr;\n    var list = zr.storage.getDisplayList();\n    // Stop animations\n    each(list, function (el) {\n      el.stopAnimation(null, true);\n    });\n    return zr.painter.toDataURL();\n  };\n  ECharts.prototype.getDataURL = function (opts) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    opts = opts || {};\n    var excludeComponents = opts.excludeComponents;\n    var ecModel = this._model;\n    var excludesComponentViews = [];\n    var self = this;\n    each(excludeComponents, function (componentType) {\n      ecModel.eachComponent({\n        mainType: componentType\n      }, function (component) {\n        var view = self._componentsMap[component.__viewId];\n        if (!view.group.ignore) {\n          excludesComponentViews.push(view);\n          view.group.ignore = true;\n        }\n      });\n    });\n    var url = this._zr.painter.getType() === 'svg' ? this.getSvgDataURL() : this.renderToCanvas(opts).toDataURL('image/' + (opts && opts.type || 'png'));\n    each(excludesComponentViews, function (view) {\n      view.group.ignore = false;\n    });\n    return url;\n  };\n  ECharts.prototype.getConnectedDataURL = function (opts) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var isSvg = opts.type === 'svg';\n    var groupId = this.group;\n    var mathMin = Math.min;\n    var mathMax = Math.max;\n    var MAX_NUMBER = Infinity;\n    if (connectedGroups[groupId]) {\n      var left_1 = MAX_NUMBER;\n      var top_1 = MAX_NUMBER;\n      var right_1 = -MAX_NUMBER;\n      var bottom_1 = -MAX_NUMBER;\n      var canvasList_1 = [];\n      var dpr_1 = opts && opts.pixelRatio || this.getDevicePixelRatio();\n      each(instances, function (chart, id) {\n        if (chart.group === groupId) {\n          var canvas = isSvg ? chart.getZr().painter.getSvgDom().innerHTML : chart.renderToCanvas(clone(opts));\n          var boundingRect = chart.getDom().getBoundingClientRect();\n          left_1 = mathMin(boundingRect.left, left_1);\n          top_1 = mathMin(boundingRect.top, top_1);\n          right_1 = mathMax(boundingRect.right, right_1);\n          bottom_1 = mathMax(boundingRect.bottom, bottom_1);\n          canvasList_1.push({\n            dom: canvas,\n            left: boundingRect.left,\n            top: boundingRect.top\n          });\n        }\n      });\n      left_1 *= dpr_1;\n      top_1 *= dpr_1;\n      right_1 *= dpr_1;\n      bottom_1 *= dpr_1;\n      var width = right_1 - left_1;\n      var height = bottom_1 - top_1;\n      var targetCanvas = platformApi.createCanvas();\n      var zr_1 = zrender.init(targetCanvas, {\n        renderer: isSvg ? 'svg' : 'canvas'\n      });\n      zr_1.resize({\n        width: width,\n        height: height\n      });\n      if (isSvg) {\n        var content_1 = '';\n        each(canvasList_1, function (item) {\n          var x = item.left - left_1;\n          var y = item.top - top_1;\n          content_1 += '<g transform=\"translate(' + x + ',' + y + ')\">' + item.dom + '</g>';\n        });\n        zr_1.painter.getSvgRoot().innerHTML = content_1;\n        if (opts.connectedBackgroundColor) {\n          zr_1.painter.setBackgroundColor(opts.connectedBackgroundColor);\n        }\n        zr_1.refreshImmediately();\n        return zr_1.painter.toDataURL();\n      } else {\n        // Background between the charts\n        if (opts.connectedBackgroundColor) {\n          zr_1.add(new graphic.Rect({\n            shape: {\n              x: 0,\n              y: 0,\n              width: width,\n              height: height\n            },\n            style: {\n              fill: opts.connectedBackgroundColor\n            }\n          }));\n        }\n        each(canvasList_1, function (item) {\n          var img = new graphic.Image({\n            style: {\n              x: item.left * dpr_1 - left_1,\n              y: item.top * dpr_1 - top_1,\n              image: item.dom\n            }\n          });\n          zr_1.add(img);\n        });\n        zr_1.refreshImmediately();\n        return targetCanvas.toDataURL('image/' + (opts && opts.type || 'png'));\n      }\n    } else {\n      return this.getDataURL(opts);\n    }\n  };\n  ECharts.prototype.convertToPixel = function (finder, value) {\n    return doConvertPixel(this, 'convertToPixel', finder, value);\n  };\n  ECharts.prototype.convertFromPixel = function (finder, value) {\n    return doConvertPixel(this, 'convertFromPixel', finder, value);\n  };\n  /**\n   * Is the specified coordinate systems or components contain the given pixel point.\n   * @param {Array|number} value\n   * @return {boolean} result\n   */\n  ECharts.prototype.containPixel = function (finder, value) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var ecModel = this._model;\n    var result;\n    var findResult = modelUtil.parseFinder(ecModel, finder);\n    each(findResult, function (models, key) {\n      key.indexOf('Models') >= 0 && each(models, function (model) {\n        var coordSys = model.coordinateSystem;\n        if (coordSys && coordSys.containPoint) {\n          result = result || !!coordSys.containPoint(value);\n        } else if (key === 'seriesModels') {\n          var view = this._chartsMap[model.__viewId];\n          if (view && view.containPoint) {\n            result = result || view.containPoint(value, model);\n          } else {\n            if (process.env.NODE_ENV !== 'production') {\n              warn(key + ': ' + (view ? 'The found component do not support containPoint.' : 'No view mapping to the found component.'));\n            }\n          }\n        } else {\n          if (process.env.NODE_ENV !== 'production') {\n            warn(key + ': containPoint is not supported');\n          }\n        }\n      }, this);\n    }, this);\n    return !!result;\n  };\n  /**\n   * Get visual from series or data.\n   * @param finder\n   *        If string, e.g., 'series', means {seriesIndex: 0}.\n   *        If Object, could contain some of these properties below:\n   *        {\n   *            seriesIndex / seriesId / seriesName,\n   *            dataIndex / dataIndexInside\n   *        }\n   *        If dataIndex is not specified, series visual will be fetched,\n   *        but not data item visual.\n   *        If all of seriesIndex, seriesId, seriesName are not specified,\n   *        visual will be fetched from first series.\n   * @param visualType 'color', 'symbol', 'symbolSize'\n   */\n  ECharts.prototype.getVisual = function (finder, visualType) {\n    var ecModel = this._model;\n    var parsedFinder = modelUtil.parseFinder(ecModel, finder, {\n      defaultMainType: 'series'\n    });\n    var seriesModel = parsedFinder.seriesModel;\n    if (process.env.NODE_ENV !== 'production') {\n      if (!seriesModel) {\n        warn('There is no specified series model');\n      }\n    }\n    var data = seriesModel.getData();\n    var dataIndexInside = parsedFinder.hasOwnProperty('dataIndexInside') ? parsedFinder.dataIndexInside : parsedFinder.hasOwnProperty('dataIndex') ? data.indexOfRawIndex(parsedFinder.dataIndex) : null;\n    return dataIndexInside != null ? getItemVisualFromData(data, dataIndexInside, visualType) : getVisualFromData(data, visualType);\n  };\n  /**\n   * Get view of corresponding component model\n   */\n  ECharts.prototype.getViewOfComponentModel = function (componentModel) {\n    return this._componentsMap[componentModel.__viewId];\n  };\n  /**\n   * Get view of corresponding series model\n   */\n  ECharts.prototype.getViewOfSeriesModel = function (seriesModel) {\n    return this._chartsMap[seriesModel.__viewId];\n  };\n  ECharts.prototype._initEvents = function () {\n    var _this = this;\n    each(MOUSE_EVENT_NAMES, function (eveName) {\n      var handler = function (e) {\n        var ecModel = _this.getModel();\n        var el = e.target;\n        var params;\n        var isGlobalOut = eveName === 'globalout';\n        // no e.target when 'globalout'.\n        if (isGlobalOut) {\n          params = {};\n        } else {\n          el && findEventDispatcher(el, function (parent) {\n            var ecData = getECData(parent);\n            if (ecData && ecData.dataIndex != null) {\n              var dataModel = ecData.dataModel || ecModel.getSeriesByIndex(ecData.seriesIndex);\n              params = dataModel && dataModel.getDataParams(ecData.dataIndex, ecData.dataType, el) || {};\n              return true;\n            }\n            // If element has custom eventData of components\n            else if (ecData.eventData) {\n              params = extend({}, ecData.eventData);\n              return true;\n            }\n          }, true);\n        }\n        // Contract: if params prepared in mouse event,\n        // these properties must be specified:\n        // {\n        //    componentType: string (component main type)\n        //    componentIndex: number\n        // }\n        // Otherwise event query can not work.\n        if (params) {\n          var componentType = params.componentType;\n          var componentIndex = params.componentIndex;\n          // Special handling for historic reason: when trigger by\n          // markLine/markPoint/markArea, the componentType is\n          // 'markLine'/'markPoint'/'markArea', but we should better\n          // enable them to be queried by seriesIndex, since their\n          // option is set in each series.\n          if (componentType === 'markLine' || componentType === 'markPoint' || componentType === 'markArea') {\n            componentType = 'series';\n            componentIndex = params.seriesIndex;\n          }\n          var model = componentType && componentIndex != null && ecModel.getComponent(componentType, componentIndex);\n          var view = model && _this[model.mainType === 'series' ? '_chartsMap' : '_componentsMap'][model.__viewId];\n          if (process.env.NODE_ENV !== 'production') {\n            // `event.componentType` and `event[componentTpype + 'Index']` must not\n            // be missed, otherwise there is no way to distinguish source component.\n            // See `dataFormat.getDataParams`.\n            if (!isGlobalOut && !(model && view)) {\n              warn('model or view can not be found by params');\n            }\n          }\n          params.event = e;\n          params.type = eveName;\n          _this._$eventProcessor.eventInfo = {\n            targetEl: el,\n            packedEvent: params,\n            model: model,\n            view: view\n          };\n          _this.trigger(eveName, params);\n        }\n      };\n      // Consider that some component (like tooltip, brush, ...)\n      // register zr event handler, but user event handler might\n      // do anything, such as call `setOption` or `dispatchAction`,\n      // which probably update any of the content and probably\n      // cause problem if it is called previous other inner handlers.\n      handler.zrEventfulCallAtLast = true;\n      _this._zr.on(eveName, handler, _this);\n    });\n    each(eventActionMap, function (actionType, eventType) {\n      _this._messageCenter.on(eventType, function (event) {\n        this.trigger(eventType, event);\n      }, _this);\n    });\n    // Extra events\n    // TODO register?\n    each(['selectchanged'], function (eventType) {\n      _this._messageCenter.on(eventType, function (event) {\n        this.trigger(eventType, event);\n      }, _this);\n    });\n    handleLegacySelectEvents(this._messageCenter, this, this._api);\n  };\n  ECharts.prototype.isDisposed = function () {\n    return this._disposed;\n  };\n  ECharts.prototype.clear = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this.setOption({\n      series: []\n    }, true);\n  };\n  ECharts.prototype.dispose = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._disposed = true;\n    var dom = this.getDom();\n    if (dom) {\n      modelUtil.setAttribute(this.getDom(), DOM_ATTRIBUTE_KEY, '');\n    }\n    var chart = this;\n    var api = chart._api;\n    var ecModel = chart._model;\n    each(chart._componentsViews, function (component) {\n      component.dispose(ecModel, api);\n    });\n    each(chart._chartsViews, function (chart) {\n      chart.dispose(ecModel, api);\n    });\n    // Dispose after all views disposed\n    chart._zr.dispose();\n    // Set properties to null.\n    // To reduce the memory cost in case the top code still holds this instance unexpectedly.\n    chart._dom = chart._model = chart._chartsMap = chart._componentsMap = chart._chartsViews = chart._componentsViews = chart._scheduler = chart._api = chart._zr = chart._throttledZrFlush = chart._theme = chart._coordSysMgr = chart._messageCenter = null;\n    delete instances[chart.id];\n  };\n  /**\n   * Resize the chart\n   */\n  ECharts.prototype.resize = function (opts) {\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      if (process.env.NODE_ENV !== 'production') {\n        error('`resize` should not be called during main process.');\n      }\n      return;\n    }\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._zr.resize(opts);\n    var ecModel = this._model;\n    // Resize loading effect\n    this._loadingFX && this._loadingFX.resize();\n    if (!ecModel) {\n      return;\n    }\n    var needPrepare = ecModel.resetOption('media');\n    var silent = opts && opts.silent;\n    // There is some real cases that:\n    // chart.setOption(option, { lazyUpdate: true });\n    // chart.resize();\n    if (this[PENDING_UPDATE]) {\n      if (silent == null) {\n        silent = this[PENDING_UPDATE].silent;\n      }\n      needPrepare = true;\n      this[PENDING_UPDATE] = null;\n    }\n    this[IN_MAIN_PROCESS_KEY] = true;\n    try {\n      needPrepare && prepare(this);\n      updateMethods.update.call(this, {\n        type: 'resize',\n        animation: extend({\n          // Disable animation\n          duration: 0\n        }, opts && opts.animation)\n      });\n    } catch (e) {\n      this[IN_MAIN_PROCESS_KEY] = false;\n      throw e;\n    }\n    this[IN_MAIN_PROCESS_KEY] = false;\n    flushPendingActions.call(this, silent);\n    triggerUpdatedEvent.call(this, silent);\n  };\n  ECharts.prototype.showLoading = function (name, cfg) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    if (isObject(name)) {\n      cfg = name;\n      name = '';\n    }\n    name = name || 'default';\n    this.hideLoading();\n    if (!loadingEffects[name]) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('Loading effects ' + name + ' not exists.');\n      }\n      return;\n    }\n    var el = loadingEffects[name](this._api, cfg);\n    var zr = this._zr;\n    this._loadingFX = el;\n    zr.add(el);\n  };\n  /**\n   * Hide loading effect\n   */\n  ECharts.prototype.hideLoading = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._loadingFX && this._zr.remove(this._loadingFX);\n    this._loadingFX = null;\n  };\n  ECharts.prototype.makeActionFromEvent = function (eventObj) {\n    var payload = extend({}, eventObj);\n    payload.type = eventActionMap[eventObj.type];\n    return payload;\n  };\n  /**\n   * @param opt If pass boolean, means opt.silent\n   * @param opt.silent Default `false`. Whether trigger events.\n   * @param opt.flush Default `undefined`.\n   *        true: Flush immediately, and then pixel in canvas can be fetched\n   *            immediately. Caution: it might affect performance.\n   *        false: Not flush.\n   *        undefined: Auto decide whether perform flush.\n   */\n  ECharts.prototype.dispatchAction = function (payload, opt) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    if (!isObject(opt)) {\n      opt = {\n        silent: !!opt\n      };\n    }\n    if (!actions[payload.type]) {\n      return;\n    }\n    // Avoid dispatch action before setOption. Especially in `connect`.\n    if (!this._model) {\n      return;\n    }\n    // May dispatchAction in rendering procedure\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      this._pendingActions.push(payload);\n      return;\n    }\n    var silent = opt.silent;\n    doDispatchAction.call(this, payload, silent);\n    var flush = opt.flush;\n    if (flush) {\n      this._zr.flush();\n    } else if (flush !== false && env.browser.weChat) {\n      // In WeChat embedded browser, `requestAnimationFrame` and `setInterval`\n      // hang when sliding page (on touch event), which cause that zr does not\n      // refresh until user interaction finished, which is not expected.\n      // But `dispatchAction` may be called too frequently when pan on touch\n      // screen, which impacts performance if do not throttle them.\n      this._throttledZrFlush();\n    }\n    flushPendingActions.call(this, silent);\n    triggerUpdatedEvent.call(this, silent);\n  };\n  ECharts.prototype.updateLabelLayout = function () {\n    lifecycle.trigger('series:layoutlabels', this._model, this._api, {\n      // Not adding series labels.\n      // TODO\n      updatedSeries: []\n    });\n  };\n  ECharts.prototype.appendData = function (params) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var seriesIndex = params.seriesIndex;\n    var ecModel = this.getModel();\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    if (process.env.NODE_ENV !== 'production') {\n      assert(params.data && seriesModel);\n    }\n    seriesModel.appendData(params);\n    // Note: `appendData` does not support that update extent of coordinate\n    // system, util some scenario require that. In the expected usage of\n    // `appendData`, the initial extent of coordinate system should better\n    // be fixed by axis `min`/`max` setting or initial data, otherwise if\n    // the extent changed while `appendData`, the location of the painted\n    // graphic elements have to be changed, which make the usage of\n    // `appendData` meaningless.\n    this._scheduler.unfinished = true;\n    this.getZr().wakeUp();\n  };\n  // A work around for no `internal` modifier in ts yet but\n  // need to strictly hide private methods to JS users.\n  ECharts.internalField = function () {\n    prepare = function (ecIns) {\n      var scheduler = ecIns._scheduler;\n      scheduler.restorePipelines(ecIns._model);\n      scheduler.prepareStageTasks();\n      prepareView(ecIns, true);\n      prepareView(ecIns, false);\n      scheduler.plan();\n    };\n    /**\n     * Prepare view instances of charts and components\n     */\n    prepareView = function (ecIns, isComponent) {\n      var ecModel = ecIns._model;\n      var scheduler = ecIns._scheduler;\n      var viewList = isComponent ? ecIns._componentsViews : ecIns._chartsViews;\n      var viewMap = isComponent ? ecIns._componentsMap : ecIns._chartsMap;\n      var zr = ecIns._zr;\n      var api = ecIns._api;\n      for (var i = 0; i < viewList.length; i++) {\n        viewList[i].__alive = false;\n      }\n      isComponent ? ecModel.eachComponent(function (componentType, model) {\n        componentType !== 'series' && doPrepare(model);\n      }) : ecModel.eachSeries(doPrepare);\n      function doPrepare(model) {\n        // By default view will be reused if possible for the case that `setOption` with \"notMerge\"\n        // mode and need to enable transition animation. (Usually, when they have the same id, or\n        // especially no id but have the same type & name & index. See the `model.id` generation\n        // rule in `makeIdAndName` and `viewId` generation rule here).\n        // But in `replaceMerge` mode, this feature should be able to disabled when it is clear that\n        // the new model has nothing to do with the old model.\n        var requireNewView = model.__requireNewView;\n        // This command should not work twice.\n        model.__requireNewView = false;\n        // Consider: id same and type changed.\n        var viewId = '_ec_' + model.id + '_' + model.type;\n        var view = !requireNewView && viewMap[viewId];\n        if (!view) {\n          var classType = parseClassType(model.type);\n          var Clazz = isComponent ? ComponentView.getClass(classType.main, classType.sub) :\n          // FIXME:TS\n          // (ChartView as ChartViewConstructor).getClass('series', classType.sub)\n          // For backward compat, still support a chart type declared as only subType\n          // like \"liquidfill\", but recommend \"series.liquidfill\"\n          // But need a base class to make a type series.\n          ChartView.getClass(classType.sub);\n          if (process.env.NODE_ENV !== 'production') {\n            assert(Clazz, classType.sub + ' does not exist.');\n          }\n          view = new Clazz();\n          view.init(ecModel, api);\n          viewMap[viewId] = view;\n          viewList.push(view);\n          zr.add(view.group);\n        }\n        model.__viewId = view.__id = viewId;\n        view.__alive = true;\n        view.__model = model;\n        view.group.__ecComponentInfo = {\n          mainType: model.mainType,\n          index: model.componentIndex\n        };\n        !isComponent && scheduler.prepareView(view, model, ecModel, api);\n      }\n      for (var i = 0; i < viewList.length;) {\n        var view = viewList[i];\n        if (!view.__alive) {\n          !isComponent && view.renderTask.dispose();\n          zr.remove(view.group);\n          view.dispose(ecModel, api);\n          viewList.splice(i, 1);\n          if (viewMap[view.__id] === view) {\n            delete viewMap[view.__id];\n          }\n          view.__id = view.group.__ecComponentInfo = null;\n        } else {\n          i++;\n        }\n      }\n    };\n    updateDirectly = function (ecIns, method, payload, mainType, subType) {\n      var ecModel = ecIns._model;\n      ecModel.setUpdatePayload(payload);\n      // broadcast\n      if (!mainType) {\n        // FIXME\n        // Chart will not be update directly here, except set dirty.\n        // But there is no such scenario now.\n        each([].concat(ecIns._componentsViews).concat(ecIns._chartsViews), callView);\n        return;\n      }\n      var query = {};\n      query[mainType + 'Id'] = payload[mainType + 'Id'];\n      query[mainType + 'Index'] = payload[mainType + 'Index'];\n      query[mainType + 'Name'] = payload[mainType + 'Name'];\n      var condition = {\n        mainType: mainType,\n        query: query\n      };\n      subType && (condition.subType = subType); // subType may be '' by parseClassType;\n      var excludeSeriesId = payload.excludeSeriesId;\n      var excludeSeriesIdMap;\n      if (excludeSeriesId != null) {\n        excludeSeriesIdMap = createHashMap();\n        each(modelUtil.normalizeToArray(excludeSeriesId), function (id) {\n          var modelId = modelUtil.convertOptionIdName(id, null);\n          if (modelId != null) {\n            excludeSeriesIdMap.set(modelId, true);\n          }\n        });\n      }\n      // If dispatchAction before setOption, do nothing.\n      ecModel && ecModel.eachComponent(condition, function (model) {\n        var isExcluded = excludeSeriesIdMap && excludeSeriesIdMap.get(model.id) != null;\n        if (isExcluded) {\n          return;\n        }\n        ;\n        if (isHighDownPayload(payload)) {\n          if (model instanceof SeriesModel) {\n            if (payload.type === HIGHLIGHT_ACTION_TYPE && !payload.notBlur && !model.get(['emphasis', 'disabled'])) {\n              blurSeriesFromHighlightPayload(model, payload, ecIns._api);\n            }\n          } else {\n            var _a = findComponentHighDownDispatchers(model.mainType, model.componentIndex, payload.name, ecIns._api),\n              focusSelf = _a.focusSelf,\n              dispatchers = _a.dispatchers;\n            if (payload.type === HIGHLIGHT_ACTION_TYPE && focusSelf && !payload.notBlur) {\n              blurComponent(model.mainType, model.componentIndex, ecIns._api);\n            }\n            // PENDING:\n            // Whether to put this \"enter emphasis\" code in `ComponentView`,\n            // which will be the same as `ChartView` but might be not necessary\n            // and will be far from this logic.\n            if (dispatchers) {\n              each(dispatchers, function (dispatcher) {\n                payload.type === HIGHLIGHT_ACTION_TYPE ? enterEmphasis(dispatcher) : leaveEmphasis(dispatcher);\n              });\n            }\n          }\n        } else if (isSelectChangePayload(payload)) {\n          // TODO geo\n          if (model instanceof SeriesModel) {\n            toggleSelectionFromPayload(model, payload, ecIns._api);\n            updateSeriesElementSelection(model);\n            markStatusToUpdate(ecIns);\n          }\n        }\n      }, ecIns);\n      ecModel && ecModel.eachComponent(condition, function (model) {\n        var isExcluded = excludeSeriesIdMap && excludeSeriesIdMap.get(model.id) != null;\n        if (isExcluded) {\n          return;\n        }\n        ;\n        callView(ecIns[mainType === 'series' ? '_chartsMap' : '_componentsMap'][model.__viewId]);\n      }, ecIns);\n      function callView(view) {\n        view && view.__alive && view[method] && view[method](view.__model, ecModel, ecIns._api, payload);\n      }\n    };\n    updateMethods = {\n      prepareAndUpdate: function (payload) {\n        prepare(this);\n        updateMethods.update.call(this, payload, {\n          // Needs to mark option changed if newOption is given.\n          // It's from MagicType.\n          // TODO If use a separate flag optionChanged in payload?\n          optionChanged: payload.newOption != null\n        });\n      },\n      update: function (payload, updateParams) {\n        var ecModel = this._model;\n        var api = this._api;\n        var zr = this._zr;\n        var coordSysMgr = this._coordSysMgr;\n        var scheduler = this._scheduler;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        scheduler.restoreData(ecModel, payload);\n        scheduler.performSeriesTasks(ecModel);\n        // TODO\n        // Save total ecModel here for undo/redo (after restoring data and before processing data).\n        // Undo (restoration of total ecModel) can be carried out in 'action' or outside API call.\n        // Create new coordinate system each update\n        // In LineView may save the old coordinate system and use it to get the original point.\n        coordSysMgr.create(ecModel, api);\n        scheduler.performDataProcessorTasks(ecModel, payload);\n        // Current stream render is not supported in data process. So we can update\n        // stream modes after data processing, where the filtered data is used to\n        // determine whether to use progressive rendering.\n        updateStreamModes(this, ecModel);\n        // We update stream modes before coordinate system updated, then the modes info\n        // can be fetched when coord sys updating (consider the barGrid extent fix). But\n        // the drawback is the full coord info can not be fetched. Fortunately this full\n        // coord is not required in stream mode updater currently.\n        coordSysMgr.update(ecModel, api);\n        clearColorPalette(ecModel);\n        scheduler.performVisualTasks(ecModel, payload);\n        render(this, ecModel, api, payload, updateParams);\n        // Set background\n        var backgroundColor = ecModel.get('backgroundColor') || 'transparent';\n        var darkMode = ecModel.get('darkMode');\n        zr.setBackgroundColor(backgroundColor);\n        // Force set dark mode.\n        if (darkMode != null && darkMode !== 'auto') {\n          zr.setDarkMode(darkMode);\n        }\n        lifecycle.trigger('afterupdate', ecModel, api);\n      },\n      updateTransform: function (payload) {\n        var _this = this;\n        var ecModel = this._model;\n        var api = this._api;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        // ChartView.markUpdateMethod(payload, 'updateTransform');\n        var componentDirtyList = [];\n        ecModel.eachComponent(function (componentType, componentModel) {\n          if (componentType === 'series') {\n            return;\n          }\n          var componentView = _this.getViewOfComponentModel(componentModel);\n          if (componentView && componentView.__alive) {\n            if (componentView.updateTransform) {\n              var result = componentView.updateTransform(componentModel, ecModel, api, payload);\n              result && result.update && componentDirtyList.push(componentView);\n            } else {\n              componentDirtyList.push(componentView);\n            }\n          }\n        });\n        var seriesDirtyMap = createHashMap();\n        ecModel.eachSeries(function (seriesModel) {\n          var chartView = _this._chartsMap[seriesModel.__viewId];\n          if (chartView.updateTransform) {\n            var result = chartView.updateTransform(seriesModel, ecModel, api, payload);\n            result && result.update && seriesDirtyMap.set(seriesModel.uid, 1);\n          } else {\n            seriesDirtyMap.set(seriesModel.uid, 1);\n          }\n        });\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        // this._scheduler.performVisualTasks(ecModel, payload, 'layout', true);\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          setDirty: true,\n          dirtyMap: seriesDirtyMap\n        });\n        // Currently, not call render of components. Geo render cost a lot.\n        // renderComponents(ecIns, ecModel, api, payload, componentDirtyList);\n        renderSeries(this, ecModel, api, payload, {}, seriesDirtyMap);\n        lifecycle.trigger('afterupdate', ecModel, api);\n      },\n      updateView: function (payload) {\n        var ecModel = this._model;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        ChartView.markUpdateMethod(payload, 'updateView');\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          setDirty: true\n        });\n        render(this, ecModel, this._api, payload, {});\n        lifecycle.trigger('afterupdate', ecModel, this._api);\n      },\n      updateVisual: function (payload) {\n        // updateMethods.update.call(this, payload);\n        var _this = this;\n        var ecModel = this._model;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        // clear all visual\n        ecModel.eachSeries(function (seriesModel) {\n          seriesModel.getData().clearAllVisual();\n        });\n        // Perform visual\n        ChartView.markUpdateMethod(payload, 'updateVisual');\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          visualType: 'visual',\n          setDirty: true\n        });\n        ecModel.eachComponent(function (componentType, componentModel) {\n          if (componentType !== 'series') {\n            var componentView = _this.getViewOfComponentModel(componentModel);\n            componentView && componentView.__alive && componentView.updateVisual(componentModel, ecModel, _this._api, payload);\n          }\n        });\n        ecModel.eachSeries(function (seriesModel) {\n          var chartView = _this._chartsMap[seriesModel.__viewId];\n          chartView.updateVisual(seriesModel, ecModel, _this._api, payload);\n        });\n        lifecycle.trigger('afterupdate', ecModel, this._api);\n      },\n      updateLayout: function (payload) {\n        updateMethods.update.call(this, payload);\n      }\n    };\n    doConvertPixel = function (ecIns, methodName, finder, value) {\n      if (ecIns._disposed) {\n        disposedWarning(ecIns.id);\n        return;\n      }\n      var ecModel = ecIns._model;\n      var coordSysList = ecIns._coordSysMgr.getCoordinateSystems();\n      var result;\n      var parsedFinder = modelUtil.parseFinder(ecModel, finder);\n      for (var i = 0; i < coordSysList.length; i++) {\n        var coordSys = coordSysList[i];\n        if (coordSys[methodName] && (result = coordSys[methodName](ecModel, parsedFinder, value)) != null) {\n          return result;\n        }\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        warn('No coordinate system that supports ' + methodName + ' found by the given finder.');\n      }\n    };\n    updateStreamModes = function (ecIns, ecModel) {\n      var chartsMap = ecIns._chartsMap;\n      var scheduler = ecIns._scheduler;\n      ecModel.eachSeries(function (seriesModel) {\n        scheduler.updateStreamModes(seriesModel, chartsMap[seriesModel.__viewId]);\n      });\n    };\n    doDispatchAction = function (payload, silent) {\n      var _this = this;\n      var ecModel = this.getModel();\n      var payloadType = payload.type;\n      var escapeConnect = payload.escapeConnect;\n      var actionWrap = actions[payloadType];\n      var actionInfo = actionWrap.actionInfo;\n      var cptTypeTmp = (actionInfo.update || 'update').split(':');\n      var updateMethod = cptTypeTmp.pop();\n      var cptType = cptTypeTmp[0] != null && parseClassType(cptTypeTmp[0]);\n      this[IN_MAIN_PROCESS_KEY] = true;\n      var payloads = [payload];\n      var batched = false;\n      // Batch action\n      if (payload.batch) {\n        batched = true;\n        payloads = map(payload.batch, function (item) {\n          item = defaults(extend({}, item), payload);\n          item.batch = null;\n          return item;\n        });\n      }\n      var eventObjBatch = [];\n      var eventObj;\n      var isSelectChange = isSelectChangePayload(payload);\n      var isHighDown = isHighDownPayload(payload);\n      // Only leave blur once if there are multiple batches.\n      if (isHighDown) {\n        allLeaveBlur(this._api);\n      }\n      each(payloads, function (batchItem) {\n        // Action can specify the event by return it.\n        eventObj = actionWrap.action(batchItem, _this._model, _this._api);\n        // Emit event outside\n        eventObj = eventObj || extend({}, batchItem);\n        // Convert type to eventType\n        eventObj.type = actionInfo.event || eventObj.type;\n        eventObjBatch.push(eventObj);\n        // light update does not perform data process, layout and visual.\n        if (isHighDown) {\n          var _a = modelUtil.preParseFinder(payload),\n            queryOptionMap = _a.queryOptionMap,\n            mainTypeSpecified = _a.mainTypeSpecified;\n          var componentMainType = mainTypeSpecified ? queryOptionMap.keys()[0] : 'series';\n          updateDirectly(_this, updateMethod, batchItem, componentMainType);\n          markStatusToUpdate(_this);\n        } else if (isSelectChange) {\n          // At present `dispatchAction({ type: 'select', ... })` is not supported on components.\n          // geo still use 'geoselect'.\n          updateDirectly(_this, updateMethod, batchItem, 'series');\n          markStatusToUpdate(_this);\n        } else if (cptType) {\n          updateDirectly(_this, updateMethod, batchItem, cptType.main, cptType.sub);\n        }\n      });\n      if (updateMethod !== 'none' && !isHighDown && !isSelectChange && !cptType) {\n        try {\n          // Still dirty\n          if (this[PENDING_UPDATE]) {\n            prepare(this);\n            updateMethods.update.call(this, payload);\n            this[PENDING_UPDATE] = null;\n          } else {\n            updateMethods[updateMethod].call(this, payload);\n          }\n        } catch (e) {\n          this[IN_MAIN_PROCESS_KEY] = false;\n          throw e;\n        }\n      }\n      // Follow the rule of action batch\n      if (batched) {\n        eventObj = {\n          type: actionInfo.event || payloadType,\n          escapeConnect: escapeConnect,\n          batch: eventObjBatch\n        };\n      } else {\n        eventObj = eventObjBatch[0];\n      }\n      this[IN_MAIN_PROCESS_KEY] = false;\n      if (!silent) {\n        var messageCenter = this._messageCenter;\n        messageCenter.trigger(eventObj.type, eventObj);\n        // Extra triggered 'selectchanged' event\n        if (isSelectChange) {\n          var newObj = {\n            type: 'selectchanged',\n            escapeConnect: escapeConnect,\n            selected: getAllSelectedIndices(ecModel),\n            isFromClick: payload.isFromClick || false,\n            fromAction: payload.type,\n            fromActionPayload: payload\n          };\n          messageCenter.trigger(newObj.type, newObj);\n        }\n      }\n    };\n    flushPendingActions = function (silent) {\n      var pendingActions = this._pendingActions;\n      while (pendingActions.length) {\n        var payload = pendingActions.shift();\n        doDispatchAction.call(this, payload, silent);\n      }\n    };\n    triggerUpdatedEvent = function (silent) {\n      !silent && this.trigger('updated');\n    };\n    /**\n     * Event `rendered` is triggered when zr\n     * rendered. It is useful for realtime\n     * snapshot (reflect animation).\n     *\n     * Event `finished` is triggered when:\n     * (1) zrender rendering finished.\n     * (2) initial animation finished.\n     * (3) progressive rendering finished.\n     * (4) no pending action.\n     * (5) no delayed setOption needs to be processed.\n     */\n    bindRenderedEvent = function (zr, ecIns) {\n      zr.on('rendered', function (params) {\n        ecIns.trigger('rendered', params);\n        // The `finished` event should not be triggered repeatedly,\n        // so it should only be triggered when rendering indeed happens\n        // in zrender. (Consider the case that dipatchAction is keep\n        // triggering when mouse move).\n        if (\n        // Although zr is dirty if initial animation is not finished\n        // and this checking is called on frame, we also check\n        // animation finished for robustness.\n        zr.animation.isFinished() && !ecIns[PENDING_UPDATE] && !ecIns._scheduler.unfinished && !ecIns._pendingActions.length) {\n          ecIns.trigger('finished');\n        }\n      });\n    };\n    bindMouseEvent = function (zr, ecIns) {\n      zr.on('mouseover', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, isHighDownDispatcher);\n        if (dispatcher) {\n          handleGlobalMouseOverForHighDown(dispatcher, e, ecIns._api);\n          markStatusToUpdate(ecIns);\n        }\n      }).on('mouseout', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, isHighDownDispatcher);\n        if (dispatcher) {\n          handleGlobalMouseOutForHighDown(dispatcher, e, ecIns._api);\n          markStatusToUpdate(ecIns);\n        }\n      }).on('click', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, function (target) {\n          return getECData(target).dataIndex != null;\n        }, true);\n        if (dispatcher) {\n          var actionType = dispatcher.selected ? 'unselect' : 'select';\n          var ecData = getECData(dispatcher);\n          ecIns._api.dispatchAction({\n            type: actionType,\n            dataType: ecData.dataType,\n            dataIndexInside: ecData.dataIndex,\n            seriesIndex: ecData.seriesIndex,\n            isFromClick: true\n          });\n        }\n      });\n    };\n    function clearColorPalette(ecModel) {\n      ecModel.clearColorPalette();\n      ecModel.eachSeries(function (seriesModel) {\n        seriesModel.clearColorPalette();\n      });\n    }\n    ;\n    // Allocate zlevels for series and components\n    function allocateZlevels(ecModel) {\n      ;\n      var componentZLevels = [];\n      var seriesZLevels = [];\n      var hasSeparateZLevel = false;\n      ecModel.eachComponent(function (componentType, componentModel) {\n        var zlevel = componentModel.get('zlevel') || 0;\n        var z = componentModel.get('z') || 0;\n        var zlevelKey = componentModel.getZLevelKey();\n        hasSeparateZLevel = hasSeparateZLevel || !!zlevelKey;\n        (componentType === 'series' ? seriesZLevels : componentZLevels).push({\n          zlevel: zlevel,\n          z: z,\n          idx: componentModel.componentIndex,\n          type: componentType,\n          key: zlevelKey\n        });\n      });\n      if (hasSeparateZLevel) {\n        // Series after component\n        var zLevels = componentZLevels.concat(seriesZLevels);\n        var lastSeriesZLevel_1;\n        var lastSeriesKey_1;\n        timsort(zLevels, function (a, b) {\n          if (a.zlevel === b.zlevel) {\n            return a.z - b.z;\n          }\n          return a.zlevel - b.zlevel;\n        });\n        each(zLevels, function (item) {\n          var componentModel = ecModel.getComponent(item.type, item.idx);\n          var zlevel = item.zlevel;\n          var key = item.key;\n          if (lastSeriesZLevel_1 != null) {\n            zlevel = Math.max(lastSeriesZLevel_1, zlevel);\n          }\n          if (key) {\n            if (zlevel === lastSeriesZLevel_1 && key !== lastSeriesKey_1) {\n              zlevel++;\n            }\n            lastSeriesKey_1 = key;\n          } else if (lastSeriesKey_1) {\n            if (zlevel === lastSeriesZLevel_1) {\n              zlevel++;\n            }\n            lastSeriesKey_1 = '';\n          }\n          lastSeriesZLevel_1 = zlevel;\n          componentModel.setZLevel(zlevel);\n        });\n      }\n    }\n    render = function (ecIns, ecModel, api, payload, updateParams) {\n      allocateZlevels(ecModel);\n      renderComponents(ecIns, ecModel, api, payload, updateParams);\n      each(ecIns._chartsViews, function (chart) {\n        chart.__alive = false;\n      });\n      renderSeries(ecIns, ecModel, api, payload, updateParams);\n      // Remove groups of unrendered charts\n      each(ecIns._chartsViews, function (chart) {\n        if (!chart.__alive) {\n          chart.remove(ecModel, api);\n        }\n      });\n    };\n    renderComponents = function (ecIns, ecModel, api, payload, updateParams, dirtyList) {\n      each(dirtyList || ecIns._componentsViews, function (componentView) {\n        var componentModel = componentView.__model;\n        clearStates(componentModel, componentView);\n        componentView.render(componentModel, ecModel, api, payload);\n        updateZ(componentModel, componentView);\n        updateStates(componentModel, componentView);\n      });\n    };\n    /**\n     * Render each chart and component\n     */\n    renderSeries = function (ecIns, ecModel, api, payload, updateParams, dirtyMap) {\n      // Render all charts\n      var scheduler = ecIns._scheduler;\n      updateParams = extend(updateParams || {}, {\n        updatedSeries: ecModel.getSeries()\n      });\n      // TODO progressive?\n      lifecycle.trigger('series:beforeupdate', ecModel, api, updateParams);\n      var unfinished = false;\n      ecModel.eachSeries(function (seriesModel) {\n        var chartView = ecIns._chartsMap[seriesModel.__viewId];\n        chartView.__alive = true;\n        var renderTask = chartView.renderTask;\n        scheduler.updatePayload(renderTask, payload);\n        // TODO states on marker.\n        clearStates(seriesModel, chartView);\n        if (dirtyMap && dirtyMap.get(seriesModel.uid)) {\n          renderTask.dirty();\n        }\n        if (renderTask.perform(scheduler.getPerformArgs(renderTask))) {\n          unfinished = true;\n        }\n        chartView.group.silent = !!seriesModel.get('silent');\n        // Should not call markRedraw on group, because it will disable zrender\n        // incremental render (always render from the __startIndex each frame)\n        // chartView.group.markRedraw();\n        updateBlend(seriesModel, chartView);\n        updateSeriesElementSelection(seriesModel);\n      });\n      scheduler.unfinished = unfinished || scheduler.unfinished;\n      lifecycle.trigger('series:layoutlabels', ecModel, api, updateParams);\n      // transition after label is layouted.\n      lifecycle.trigger('series:transition', ecModel, api, updateParams);\n      ecModel.eachSeries(function (seriesModel) {\n        var chartView = ecIns._chartsMap[seriesModel.__viewId];\n        // Update Z after labels updated. Before applying states.\n        updateZ(seriesModel, chartView);\n        // NOTE: Update states after label is updated.\n        // label should be in normal status when layouting.\n        updateStates(seriesModel, chartView);\n      });\n      // If use hover layer\n      updateHoverLayerStatus(ecIns, ecModel);\n      lifecycle.trigger('series:afterupdate', ecModel, api, updateParams);\n    };\n    markStatusToUpdate = function (ecIns) {\n      ecIns[STATUS_NEEDS_UPDATE_KEY] = true;\n      // Wake up zrender if it's sleep. Let it update states in the next frame.\n      ecIns.getZr().wakeUp();\n    };\n    applyChangedStates = function (ecIns) {\n      if (!ecIns[STATUS_NEEDS_UPDATE_KEY]) {\n        return;\n      }\n      ecIns.getZr().storage.traverse(function (el) {\n        // Not applied on removed elements, it may still in fading.\n        if (graphic.isElementRemoved(el)) {\n          return;\n        }\n        applyElementStates(el);\n      });\n      ecIns[STATUS_NEEDS_UPDATE_KEY] = false;\n    };\n    function applyElementStates(el) {\n      var newStates = [];\n      var oldStates = el.currentStates;\n      // Keep other states.\n      for (var i = 0; i < oldStates.length; i++) {\n        var stateName = oldStates[i];\n        if (!(stateName === 'emphasis' || stateName === 'blur' || stateName === 'select')) {\n          newStates.push(stateName);\n        }\n      }\n      // Only use states when it's exists.\n      if (el.selected && el.states.select) {\n        newStates.push('select');\n      }\n      if (el.hoverState === HOVER_STATE_EMPHASIS && el.states.emphasis) {\n        newStates.push('emphasis');\n      } else if (el.hoverState === HOVER_STATE_BLUR && el.states.blur) {\n        newStates.push('blur');\n      }\n      el.useStates(newStates);\n    }\n    function updateHoverLayerStatus(ecIns, ecModel) {\n      var zr = ecIns._zr;\n      var storage = zr.storage;\n      var elCount = 0;\n      storage.traverse(function (el) {\n        if (!el.isGroup) {\n          elCount++;\n        }\n      });\n      if (elCount > ecModel.get('hoverLayerThreshold') && !env.node && !env.worker) {\n        ecModel.eachSeries(function (seriesModel) {\n          if (seriesModel.preventUsingHoverLayer) {\n            return;\n          }\n          var chartView = ecIns._chartsMap[seriesModel.__viewId];\n          if (chartView.__alive) {\n            chartView.eachRendered(function (el) {\n              if (el.states.emphasis) {\n                el.states.emphasis.hoverLayer = true;\n              }\n            });\n          }\n        });\n      }\n    }\n    ;\n    /**\n     * Update chart and blend.\n     */\n    function updateBlend(seriesModel, chartView) {\n      var blendMode = seriesModel.get('blendMode') || null;\n      chartView.eachRendered(function (el) {\n        // FIXME marker and other components\n        if (!el.isGroup) {\n          // DON'T mark the element dirty. In case element is incremental and don't want to rerender.\n          el.style.blend = blendMode;\n        }\n      });\n    }\n    ;\n    function updateZ(model, view) {\n      if (model.preventAutoZ) {\n        return;\n      }\n      var z = model.get('z') || 0;\n      var zlevel = model.get('zlevel') || 0;\n      // Set z and zlevel\n      view.eachRendered(function (el) {\n        doUpdateZ(el, z, zlevel, -Infinity);\n        // Don't traverse the children because it has been traversed in _updateZ.\n        return true;\n      });\n    }\n    ;\n    function doUpdateZ(el, z, zlevel, maxZ2) {\n      // Group may also have textContent\n      var label = el.getTextContent();\n      var labelLine = el.getTextGuideLine();\n      var isGroup = el.isGroup;\n      if (isGroup) {\n        // set z & zlevel of children elements of Group\n        var children = el.childrenRef();\n        for (var i = 0; i < children.length; i++) {\n          maxZ2 = Math.max(doUpdateZ(children[i], z, zlevel, maxZ2), maxZ2);\n        }\n      } else {\n        // not Group\n        el.z = z;\n        el.zlevel = zlevel;\n        maxZ2 = Math.max(el.z2, maxZ2);\n      }\n      // always set z and zlevel if label/labelLine exists\n      if (label) {\n        label.z = z;\n        label.zlevel = zlevel;\n        // lift z2 of text content\n        // TODO if el.emphasis.z2 is spcefied, what about textContent.\n        isFinite(maxZ2) && (label.z2 = maxZ2 + 2);\n      }\n      if (labelLine) {\n        var textGuideLineConfig = el.textGuideLineConfig;\n        labelLine.z = z;\n        labelLine.zlevel = zlevel;\n        isFinite(maxZ2) && (labelLine.z2 = maxZ2 + (textGuideLineConfig && textGuideLineConfig.showAbove ? 1 : -1));\n      }\n      return maxZ2;\n    }\n    // Clear states without animation.\n    // TODO States on component.\n    function clearStates(model, view) {\n      view.eachRendered(function (el) {\n        // Not applied on removed elements, it may still in fading.\n        if (graphic.isElementRemoved(el)) {\n          return;\n        }\n        var textContent = el.getTextContent();\n        var textGuide = el.getTextGuideLine();\n        if (el.stateTransition) {\n          el.stateTransition = null;\n        }\n        if (textContent && textContent.stateTransition) {\n          textContent.stateTransition = null;\n        }\n        if (textGuide && textGuide.stateTransition) {\n          textGuide.stateTransition = null;\n        }\n        // TODO If el is incremental.\n        if (el.hasState()) {\n          el.prevStates = el.currentStates;\n          el.clearStates();\n        } else if (el.prevStates) {\n          el.prevStates = null;\n        }\n      });\n    }\n    function updateStates(model, view) {\n      var stateAnimationModel = model.getModel('stateAnimation');\n      var enableAnimation = model.isAnimationEnabled();\n      var duration = stateAnimationModel.get('duration');\n      var stateTransition = duration > 0 ? {\n        duration: duration,\n        delay: stateAnimationModel.get('delay'),\n        easing: stateAnimationModel.get('easing')\n        // additive: stateAnimationModel.get('additive')\n      } : null;\n      view.eachRendered(function (el) {\n        if (el.states && el.states.emphasis) {\n          // Not applied on removed elements, it may still in fading.\n          if (graphic.isElementRemoved(el)) {\n            return;\n          }\n          if (el instanceof graphic.Path) {\n            savePathStates(el);\n          }\n          // Only updated on changed element. In case element is incremental and don't want to rerender.\n          // TODO, a more proper way?\n          if (el.__dirty) {\n            var prevStates = el.prevStates;\n            // Restore states without animation\n            if (prevStates) {\n              el.useStates(prevStates);\n            }\n          }\n          // Update state transition and enable animation again.\n          if (enableAnimation) {\n            el.stateTransition = stateTransition;\n            var textContent = el.getTextContent();\n            var textGuide = el.getTextGuideLine();\n            // TODO Is it necessary to animate label?\n            if (textContent) {\n              textContent.stateTransition = stateTransition;\n            }\n            if (textGuide) {\n              textGuide.stateTransition = stateTransition;\n            }\n          }\n          // Use highlighted and selected flag to toggle states.\n          if (el.__dirty) {\n            applyElementStates(el);\n          }\n        }\n      });\n    }\n    ;\n    createExtensionAPI = function (ecIns) {\n      return new ( /** @class */function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n          return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.getCoordinateSystems = function () {\n          return ecIns._coordSysMgr.getCoordinateSystems();\n        };\n        class_1.prototype.getComponentByElement = function (el) {\n          while (el) {\n            var modelInfo = el.__ecComponentInfo;\n            if (modelInfo != null) {\n              return ecIns._model.getComponent(modelInfo.mainType, modelInfo.index);\n            }\n            el = el.parent;\n          }\n        };\n        class_1.prototype.enterEmphasis = function (el, highlightDigit) {\n          enterEmphasis(el, highlightDigit);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveEmphasis = function (el, highlightDigit) {\n          leaveEmphasis(el, highlightDigit);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.enterBlur = function (el) {\n          enterBlur(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveBlur = function (el) {\n          leaveBlur(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.enterSelect = function (el) {\n          enterSelect(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveSelect = function (el) {\n          leaveSelect(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.getModel = function () {\n          return ecIns.getModel();\n        };\n        class_1.prototype.getViewOfComponentModel = function (componentModel) {\n          return ecIns.getViewOfComponentModel(componentModel);\n        };\n        class_1.prototype.getViewOfSeriesModel = function (seriesModel) {\n          return ecIns.getViewOfSeriesModel(seriesModel);\n        };\n        return class_1;\n      }(ExtensionAPI))(ecIns);\n    };\n    enableConnect = function (chart) {\n      function updateConnectedChartsStatus(charts, status) {\n        for (var i = 0; i < charts.length; i++) {\n          var otherChart = charts[i];\n          otherChart[CONNECT_STATUS_KEY] = status;\n        }\n      }\n      each(eventActionMap, function (actionType, eventType) {\n        chart._messageCenter.on(eventType, function (event) {\n          if (connectedGroups[chart.group] && chart[CONNECT_STATUS_KEY] !== CONNECT_STATUS_PENDING) {\n            if (event && event.escapeConnect) {\n              return;\n            }\n            var action_1 = chart.makeActionFromEvent(event);\n            var otherCharts_1 = [];\n            each(instances, function (otherChart) {\n              if (otherChart !== chart && otherChart.group === chart.group) {\n                otherCharts_1.push(otherChart);\n              }\n            });\n            updateConnectedChartsStatus(otherCharts_1, CONNECT_STATUS_PENDING);\n            each(otherCharts_1, function (otherChart) {\n              if (otherChart[CONNECT_STATUS_KEY] !== CONNECT_STATUS_UPDATING) {\n                otherChart.dispatchAction(action_1);\n              }\n            });\n            updateConnectedChartsStatus(otherCharts_1, CONNECT_STATUS_UPDATED);\n          }\n        });\n      });\n    };\n  }();\n  return ECharts;\n}(Eventful);\nvar echartsProto = ECharts.prototype;\nechartsProto.on = createRegisterEventWithLowercaseECharts('on');\nechartsProto.off = createRegisterEventWithLowercaseECharts('off');\n/**\n * @deprecated\n */\n// @ts-ignore\nechartsProto.one = function (eventName, cb, ctx) {\n  var self = this;\n  deprecateLog('ECharts#one is deprecated.');\n  function wrapped() {\n    var args2 = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args2[_i] = arguments[_i];\n    }\n    cb && cb.apply && cb.apply(this, args2);\n    // @ts-ignore\n    self.off(eventName, wrapped);\n  }\n  ;\n  // @ts-ignore\n  this.on.call(this, eventName, wrapped, ctx);\n};\nvar MOUSE_EVENT_NAMES = ['click', 'dblclick', 'mouseover', 'mouseout', 'mousemove', 'mousedown', 'mouseup', 'globalout', 'contextmenu'];\nfunction disposedWarning(id) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn('Instance ' + id + ' has been disposed');\n  }\n}\nvar actions = {};\n/**\n * Map eventType to actionType\n */\nvar eventActionMap = {};\nvar dataProcessorFuncs = [];\nvar optionPreprocessorFuncs = [];\nvar visualFuncs = [];\nvar themeStorage = {};\nvar loadingEffects = {};\nvar instances = {};\nvar connectedGroups = {};\nvar idBase = +new Date() - 0;\nvar groupIdBase = +new Date() - 0;\nvar DOM_ATTRIBUTE_KEY = '_echarts_instance_';\n/**\n * @param opts.devicePixelRatio Use window.devicePixelRatio by default\n * @param opts.renderer Can choose 'canvas' or 'svg' to render the chart.\n * @param opts.width Use clientWidth of the input `dom` by default.\n *        Can be 'auto' (the same as null/undefined)\n * @param opts.height Use clientHeight of the input `dom` by default.\n *        Can be 'auto' (the same as null/undefined)\n * @param opts.locale Specify the locale.\n * @param opts.useDirtyRect Enable dirty rectangle rendering or not.\n */\nexport function init(dom, theme, opts) {\n  var isClient = !(opts && opts.ssr);\n  if (isClient) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!dom) {\n        throw new Error('Initialize failed: invalid dom.');\n      }\n    }\n    var existInstance = getInstanceByDom(dom);\n    if (existInstance) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('There is a chart instance already initialized on the dom.');\n      }\n      return existInstance;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isDom(dom) && dom.nodeName.toUpperCase() !== 'CANVAS' && (!dom.clientWidth && (!opts || opts.width == null) || !dom.clientHeight && (!opts || opts.height == null))) {\n        warn('Can\\'t get DOM width or height. Please check ' + 'dom.clientWidth and dom.clientHeight. They should not be 0.' + 'For example, you may need to call this in the callback ' + 'of window.onload.');\n      }\n    }\n  }\n  var chart = new ECharts(dom, theme, opts);\n  chart.id = 'ec_' + idBase++;\n  instances[chart.id] = chart;\n  isClient && modelUtil.setAttribute(dom, DOM_ATTRIBUTE_KEY, chart.id);\n  enableConnect(chart);\n  lifecycle.trigger('afterinit', chart);\n  return chart;\n}\n/**\n * @usage\n * (A)\n * ```js\n * let chart1 = echarts.init(dom1);\n * let chart2 = echarts.init(dom2);\n * chart1.group = 'xxx';\n * chart2.group = 'xxx';\n * echarts.connect('xxx');\n * ```\n * (B)\n * ```js\n * let chart1 = echarts.init(dom1);\n * let chart2 = echarts.init(dom2);\n * echarts.connect('xxx', [chart1, chart2]);\n * ```\n */\nexport function connect(groupId) {\n  // Is array of charts\n  if (isArray(groupId)) {\n    var charts = groupId;\n    groupId = null;\n    // If any chart has group\n    each(charts, function (chart) {\n      if (chart.group != null) {\n        groupId = chart.group;\n      }\n    });\n    groupId = groupId || 'g_' + groupIdBase++;\n    each(charts, function (chart) {\n      chart.group = groupId;\n    });\n  }\n  connectedGroups[groupId] = true;\n  return groupId;\n}\nexport function disconnect(groupId) {\n  connectedGroups[groupId] = false;\n}\n/**\n * Alias and backward compatibility\n * @deprecated\n */\nexport var disConnect = disconnect;\n/**\n * Dispose a chart instance\n */\nexport function dispose(chart) {\n  if (isString(chart)) {\n    chart = instances[chart];\n  } else if (!(chart instanceof ECharts)) {\n    // Try to treat as dom\n    chart = getInstanceByDom(chart);\n  }\n  if (chart instanceof ECharts && !chart.isDisposed()) {\n    chart.dispose();\n  }\n}\nexport function getInstanceByDom(dom) {\n  return instances[modelUtil.getAttribute(dom, DOM_ATTRIBUTE_KEY)];\n}\nexport function getInstanceById(key) {\n  return instances[key];\n}\n/**\n * Register theme\n */\nexport function registerTheme(name, theme) {\n  themeStorage[name] = theme;\n}\n/**\n * Register option preprocessor\n */\nexport function registerPreprocessor(preprocessorFunc) {\n  if (indexOf(optionPreprocessorFuncs, preprocessorFunc) < 0) {\n    optionPreprocessorFuncs.push(preprocessorFunc);\n  }\n}\nexport function registerProcessor(priority, processor) {\n  normalizeRegister(dataProcessorFuncs, priority, processor, PRIORITY_PROCESSOR_DEFAULT);\n}\n/**\n * Register postIniter\n * @param {Function} postInitFunc\n */\nexport function registerPostInit(postInitFunc) {\n  registerUpdateLifecycle('afterinit', postInitFunc);\n}\n/**\n * Register postUpdater\n * @param {Function} postUpdateFunc\n */\nexport function registerPostUpdate(postUpdateFunc) {\n  registerUpdateLifecycle('afterupdate', postUpdateFunc);\n}\nexport function registerUpdateLifecycle(name, cb) {\n  lifecycle.on(name, cb);\n}\nexport function registerAction(actionInfo, eventName, action) {\n  if (isFunction(eventName)) {\n    action = eventName;\n    eventName = '';\n  }\n  var actionType = isObject(actionInfo) ? actionInfo.type : [actionInfo, actionInfo = {\n    event: eventName\n  }][0];\n  // Event name is all lowercase\n  actionInfo.event = (actionInfo.event || actionType).toLowerCase();\n  eventName = actionInfo.event;\n  if (eventActionMap[eventName]) {\n    // Already registered.\n    return;\n  }\n  // Validate action type and event name.\n  assert(ACTION_REG.test(actionType) && ACTION_REG.test(eventName));\n  if (!actions[actionType]) {\n    actions[actionType] = {\n      action: action,\n      actionInfo: actionInfo\n    };\n  }\n  eventActionMap[eventName] = actionType;\n}\nexport function registerCoordinateSystem(type, coordSysCreator) {\n  CoordinateSystemManager.register(type, coordSysCreator);\n}\n/**\n * Get dimensions of specified coordinate system.\n * @param {string} type\n * @return {Array.<string|Object>}\n */\nexport function getCoordinateSystemDimensions(type) {\n  var coordSysCreator = CoordinateSystemManager.get(type);\n  if (coordSysCreator) {\n    return coordSysCreator.getDimensionsInfo ? coordSysCreator.getDimensionsInfo() : coordSysCreator.dimensions.slice();\n  }\n}\nexport { registerLocale } from './locale.js';\nfunction registerLayout(priority, layoutTask) {\n  normalizeRegister(visualFuncs, priority, layoutTask, PRIORITY_VISUAL_LAYOUT, 'layout');\n}\nfunction registerVisual(priority, visualTask) {\n  normalizeRegister(visualFuncs, priority, visualTask, PRIORITY_VISUAL_CHART, 'visual');\n}\nexport { registerLayout, registerVisual };\nvar registeredTasks = [];\nfunction normalizeRegister(targetList, priority, fn, defaultPriority, visualType) {\n  if (isFunction(priority) || isObject(priority)) {\n    fn = priority;\n    priority = defaultPriority;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (isNaN(priority) || priority == null) {\n      throw new Error('Illegal priority');\n    }\n    // Check duplicate\n    each(targetList, function (wrap) {\n      assert(wrap.__raw !== fn);\n    });\n  }\n  // Already registered\n  if (indexOf(registeredTasks, fn) >= 0) {\n    return;\n  }\n  registeredTasks.push(fn);\n  var stageHandler = Scheduler.wrapStageHandler(fn, visualType);\n  stageHandler.__prio = priority;\n  stageHandler.__raw = fn;\n  targetList.push(stageHandler);\n}\nexport function registerLoading(name, loadingFx) {\n  loadingEffects[name] = loadingFx;\n}\n/**\n * ZRender need a canvas context to do measureText.\n * But in node environment canvas may be created by node-canvas.\n * So we need to specify how to create a canvas instead of using document.createElement('canvas')\n *\n *\n * @deprecated use setPlatformAPI({ createCanvas }) instead.\n *\n * @example\n *     let Canvas = require('canvas');\n *     let echarts = require('echarts');\n *     echarts.setCanvasCreator(function () {\n *         // Small size is enough.\n *         return new Canvas(32, 32);\n *     });\n */\nexport function setCanvasCreator(creator) {\n  if (process.env.NODE_ENV !== 'production') {\n    deprecateLog('setCanvasCreator is deprecated. Use setPlatformAPI({ createCanvas }) instead.');\n  }\n  setPlatformAPI({\n    createCanvas: creator\n  });\n}\n/**\n * The parameters and usage: see `geoSourceManager.registerMap`.\n * Compatible with previous `echarts.registerMap`.\n */\nexport function registerMap(mapName, geoJson, specialAreas) {\n  var registerMap = getImpl('registerMap');\n  registerMap && registerMap(mapName, geoJson, specialAreas);\n}\nexport function getMap(mapName) {\n  var getMap = getImpl('getMap');\n  return getMap && getMap(mapName);\n}\nexport var registerTransform = registerExternalTransform;\n/**\n * Globa dispatchAction to a specified chart instance.\n */\n// export function dispatchAction(payload: { chartId: string } & Payload, opt?: Parameters<ECharts['dispatchAction']>[1]) {\n//     if (!payload || !payload.chartId) {\n//         // Must have chartId to find chart\n//         return;\n//     }\n//     const chart = instances[payload.chartId];\n//     if (chart) {\n//         chart.dispatchAction(payload, opt);\n//     }\n// }\n// Builtin global visual\nregisterVisual(PRIORITY_VISUAL_GLOBAL, seriesStyleTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataStyleTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataColorPaletteTask);\nregisterVisual(PRIORITY_VISUAL_GLOBAL, seriesSymbolTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataSymbolTask);\nregisterVisual(PRIORITY_VISUAL_DECAL, decal);\nregisterPreprocessor(backwardCompat);\nregisterProcessor(PRIORITY_PROCESSOR_DATASTACK, dataStack);\nregisterLoading('default', loadingDefault);\n// Default actions\nregisterAction({\n  type: HIGHLIGHT_ACTION_TYPE,\n  event: HIGHLIGHT_ACTION_TYPE,\n  update: HIGHLIGHT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: DOWNPLAY_ACTION_TYPE,\n  event: DOWNPLAY_ACTION_TYPE,\n  update: DOWNPLAY_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: SELECT_ACTION_TYPE,\n  event: SELECT_ACTION_TYPE,\n  update: SELECT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: UNSELECT_ACTION_TYPE,\n  event: UNSELECT_ACTION_TYPE,\n  update: UNSELECT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: TOGGLE_SELECT_ACTION_TYPE,\n  event: TOGGLE_SELECT_ACTION_TYPE,\n  update: TOGGLE_SELECT_ACTION_TYPE\n}, noop);\n// Default theme\nregisterTheme('light', lightTheme);\nregisterTheme('dark', darkTheme);\n// For backward compatibility, where the namespace `dataTool` will\n// be mounted on `echarts` is the extension `dataTool` is imported.\nexport var dataTool = {};", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { each, createHashMap, assert, map } from 'zrender/lib/core/util.js';\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nvar DimensionUserOuput = /** @class */function () {\n  function DimensionUserOuput(encode, dimRequest) {\n    this._encode = encode;\n    this._schema = dimRequest;\n  }\n  DimensionUserOuput.prototype.get = function () {\n    return {\n      // Do not generate full dimension name until fist used.\n      fullDimensions: this._getFullDimensionNames(),\n      encode: this._encode\n    };\n  };\n  /**\n   * Get all data store dimension names.\n   * Theoretically a series data store is defined both by series and used dataset (if any).\n   * If some dimensions are omitted for performance reason in `this.dimensions`,\n   * the dimension name may not be auto-generated if user does not specify a dimension name.\n   * In this case, the dimension name is `null`/`undefined`.\n   */\n  DimensionUserOuput.prototype._getFullDimensionNames = function () {\n    if (!this._cachedDimNames) {\n      this._cachedDimNames = this._schema ? this._schema.makeOutputDimensionNames() : [];\n    }\n    return this._cachedDimNames;\n  };\n  return DimensionUserOuput;\n}();\n;\nexport function summarizeDimensions(data, schema) {\n  var summary = {};\n  var encode = summary.encode = {};\n  var notExtraCoordDimMap = createHashMap();\n  var defaultedLabel = [];\n  var defaultedTooltip = [];\n  var userOutputEncode = {};\n  each(data.dimensions, function (dimName) {\n    var dimItem = data.getDimensionInfo(dimName);\n    var coordDim = dimItem.coordDim;\n    if (coordDim) {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(VISUAL_DIMENSIONS.get(coordDim) == null);\n      }\n      var coordDimIndex = dimItem.coordDimIndex;\n      getOrCreateEncodeArr(encode, coordDim)[coordDimIndex] = dimName;\n      if (!dimItem.isExtraCoord) {\n        notExtraCoordDimMap.set(coordDim, 1);\n        // Use the last coord dim (and label friendly) as default label,\n        // because when dataset is used, it is hard to guess which dimension\n        // can be value dimension. If both show x, y on label is not look good,\n        // and conventionally y axis is focused more.\n        if (mayLabelDimType(dimItem.type)) {\n          defaultedLabel[0] = dimName;\n        }\n        // User output encode do not contain generated coords.\n        // And it only has index. User can use index to retrieve value from the raw item array.\n        getOrCreateEncodeArr(userOutputEncode, coordDim)[coordDimIndex] = data.getDimensionIndex(dimItem.name);\n      }\n      if (dimItem.defaultTooltip) {\n        defaultedTooltip.push(dimName);\n      }\n    }\n    VISUAL_DIMENSIONS.each(function (v, otherDim) {\n      var encodeArr = getOrCreateEncodeArr(encode, otherDim);\n      var dimIndex = dimItem.otherDims[otherDim];\n      if (dimIndex != null && dimIndex !== false) {\n        encodeArr[dimIndex] = dimItem.name;\n      }\n    });\n  });\n  var dataDimsOnCoord = [];\n  var encodeFirstDimNotExtra = {};\n  notExtraCoordDimMap.each(function (v, coordDim) {\n    var dimArr = encode[coordDim];\n    encodeFirstDimNotExtra[coordDim] = dimArr[0];\n    // Not necessary to remove duplicate, because a data\n    // dim canot on more than one coordDim.\n    dataDimsOnCoord = dataDimsOnCoord.concat(dimArr);\n  });\n  summary.dataDimsOnCoord = dataDimsOnCoord;\n  summary.dataDimIndicesOnCoord = map(dataDimsOnCoord, function (dimName) {\n    return data.getDimensionInfo(dimName).storeDimIndex;\n  });\n  summary.encodeFirstDimNotExtra = encodeFirstDimNotExtra;\n  var encodeLabel = encode.label;\n  // FIXME `encode.label` is not recommended, because formatter cannot be set\n  // in this way. Use label.formatter instead. Maybe remove this approach someday.\n  if (encodeLabel && encodeLabel.length) {\n    defaultedLabel = encodeLabel.slice();\n  }\n  var encodeTooltip = encode.tooltip;\n  if (encodeTooltip && encodeTooltip.length) {\n    defaultedTooltip = encodeTooltip.slice();\n  } else if (!defaultedTooltip.length) {\n    defaultedTooltip = defaultedLabel.slice();\n  }\n  encode.defaultedLabel = defaultedLabel;\n  encode.defaultedTooltip = defaultedTooltip;\n  summary.userOutput = new DimensionUserOuput(userOutputEncode, schema);\n  return summary;\n}\nfunction getOrCreateEncodeArr(encode, dim) {\n  if (!encode.hasOwnProperty(dim)) {\n    encode[dim] = [];\n  }\n  return encode[dim];\n}\n// FIXME:TS should be type `AxisType`\nexport function getDimensionTypeByAxis(axisType) {\n  return axisType === 'category' ? 'ordinal' : axisType === 'time' ? 'time' : 'float';\n}\nfunction mayLabelDimType(dimType) {\n  // In most cases, ordinal and time do not suitable for label.\n  // Ordinal info can be displayed on axis. Time is too long.\n  return !(dimType === 'ordinal' || dimType === 'time');\n}\n// function findTheLastDimMayLabel(data) {\n//     // Get last value dim\n//     let dimensions = data.dimensions.slice();\n//     let valueType;\n//     let valueDim;\n//     while (dimensions.length && (\n//         valueDim = dimensions.pop(),\n//         valueType = data.getDimensionInfo(valueDim).type,\n//         valueType === 'ordinal' || valueType === 'time'\n//     )) {} // jshint ignore:line\n//     return valueDim;\n// }", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar SeriesDimensionDefine = /** @class */function () {\n  /**\n   * @param opt All of the fields will be shallow copied.\n   */\n  function SeriesDimensionDefine(opt) {\n    /**\n     * The format of `otherDims` is:\n     * ```js\n     * {\n     *     tooltip?: number\n     *     label?: number\n     *     itemName?: number\n     *     seriesName?: number\n     * }\n     * ```\n     *\n     * A `series.encode` can specified these fields:\n     * ```js\n     * encode: {\n     *     // \"3, 1, 5\" is the index of data dimension.\n     *     tooltip: [3, 1, 5],\n     *     label: [0, 3],\n     *     ...\n     * }\n     * ```\n     * `otherDims` is the parse result of the `series.encode` above, like:\n     * ```js\n     * // Suppose the index of this data dimension is `3`.\n     * this.otherDims = {\n     *     // `3` is at the index `0` of the `encode.tooltip`\n     *     tooltip: 0,\n     *     // `3` is at the index `1` of the `encode.label`\n     *     label: 1\n     * };\n     * ```\n     *\n     * This prop should never be `null`/`undefined` after initialized.\n     */\n    this.otherDims = {};\n    if (opt != null) {\n      zrUtil.extend(this, opt);\n    }\n  }\n  return SeriesDimensionDefine;\n}();\n;\nexport default SeriesDimensionDefine;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, isObject, retrieve2 } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nimport { shouldRetrieveDataByName } from '../Source.js';\nvar inner = makeInner();\nvar dimTypeShort = {\n  float: 'f',\n  int: 'i',\n  ordinal: 'o',\n  number: 'n',\n  time: 't'\n};\n/**\n * Represents the dimension requirement of a series.\n *\n * NOTICE:\n * When there are too many dimensions in dataset and many series, only the used dimensions\n * (i.e., used by coord sys and declared in `series.encode`) are add to `dimensionDefineList`.\n * But users may query data by other unused dimension names.\n * In this case, users can only query data if and only if they have defined dimension names\n * via ec option, so we provide `getDimensionIndexFromSource`, which only query them from\n * `source` dimensions.\n */\nvar SeriesDataSchema = /** @class */function () {\n  function SeriesDataSchema(opt) {\n    this.dimensions = opt.dimensions;\n    this._dimOmitted = opt.dimensionOmitted;\n    this.source = opt.source;\n    this._fullDimCount = opt.fullDimensionCount;\n    this._updateDimOmitted(opt.dimensionOmitted);\n  }\n  SeriesDataSchema.prototype.isDimensionOmitted = function () {\n    return this._dimOmitted;\n  };\n  SeriesDataSchema.prototype._updateDimOmitted = function (dimensionOmitted) {\n    this._dimOmitted = dimensionOmitted;\n    if (!dimensionOmitted) {\n      return;\n    }\n    if (!this._dimNameMap) {\n      this._dimNameMap = ensureSourceDimNameMap(this.source);\n    }\n  };\n  /**\n   * @caution Can only be used when `dimensionOmitted: true`.\n   *\n   * Get index by user defined dimension name (i.e., not internal generate name).\n   * That is, get index from `dimensionsDefine`.\n   * If no `dimensionsDefine`, or no name get, return -1.\n   */\n  SeriesDataSchema.prototype.getSourceDimensionIndex = function (dimName) {\n    return retrieve2(this._dimNameMap.get(dimName), -1);\n  };\n  /**\n   * @caution Can only be used when `dimensionOmitted: true`.\n   *\n   * Notice: may return `null`/`undefined` if user not specify dimension names.\n   */\n  SeriesDataSchema.prototype.getSourceDimension = function (dimIndex) {\n    var dimensionsDefine = this.source.dimensionsDefine;\n    if (dimensionsDefine) {\n      return dimensionsDefine[dimIndex];\n    }\n  };\n  SeriesDataSchema.prototype.makeStoreSchema = function () {\n    var dimCount = this._fullDimCount;\n    var willRetrieveDataByName = shouldRetrieveDataByName(this.source);\n    var makeHashStrict = !shouldOmitUnusedDimensions(dimCount);\n    // If source don't have dimensions or series don't omit unsed dimensions.\n    // Generate from seriesDimList directly\n    var dimHash = '';\n    var dims = [];\n    for (var fullDimIdx = 0, seriesDimIdx = 0; fullDimIdx < dimCount; fullDimIdx++) {\n      var property = void 0;\n      var type = void 0;\n      var ordinalMeta = void 0;\n      var seriesDimDef = this.dimensions[seriesDimIdx];\n      // The list has been sorted by `storeDimIndex` asc.\n      if (seriesDimDef && seriesDimDef.storeDimIndex === fullDimIdx) {\n        property = willRetrieveDataByName ? seriesDimDef.name : null;\n        type = seriesDimDef.type;\n        ordinalMeta = seriesDimDef.ordinalMeta;\n        seriesDimIdx++;\n      } else {\n        var sourceDimDef = this.getSourceDimension(fullDimIdx);\n        if (sourceDimDef) {\n          property = willRetrieveDataByName ? sourceDimDef.name : null;\n          type = sourceDimDef.type;\n        }\n      }\n      dims.push({\n        property: property,\n        type: type,\n        ordinalMeta: ordinalMeta\n      });\n      // If retrieving data by index,\n      //   use <index, type, ordinalMeta> to determine whether data can be shared.\n      //   (Because in this case there might be no dimension name defined in dataset, but indices always exists).\n      //   (Indices are always 0, 1, 2, ..., so we can ignore them to shorten the hash).\n      // Otherwise if retrieving data by property name (like `data: [{aa: 123, bb: 765}, ...]`),\n      //   use <property, type, ordinalMeta> in hash.\n      if (willRetrieveDataByName && property != null\n      // For data stack, we have make sure each series has its own dim on this store.\n      // So we do not add property to hash to make sure they can share this store.\n      && (!seriesDimDef || !seriesDimDef.isCalculationCoord)) {\n        dimHash += makeHashStrict\n        // Use escape character '`' in case that property name contains '$'.\n        ? property.replace(/\\`/g, '`1').replace(/\\$/g, '`2')\n        // For better performance, when there are large dimensions, tolerant this defects that hardly meet.\n        : property;\n      }\n      dimHash += '$';\n      dimHash += dimTypeShort[type] || 'f';\n      if (ordinalMeta) {\n        dimHash += ordinalMeta.uid;\n      }\n      dimHash += '$';\n    }\n    // Source from endpoint(usually series) will be read differently\n    // when seriesLayoutBy or startIndex(which is affected by sourceHeader) are different.\n    // So we use this three props as key.\n    var source = this.source;\n    var hash = [source.seriesLayoutBy, source.startIndex, dimHash].join('$$');\n    return {\n      dimensions: dims,\n      hash: hash\n    };\n  };\n  SeriesDataSchema.prototype.makeOutputDimensionNames = function () {\n    var result = [];\n    for (var fullDimIdx = 0, seriesDimIdx = 0; fullDimIdx < this._fullDimCount; fullDimIdx++) {\n      var name_1 = void 0;\n      var seriesDimDef = this.dimensions[seriesDimIdx];\n      // The list has been sorted by `storeDimIndex` asc.\n      if (seriesDimDef && seriesDimDef.storeDimIndex === fullDimIdx) {\n        if (!seriesDimDef.isCalculationCoord) {\n          name_1 = seriesDimDef.name;\n        }\n        seriesDimIdx++;\n      } else {\n        var sourceDimDef = this.getSourceDimension(fullDimIdx);\n        if (sourceDimDef) {\n          name_1 = sourceDimDef.name;\n        }\n      }\n      result.push(name_1);\n    }\n    return result;\n  };\n  SeriesDataSchema.prototype.appendCalculationDimension = function (dimDef) {\n    this.dimensions.push(dimDef);\n    dimDef.isCalculationCoord = true;\n    this._fullDimCount++;\n    // If append dimension on a data store, consider the store\n    // might be shared by different series, series dimensions not\n    // really map to store dimensions.\n    this._updateDimOmitted(true);\n  };\n  return SeriesDataSchema;\n}();\nexport { SeriesDataSchema };\nexport function isSeriesDataSchema(schema) {\n  return schema instanceof SeriesDataSchema;\n}\nexport function createDimNameMap(dimsDef) {\n  var dataDimNameMap = createHashMap();\n  for (var i = 0; i < (dimsDef || []).length; i++) {\n    var dimDefItemRaw = dimsDef[i];\n    var userDimName = isObject(dimDefItemRaw) ? dimDefItemRaw.name : dimDefItemRaw;\n    if (userDimName != null && dataDimNameMap.get(userDimName) == null) {\n      dataDimNameMap.set(userDimName, i);\n    }\n  }\n  return dataDimNameMap;\n}\nexport function ensureSourceDimNameMap(source) {\n  var innerSource = inner(source);\n  return innerSource.dimNameMap || (innerSource.dimNameMap = createDimNameMap(source.dimensionsDefine));\n}\nexport function shouldOmitUnusedDimensions(dimCount) {\n  return dimCount > 30;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/* global Int32Array */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from '../model/Model.js';\nimport DataDiffer from './DataDiffer.js';\nimport { DefaultDataProvider } from './helper/dataProvider.js';\nimport { summarizeDimensions } from './helper/dimensionHelper.js';\nimport SeriesDimensionDefine from './SeriesDimensionDefine.js';\nimport { SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ORIGINAL } from '../util/types.js';\nimport { convertOptionIdName, isDataItemOption } from '../util/model.js';\nimport { setCommonECData } from '../util/innerStore.js';\nimport { isSourceInstance } from './Source.js';\nimport DataStore from './DataStore.js';\nimport { isSeriesDataSchema } from './helper/SeriesDataSchema.js';\nvar isObject = zrUtil.isObject;\nvar map = zrUtil.map;\nvar CtorInt32Array = typeof Int32Array === 'undefined' ? Array : Int32Array;\n// Use prefix to avoid index to be the same as otherIdList[idx],\n// which will cause weird update animation.\nvar ID_PREFIX = 'e\\0\\0';\nvar INDEX_NOT_FOUND = -1;\n// type SeriesDimensionIndex = DimensionIndex;\nvar TRANSFERABLE_PROPERTIES = ['hasItemOption', '_nameList', '_idList', '_invertedIndicesMap', '_dimSummary', 'userOutput', '_rawData', '_dimValueGetter', '_nameDimIdx', '_idDimIdx', '_nameRepeatCount'];\nvar CLONE_PROPERTIES = ['_approximateExtent'];\n// -----------------------------\n// Internal method declarations:\n// -----------------------------\nvar prepareInvertedIndex;\nvar getId;\nvar getIdNameFromStore;\nvar normalizeDimensions;\nvar transferProperties;\nvar cloneListForMapAndSample;\nvar makeIdFromName;\nvar SeriesData = /** @class */function () {\n  /**\n   * @param dimensionsInput.dimensions\n   *        For example, ['someDimName', {name: 'someDimName', type: 'someDimType'}, ...].\n   *        Dimensions should be concrete names like x, y, z, lng, lat, angle, radius\n   */\n  function SeriesData(dimensionsInput, hostModel) {\n    this.type = 'list';\n    this._dimOmitted = false;\n    this._nameList = [];\n    this._idList = [];\n    // Models of data option is stored sparse for optimizing memory cost\n    // Never used yet (not used yet).\n    // private _optionModels: Model[] = [];\n    // Global visual properties after visual coding\n    this._visual = {};\n    // Global layout properties.\n    this._layout = {};\n    // Item visual properties after visual coding\n    this._itemVisuals = [];\n    // Item layout properties after layout\n    this._itemLayouts = [];\n    // Graphic elements\n    this._graphicEls = [];\n    // key: dim, value: extent\n    this._approximateExtent = {};\n    this._calculationInfo = {};\n    // Having detected that there is data item is non primitive type\n    // (in type `OptionDataItemObject`).\n    // Like `data: [ { value: xx, itemStyle: {...} }, ...]`\n    // At present it only happen in `SOURCE_FORMAT_ORIGINAL`.\n    this.hasItemOption = false;\n    // Methods that create a new list based on this list should be listed here.\n    // Notice that those method should `RETURN` the new list.\n    this.TRANSFERABLE_METHODS = ['cloneShallow', 'downSample', 'lttbDownSample', 'map'];\n    // Methods that change indices of this list should be listed here.\n    this.CHANGABLE_METHODS = ['filterSelf', 'selectRange'];\n    this.DOWNSAMPLE_METHODS = ['downSample', 'lttbDownSample'];\n    var dimensions;\n    var assignStoreDimIdx = false;\n    if (isSeriesDataSchema(dimensionsInput)) {\n      dimensions = dimensionsInput.dimensions;\n      this._dimOmitted = dimensionsInput.isDimensionOmitted();\n      this._schema = dimensionsInput;\n    } else {\n      assignStoreDimIdx = true;\n      dimensions = dimensionsInput;\n    }\n    dimensions = dimensions || ['x', 'y'];\n    var dimensionInfos = {};\n    var dimensionNames = [];\n    var invertedIndicesMap = {};\n    var needsHasOwn = false;\n    var emptyObj = {};\n    for (var i = 0; i < dimensions.length; i++) {\n      // Use the original dimensions[i], where other flag props may exists.\n      var dimInfoInput = dimensions[i];\n      var dimensionInfo = zrUtil.isString(dimInfoInput) ? new SeriesDimensionDefine({\n        name: dimInfoInput\n      }) : !(dimInfoInput instanceof SeriesDimensionDefine) ? new SeriesDimensionDefine(dimInfoInput) : dimInfoInput;\n      var dimensionName = dimensionInfo.name;\n      dimensionInfo.type = dimensionInfo.type || 'float';\n      if (!dimensionInfo.coordDim) {\n        dimensionInfo.coordDim = dimensionName;\n        dimensionInfo.coordDimIndex = 0;\n      }\n      var otherDims = dimensionInfo.otherDims = dimensionInfo.otherDims || {};\n      dimensionNames.push(dimensionName);\n      dimensionInfos[dimensionName] = dimensionInfo;\n      if (emptyObj[dimensionName] != null) {\n        needsHasOwn = true;\n      }\n      if (dimensionInfo.createInvertedIndices) {\n        invertedIndicesMap[dimensionName] = [];\n      }\n      if (otherDims.itemName === 0) {\n        this._nameDimIdx = i;\n      }\n      if (otherDims.itemId === 0) {\n        this._idDimIdx = i;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.assert(assignStoreDimIdx || dimensionInfo.storeDimIndex >= 0);\n      }\n      if (assignStoreDimIdx) {\n        dimensionInfo.storeDimIndex = i;\n      }\n    }\n    this.dimensions = dimensionNames;\n    this._dimInfos = dimensionInfos;\n    this._initGetDimensionInfo(needsHasOwn);\n    this.hostModel = hostModel;\n    this._invertedIndicesMap = invertedIndicesMap;\n    if (this._dimOmitted) {\n      var dimIdxToName_1 = this._dimIdxToName = zrUtil.createHashMap();\n      zrUtil.each(dimensionNames, function (dimName) {\n        dimIdxToName_1.set(dimensionInfos[dimName].storeDimIndex, dimName);\n      });\n    }\n  }\n  /**\n   *\n   * Get concrete dimension name by dimension name or dimension index.\n   * If input a dimension name, do not validate whether the dimension name exits.\n   *\n   * @caution\n   * @param dim Must make sure the dimension is `SeriesDimensionLoose`.\n   * Because only those dimensions will have auto-generated dimension names if not\n   * have a user-specified name, and other dimensions will get a return of null/undefined.\n   *\n   * @notice Because of this reason, should better use `getDimensionIndex` instead, for examples:\n   * ```js\n   * const val = data.getStore().get(data.getDimensionIndex(dim), dataIdx);\n   * ```\n   *\n   * @return Concrete dim name.\n   */\n  SeriesData.prototype.getDimension = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx == null) {\n      return dim;\n    }\n    dimIdx = dim;\n    if (!this._dimOmitted) {\n      return this.dimensions[dimIdx];\n    }\n    // Retrieve from series dimension definition because it probably contains\n    // generated dimension name (like 'x', 'y').\n    var dimName = this._dimIdxToName.get(dimIdx);\n    if (dimName != null) {\n      return dimName;\n    }\n    var sourceDimDef = this._schema.getSourceDimension(dimIdx);\n    if (sourceDimDef) {\n      return sourceDimDef.name;\n    }\n  };\n  /**\n   * Get dimension index in data store. Return -1 if not found.\n   * Can be used to index value from getRawValue.\n   */\n  SeriesData.prototype.getDimensionIndex = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx != null) {\n      return dimIdx;\n    }\n    if (dim == null) {\n      return -1;\n    }\n    var dimInfo = this._getDimInfo(dim);\n    return dimInfo ? dimInfo.storeDimIndex : this._dimOmitted ? this._schema.getSourceDimensionIndex(dim) : -1;\n  };\n  /**\n   * The meanings of the input parameter `dim`:\n   *\n   * + If dim is a number (e.g., `1`), it means the index of the dimension.\n   *   For example, `getDimension(0)` will return 'x' or 'lng' or 'radius'.\n   * + If dim is a number-like string (e.g., `\"1\"`):\n   *     + If there is the same concrete dim name defined in `series.dimensions` or `dataset.dimensions`,\n   *        it means that concrete name.\n   *     + If not, it will be converted to a number, which means the index of the dimension.\n   *        (why? because of the backward compatibility. We have been tolerating number-like string in\n   *        dimension setting, although now it seems that it is not a good idea.)\n   *     For example, `visualMap[i].dimension: \"1\"` is the same meaning as `visualMap[i].dimension: 1`,\n   *     if no dimension name is defined as `\"1\"`.\n   * + If dim is a not-number-like string, it means the concrete dim name.\n   *   For example, it can be be default name `\"x\"`, `\"y\"`, `\"z\"`, `\"lng\"`, `\"lat\"`, `\"angle\"`, `\"radius\"`,\n   *   or customized in `dimensions` property of option like `\"age\"`.\n   *\n   * @return recognized `DimensionIndex`. Otherwise return null/undefined (means that dim is `DimensionName`).\n   */\n  SeriesData.prototype._recognizeDimIndex = function (dim) {\n    if (zrUtil.isNumber(dim)\n    // If being a number-like string but not being defined as a dimension name.\n    || dim != null && !isNaN(dim) && !this._getDimInfo(dim) && (!this._dimOmitted || this._schema.getSourceDimensionIndex(dim) < 0)) {\n      return +dim;\n    }\n  };\n  SeriesData.prototype._getStoreDimIndex = function (dim) {\n    var dimIdx = this.getDimensionIndex(dim);\n    if (process.env.NODE_ENV !== 'production') {\n      if (dimIdx == null) {\n        throw new Error('Unknown dimension ' + dim);\n      }\n    }\n    return dimIdx;\n  };\n  /**\n   * Get type and calculation info of particular dimension\n   * @param dim\n   *        Dimension can be concrete names like x, y, z, lng, lat, angle, radius\n   *        Or a ordinal number. For example getDimensionInfo(0) will return 'x' or 'lng' or 'radius'\n   */\n  SeriesData.prototype.getDimensionInfo = function (dim) {\n    // Do not clone, because there may be categories in dimInfo.\n    return this._getDimInfo(this.getDimension(dim));\n  };\n  SeriesData.prototype._initGetDimensionInfo = function (needsHasOwn) {\n    var dimensionInfos = this._dimInfos;\n    this._getDimInfo = needsHasOwn ? function (dimName) {\n      return dimensionInfos.hasOwnProperty(dimName) ? dimensionInfos[dimName] : undefined;\n    } : function (dimName) {\n      return dimensionInfos[dimName];\n    };\n  };\n  /**\n   * concrete dimension name list on coord.\n   */\n  SeriesData.prototype.getDimensionsOnCoord = function () {\n    return this._dimSummary.dataDimsOnCoord.slice();\n  };\n  SeriesData.prototype.mapDimension = function (coordDim, idx) {\n    var dimensionsSummary = this._dimSummary;\n    if (idx == null) {\n      return dimensionsSummary.encodeFirstDimNotExtra[coordDim];\n    }\n    var dims = dimensionsSummary.encode[coordDim];\n    return dims ? dims[idx] : null;\n  };\n  SeriesData.prototype.mapDimensionsAll = function (coordDim) {\n    var dimensionsSummary = this._dimSummary;\n    var dims = dimensionsSummary.encode[coordDim];\n    return (dims || []).slice();\n  };\n  SeriesData.prototype.getStore = function () {\n    return this._store;\n  };\n  /**\n   * Initialize from data\n   * @param data source or data or data store.\n   * @param nameList The name of a datum is used on data diff and\n   *        default label/tooltip.\n   *        A name can be specified in encode.itemName,\n   *        or dataItem.name (only for series option data),\n   *        or provided in nameList from outside.\n   */\n  SeriesData.prototype.initData = function (data, nameList, dimValueGetter) {\n    var _this = this;\n    var store;\n    if (data instanceof DataStore) {\n      store = data;\n    }\n    if (!store) {\n      var dimensions = this.dimensions;\n      var provider = isSourceInstance(data) || zrUtil.isArrayLike(data) ? new DefaultDataProvider(data, dimensions.length) : data;\n      store = new DataStore();\n      var dimensionInfos = map(dimensions, function (dimName) {\n        return {\n          type: _this._dimInfos[dimName].type,\n          property: dimName\n        };\n      });\n      store.initData(provider, dimensionInfos, dimValueGetter);\n    }\n    this._store = store;\n    // Reset\n    this._nameList = (nameList || []).slice();\n    this._idList = [];\n    this._nameRepeatCount = {};\n    this._doInit(0, store.count());\n    // Cache summary info for fast visit. See \"dimensionHelper\".\n    // Needs to be initialized after store is prepared.\n    this._dimSummary = summarizeDimensions(this, this._schema);\n    this.userOutput = this._dimSummary.userOutput;\n  };\n  /**\n   * Caution: Can be only called on raw data (before `this._indices` created).\n   */\n  SeriesData.prototype.appendData = function (data) {\n    var range = this._store.appendData(data);\n    this._doInit(range[0], range[1]);\n  };\n  /**\n   * Caution: Can be only called on raw data (before `this._indices` created).\n   * This method does not modify `rawData` (`dataProvider`), but only\n   * add values to store.\n   *\n   * The final count will be increased by `Math.max(values.length, names.length)`.\n   *\n   * @param values That is the SourceType: 'arrayRows', like\n   *        [\n   *            [12, 33, 44],\n   *            [NaN, 43, 1],\n   *            ['-', 'asdf', 0]\n   *        ]\n   *        Each item is exactly corresponding to a dimension.\n   */\n  SeriesData.prototype.appendValues = function (values, names) {\n    var _a = this._store.appendValues(values, names.length),\n      start = _a.start,\n      end = _a.end;\n    var shouldMakeIdFromName = this._shouldMakeIdFromName();\n    this._updateOrdinalMeta();\n    if (names) {\n      for (var idx = start; idx < end; idx++) {\n        var sourceIdx = idx - start;\n        this._nameList[idx] = names[sourceIdx];\n        if (shouldMakeIdFromName) {\n          makeIdFromName(this, idx);\n        }\n      }\n    }\n  };\n  SeriesData.prototype._updateOrdinalMeta = function () {\n    var store = this._store;\n    var dimensions = this.dimensions;\n    for (var i = 0; i < dimensions.length; i++) {\n      var dimInfo = this._dimInfos[dimensions[i]];\n      if (dimInfo.ordinalMeta) {\n        store.collectOrdinalMeta(dimInfo.storeDimIndex, dimInfo.ordinalMeta);\n      }\n    }\n  };\n  SeriesData.prototype._shouldMakeIdFromName = function () {\n    var provider = this._store.getProvider();\n    return this._idDimIdx == null && provider.getSource().sourceFormat !== SOURCE_FORMAT_TYPED_ARRAY && !provider.fillStorage;\n  };\n  SeriesData.prototype._doInit = function (start, end) {\n    if (start >= end) {\n      return;\n    }\n    var store = this._store;\n    var provider = store.getProvider();\n    this._updateOrdinalMeta();\n    var nameList = this._nameList;\n    var idList = this._idList;\n    var sourceFormat = provider.getSource().sourceFormat;\n    var isFormatOriginal = sourceFormat === SOURCE_FORMAT_ORIGINAL;\n    // Each data item is value\n    // [1, 2]\n    // 2\n    // Bar chart, line chart which uses category axis\n    // only gives the 'y' value. 'x' value is the indices of category\n    // Use a tempValue to normalize the value to be a (x, y) value\n    // If dataItem is {name: ...} or {id: ...}, it has highest priority.\n    // This kind of ids and names are always stored `_nameList` and `_idList`.\n    if (isFormatOriginal && !provider.pure) {\n      var sharedDataItem = [];\n      for (var idx = start; idx < end; idx++) {\n        // NOTICE: Try not to write things into dataItem\n        var dataItem = provider.getItem(idx, sharedDataItem);\n        if (!this.hasItemOption && isDataItemOption(dataItem)) {\n          this.hasItemOption = true;\n        }\n        if (dataItem) {\n          var itemName = dataItem.name;\n          if (nameList[idx] == null && itemName != null) {\n            nameList[idx] = convertOptionIdName(itemName, null);\n          }\n          var itemId = dataItem.id;\n          if (idList[idx] == null && itemId != null) {\n            idList[idx] = convertOptionIdName(itemId, null);\n          }\n        }\n      }\n    }\n    if (this._shouldMakeIdFromName()) {\n      for (var idx = start; idx < end; idx++) {\n        makeIdFromName(this, idx);\n      }\n    }\n    prepareInvertedIndex(this);\n  };\n  /**\n   * PENDING: In fact currently this function is only used to short-circuit\n   * the calling of `scale.unionExtentFromData` when data have been filtered by modules\n   * like \"dataZoom\". `scale.unionExtentFromData` is used to calculate data extent for series on\n   * an axis, but if a \"axis related data filter module\" is used, the extent of the axis have\n   * been fixed and no need to calling `scale.unionExtentFromData` actually.\n   * But if we add \"custom data filter\" in future, which is not \"axis related\", this method may\n   * be still needed.\n   *\n   * Optimize for the scenario that data is filtered by a given extent.\n   * Consider that if data amount is more than hundreds of thousand,\n   * extent calculation will cost more than 10ms and the cache will\n   * be erased because of the filtering.\n   */\n  SeriesData.prototype.getApproximateExtent = function (dim) {\n    return this._approximateExtent[dim] || this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  /**\n   * Calculate extent on a filtered data might be time consuming.\n   * Approximate extent is only used for: calculate extent of filtered data outside.\n   */\n  SeriesData.prototype.setApproximateExtent = function (extent, dim) {\n    dim = this.getDimension(dim);\n    this._approximateExtent[dim] = extent.slice();\n  };\n  SeriesData.prototype.getCalculationInfo = function (key) {\n    return this._calculationInfo[key];\n  };\n  SeriesData.prototype.setCalculationInfo = function (key, value) {\n    isObject(key) ? zrUtil.extend(this._calculationInfo, key) : this._calculationInfo[key] = value;\n  };\n  /**\n   * @return Never be null/undefined. `number` will be converted to string. Because:\n   * In most cases, name is used in display, where returning a string is more convenient.\n   * In other cases, name is used in query (see `indexOfName`), where we can keep the\n   * rule that name `2` equals to name `'2'`.\n   */\n  SeriesData.prototype.getName = function (idx) {\n    var rawIndex = this.getRawIndex(idx);\n    var name = this._nameList[rawIndex];\n    if (name == null && this._nameDimIdx != null) {\n      name = getIdNameFromStore(this, this._nameDimIdx, rawIndex);\n    }\n    if (name == null) {\n      name = '';\n    }\n    return name;\n  };\n  SeriesData.prototype._getCategory = function (dimIdx, idx) {\n    var ordinal = this._store.get(dimIdx, idx);\n    var ordinalMeta = this._store.getOrdinalMeta(dimIdx);\n    if (ordinalMeta) {\n      return ordinalMeta.categories[ordinal];\n    }\n    return ordinal;\n  };\n  /**\n   * @return Never null/undefined. `number` will be converted to string. Because:\n   * In all cases having encountered at present, id is used in making diff comparison, which\n   * are usually based on hash map. We can keep the rule that the internal id are always string\n   * (treat `2` is the same as `'2'`) to make the related logic simple.\n   */\n  SeriesData.prototype.getId = function (idx) {\n    return getId(this, this.getRawIndex(idx));\n  };\n  SeriesData.prototype.count = function () {\n    return this._store.count();\n  };\n  /**\n   * Get value. Return NaN if idx is out of range.\n   *\n   * @notice Should better to use `data.getStore().get(dimIndex, dataIdx)` instead.\n   */\n  SeriesData.prototype.get = function (dim, idx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.get(dimInfo.storeDimIndex, idx);\n    }\n  };\n  /**\n   * @notice Should better to use `data.getStore().getByRawIndex(dimIndex, dataIdx)` instead.\n   */\n  SeriesData.prototype.getByRawIndex = function (dim, rawIdx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.getByRawIndex(dimInfo.storeDimIndex, rawIdx);\n    }\n  };\n  SeriesData.prototype.getIndices = function () {\n    return this._store.getIndices();\n  };\n  SeriesData.prototype.getDataExtent = function (dim) {\n    return this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getSum = function (dim) {\n    return this._store.getSum(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getMedian = function (dim) {\n    return this._store.getMedian(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getValues = function (dimensions, idx) {\n    var _this = this;\n    var store = this._store;\n    return zrUtil.isArray(dimensions) ? store.getValues(map(dimensions, function (dim) {\n      return _this._getStoreDimIndex(dim);\n    }), idx) : store.getValues(dimensions);\n  };\n  /**\n   * If value is NaN. Including '-'\n   * Only check the coord dimensions.\n   */\n  SeriesData.prototype.hasValue = function (idx) {\n    var dataDimIndicesOnCoord = this._dimSummary.dataDimIndicesOnCoord;\n    for (var i = 0, len = dataDimIndicesOnCoord.length; i < len; i++) {\n      // Ordinal type originally can be string or number.\n      // But when an ordinal type is used on coord, it can\n      // not be string but only number. So we can also use isNaN.\n      if (isNaN(this._store.get(dataDimIndicesOnCoord[i], idx))) {\n        return false;\n      }\n    }\n    return true;\n  };\n  /**\n   * Retrieve the index with given name\n   */\n  SeriesData.prototype.indexOfName = function (name) {\n    for (var i = 0, len = this._store.count(); i < len; i++) {\n      if (this.getName(i) === name) {\n        return i;\n      }\n    }\n    return -1;\n  };\n  SeriesData.prototype.getRawIndex = function (idx) {\n    return this._store.getRawIndex(idx);\n  };\n  SeriesData.prototype.indexOfRawIndex = function (rawIndex) {\n    return this._store.indexOfRawIndex(rawIndex);\n  };\n  /**\n   * Only support the dimension which inverted index created.\n   * Do not support other cases until required.\n   * @param dim concrete dim\n   * @param value ordinal index\n   * @return rawIndex\n   */\n  SeriesData.prototype.rawIndexOf = function (dim, value) {\n    var invertedIndices = dim && this._invertedIndicesMap[dim];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!invertedIndices) {\n        throw new Error('Do not supported yet');\n      }\n    }\n    var rawIndex = invertedIndices[value];\n    if (rawIndex == null || isNaN(rawIndex)) {\n      return INDEX_NOT_FOUND;\n    }\n    return rawIndex;\n  };\n  /**\n   * Retrieve the index of nearest value\n   * @param dim\n   * @param value\n   * @param [maxDistance=Infinity]\n   * @return If and only if multiple indices has\n   *         the same value, they are put to the result.\n   */\n  SeriesData.prototype.indicesOfNearest = function (dim, value, maxDistance) {\n    return this._store.indicesOfNearest(this._getStoreDimIndex(dim), value, maxDistance);\n  };\n  SeriesData.prototype.each = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store.each(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  SeriesData.prototype.filterSelf = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store = this._store.filter(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return this;\n  };\n  /**\n   * Select data in range. (For optimization of filter)\n   * (Manually inline code, support 5 million data filtering in data zoom.)\n   */\n  SeriesData.prototype.selectRange = function (range) {\n    'use strict';\n\n    var _this = this;\n    var innerRange = {};\n    var dims = zrUtil.keys(range);\n    var dimIndices = [];\n    zrUtil.each(dims, function (dim) {\n      var dimIdx = _this._getStoreDimIndex(dim);\n      innerRange[dimIdx] = range[dim];\n      dimIndices.push(dimIdx);\n    });\n    this._store = this._store.selectRange(innerRange);\n    return this;\n  };\n  /* eslint-enable max-len */\n  SeriesData.prototype.mapArray = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    ctx = ctx || this;\n    var result = [];\n    this.each(dims, function () {\n      result.push(cb && cb.apply(this, arguments));\n    }, ctx);\n    return result;\n  };\n  SeriesData.prototype.map = function (dims, cb, ctx, ctxCompat) {\n    'use strict';\n\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.map(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return list;\n  };\n  SeriesData.prototype.modify = function (dims, cb, ctx, ctxCompat) {\n    var _this = this;\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.each(normalizeDimensions(dims), function (dim) {\n        var dimInfo = _this.getDimensionInfo(dim);\n        if (!dimInfo.isCalculationCoord) {\n          console.error('Danger: only stack dimension can be modified');\n        }\n      });\n    }\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    // If do shallow clone here, if there are too many stacked series,\n    // it still cost lots of memory, because `_store.dimensions` are not shared.\n    // We should consider there probably be shallow clone happen in each series\n    // in consequent filter/map.\n    this._store.modify(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  /**\n   * Large data down sampling on given dimension\n   * @param sampleIndex Sample index for name and id\n   */\n  SeriesData.prototype.downSample = function (dimension, rate, sampleValue, sampleIndex) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.downSample(this._getStoreDimIndex(dimension), rate, sampleValue, sampleIndex);\n    return list;\n  };\n  /**\n   * Large data down sampling using largest-triangle-three-buckets\n   * @param {string} valueDimension\n   * @param {number} targetCount\n   */\n  SeriesData.prototype.lttbDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.lttbDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  SeriesData.prototype.getRawDataItem = function (idx) {\n    return this._store.getRawDataItem(idx);\n  };\n  /**\n   * Get model of one data item.\n   */\n  // TODO: Type of data item\n  SeriesData.prototype.getItemModel = function (idx) {\n    var hostModel = this.hostModel;\n    var dataItem = this.getRawDataItem(idx);\n    return new Model(dataItem, hostModel, hostModel && hostModel.ecModel);\n  };\n  /**\n   * Create a data differ\n   */\n  SeriesData.prototype.diff = function (otherList) {\n    var thisList = this;\n    return new DataDiffer(otherList ? otherList.getStore().getIndices() : [], this.getStore().getIndices(), function (idx) {\n      return getId(otherList, idx);\n    }, function (idx) {\n      return getId(thisList, idx);\n    });\n  };\n  /**\n   * Get visual property.\n   */\n  SeriesData.prototype.getVisual = function (key) {\n    var visual = this._visual;\n    return visual && visual[key];\n  };\n  SeriesData.prototype.setVisual = function (kvObj, val) {\n    this._visual = this._visual || {};\n    if (isObject(kvObj)) {\n      zrUtil.extend(this._visual, kvObj);\n    } else {\n      this._visual[kvObj] = val;\n    }\n  };\n  /**\n   * Get visual property of single data item\n   */\n  // eslint-disable-next-line\n  SeriesData.prototype.getItemVisual = function (idx, key) {\n    var itemVisual = this._itemVisuals[idx];\n    var val = itemVisual && itemVisual[key];\n    if (val == null) {\n      // Use global visual property\n      return this.getVisual(key);\n    }\n    return val;\n  };\n  /**\n   * If exists visual property of single data item\n   */\n  SeriesData.prototype.hasItemVisual = function () {\n    return this._itemVisuals.length > 0;\n  };\n  /**\n   * Make sure itemVisual property is unique\n   */\n  // TODO: use key to save visual to reduce memory.\n  SeriesData.prototype.ensureUniqueItemVisual = function (idx, key) {\n    var itemVisuals = this._itemVisuals;\n    var itemVisual = itemVisuals[idx];\n    if (!itemVisual) {\n      itemVisual = itemVisuals[idx] = {};\n    }\n    var val = itemVisual[key];\n    if (val == null) {\n      val = this.getVisual(key);\n      // TODO Performance?\n      if (zrUtil.isArray(val)) {\n        val = val.slice();\n      } else if (isObject(val)) {\n        val = zrUtil.extend({}, val);\n      }\n      itemVisual[key] = val;\n    }\n    return val;\n  };\n  // eslint-disable-next-line\n  SeriesData.prototype.setItemVisual = function (idx, key, value) {\n    var itemVisual = this._itemVisuals[idx] || {};\n    this._itemVisuals[idx] = itemVisual;\n    if (isObject(key)) {\n      zrUtil.extend(itemVisual, key);\n    } else {\n      itemVisual[key] = value;\n    }\n  };\n  /**\n   * Clear itemVisuals and list visual.\n   */\n  SeriesData.prototype.clearAllVisual = function () {\n    this._visual = {};\n    this._itemVisuals = [];\n  };\n  SeriesData.prototype.setLayout = function (key, val) {\n    isObject(key) ? zrUtil.extend(this._layout, key) : this._layout[key] = val;\n  };\n  /**\n   * Get layout property.\n   */\n  SeriesData.prototype.getLayout = function (key) {\n    return this._layout[key];\n  };\n  /**\n   * Get layout of single data item\n   */\n  SeriesData.prototype.getItemLayout = function (idx) {\n    return this._itemLayouts[idx];\n  };\n  /**\n   * Set layout of single data item\n   */\n  SeriesData.prototype.setItemLayout = function (idx, layout, merge) {\n    this._itemLayouts[idx] = merge ? zrUtil.extend(this._itemLayouts[idx] || {}, layout) : layout;\n  };\n  /**\n   * Clear all layout of single data item\n   */\n  SeriesData.prototype.clearItemLayouts = function () {\n    this._itemLayouts.length = 0;\n  };\n  /**\n   * Set graphic element relative to data. It can be set as null\n   */\n  SeriesData.prototype.setItemGraphicEl = function (idx, el) {\n    var seriesIndex = this.hostModel && this.hostModel.seriesIndex;\n    setCommonECData(seriesIndex, this.dataType, idx, el);\n    this._graphicEls[idx] = el;\n  };\n  SeriesData.prototype.getItemGraphicEl = function (idx) {\n    return this._graphicEls[idx];\n  };\n  SeriesData.prototype.eachItemGraphicEl = function (cb, context) {\n    zrUtil.each(this._graphicEls, function (el, idx) {\n      if (el) {\n        cb && cb.call(context, el, idx);\n      }\n    });\n  };\n  /**\n   * Shallow clone a new list except visual and layout properties, and graph elements.\n   * New list only change the indices.\n   */\n  SeriesData.prototype.cloneShallow = function (list) {\n    if (!list) {\n      list = new SeriesData(this._schema ? this._schema : map(this.dimensions, this._getDimInfo, this), this.hostModel);\n    }\n    transferProperties(list, this);\n    list._store = this._store;\n    return list;\n  };\n  /**\n   * Wrap some method to add more feature\n   */\n  SeriesData.prototype.wrapMethod = function (methodName, injectFunction) {\n    var originalMethod = this[methodName];\n    if (!zrUtil.isFunction(originalMethod)) {\n      return;\n    }\n    this.__wrappedMethods = this.__wrappedMethods || [];\n    this.__wrappedMethods.push(methodName);\n    this[methodName] = function () {\n      var res = originalMethod.apply(this, arguments);\n      return injectFunction.apply(this, [res].concat(zrUtil.slice(arguments)));\n    };\n  };\n  // ----------------------------------------------------------\n  // A work around for internal method visiting private member.\n  // ----------------------------------------------------------\n  SeriesData.internalField = function () {\n    prepareInvertedIndex = function (data) {\n      var invertedIndicesMap = data._invertedIndicesMap;\n      zrUtil.each(invertedIndicesMap, function (invertedIndices, dim) {\n        var dimInfo = data._dimInfos[dim];\n        // Currently, only dimensions that has ordinalMeta can create inverted indices.\n        var ordinalMeta = dimInfo.ordinalMeta;\n        var store = data._store;\n        if (ordinalMeta) {\n          invertedIndices = invertedIndicesMap[dim] = new CtorInt32Array(ordinalMeta.categories.length);\n          // The default value of TypedArray is 0. To avoid miss\n          // mapping to 0, we should set it as INDEX_NOT_FOUND.\n          for (var i = 0; i < invertedIndices.length; i++) {\n            invertedIndices[i] = INDEX_NOT_FOUND;\n          }\n          for (var i = 0; i < store.count(); i++) {\n            // Only support the case that all values are distinct.\n            invertedIndices[store.get(dimInfo.storeDimIndex, i)] = i;\n          }\n        }\n      });\n    };\n    getIdNameFromStore = function (data, dimIdx, idx) {\n      return convertOptionIdName(data._getCategory(dimIdx, idx), null);\n    };\n    /**\n     * @see the comment of `List['getId']`.\n     */\n    getId = function (data, rawIndex) {\n      var id = data._idList[rawIndex];\n      if (id == null && data._idDimIdx != null) {\n        id = getIdNameFromStore(data, data._idDimIdx, rawIndex);\n      }\n      if (id == null) {\n        id = ID_PREFIX + rawIndex;\n      }\n      return id;\n    };\n    normalizeDimensions = function (dimensions) {\n      if (!zrUtil.isArray(dimensions)) {\n        dimensions = dimensions != null ? [dimensions] : [];\n      }\n      return dimensions;\n    };\n    /**\n     * Data in excludeDimensions is copied, otherwise transferred.\n     */\n    cloneListForMapAndSample = function (original) {\n      var list = new SeriesData(original._schema ? original._schema : map(original.dimensions, original._getDimInfo, original), original.hostModel);\n      // FIXME If needs stackedOn, value may already been stacked\n      transferProperties(list, original);\n      return list;\n    };\n    transferProperties = function (target, source) {\n      zrUtil.each(TRANSFERABLE_PROPERTIES.concat(source.__wrappedMethods || []), function (propName) {\n        if (source.hasOwnProperty(propName)) {\n          target[propName] = source[propName];\n        }\n      });\n      target.__wrappedMethods = source.__wrappedMethods;\n      zrUtil.each(CLONE_PROPERTIES, function (propName) {\n        target[propName] = zrUtil.clone(source[propName]);\n      });\n      target._calculationInfo = zrUtil.extend({}, source._calculationInfo);\n    };\n    makeIdFromName = function (data, idx) {\n      var nameList = data._nameList;\n      var idList = data._idList;\n      var nameDimIdx = data._nameDimIdx;\n      var idDimIdx = data._idDimIdx;\n      var name = nameList[idx];\n      var id = idList[idx];\n      if (name == null && nameDimIdx != null) {\n        nameList[idx] = name = getIdNameFromStore(data, nameDimIdx, idx);\n      }\n      if (id == null && idDimIdx != null) {\n        idList[idx] = id = getIdNameFromStore(data, idDimIdx, idx);\n      }\n      if (id == null && name != null) {\n        var nameRepeatCount = data._nameRepeatCount;\n        var nmCnt = nameRepeatCount[name] = (nameRepeatCount[name] || 0) + 1;\n        id = name;\n        if (nmCnt > 1) {\n          id += '__ec__' + nmCnt;\n        }\n        idList[idx] = id;\n      }\n    };\n  }();\n  return SeriesData;\n}();\nexport default SeriesData;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { registerPreprocessor, registerProcessor, registerPostInit, registerPostUpdate, registerAction, registerCoordinateSystem, registerLayout, registerVisual, registerTransform, registerLoading, registerMap, registerUpdateLifecycle, PRIORITY } from './core/echarts.js';\nimport ComponentView from './view/Component.js';\nimport ChartView from './view/Chart.js';\nimport ComponentModel from './model/Component.js';\nimport SeriesModel from './model/Series.js';\nimport { isFunction, indexOf, isArray, each } from 'zrender/lib/core/util.js';\nimport { registerImpl } from './core/impl.js';\nimport { registerPainter } from 'zrender/lib/zrender.js';\nvar extensions = [];\nvar extensionRegisters = {\n  registerPreprocessor: registerPreprocessor,\n  registerProcessor: registerProcessor,\n  registerPostInit: registerPostInit,\n  registerPostUpdate: registerPostUpdate,\n  registerUpdateLifecycle: registerUpdateLifecycle,\n  registerAction: registerAction,\n  registerCoordinateSystem: registerCoordinateSystem,\n  registerLayout: registerLayout,\n  registerVisual: registerVisual,\n  registerTransform: registerTransform,\n  registerLoading: registerLoading,\n  registerMap: registerMap,\n  registerImpl: registerImpl,\n  PRIORITY: PRIORITY,\n  ComponentModel: ComponentModel,\n  ComponentView: ComponentView,\n  SeriesModel: SeriesModel,\n  ChartView: ChartView,\n  // TODO Use ComponentModel and SeriesModel instead of Constructor\n  registerComponentModel: function (ComponentModelClass) {\n    ComponentModel.registerClass(ComponentModelClass);\n  },\n  registerComponentView: function (ComponentViewClass) {\n    ComponentView.registerClass(ComponentViewClass);\n  },\n  registerSeriesModel: function (SeriesModelClass) {\n    SeriesModel.registerClass(SeriesModelClass);\n  },\n  registerChartView: function (ChartViewClass) {\n    ChartView.registerClass(ChartViewClass);\n  },\n  registerSubTypeDefaulter: function (componentType, defaulter) {\n    ComponentModel.registerSubTypeDefaulter(componentType, defaulter);\n  },\n  registerPainter: function (painterType, PainterCtor) {\n    registerPainter(painterType, PainterCtor);\n  }\n};\nexport function use(ext) {\n  if (isArray(ext)) {\n    // use([ChartLine, ChartBar]);\n    each(ext, function (singleExt) {\n      use(singleExt);\n    });\n    return;\n  }\n  if (indexOf(extensions, ext) >= 0) {\n    return;\n  }\n  extensions.push(ext);\n  if (isFunction(ext)) {\n    ext = {\n      install: ext\n    };\n  }\n  ext.install(extensionRegisters);\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { each, isString } from 'zrender/lib/core/util.js';\nimport { isSeriesDataSchema } from './SeriesDataSchema.js';\n/**\n * Note that it is too complicated to support 3d stack by value\n * (have to create two-dimension inverted index), so in 3d case\n * we just support that stacked by index.\n *\n * @param seriesModel\n * @param dimensionsInput The same as the input of <module:echarts/data/SeriesData>.\n *        The input will be modified.\n * @param opt\n * @param opt.stackedCoordDimension Specify a coord dimension if needed.\n * @param opt.byIndex=false\n * @return calculationInfo\n * {\n *     stackedDimension: string\n *     stackedByDimension: string\n *     isStackedByIndex: boolean\n *     stackedOverDimension: string\n *     stackResultDimension: string\n * }\n */\nexport function enableDataStack(seriesModel, dimensionsInput, opt) {\n  opt = opt || {};\n  var byIndex = opt.byIndex;\n  var stackedCoordDimension = opt.stackedCoordDimension;\n  var dimensionDefineList;\n  var schema;\n  var store;\n  if (isLegacyDimensionsInput(dimensionsInput)) {\n    dimensionDefineList = dimensionsInput;\n  } else {\n    schema = dimensionsInput.schema;\n    dimensionDefineList = schema.dimensions;\n    store = dimensionsInput.store;\n  }\n  // Compatibal: when `stack` is set as '', do not stack.\n  var mayStack = !!(seriesModel && seriesModel.get('stack'));\n  var stackedByDimInfo;\n  var stackedDimInfo;\n  var stackResultDimension;\n  var stackedOverDimension;\n  each(dimensionDefineList, function (dimensionInfo, index) {\n    if (isString(dimensionInfo)) {\n      dimensionDefineList[index] = dimensionInfo = {\n        name: dimensionInfo\n      };\n    }\n    if (mayStack && !dimensionInfo.isExtraCoord) {\n      // Find the first ordinal dimension as the stackedByDimInfo.\n      if (!byIndex && !stackedByDimInfo && dimensionInfo.ordinalMeta) {\n        stackedByDimInfo = dimensionInfo;\n      }\n      // Find the first stackable dimension as the stackedDimInfo.\n      if (!stackedDimInfo && dimensionInfo.type !== 'ordinal' && dimensionInfo.type !== 'time' && (!stackedCoordDimension || stackedCoordDimension === dimensionInfo.coordDim)) {\n        stackedDimInfo = dimensionInfo;\n      }\n    }\n  });\n  if (stackedDimInfo && !byIndex && !stackedByDimInfo) {\n    // Compatible with previous design, value axis (time axis) only stack by index.\n    // It may make sense if the user provides elaborately constructed data.\n    byIndex = true;\n  }\n  // Add stack dimension, they can be both calculated by coordinate system in `unionExtent`.\n  // That put stack logic in List is for using conveniently in echarts extensions, but it\n  // might not be a good way.\n  if (stackedDimInfo) {\n    // Use a weird name that not duplicated with other names.\n    // Also need to use seriesModel.id as postfix because different\n    // series may share same data store. The stack dimension needs to be distinguished.\n    stackResultDimension = '__\\0ecstackresult_' + seriesModel.id;\n    stackedOverDimension = '__\\0ecstackedover_' + seriesModel.id;\n    // Create inverted index to fast query index by value.\n    if (stackedByDimInfo) {\n      stackedByDimInfo.createInvertedIndices = true;\n    }\n    var stackedDimCoordDim_1 = stackedDimInfo.coordDim;\n    var stackedDimType = stackedDimInfo.type;\n    var stackedDimCoordIndex_1 = 0;\n    each(dimensionDefineList, function (dimensionInfo) {\n      if (dimensionInfo.coordDim === stackedDimCoordDim_1) {\n        stackedDimCoordIndex_1++;\n      }\n    });\n    var stackedOverDimensionDefine = {\n      name: stackResultDimension,\n      coordDim: stackedDimCoordDim_1,\n      coordDimIndex: stackedDimCoordIndex_1,\n      type: stackedDimType,\n      isExtraCoord: true,\n      isCalculationCoord: true,\n      storeDimIndex: dimensionDefineList.length\n    };\n    var stackResultDimensionDefine = {\n      name: stackedOverDimension,\n      // This dimension contains stack base (generally, 0), so do not set it as\n      // `stackedDimCoordDim` to avoid extent calculation, consider log scale.\n      coordDim: stackedOverDimension,\n      coordDimIndex: stackedDimCoordIndex_1 + 1,\n      type: stackedDimType,\n      isExtraCoord: true,\n      isCalculationCoord: true,\n      storeDimIndex: dimensionDefineList.length + 1\n    };\n    if (schema) {\n      if (store) {\n        stackedOverDimensionDefine.storeDimIndex = store.ensureCalculationDimension(stackedOverDimension, stackedDimType);\n        stackResultDimensionDefine.storeDimIndex = store.ensureCalculationDimension(stackResultDimension, stackedDimType);\n      }\n      schema.appendCalculationDimension(stackedOverDimensionDefine);\n      schema.appendCalculationDimension(stackResultDimensionDefine);\n    } else {\n      dimensionDefineList.push(stackedOverDimensionDefine);\n      dimensionDefineList.push(stackResultDimensionDefine);\n    }\n  }\n  return {\n    stackedDimension: stackedDimInfo && stackedDimInfo.name,\n    stackedByDimension: stackedByDimInfo && stackedByDimInfo.name,\n    isStackedByIndex: byIndex,\n    stackedOverDimension: stackedOverDimension,\n    stackResultDimension: stackResultDimension\n  };\n}\nfunction isLegacyDimensionsInput(dimensionsInput) {\n  return !isSeriesDataSchema(dimensionsInput.schema);\n}\nexport function isDimensionStacked(data, stackedDim) {\n  // Each single series only maps to one pair of axis. So we do not need to\n  // check stackByDim, whatever stacked by a dimension or stacked by index.\n  return !!stackedDim && stackedDim === data.getCalculationInfo('stackedDimension');\n}\nexport function getStackedDimension(data, targetDim) {\n  return isDimensionStacked(data, targetDim) ? data.getCalculationInfo('stackResultDimension') : targetDim;\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,IAAI;AAAA;AAAA,EAA6B,WAAY;AAC3C,aAASA,iBAAgB;AACvB,WAAK,QAAQ,IAAI,cAAM;AACvB,WAAK,MAAoB,OAAO,eAAe;AAAA,IACjD;AACA,IAAAA,eAAc,UAAU,OAAO,SAAU,SAAS,KAAK;AAAA,IAAC;AACxD,IAAAA,eAAc,UAAU,SAAS,SAAU,OAAO,SAAS,KAAK,SAAS;AAAA,IAAC;AAC1E,IAAAA,eAAc,UAAU,UAAU,SAAU,SAAS,KAAK;AAAA,IAAC;AAC3D,IAAAA,eAAc,UAAU,aAAa,SAAU,OAAO,SAAS,KAAK,SAAS;AAAA,IAE7E;AACA,IAAAA,eAAc,UAAU,eAAe,SAAU,OAAO,SAAS,KAAK,SAAS;AAAA,IAE/E;AACA,IAAAA,eAAc,UAAU,eAAe,SAAU,OAAO,SAAS,KAAK,SAAS;AAAA,IAE/E;AAKA,IAAAA,eAAc,UAAU,mBAAmB,SAAU,cAAc,QAAQ,SAAS;AAAA,IAEpF;AAOA,IAAAA,eAAc,UAAU,eAAe,SAAU,IAAI;AACnD,UAAI,QAAQ,KAAK;AACjB,UAAI,OAAO;AACT,cAAM,SAAS,EAAE;AAAA,MACnB;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEQ,kBAAkB,aAAa;AAC/B,sBAAsB,aAAa;AAC7C,IAAOC,qBAAQ;;;ACxCA,SAAR,sBAAuC;AAC5C,MAAIC,SAAQ,UAAU;AACtB,SAAO,SAAU,aAAa;AAC5B,QAAI,SAASA,OAAM,WAAW;AAC9B,QAAI,kBAAkB,YAAY;AAClC,QAAI,gBAAgB,CAAC,CAAC,OAAO;AAC7B,QAAI,sBAAsB,CAAC,CAAC,OAAO;AAInC,QAAI,QAAQ,OAAO,QAAQ,CAAC,EAAE,mBAAmB,gBAAgB;AACjE,QAAI,cAAc,OAAO,oBAAoB,CAAC,EAAE,mBAAmB,gBAAgB;AACnF,WAAO,CAAC,EAAE,kBAAkB,SAAS,wBAAwB,gBAAgB;AAAA,EAC/E;AACF;;;ACRA,IAAI,QAAkB,UAAU;AAChC,IAAI,gBAAgB,oBAAoB;AACxC,IAAI;AAAA;AAAA,EAAyB,WAAY;AACvC,aAASC,aAAY;AACnB,WAAK,QAAQ,IAAI,cAAM;AACvB,WAAK,MAAoB,OAAO,WAAW;AAC3C,WAAK,aAAa,WAAW;AAAA,QAC3B,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AACD,WAAK,WAAW,UAAU;AAAA,QACxB,MAAM;AAAA,MACR;AAAA,IACF;AACA,IAAAA,WAAU,UAAU,OAAO,SAAU,SAAS,KAAK;AAAA,IAAC;AACpD,IAAAA,WAAU,UAAU,SAAS,SAAU,aAAa,SAAS,KAAK,SAAS;AACzE,UAAI,MAAuC;AACzC,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACvD;AAAA,IACF;AAIA,IAAAA,WAAU,UAAU,YAAY,SAAU,aAAa,SAAS,KAAK,SAAS;AAC5E,UAAI,OAAO,YAAY,QAAQ,WAAW,QAAQ,QAAQ;AAC1D,UAAI,CAAC,MAAM;AACT,YAAI,MAAuC;AACzC,gBAAM,sBAAsB,QAAQ,QAAQ;AAAA,QAC9C;AACA;AAAA,MACF;AACA,sBAAgB,MAAM,SAAS,UAAU;AAAA,IAC3C;AAIA,IAAAA,WAAU,UAAU,WAAW,SAAU,aAAa,SAAS,KAAK,SAAS;AAC3E,UAAI,OAAO,YAAY,QAAQ,WAAW,QAAQ,QAAQ;AAC1D,UAAI,CAAC,MAAM;AACT,YAAI,MAAuC;AACzC,gBAAM,sBAAsB,QAAQ,QAAQ;AAAA,QAC9C;AACA;AAAA,MACF;AACA,sBAAgB,MAAM,SAAS,QAAQ;AAAA,IACzC;AAIA,IAAAA,WAAU,UAAU,SAAS,SAAU,SAAS,KAAK;AACnD,WAAK,MAAM,UAAU;AAAA,IACvB;AAIA,IAAAA,WAAU,UAAU,UAAU,SAAU,SAAS,KAAK;AAAA,IAAC;AACvD,IAAAA,WAAU,UAAU,aAAa,SAAU,aAAa,SAAS,KAAK,SAAS;AAC7E,WAAK,OAAO,aAAa,SAAS,KAAK,OAAO;AAAA,IAChD;AAEA,IAAAA,WAAU,UAAU,eAAe,SAAU,aAAa,SAAS,KAAK,SAAS;AAC/E,WAAK,OAAO,aAAa,SAAS,KAAK,OAAO;AAAA,IAChD;AAEA,IAAAA,WAAU,UAAU,eAAe,SAAU,aAAa,SAAS,KAAK,SAAS;AAC/E,WAAK,OAAO,aAAa,SAAS,KAAK,OAAO;AAAA,IAChD;AAOA,IAAAA,WAAU,UAAU,eAAe,SAAU,IAAI;AAC/C,uBAAiB,KAAK,OAAO,EAAE;AAAA,IACjC;AACA,IAAAA,WAAU,mBAAmB,SAAU,SAAS,YAAY;AAC1D,YAAM,OAAO,EAAE,eAAe;AAAA,IAChC;AACA,IAAAA,WAAU,kBAAkB,WAAY;AACtC,UAAI,QAAQA,WAAU;AACtB,YAAM,OAAO;AAAA,IACf,EAAE;AACF,WAAOA;AAAA,EACT,EAAE;AAAA;AAKF,SAAS,WAAW,IAAI,OAAO,gBAAgB;AAC7C,MAAI,MAAM,qBAAqB,EAAE,GAAG;AAClC,KAAC,UAAU,aAAa,gBAAgB,eAAe,IAAI,cAAc;AAAA,EAC3E;AACF;AACA,SAAS,gBAAgB,MAAM,SAAS,OAAO;AAC7C,MAAI,YAAsB,eAAe,MAAM,OAAO;AACtD,MAAI,iBAAiB,WAAW,QAAQ,gBAAgB,OAAO,kBAAkB,QAAQ,YAAY,IAAI;AACzG,MAAI,aAAa,MAAM;AACrB,SAAe,iBAAiB,SAAS,GAAG,SAAU,SAAS;AAC7D,iBAAW,KAAK,iBAAiB,OAAO,GAAG,OAAO,cAAc;AAAA,IAClE,CAAC;AAAA,EACH,OAAO;AACL,SAAK,kBAAkB,SAAU,IAAI;AACnC,iBAAW,IAAI,OAAO,cAAc;AAAA,IACtC,CAAC;AAAA,EACH;AACF;AACU,kBAAkB,WAAW,CAAC,SAAS,CAAC;AACxC,sBAAsB,SAAS;AACzC,SAAS,eAAe,SAAS;AAC/B,SAAO,cAAc,QAAQ,KAAK;AACpC;AACA,SAAS,gBAAgB,SAAS;AAChC,MAAI,cAAc,QAAQ;AAC1B,MAAI,UAAU,QAAQ;AACtB,MAAI,MAAM,QAAQ;AAClB,MAAI,UAAU,QAAQ;AAEtB,MAAI,oBAAoB,YAAY,gBAAgB;AACpD,MAAI,OAAO,QAAQ;AACnB,MAAI,eAAe,WAAW,MAAM,OAAO,EAAE;AAC7C,MAAI,aAAa,oBAAoB,6BAA6B,gBAAgB,KAAK,YAAY,IAAI,eAGrG;AACF,MAAI,eAAe,UAAU;AAC3B,SAAK,UAAU,EAAE,aAAa,SAAS,KAAK,OAAO;AAAA,EACrD;AACA,SAAO,kBAAkB,UAAU;AACrC;AACA,IAAI,oBAAoB;AAAA,EACtB,0BAA0B;AAAA,IACxB,UAAU,SAAU,QAAQ,SAAS;AACnC,cAAQ,KAAK,kBAAkB,QAAQ,QAAQ,OAAO,QAAQ,SAAS,QAAQ,KAAK,QAAQ,OAAO;AAAA,IACrG;AAAA,EACF;AAAA,EACA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,IAKN,oBAAoB;AAAA,IACpB,UAAU,SAAU,QAAQ,SAAS;AACnC,cAAQ,KAAK,OAAO,QAAQ,OAAO,QAAQ,SAAS,QAAQ,KAAK,QAAQ,OAAO;AAAA,IAClF;AAAA,EACF;AACF;AACA,IAAO,gBAAQ;;;AC7Jf,IAAI,gBAAgB;AACpB,IAAI,OAAO;AACX,IAAI,gBAAgB;AAWb,SAAS,SAAS,IAAI,OAAO,UAAU;AAC5C,MAAI;AACJ,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,UAAQ,SAAS;AACjB,WAAS,OAAO;AACd,gBAAW,oBAAI,KAAK,GAAE,QAAQ;AAC9B,YAAQ;AACR,OAAG,MAAM,OAAO,QAAQ,CAAC,CAAC;AAAA,EAC5B;AACA,MAAI,KAAK,WAAY;AACnB,QAAI,SAAS,CAAC;AACd,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,aAAO,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,gBAAW,oBAAI,KAAK,GAAE,QAAQ;AAC9B,YAAQ;AACR,WAAO;AACP,QAAI,YAAY,oBAAoB;AACpC,QAAI,eAAe,oBAAoB;AACvC,uBAAmB;AACnB,WAAO,YAAY,eAAe,WAAW,YAAY;AACzD,iBAAa,KAAK;AASlB,QAAI,cAAc;AAChB,cAAQ,WAAW,MAAM,SAAS;AAAA,IACpC,OAAO;AACL,UAAI,QAAQ,GAAG;AACb,aAAK;AAAA,MACP,OAAO;AACL,gBAAQ,WAAW,MAAM,CAAC,IAAI;AAAA,MAChC;AAAA,IACF;AACA,eAAW;AAAA,EACb;AAKA,KAAG,QAAQ,WAAY;AACrB,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AAAA,IACV;AAAA,EACF;AAIA,KAAG,mBAAmB,SAAU,eAAe;AAC7C,uBAAmB;AAAA,EACrB;AACA,SAAO;AACT;AAsBO,SAAS,eAAe,KAAK,QAAQ,MAAM,cAAc;AAC9D,MAAI,KAAK,IAAI,MAAM;AACnB,MAAI,CAAC,IAAI;AACP;AAAA,EACF;AACA,MAAI,WAAW,GAAG,aAAa,KAAK;AACpC,MAAI,mBAAmB,GAAG,aAAa;AACvC,MAAI,WAAW,GAAG,IAAI;AACtB,MAAI,aAAa,QAAQ,qBAAqB,cAAc;AAC1D,QAAI,QAAQ,QAAQ,CAAC,cAAc;AACjC,aAAO,IAAI,MAAM,IAAI;AAAA,IACvB;AACA,SAAK,IAAI,MAAM,IAAI,SAAS,UAAU,MAAM,iBAAiB,UAAU;AACvE,OAAG,aAAa,IAAI;AACpB,OAAG,aAAa,IAAI;AACpB,OAAG,IAAI,IAAI;AAAA,EACb;AACA,SAAO;AACT;AAIO,SAAS,MAAM,KAAK,QAAQ;AACjC,MAAI,KAAK,IAAI,MAAM;AACnB,MAAI,MAAM,GAAG,aAAa,GAAG;AAE3B,OAAG,SAAS,GAAG,MAAM;AACrB,QAAI,MAAM,IAAI,GAAG,aAAa;AAAA,EAChC;AACF;;;AChIA,IAAI,WAAW;AAEf,IAAI,OAAO,cAAc,aAAa;AAEpC,aAAW,UAAU,YAAY;AACnC;AACA,IAAI,aAAa;AACjB,IAAO,wBAAQ;AAAA,EACb,UAAU;AAAA;AAAA,EAEV,SAAS;AAAA,EACT,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAAA,EACzG,eAAe,CAAC,WAAW,WAAW,SAAS;AAAA,EAC/C,MAAM;AAAA,IACJ,OAAO;AAAA,MACL,QAAQ,CAAC;AAAA,QACP,OAAO;AAAA,QACP,YAAY,CAAC,GAAG,CAAC;AAAA,QACjB,YAAY,CAAC,GAAG,CAAC;AAAA,QACjB,YAAY;AAAA,QACZ,UAAU,KAAK,KAAK;AAAA,MACtB,GAAG;AAAA,QACD,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,QACjC,YAAY,CAAC,GAAG,CAAC;AAAA,QACjB,YAAY;AAAA,MACd,GAAG;AAAA,QACD,OAAO;AAAA,QACP,YAAY,CAAC,GAAG,CAAC;AAAA,QACjB,YAAY,CAAC,GAAG,CAAC;AAAA,QACjB,UAAU,CAAC,KAAK,KAAK;AAAA,MACvB,GAAG;AAAA,QACD,OAAO;AAAA,QACP,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,QACjC,YAAY,CAAC,GAAG,CAAC;AAAA,MACnB,GAAG;AAAA,QACD,OAAO;AAAA,QACP,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,QAC3B,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,QACvB,UAAU,KAAK,KAAK;AAAA,MACtB,GAAG;AAAA,QACD,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,QACjC,YAAY,CAAC,GAAG,CAAC;AAAA,QACjB,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA,EAGA,WAAW;AAAA;AAAA;AAAA;AAAA,IAIT,YAAY,SAAS,MAAM,MAAM,IAAI,oBAAoB;AAAA;AAAA,IAEzD,UAAU;AAAA,IACV,WAAW;AAAA,IACX,YAAY;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AAAA,EACX,gBAAgB;AAAA,IACd,UAAU;AAAA,IACV,QAAQ;AAAA,EACV;AAAA,EACA,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,yBAAyB;AAAA,EACzB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA;AAAA,EAEpB,sBAAsB;AAAA,EACtB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,qBAAqB;AAAA;AAAA,EAErB,QAAQ;AACV;;;ACrFA,IAAI,2BAA2B,cAAc;AACtC,SAAS,8BAA8B,UAAU,SAAS;AAC/D,SAAO,yBAAyB,IAAI,QAAQ,KAAK,QAAQ,OAAO;AAChE,2BAAyB,IAAI,UAAU,OAAO;AAChD;AACO,SAAS,sBAAsB,SAAS,UAAU,mBAAmB;AAC1E,MAAI,wBAAwB,yBAAyB,IAAI,QAAQ;AACjE,MAAI,CAAC,uBAAuB;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,sBAAsB,OAAO;AACnD,MAAI,CAAC,iBAAiB;AACpB,WAAO;AAAA,EACT;AACA,MAAI,MAAuC;AACzC,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,aAAO,sBAAsB,gBAAgB,CAAC,CAAC,CAAC;AAAA,IAClD;AAAA,EACF;AACA,SAAO,kBAAkB,OAAO,eAAe;AACjD;;;ACKA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,wBAAwB;AAAA,EAC1B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,aAAa;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AACd;AACA,IAAI,qBAAqB;AAAA,EACvB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,KAAK;AAAA,EACL,SAAS;AAAA,EACT,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,aAAa;AAAA,EACb,eAAe;AAAA,EACf,OAAO;AAAA,EACP,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,QAAQ;AACV;AACA,IAAI,6BAA6B,CAAC;AAClC,SAAS,uBAAuB,QAAQ;AACtC,OAAK,QAAQ,SAAU,iBAAiB,UAAU;AAChD,QAAI,CAAC,kBAAe,SAAS,QAAQ,GAAG;AACtC,UAAI,sBAAsB,sBAAsB,QAAQ;AACxD,UAAI,uBAAuB,CAAC,2BAA2B,mBAAmB,GAAG;AAC3E,cAAM,eAAe,WAAW,0CAA0C,sBAAsB,iDAAiD,sBAAsB,KAAK;AAC5K,mCAA2B,mBAAmB,IAAI;AAAA,MACpD;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC/C,cAAUC,cAAa,MAAM;AAC7B,aAASA,eAAc;AACrB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,aAAY,UAAU,OAAO,SAAU,QAAQ,aAAa,SAASC,QAAO,QAAQ,eAAe;AACjG,MAAAA,SAAQA,UAAS,CAAC;AAClB,WAAK,SAAS;AACd,WAAK,SAAS,IAAI,cAAMA,MAAK;AAC7B,WAAK,UAAU,IAAI,cAAM,MAAM;AAC/B,WAAK,iBAAiB;AAAA,IACxB;AACA,IAAAD,aAAY,UAAU,YAAY,SAAU,QAAQ,MAAME,0BAAyB;AACjF,UAAI,MAAuC;AACzC,eAAO,UAAU,MAAM,0BAA0B;AACjD,eAAO,OAAO,gBAAgB,MAAM,oBAAoB,8BAA8B;AAAA,MACxF;AACA,UAAI,WAAW,wBAAwB,IAAI;AAC3C,WAAK,eAAe,UAAU,QAAQA,0BAAyB,QAAQ;AACvE,WAAK,aAAa,MAAM,QAAQ;AAAA,IAClC;AAQA,IAAAF,aAAY,UAAU,cAAc,SAAU,MAAM,KAAK;AACvD,aAAO,KAAK,aAAa,MAAM,wBAAwB,GAAG,CAAC;AAAA,IAC7D;AACA,IAAAA,aAAY,UAAU,eAAe,SAAU,MAAM,KAAK;AACxD,UAAI,gBAAgB;AACpB,UAAI,gBAAgB,KAAK;AACzB,UAAI,CAAC,QAAQ,SAAS,YAAY;AAChC,YAAI,aAAa,cAAc,YAAY,SAAS,UAAU;AAC9D,YAAI,MAAuC;AACzC,iCAAuB,UAAU;AAAA,QACnC;AACA,YAAI,CAAC,KAAK,UAAU,SAAS,YAAY;AACvC,mBAAS,MAAM,UAAU;AAAA,QAC3B,OAAO;AACL,eAAK,YAAY;AACjB,eAAK,aAAa,YAAY,GAAG;AAAA,QACnC;AACA,wBAAgB;AAAA,MAClB;AACA,UAAI,SAAS,cAAc,SAAS,SAAS;AAC3C,aAAK,YAAY;AAAA,MACnB;AASA,UAAI,CAAC,QAAQ,SAAS,cAAc,SAAS,YAAY;AACvD,YAAI,iBAAiB,cAAc,kBAAkB,IAAI;AACzD,YAAI,gBAAgB;AAClB,0BAAgB;AAChB,eAAK,aAAa,gBAAgB,GAAG;AAAA,QACvC;AAAA,MACF;AACA,UAAI,CAAC,QAAQ,SAAS,cAAc,SAAS,SAAS;AACpD,YAAI,eAAe,cAAc,eAAe,IAAI;AACpD,YAAI,aAAa,QAAQ;AACvB,eAAK,cAAc,SAAU,aAAa;AACxC,4BAAgB;AAChB,iBAAK,aAAa,aAAa,GAAG;AAAA,UACpC,GAAG,IAAI;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAAA,aAAY,UAAU,cAAc,SAAU,QAAQ;AACpD,WAAK,aAAa,QAAQ,IAAI;AAAA,IAChC;AACA,IAAAA,aAAY,UAAU,eAAe,SAAU,WAAW,KAAK;AAC7D,UAAI,SAAS,KAAK;AAClB,UAAI,gBAAgB,KAAK;AACzB,UAAI,kBAAkB,KAAK;AAC3B,UAAI,eAAe,CAAC;AACpB,UAAI,iBAAiB,cAAc;AACnC,UAAI,0BAA0B,OAAO,IAAI;AACzC,2BAAqB,IAAI;AAGzB,WAAK,WAAW,SAAU,iBAAiB,UAAU;AACnD,YAAI,mBAAmB,MAAM;AAC3B;AAAA,QACF;AACA,YAAI,CAAC,kBAAe,SAAS,QAAQ,GAAG;AAEtC,iBAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,OAAO,MAAM,eAAe,IAAI,MAAM,OAAO,QAAQ,GAAG,iBAAiB,IAAI;AAAA,QACtH,WAAW,UAAU;AACnB,uBAAa,KAAK,QAAQ;AAC1B,yBAAe,IAAI,UAAU,IAAI;AAAA,QACnC;AAAA,MACF,CAAC;AACD,UAAI,yBAAyB;AAK3B,gCAAwB,KAAK,SAAU,KAAK,wBAAwB;AAClE,cAAI,kBAAe,SAAS,sBAAsB,KAAK,CAAC,eAAe,IAAI,sBAAsB,GAAG;AAClG,yBAAa,KAAK,sBAAsB;AACxC,2BAAe,IAAI,wBAAwB,IAAI;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,MACH;AACA,wBAAe,kBAAkB,cAAc,kBAAe,qBAAqB,GAAG,gBAAgB,IAAI;AAC1G,eAAS,eAAe,UAAU;AAChC,YAAI,oBAAoB,sBAAsB,MAAM,UAAoB,iBAAiB,UAAU,QAAQ,CAAC,CAAC;AAC7G,YAAI,cAAc,cAAc,IAAI,QAAQ;AAC5C,YAAI;AAAA;AAAA,UAEJ,CAAC,cAAc,eAAe,2BAA2B,wBAAwB,IAAI,QAAQ,IAAI,iBAAiB;AAAA;AAClH,YAAI,gBAA0B,gBAAgB,aAAa,mBAAmB,SAAS;AAEvF,QAAU,0BAA0B,eAAe,UAAU,iBAAc;AAI3E,eAAO,QAAQ,IAAI;AACnB,sBAAc,IAAI,UAAU,IAAI;AAChC,wBAAgB,IAAI,UAAU,CAAC;AAC/B,YAAI,oBAAoB,CAAC;AACzB,YAAI,kBAAkB,CAAC;AACvB,YAAI,uBAAuB;AAC3B,YAAI;AACJ,YAAI;AACJ,aAAK,eAAe,SAAU,YAAY,OAAO;AAC/C,cAAI,iBAAiB,WAAW;AAChC,cAAI,gBAAgB,WAAW;AAC/B,cAAI,CAAC,eAAe;AAClB,gBAAI,gBAAgB;AAIlB,6BAAe,YAAY,CAAC,GAAG,IAAI;AACnC,6BAAe,cAAc,CAAC,GAAG,KAAK;AAAA,YACxC;AAAA,UAIF,OAAO;AACL,gBAAI,eAAe,aAAa;AAChC,gBAAI,sBAAsB,kBAAe;AAAA,cAAS;AAAA,cAAU,WAAW,QAAQ;AAAA,cAAS,CAAC;AAAA;AAAA,YACzF;AAEA,gBAAI,CAAC,qBAAqB;AACxB,kBAAI,MAAuC;AACzC,oBAAI,UAAU,WAAW,QAAQ;AACjC,oBAAI,mBAAmB,mBAAmB,OAAO;AACjD,oBAAI,CAAC,2BAA2B,OAAO,GAAG;AACxC,6CAA2B,OAAO,IAAI;AACtC,sBAAI,kBAAkB;AACpB,0BAAM,YAAY,UAAU,0CAA0C,mBAAmB,6CAA6C,mBAAmB,KAAK;AAAA,kBAChK,OAAO;AACL,0BAAM,oBAAoB,OAAO;AAAA,kBACnC;AAAA,gBACF;AAAA,cACF;AACA;AAAA,YACF;AAEA,gBAAI,aAAa,WAAW;AAC1B,kBAAI,eAAe;AACjB,oBAAI,MAAuC;AACzC,sBAAI,CAAC,sBAAsB;AACzB,yBAAK,kDAAkD;AACvD,2CAAuB;AAAA,kBACzB;AAAA,gBACF;AACA;AAAA,cACF;AACA,8BAAgB;AAAA,YAClB;AACA,gBAAI,kBAAkB,eAAe,gBAAgB,qBAAqB;AACxE,6BAAe,OAAO,WAAW,QAAQ;AAEzC,6BAAe,YAAY,eAAe,IAAI;AAC9C,6BAAe,cAAc,eAAe,KAAK;AAAA,YACnD,OAAO;AAEL,kBAAI,WAAW,OAAO;AAAA,gBACpB,gBAAgB;AAAA,cAClB,GAAG,WAAW,OAAO;AACrB,+BAAiB,IAAI,oBAAoB,eAAe,MAAM,MAAM,QAAQ;AAE5E,qBAAO,gBAAgB,QAAQ;AAC/B,kBAAI,WAAW,UAAU;AACvB,+BAAe,mBAAmB;AAAA,cACpC;AACA,6BAAe,KAAK,eAAe,MAAM,IAAI;AAK7C,6BAAe,cAAc,MAAM,IAAI;AAAA,YACzC;AAAA,UACF;AACA,cAAI,gBAAgB;AAClB,8BAAkB,KAAK,eAAe,MAAM;AAC5C,4BAAgB,KAAK,cAAc;AACnC;AAAA,UACF,OAAO;AAEL,8BAAkB,KAAK,MAAM;AAC7B,4BAAgB,KAAK,MAAM;AAAA,UAC7B;AAAA,QACF,GAAG,IAAI;AACP,eAAO,QAAQ,IAAI;AACnB,sBAAc,IAAI,UAAU,eAAe;AAC3C,wBAAgB,IAAI,UAAU,oBAAoB;AAElD,YAAI,aAAa,UAAU;AACzB,gCAAsB,IAAI;AAAA,QAC5B;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,gBAAgB;AACxB,8BAAsB,IAAI;AAAA,MAC5B;AAAA,IACF;AAIA,IAAAA,aAAY,UAAU,YAAY,WAAY;AAC5C,UAAI,SAAS,MAAM,KAAK,MAAM;AAC9B,WAAK,QAAQ,SAAU,eAAe,UAAU;AAC9C,YAAI,kBAAe,SAAS,QAAQ,GAAG;AACrC,cAAI,OAAiB,iBAAiB,aAAa;AAInD,cAAI,UAAU,KAAK;AACnB,cAAI,cAAc;AAClB,mBAAS,IAAI,UAAU,GAAG,KAAK,GAAG,KAAK;AAErC,gBAAI,KAAK,CAAC,KAAK,CAAW,sBAAsB,KAAK,CAAC,CAAC,GAAG;AACxD,4BAAc;AAAA,YAChB,OAAO;AACL,mBAAK,CAAC,IAAI;AACV,eAAC,eAAe;AAAA,YAClB;AAAA,UACF;AACA,eAAK,SAAS;AACd,iBAAO,QAAQ,IAAI;AAAA,QACrB;AAAA,MACF,CAAC;AACD,aAAO,OAAO,gBAAgB;AAC9B,aAAO;AAAA,IACT;AACA,IAAAA,aAAY,UAAU,WAAW,WAAY;AAC3C,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,aAAY,UAAU,iBAAiB,WAAY;AACjD,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,aAAY,UAAU,mBAAmB,SAAU,SAAS;AAC1D,WAAK,WAAW;AAAA,IAClB;AACA,IAAAA,aAAY,UAAU,mBAAmB,WAAY;AACnD,aAAO,KAAK;AAAA,IACd;AAIA,IAAAA,aAAY,UAAU,eAAe,SAAU,UAAU,KAAK;AAC5D,UAAI,OAAO,KAAK,eAAe,IAAI,QAAQ;AAC3C,UAAI,MAAM;AACR,YAAI,OAAO,KAAK,OAAO,CAAC;AACxB,YAAI,MAAM;AACR,iBAAO;AAAA,QACT,WAAW,OAAO,MAAM;AACtB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,gBAAI,KAAK,CAAC,GAAG;AACX,qBAAO,KAAK,CAAC;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAIA,IAAAA,aAAY,UAAU,kBAAkB,SAAU,WAAW;AAC3D,UAAI,WAAW,UAAU;AACzB,UAAI,CAAC,UAAU;AACb,eAAO,CAAC;AAAA,MACV;AACA,UAAI,QAAQ,UAAU;AACtB,UAAI,KAAK,UAAU;AACnB,UAAI,OAAO,UAAU;AACrB,UAAI,QAAQ,KAAK,eAAe,IAAI,QAAQ;AAC5C,UAAI,CAAC,SAAS,CAAC,MAAM,QAAQ;AAC3B,eAAO,CAAC;AAAA,MACV;AACA,UAAI;AACJ,UAAI,SAAS,MAAM;AACjB,iBAAS,CAAC;AACV,aAAe,iBAAiB,KAAK,GAAG,SAAU,KAAK;AACrD,gBAAM,GAAG,KAAK,OAAO,KAAK,MAAM,GAAG,CAAC;AAAA,QACtC,CAAC;AAAA,MACH,WAAW,MAAM,MAAM;AACrB,iBAAS,gBAAgB,MAAM,IAAI,KAAK;AAAA,MAC1C,WAAW,QAAQ,MAAM;AACvB,iBAAS,gBAAgB,QAAQ,MAAM,KAAK;AAAA,MAC9C,OAAO;AAEL,iBAAS,OAAO,OAAO,SAAU,MAAM;AACrC,iBAAO,CAAC,CAAC;AAAA,QACX,CAAC;AAAA,MACH;AACA,aAAO,gBAAgB,QAAQ,SAAS;AAAA,IAC1C;AAkBA,IAAAA,aAAY,UAAU,iBAAiB,SAAU,WAAW;AAC1D,UAAI,QAAQ,UAAU;AACtB,UAAI,WAAW,UAAU;AACzB,UAAI,YAAY,aAAa,KAAK;AAClC,UAAI,SAAS,YAAY,KAAK,gBAAgB,SAAS,IAErD,OAAO,KAAK,eAAe,IAAI,QAAQ,GAAG,SAAU,MAAM;AAC1D,eAAO,CAAC,CAAC;AAAA,MACX,CAAC;AACD,aAAO,SAAS,gBAAgB,QAAQ,SAAS,CAAC;AAClD,eAAS,aAAa,GAAG;AACvB,YAAI,YAAY,WAAW;AAC3B,YAAI,SAAS,WAAW;AACxB,YAAI,WAAW,WAAW;AAC1B,eAAO,MAAM,EAAE,SAAS,KAAK,QAAQ,EAAE,MAAM,KAAK,QAAQ,EAAE,QAAQ,KAAK,QAAQ;AAAA,UAC/E;AAAA;AAAA,UAEA,OAAO,EAAE,SAAS;AAAA,UAClB,IAAI,EAAE,MAAM;AAAA,UACZ,MAAM,EAAE,QAAQ;AAAA,QAClB,IAAI;AAAA,MACN;AACA,eAAS,SAAS,KAAK;AACrB,eAAO,UAAU,SAAS,OAAO,KAAK,UAAU,MAAM,IAAI;AAAA,MAC5D;AAAA,IACF;AACA,IAAAA,aAAY,UAAU,gBAAgB,SAAU,UAAU,IAAI,SAAS;AACrE,UAAI,gBAAgB,KAAK;AACzB,UAAI,WAAW,QAAQ,GAAG;AACxB,YAAI,cAAc;AAClB,YAAI,aAAa;AACjB,sBAAc,KAAK,SAAUG,QAAO,eAAe;AACjD,mBAASC,KAAI,GAAGD,UAASC,KAAID,OAAM,QAAQC,MAAK;AAC9C,gBAAIC,QAAOF,OAAMC,EAAC;AAClB,YAAAC,SAAQ,WAAW,KAAK,aAAa,eAAeA,OAAMA,MAAK,cAAc;AAAA,UAC/E;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,YAAI,QAAQ,SAAS,QAAQ,IAAI,cAAc,IAAI,QAAQ,IAAI,SAAS,QAAQ,IAAI,KAAK,eAAe,QAAQ,IAAI;AACpH,iBAAS,IAAI,GAAG,SAAS,IAAI,MAAM,QAAQ,KAAK;AAC9C,cAAI,OAAO,MAAM,CAAC;AAClB,kBAAQ,GAAG,KAAK,SAAS,MAAM,KAAK,cAAc;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAIA,IAAAL,aAAY,UAAU,kBAAkB,SAAU,MAAM;AACtD,UAAI,UAAoB,oBAAoB,MAAM,IAAI;AACtD,aAAO,OAAO,KAAK,eAAe,IAAI,QAAQ,GAAG,SAAU,WAAW;AACpE,eAAO,CAAC,CAAC,aAAa,WAAW,QAAQ,UAAU,SAAS;AAAA,MAC9D,CAAC;AAAA,IACH;AAIA,IAAAA,aAAY,UAAU,mBAAmB,SAAU,aAAa;AAC9D,aAAO,KAAK,eAAe,IAAI,QAAQ,EAAE,WAAW;AAAA,IACtD;AAKA,IAAAA,aAAY,UAAU,kBAAkB,SAAU,SAAS;AACzD,aAAO,OAAO,KAAK,eAAe,IAAI,QAAQ,GAAG,SAAU,WAAW;AACpE,eAAO,CAAC,CAAC,aAAa,UAAU,YAAY;AAAA,MAC9C,CAAC;AAAA,IACH;AAIA,IAAAA,aAAY,UAAU,YAAY,WAAY;AAC5C,aAAO,OAAO,KAAK,eAAe,IAAI,QAAQ,GAAG,SAAU,WAAW;AACpE,eAAO,CAAC,CAAC;AAAA,MACX,CAAC;AAAA,IACH;AAIA,IAAAA,aAAY,UAAU,iBAAiB,WAAY;AACjD,aAAO,KAAK,iBAAiB,IAAI,QAAQ;AAAA,IAC3C;AAKA,IAAAA,aAAY,UAAU,aAAa,SAAU,IAAI,SAAS;AACxD,8BAAwB,IAAI;AAC5B,WAAK,KAAK,gBAAgB,SAAU,gBAAgB;AAClD,YAAI,SAAS,KAAK,eAAe,IAAI,QAAQ,EAAE,cAAc;AAC7D,WAAG,KAAK,SAAS,QAAQ,cAAc;AAAA,MACzC,GAAG,IAAI;AAAA,IACT;AAOA,IAAAA,aAAY,UAAU,gBAAgB,SAAU,IAAI,SAAS;AAC3D,WAAK,KAAK,eAAe,IAAI,QAAQ,GAAG,SAAU,QAAQ;AACxD,kBAAU,GAAG,KAAK,SAAS,QAAQ,OAAO,cAAc;AAAA,MAC1D,CAAC;AAAA,IACH;AAKA,IAAAA,aAAY,UAAU,mBAAmB,SAAU,SAAS,IAAI,SAAS;AACvE,8BAAwB,IAAI;AAC5B,WAAK,KAAK,gBAAgB,SAAU,gBAAgB;AAClD,YAAI,SAAS,KAAK,eAAe,IAAI,QAAQ,EAAE,cAAc;AAC7D,YAAI,OAAO,YAAY,SAAS;AAC9B,aAAG,KAAK,SAAS,QAAQ,cAAc;AAAA,QACzC;AAAA,MACF,GAAG,IAAI;AAAA,IACT;AAIA,IAAAA,aAAY,UAAU,sBAAsB,SAAU,SAAS,IAAI,SAAS;AAC1E,aAAO,KAAK,KAAK,gBAAgB,OAAO,GAAG,IAAI,OAAO;AAAA,IACxD;AACA,IAAAA,aAAY,UAAU,mBAAmB,SAAU,aAAa;AAC9D,8BAAwB,IAAI;AAC5B,aAAO,KAAK,kBAAkB,IAAI,YAAY,cAAc,KAAK;AAAA,IACnE;AACA,IAAAA,aAAY,UAAU,0BAA0B,WAAY;AAC1D,cAAQ,KAAK,kBAAkB,CAAC,GAAG,MAAM;AAAA,IAC3C;AACA,IAAAA,aAAY,UAAU,eAAe,SAAU,IAAI,SAAS;AAC1D,8BAAwB,IAAI;AAC5B,UAAI,mBAAmB,CAAC;AACxB,WAAK,KAAK,gBAAgB,SAAU,cAAc;AAChD,YAAI,SAAS,KAAK,eAAe,IAAI,QAAQ,EAAE,YAAY;AAC3D,WAAG,KAAK,SAAS,QAAQ,YAAY,KAAK,iBAAiB,KAAK,YAAY;AAAA,MAC9E,GAAG,IAAI;AACP,WAAK,iBAAiB;AACtB,WAAK,oBAAoB,cAAc,gBAAgB;AAAA,IACzD;AACA,IAAAA,aAAY,UAAU,cAAc,SAAU,SAAS;AACrD,4BAAsB,IAAI;AAC1B,UAAI,gBAAgB,KAAK;AACzB,UAAI,iBAAiB,CAAC;AACtB,oBAAc,KAAK,SAAU,YAAY,eAAe;AACtD,YAAI,kBAAe,SAAS,aAAa,GAAG;AAC1C,yBAAe,KAAK,aAAa;AAAA,QACnC;AAAA,MACF,CAAC;AACD,wBAAe,kBAAkB,gBAAgB,kBAAe,qBAAqB,GAAG,SAAU,eAAe;AAC/G,aAAK,cAAc,IAAI,aAAa,GAAG,SAAU,WAAW;AAC1D,cAAI,cAAc,kBAAkB,YAAY,CAAC,kBAAkB,WAAW,OAAO,IAAI;AACvF,sBAAU,YAAY;AAAA,UACxB;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,IAAAA,aAAY,gBAAgB,WAAY;AACtC,8BAAwB,SAAU,SAAS;AACzC,YAAI,gBAAgB,QAAQ,iBAAiB,CAAC;AAC9C,aAAK,QAAQ,eAAe,IAAI,QAAQ,GAAG,SAAU,QAAQ;AAE3D,oBAAU,cAAc,KAAK,OAAO,cAAc;AAAA,QACpD,CAAC;AACD,gBAAQ,oBAAoB,cAAc,aAAa;AAAA,MACzD;AACA,gCAA0B,SAAU,SAAS;AAG3C,YAAI,MAAuC;AACzC,cAAI,CAAC,QAAQ,gBAAgB;AAC3B,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AACA,iBAAW,SAAU,SAAS,YAAY;AAGxC,gBAAQ,SAAS,CAAC;AAClB,gBAAQ,OAAO,gBAAgB,IAAI;AAGnC,gBAAQ,iBAAiB,cAAc;AAAA,UACrC,QAAQ,CAAC;AAAA,QACX,CAAC;AACD,gBAAQ,mBAAmB,cAAc;AAGzC,YAAI,aAAa,WAAW;AAC5B,YAAI,SAAS,UAAU,KAAK,WAAW,WAAW,MAAM;AACtD,qBAAW,UAAU;AAAA,QACvB;AACA,mBAAW,YAAY,QAAQ,OAAO,MAAM;AAE5C,cAAM,YAAY,uBAAe,KAAK;AACtC,gBAAQ,aAAa,YAAY,IAAI;AAAA,MACvC;AAAA,IACF,EAAE;AACF,WAAOA;AAAA,EACT,EAAE,aAAK;AAAA;AACP,SAAS,kBAAkB,aAAa,SAAS;AAC/C,MAAI,SAAS;AACX,QAAI,QAAQ,QAAQ;AACpB,QAAI,KAAK,QAAQ;AACjB,QAAI,SAAS,QAAQ;AACrB,WAAO,SAAS,QAAQ,YAAY,mBAAmB,SAAS,MAAM,QAAQ,YAAY,OAAO,MAAM,UAAU,QAAQ,YAAY,SAAS;AAAA,EAChJ;AACF;AACA,SAAS,WAAW,QAAQC,QAAO;AAGjC,MAAI,qBAAqB,OAAO,SAAS,CAAC,OAAO;AACjD,OAAKA,QAAO,SAAU,WAAW,MAAM;AACrC,QAAI,SAAS,gBAAgB,oBAAoB;AAC/C;AAAA,IACF;AAGA,QAAI,CAAC,kBAAe,SAAS,IAAI,GAAG;AAClC,UAAI,OAAO,cAAc,UAAU;AACjC,eAAO,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,MAAM,SAAS,IAAI,MAAM,OAAO,IAAI,GAAG,WAAW,KAAK;AAAA,MACxF,OAAO;AACL,YAAI,OAAO,IAAI,KAAK,MAAM;AACxB,iBAAO,IAAI,IAAI;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,SAAS,gBAAgB,MAAM,UAAU,OAAO;AAG9C,MAAI,QAAQ,QAAQ,GAAG;AACrB,QAAI,WAAW,cAAc;AAC7B,SAAK,UAAU,SAAU,cAAc;AACrC,UAAI,gBAAgB,MAAM;AACxB,YAAI,SAAmB,oBAAoB,cAAc,IAAI;AAC7D,kBAAU,QAAQ,SAAS,IAAI,cAAc,IAAI;AAAA,MACnD;AAAA,IACF,CAAC;AACD,WAAO,OAAO,OAAO,SAAU,MAAM;AACnC,aAAO,QAAQ,SAAS,IAAI,KAAK,IAAI,CAAC;AAAA,IACxC,CAAC;AAAA,EACH,OAAO;AACL,QAAI,WAAqB,oBAAoB,UAAU,IAAI;AAC3D,WAAO,OAAO,OAAO,SAAU,MAAM;AACnC,aAAO,QAAQ,YAAY,QAAQ,KAAK,IAAI,MAAM;AAAA,IACpD,CAAC;AAAA,EACH;AACF;AACA,SAAS,gBAAgB,YAAY,WAAW;AAG9C,SAAO,UAAU,eAAe,SAAS,IAAI,OAAO,YAAY,SAAU,MAAM;AAC9E,WAAO,QAAQ,KAAK,YAAY,UAAU;AAAA,EAC5C,CAAC,IAAI;AACP;AACA,SAAS,wBAAwB,MAAM;AACrC,MAAI,0BAA0B,cAAc;AAC5C,UAAQ,KAAe,iBAAiB,KAAK,YAAY,GAAG,SAAU,UAAU;AAC9E,QAAI,MAAuC;AACzC,aAAO,kBAAe,SAAS,QAAQ,GAAG,MAAM,WAAW,sDAAsD;AAAA,IACnH;AACA,4BAAwB,IAAI,UAAU,IAAI;AAAA,EAC5C,CAAC;AACD,SAAO;AAAA,IACL;AAAA,EACF;AACF;AACA,MAAM,aAAa,YAAY;AAC/B,IAAO,iBAAQ;;;AC/rBf,IAAI,mBAAmB;AAAA,EAAC;AAAA,EAAU;AAAA,EAAS;AAAA,EAAY;AAAA,EAAa;AAAA,EAAuB;AAAA,EAAkB;AAAA,EAAS;AAAA,EAAc;AAAA,EAAM;AAAA,EAAO;AAAA,EAAc;AAAA;AAAA,EAE/J;AAAA;AAAA;AAAA,EAGA;AAAA,EAAS;AAAmB;AAC5B,IAAI;AAAA;AAAA,EAA4B,2BAAY;AAC1C,aAASK,cAAa,YAAY;AAChC,MAAO,KAAK,kBAAkB,SAAU,YAAY;AAClD,aAAK,UAAU,IAAW,KAAK,WAAW,UAAU,GAAG,UAAU;AAAA,MACnE,GAAG,IAAI;AAAA,IACT;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,IAAO,uBAAQ;;;ACdf,IAAI,2BAA2B,CAAC;AAChC,IAAI;AAAA;AAAA,EAAuC,WAAY;AACrD,aAASC,2BAA0B;AACjC,WAAK,qBAAqB,CAAC;AAAA,IAC7B;AACA,IAAAA,yBAAwB,UAAU,SAAS,SAAU,SAAS,KAAK;AACjE,UAAI,oBAAoB,CAAC;AACzB,MAAO,KAAK,0BAA0B,SAAU,SAAS,MAAM;AAC7D,YAAI,OAAO,QAAQ,OAAO,SAAS,GAAG;AACtC,4BAAoB,kBAAkB,OAAO,QAAQ,CAAC,CAAC;AAAA,MACzD,CAAC;AACD,WAAK,qBAAqB;AAAA,IAC5B;AACA,IAAAA,yBAAwB,UAAU,SAAS,SAAU,SAAS,KAAK;AACjE,MAAO,KAAK,KAAK,oBAAoB,SAAU,UAAU;AACvD,iBAAS,UAAU,SAAS,OAAO,SAAS,GAAG;AAAA,MACjD,CAAC;AAAA,IACH;AACA,IAAAA,yBAAwB,UAAU,uBAAuB,WAAY;AACnE,aAAO,KAAK,mBAAmB,MAAM;AAAA,IACvC;AACA,IAAAA,yBAAwB,WAAW,SAAU,MAAM,SAAS;AAC1D,+BAAyB,IAAI,IAAI;AAAA,IACnC;AACA,IAAAA,yBAAwB,MAAM,SAAU,MAAM;AAC5C,aAAO,yBAAyB,IAAI;AAAA,IACtC;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,IAAO,2BAAQ;;;ACvBf,IAAI,YAAY;AAOhB,IAAI;AAAA;AAAA,EAA6B,WAAY;AAS3C,aAASC,eAAc,KAAK;AAC1B,WAAK,mBAAmB,CAAC;AACzB,WAAK,aAAa,CAAC;AAKnB,WAAK,uBAAuB,CAAC;AAC7B,WAAK,OAAO;AAAA,IACd;AACA,IAAAA,eAAc,UAAU,YAAY,SAAU,WAAWC,0BAAyB,KAAK;AACrF,UAAI,WAAW;AAEb,aAAK,iBAAiB,UAAU,MAAM,GAAG,SAAU,QAAQ;AACzD,oBAAU,OAAO,QAAQ,aAAa,OAAO,IAAI,KAAK,eAAe,OAAO,IAAI;AAAA,QAClF,CAAC;AACD,aAAK,iBAAiB,UAAU,OAAO,GAAG,SAAU,SAAS;AAC3D,qBAAW,QAAQ,UAAU,aAAa,QAAQ,MAAM,KAAK,eAAe,QAAQ,MAAM;AAAA,QAC5F,CAAC;AAAA,MACH;AAIA,kBAAY,MAAM,SAAS;AAI3B,UAAI,eAAe,KAAK;AACxB,UAAI,kBAAkB,eAAe,WAAWA,0BAAyB,CAAC,YAAY;AACtF,WAAK,iBAAiB,gBAAgB;AAEtC,UAAI,cAAc;AAiBhB,YAAI,gBAAgB,gBAAgB,QAAQ;AAC1C,uBAAa,kBAAkB,gBAAgB;AAAA,QACjD;AACA,YAAI,gBAAgB,UAAU,QAAQ;AACpC,uBAAa,YAAY,gBAAgB;AAAA,QAC3C;AACA,YAAI,gBAAgB,cAAc;AAChC,uBAAa,eAAe,gBAAgB;AAAA,QAC9C;AAAA,MACF,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AACA,IAAAD,eAAc,UAAU,cAAc,SAAU,YAAY;AAC1D,UAAI,eAAe,KAAK;AACxB,WAAK,mBAAmB,aAAa;AACrC,WAAK,aAAa,aAAa;AAC/B,WAAK,gBAAgB,aAAa;AAClC,WAAK,uBAAuB,CAAC;AAC7B,aAAO,MAAM,aAMX,aAAa,aAAa,KAAK,cAAc;AAAA,IACjD;AACA,IAAAA,eAAc,UAAU,oBAAoB,SAAU,SAAS;AAC7D,UAAI;AACJ,UAAI,kBAAkB,KAAK;AAC3B,UAAI,gBAAgB,QAAQ;AAG1B,YAAI,gBAAgB,QAAQ,aAAa,UAAU;AACnD,YAAI,eAAe;AACjB,mBAAS;AAAA;AAAA,YAET,gBAAgB,cAAc,gBAAgB,CAAC;AAAA,UAAC;AAAA,QAClD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAAA,eAAc,UAAU,iBAAiB,SAAU,SAAS;AAC1D,UAAI,UAAU,KAAK,KAAK,SAAS;AACjC,UAAI,WAAW,KAAK,KAAK,UAAU;AACnC,UAAI,YAAY,KAAK;AACrB,UAAI,eAAe,KAAK;AACxB,UAAI,UAAU,CAAC;AACf,UAAI,SAAS,CAAC;AAEd,UAAI,CAAC,UAAU,UAAU,CAAC,cAAc;AACtC,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,KAAK;AACpD,YAAI,gBAAgB,UAAU,CAAC,EAAE,OAAO,SAAS,QAAQ,GAAG;AAC1D,kBAAQ,KAAK,CAAC;AAAA,QAChB;AAAA,MACF;AAIA,UAAI,CAAC,QAAQ,UAAU,cAAc;AACnC,kBAAU,CAAC,EAAE;AAAA,MACf;AACA,UAAI,QAAQ,UAAU,CAAC,cAAc,SAAS,KAAK,oBAAoB,GAAG;AACxE,iBAAS,IAAI,SAAS,SAAU,OAAO;AACrC,iBAAO,MAAM,UAAU,KAAK,aAAa,SAAS,UAAU,KAAK,EAAE,MAAM;AAAA,QAC3E,CAAC;AAAA,MACH;AAEA,WAAK,uBAAuB;AAC5B,aAAO;AAAA,IACT;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AA8DF,SAAS,eAET,WAAWC,0BAAyB,OAAO;AACzC,MAAI,YAAY,CAAC;AACjB,MAAI;AACJ,MAAI;AACJ,MAAI,qBAAqB,UAAU;AAEnC,MAAI,iBAAiB,UAAU;AAC/B,MAAI,wBAAwB,UAAU;AACtC,MAAI,cAAc,UAAU;AAC5B,MAAI,WAAW,CAAC,CAAC,UAAU;AAC3B,MAAI,cAAc,CAAC,EAAE,yBAAyB,kBAAkB,sBAAsB,mBAAmB;AACzG,MAAI,oBAAoB;AACtB,iBAAa;AAEb,QAAI,CAAC,WAAW,UAAU;AACxB,iBAAW,WAAW;AAAA,IACxB;AAAA,EACF,OAGK;AACH,QAAI,eAAe,UAAU;AAC3B,gBAAU,UAAU,UAAU,QAAQ;AAAA,IACxC;AACA,iBAAa;AAAA,EACf;AACA,MAAI,UAAU;AACZ,QAAI,QAAQ,WAAW,GAAG;AACxB,WAAK,aAAa,SAAU,aAAa;AACvC,YAAI,MAAuC;AAEzC,cAAI,eAAe,CAAC,YAAY,UAAU,SAAS,YAAY,KAAK,KAAK,SAAS,YAAY,MAAM,MAAM,GAAG;AAC3G,kBAAM,6EAA6E;AAAA,UACrF;AAAA,QACF;AACA,YAAI,eAAe,YAAY,QAAQ;AACrC,cAAI,YAAY,OAAO;AACrB,sBAAU,KAAK,WAAW;AAAA,UAC5B,WAAW,CAAC,cAAc;AAExB,2BAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,UAAI,MAAuC;AAEzC,cAAM,0EAA0E;AAAA,MAClF;AAAA,IACF;AAAA,EACF;AACA,eAAa,UAAU;AACvB,OAAK,uBAAuB,SAAU,QAAQ;AAC5C,WAAO,aAAa,MAAM;AAAA,EAC5B,CAAC;AACD,OAAK,WAAW,SAAU,OAAO;AAC/B,WAAO,aAAa,MAAM,MAAM;AAAA,EAClC,CAAC;AACD,WAAS,aAAa,QAAQ;AAC5B,SAAKA,0BAAyB,SAAU,YAAY;AAClD,iBAAW,QAAQ,KAAK;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA,iBAAiB,yBAAyB,CAAC;AAAA,IAC3C;AAAA,IACA;AAAA,EACF;AACF;AAMA,SAAS,gBAAgB,OAAO,SAAS,UAAU;AACjD,MAAI,UAAU;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa,UAAU;AAAA;AAAA,EACzB;AAEA,MAAI,aAAa;AACjB,OAAK,OAAO,SAAU,OAAO,MAAM;AACjC,QAAI,UAAU,KAAK,MAAM,SAAS;AAClC,QAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG;AAC1C;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,CAAC;AACxB,QAAI,WAAW,QAAQ,CAAC,EAAE,YAAY;AACtC,QAAI,CAAC,QAAQ,QAAQ,QAAQ,GAAG,OAAO,QAAQ,GAAG;AAChD,mBAAa;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,QAAQ,MAAM,QAAQ,UAAU;AACvC,MAAI,aAAa,OAAO;AACtB,WAAO,QAAQ;AAAA,EACjB,WAAW,aAAa,OAAO;AAC7B,WAAO,QAAQ;AAAA,EACjB,OAAO;AAEL,WAAO,SAAS;AAAA,EAClB;AACF;AACA,SAAS,cAAc,UAAU,UAAU;AAEzC,SAAO,SAAS,KAAK,GAAG,MAAM,SAAS,KAAK,GAAG;AACjD;AA+FA,IAAO,wBAAQ;;;AC3Zf,IAAIC,QAAc;AAClB,IAAIC,YAAkB;AACtB,IAAI,kBAAkB,CAAC,aAAa,aAAa,aAAa,aAAa,cAAc,SAAS,WAAW;AAC7G,SAAS,mBAAmB,KAAK;AAC/B,MAAI,eAAe,OAAO,IAAI;AAC9B,MAAI,CAAC,cAAc;AACjB;AAAA,EACF;AACA,WAAS,IAAI,GAAG,MAAM,gBAAgB,QAAQ,IAAI,KAAK,KAAK;AAC1D,QAAI,YAAY,gBAAgB,CAAC;AACjC,QAAI,qBAAqB,aAAa;AACtC,QAAI,uBAAuB,aAAa;AACxC,QAAI,sBAAsB,mBAAmB,SAAS,GAAG;AACvD,UAAI,MAAuC;AACzC,4BAAoB,sBAAsB,WAAW,SAAS;AAAA,MAChE;AACA,UAAI,SAAS,IAAI,IAAI,SAAS,KAAK,CAAC;AACpC,UAAI,CAAC,IAAI,SAAS,EAAE,QAAQ;AAC1B,YAAI,SAAS,EAAE,SAAS,mBAAmB,SAAS;AAAA,MACtD,OAAO;AACL,QAAO,MAAM,IAAI,SAAS,EAAE,QAAQ,mBAAmB,SAAS,CAAC;AAAA,MACnE;AACA,yBAAmB,SAAS,IAAI;AAAA,IAClC;AACA,QAAI,wBAAwB,qBAAqB,SAAS,GAAG;AAC3D,UAAI,MAAuC;AACzC,4BAAoB,wBAAwB,WAAW,cAAc,SAAS;AAAA,MAChF;AACA,UAAI,SAAS,IAAI,IAAI,SAAS,KAAK,CAAC;AACpC,UAAI,CAAC,IAAI,SAAS,EAAE,UAAU;AAC5B,YAAI,SAAS,EAAE,WAAW,qBAAqB,SAAS;AAAA,MAC1D,OAAO;AACL,QAAO,MAAM,IAAI,SAAS,EAAE,UAAU,qBAAqB,SAAS,CAAC;AAAA,MACvE;AACA,2BAAqB,SAAS,IAAI;AAAA,IACpC;AAAA,EACF;AACF;AACA,SAAS,sBAAsB,KAAK,SAAS,WAAW;AACtD,MAAI,OAAO,IAAI,OAAO,MAAM,IAAI,OAAO,EAAE,UAAU,IAAI,OAAO,EAAE,WAAW;AACzE,QAAI,YAAY,IAAI,OAAO,EAAE;AAC7B,QAAI,cAAc,IAAI,OAAO,EAAE;AAC/B,QAAI,WAAW;AACb,UAAI,MAAuC;AAEzC,qBAAa,2BAA2B,UAAU,yEAAyE,UAAU,gBAAgB;AAAA,MACvJ;AAEA,UAAI,WAAW;AACb,YAAI,OAAO,EAAE,SAAS,IAAI,OAAO,EAAE,WAAW;AAC9C,QAAO,SAAS,IAAI,OAAO,GAAG,SAAS;AAAA,MACzC,OAAO;AACL,YAAI,OAAO,IAAI;AAAA,MACjB;AAAA,IACF;AACA,QAAI,aAAa;AACf,UAAI,MAAuC;AACzC,qBAAa,UAAU,4CAA4C,UAAU,YAAY;AAAA,MAC3F;AACA,UAAI,WAAW,IAAI,YAAY,CAAC;AAChC,UAAI,SAAS,OAAO,IAAI;AAGxB,UAAI,YAAY,OAAO;AACrB,YAAI,SAAS,QAAQ,YAAY;AAAA,MACnC;AACA,UAAI,YAAY,WAAW;AACzB,YAAI,SAAS,YAAY,YAAY;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,sBAAsB,KAAK;AAClC,wBAAsB,KAAK,WAAW;AACtC,wBAAsB,KAAK,WAAW;AACtC,wBAAsB,KAAK,WAAW;AACtC,wBAAsB,KAAK,OAAO;AAClC,wBAAsB,KAAK,WAAW;AAEtC,wBAAsB,KAAK,YAAY;AAEvC,wBAAsB,KAAK,WAAW;AACxC;AACA,SAAS,gBAAgB,KAAK,UAAU;AAEtC,MAAI,iBAAiBA,UAAS,GAAG,KAAK,IAAI,QAAQ;AAClD,MAAI,YAAYA,UAAS,cAAc,KAAK,eAAe;AAC3D,MAAI,WAAW;AACb,QAAI,MAAuC;AAEzC,mBAAa,4BAA4B,WAAW,6EAA6E,WAAW,gBAAgB;AAAA,IAC9J;AACA,aAAS,IAAI,GAAG,MAAgB,mBAAmB,QAAQ,IAAI,KAAK,KAAK;AACvE,UAAI,eAAyB,mBAAmB,CAAC;AACjD,UAAI,UAAU,eAAe,YAAY,GAAG;AAC1C,uBAAe,YAAY,IAAI,UAAU,YAAY;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,sBAAsB,KAAK;AAClC,MAAI,KAAK;AACP,0BAAsB,GAAG;AACzB,oBAAgB,KAAK,OAAO;AAC5B,QAAI,YAAY,gBAAgB,IAAI,UAAU,OAAO;AAAA,EACvD;AACF;AACA,SAAS,cAAc,WAAW;AAChC,MAAI,CAACA,UAAS,SAAS,GAAG;AACxB;AAAA,EACF;AACA,qBAAmB,SAAS;AAC5B,wBAAsB,SAAS;AAC/B,kBAAgB,WAAW,OAAO;AAElC,kBAAgB,WAAW,YAAY;AAEvC,kBAAgB,WAAW,WAAW;AACtC,MAAI,UAAU,UAAU;AACtB,oBAAgB,UAAU,UAAU,OAAO;AAE3C,oBAAgB,UAAU,UAAU,YAAY;AAEhD,oBAAgB,UAAU,UAAU,WAAW;AAAA,EACjD;AACA,MAAI,YAAY,UAAU;AAC1B,MAAI,WAAW;AACb,uBAAmB,SAAS;AAC5B,0BAAsB,SAAS;AAAA,EACjC;AACA,MAAI,WAAW,UAAU;AACzB,MAAI,UAAU;AACZ,uBAAmB,QAAQ;AAC3B,0BAAsB,QAAQ;AAAA,EAChC;AACA,MAAI,WAAW,UAAU;AACzB,MAAI,UAAU;AACZ,0BAAsB,QAAQ;AAAA,EAChC;AACA,MAAI,OAAO,UAAU;AAGrB,MAAI,UAAU,SAAS,SAAS;AAC9B,WAAO,QAAQ,UAAU;AACzB,QAAI,WAAW,UAAU,SAAS,UAAU;AAC5C,QAAI,YAAY,CAAQ,aAAa,QAAQ,GAAG;AAC9C,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,8BAAsB,SAAS,CAAC,CAAC;AAAA,MACnC;AAAA,IACF;AACA,IAAO,KAAK,UAAU,YAAY,SAAU,KAAK;AAC/C,4BAAsB,GAAG;AAAA,IAC3B,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,CAAQ,aAAa,IAAI,GAAG;AACtC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,4BAAsB,KAAK,CAAC,CAAC;AAAA,IAC/B;AAAA,EACF;AAEA,cAAY,UAAU;AACtB,MAAI,aAAa,UAAU,MAAM;AAC/B,QAAI,SAAS,UAAU;AACvB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,4BAAsB,OAAO,CAAC,CAAC;AAAA,IACjC;AAAA,EACF;AAEA,aAAW,UAAU;AACrB,MAAI,YAAY,SAAS,MAAM;AAC7B,QAAI,SAAS,SAAS;AACtB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAW,QAAQ,OAAO,CAAC,CAAC,GAAG;AAC7B,8BAAsB,OAAO,CAAC,EAAE,CAAC,CAAC;AAClC,8BAAsB,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,MACpC,OAAO;AACL,8BAAsB,OAAO,CAAC,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAEA,MAAI,UAAU,SAAS,SAAS;AAC9B,oBAAgB,WAAW,WAAW;AACtC,oBAAgB,WAAW,OAAO;AAClC,oBAAgB,WAAW,QAAQ;AAAA,EACrC,WAAW,UAAU,SAAS,WAAW;AACvC,0BAAsB,UAAU,YAAY,WAAW;AACvD,IAAO,KAAK,UAAU,QAAQ,SAAU,KAAK;AAC3C,4BAAsB,GAAG;AAAA,IAC3B,CAAC;AAAA,EACH,WAAW,UAAU,SAAS,QAAQ;AACpC,0BAAsB,UAAU,MAAM;AAAA,EACxC;AAEF;AAEA,SAAS,MAAM,GAAG;AAChB,SAAc,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC;AAC5C;AACA,SAAS,MAAM,GAAG;AAChB,UAAe,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC;AAC5C;AACe,SAAR,kBAAmC,QAAQ,SAAS;AACzD,EAAAD,MAAK,MAAM,OAAO,MAAM,GAAG,SAAU,WAAW;AAC9C,IAAAC,UAAS,SAAS,KAAK,cAAc,SAAS;AAAA,EAChD,CAAC;AACD,MAAI,OAAO,CAAC,SAAS,SAAS,cAAc,aAAa,cAAc,gBAAgB,OAAO;AAC9F,aAAW,KAAK,KAAK,aAAa,gBAAgB,WAAW,UAAU;AACvE,EAAAD,MAAK,MAAM,SAAU,UAAU;AAC7B,IAAAA,MAAK,MAAM,OAAO,QAAQ,CAAC,GAAG,SAAU,SAAS;AAC/C,UAAI,SAAS;AACX,wBAAgB,SAAS,WAAW;AACpC,wBAAgB,QAAQ,aAAa,OAAO;AAAA,MAC9C;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,EAAAA,MAAK,MAAM,OAAO,QAAQ,GAAG,SAAU,aAAa;AAClD,QAAI,sBAAsB,eAAe,YAAY;AACrD,oBAAgB,qBAAqB,WAAW;AAChD,oBAAgB,uBAAuB,oBAAoB,aAAa,OAAO;AAAA,EACjF,CAAC;AACD,EAAAA,MAAK,MAAM,OAAO,QAAQ,GAAG,SAAU,aAAa;AAClD,0BAAsB,aAAa,WAAW;AAC9C,oBAAgB,aAAa,UAAU;AACvC,oBAAgB,aAAa,YAAY;AACzC,oBAAgB,aAAa,WAAW;AAAA,EAC1C,CAAC;AAED,EAAAA,MAAK,MAAM,OAAO,KAAK,GAAG,SAAU,UAAU;AAC5C,oBAAgB,UAAU,MAAM;AAEhC,QAAI,SAAS,QAAQ,SAAS,YAAY,MAAM;AAC9C,eAAS,WAAW,SAAS;AAC7B,aAAO,SAAS;AAChB,UAAI,MAAuC;AACzC,qBAAa,+DAA+D;AAAA,MAC9E;AAAA,IACF;AACA,QAAI,SAAS,WAAW,QAAQ,SAAS,eAAe,MAAM;AAC5D,eAAS,cAAc,SAAS;AAChC,aAAO,SAAS;AAChB,UAAI,MAAuC;AACzC,qBAAa,qEAAqE;AAAA,MACpF;AAAA,IACF;AACA,QAAI,MAAuC;AACzC,MAAAA,MAAK,SAAS,WAAW,SAAU,cAAc;AAC/C,YAAI,aAAa,MAAM;AACrB,8BAAoB,QAAQ,QAAQ,iBAAiB;AAAA,QACvD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,EAAAA,MAAK,MAAM,OAAO,GAAG,GAAG,SAAU,QAAQ;AACxC,QAAIC,UAAS,MAAM,GAAG;AACpB,4BAAsB,MAAM;AAC5B,MAAAD,MAAK,MAAM,OAAO,OAAO,GAAG,SAAU,WAAW;AAC/C,8BAAsB,SAAS;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,EAAAA,MAAK,MAAM,OAAO,QAAQ,GAAG,SAAU,aAAa;AAClD,0BAAsB,WAAW;AACjC,0BAAsB,aAAa,OAAO;AAC1C,0BAAsB,aAAa,WAAW;AAC9C,0BAAsB,aAAa,gBAAgB,IAAI;AACvD,QAAI,OAAO,YAAY;AACvB,IAAO,QAAQ,IAAI,KAAY,KAAK,MAAM,SAAU,MAAM;AACxD,UAAW,SAAS,IAAI,GAAG;AACzB,8BAAsB,MAAM,OAAO;AACnC,8BAAsB,MAAM,WAAW;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,EAAAA,MAAK,MAAM,OAAO,OAAO,GAAG,SAAU,YAAY;AAChD,0BAAsB,YAAY,WAAW;AAC7C,IAAAA,MAAK,WAAW,SAAS,SAAU,YAAY;AAC7C,4BAAsB,YAAY,WAAW;AAAA,IAC/C,CAAC;AAAA,EACH,CAAC;AACD,kBAAgB,MAAM,OAAO,WAAW,GAAG,OAAO;AAClD,kBAAgB,MAAM,OAAO,OAAO,EAAE,aAAa,OAAO;AAG5D;;;AC3RA,SAAS,IAAI,KAAK,MAAM;AACtB,MAAI,UAAU,KAAK,MAAM,GAAG;AAC5B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,OAAO,IAAI,QAAQ,CAAC,CAAC;AAC3B,QAAI,OAAO,MAAM;AACf;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,IAAI,KAAK,MAAM,KAAK,WAAW;AACtC,MAAI,UAAU,KAAK,MAAM,GAAG;AAC5B,MAAI,MAAM;AACV,MAAI;AACJ,MAAI,IAAI;AACR,SAAO,IAAI,QAAQ,SAAS,GAAG,KAAK;AAClC,UAAM,QAAQ,CAAC;AACf,QAAI,IAAI,GAAG,KAAK,MAAM;AACpB,UAAI,GAAG,IAAI,CAAC;AAAA,IACd;AACA,UAAM,IAAI,GAAG;AAAA,EACf;AACA,MAAI,aAAa,IAAI,QAAQ,CAAC,CAAC,KAAK,MAAM;AACxC,QAAI,QAAQ,CAAC,CAAC,IAAI;AAAA,EACpB;AACF;AACA,SAAS,uBAAuB,QAAQ;AACtC,YAAU,KAAK,mBAAmB,SAAU,MAAM;AAChD,QAAI,KAAK,CAAC,KAAK,UAAU,EAAE,KAAK,CAAC,KAAK,SAAS;AAC7C,aAAO,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;AAAA,IAClC;AAAA,EACF,CAAC;AACH;AACA,IAAI,oBAAoB,CAAC,CAAC,KAAK,MAAM,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,MAAM,OAAO,GAAG,CAAC,MAAM,QAAQ,CAAC;AACvF,IAAI,0BAA0B,CAAC,QAAQ,OAAO,YAAY,UAAU,WAAW,SAAS,aAAa,YAAY,UAAU;AAC3H,IAAI,qBAAqB,CAAC,CAAC,gBAAgB,iBAAiB,GAAG,CAAC,eAAe,gBAAgB,GAAG,CAAC,eAAe,gBAAgB,CAAC;AACnI,SAAS,mBAAmB,QAAQ;AAClC,MAAI,YAAY,UAAU,OAAO;AACjC,MAAI,WAAW;AACb,aAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,UAAI,UAAU,mBAAmB,CAAC,EAAE,CAAC;AACrC,UAAI,UAAU,mBAAmB,CAAC,EAAE,CAAC;AACrC,UAAI,UAAU,OAAO,KAAK,MAAM;AAC9B,kBAAU,OAAO,IAAI,UAAU,OAAO;AACtC,YAAI,MAAuC;AACzC,8BAAoB,SAAS,OAAO;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,eAAe,QAAQ;AAC9B,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,MAAI,OAAO,YAAY,UAAU,OAAO,UAAU,QAAQ,OAAO,gBAAgB,MAAM;AACrF,QAAI,MAAuC;AACzC,0BAAoB,gBAAgB,sBAAsB,KAAK;AAAA,IACjE;AACA,WAAO,eAAe,OAAO;AAAA,EAC/B;AACF;AACA,SAAS,oBAAoB,QAAQ;AACnC,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,MAAI,OAAO,YAAY,CAAC,OAAO,MAAM;AACnC,WAAO,OAAO,OAAO;AACrB,QAAI,MAAuC;AACzC,0BAAoB,YAAY,QAAQ,UAAU;AAAA,IACpD;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,QAAQ;AAChC,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,MAAI,OAAO,sBAAsB,MAAM;AACrC,WAAO,WAAW,OAAO,YAAY,CAAC;AACtC,QAAI,OAAO,SAAS,SAAS,MAAM;AACjC,UAAI,MAAuC;AACzC,4BAAoB,sBAAsB,mCAAqC,cAAc;AAAA,MAC/F;AACA,aAAO,SAAS,QAAQ;AAAA,IAC1B;AAAA,EACF;AACF;AACA,SAAS,aAAa,MAAM,IAAI;AAC9B,MAAI,MAAM;AACR,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,SAAG,KAAK,CAAC,CAAC;AACV,WAAK,CAAC,KAAK,aAAa,KAAK,CAAC,EAAE,UAAU,EAAE;AAAA,IAC9C;AAAA,EACF;AACF;AACe,SAAR,qBAAsC,QAAQ,SAAS;AAC5D,oBAAY,QAAQ,OAAO;AAE3B,SAAO,SAAS,iBAAiB,OAAO,MAAM;AAC9C,OAAK,OAAO,QAAQ,SAAU,WAAW;AACvC,QAAI,CAAC,SAAS,SAAS,GAAG;AACxB;AAAA,IACF;AACA,QAAIE,cAAa,UAAU;AAC3B,QAAIA,gBAAe,QAAQ;AACzB,UAAI,UAAU,gBAAgB,MAAM;AAClC,kBAAU,OAAO,UAAU;AAC3B,YAAI,MAAuC;AACzC,8BAAoB,gBAAgB,QAAQ,MAAM;AAAA,QACpD;AAAA,MACF;AAAA,IACF,WAAWA,gBAAe,SAASA,gBAAe,SAAS;AACzD,UAAI,UAAU,aAAa,MAAM;AAC/B,kBAAU,YAAY,UAAU;AAChC,YAAI,MAAuC;AACzC,8BAAoB,aAAa,WAAW;AAAA,QAC9C;AAAA,MACF;AACA,qBAAe,UAAU,KAAK;AAC9B,UAAI,OAAO,UAAU;AACrB,UAAI,QAAQ,CAAC,aAAa,IAAI,GAAG;AAC/B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,yBAAe,KAAK,CAAC,CAAC;AAAA,QACxB;AAAA,MACF;AACA,UAAI,UAAU,eAAe,MAAM;AACjC,kBAAU,WAAW,UAAU,YAAY,CAAC;AAC5C,YAAI,UAAU,SAAS,YAAY,MAAM;AACvC,cAAI,MAAuC;AACzC,gCAAoB,eAAe,oBAAoB;AAAA,UACzD;AACA,oBAAU,SAAS,YAAY,UAAU;AAAA,QAC3C;AAAA,MACF;AAAA,IACF,WAAWA,gBAAe,SAAS;AACjC,UAAI,eAAe,IAAI,WAAW,eAAe;AACjD,sBAAgB,QAAQ,IAAI,WAAW,mBAAmB,YAAY;AAAA,IACxE,WAAWA,gBAAe,OAAO;AAC/B,yBAAmB,SAAS;AAC5B,yBAAmB,UAAU,eAAe;AAC5C,yBAAmB,UAAU,QAAQ;AACrC,UAAI,OAAO,UAAU;AACrB,UAAI,QAAQ,CAAC,aAAa,IAAI,GAAG;AAC/B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,+BAAmB,KAAK,CAAC,CAAC;AAC1B,+BAAmB,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE,QAAQ;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAWA,gBAAe,YAAY;AACpC,UAAI,kBAAkB,UAAU;AAChC,UAAI,iBAAiB;AACnB,kBAAU,WAAW,UAAU,YAAY,CAAC;AAC5C,YAAI,CAAC,UAAU,SAAS,OAAO;AAC7B,oBAAU,SAAS,QAAQ;AAC3B,cAAI,MAAuC;AACzC,gCAAoB,mBAAmB,kBAAkB,UAAU;AAAA,UACrE;AAAA,QACF;AAAA,MACF;AACA,0BAAoB,SAAS;AAC7B,mBAAa,UAAU,MAAM,mBAAmB;AAAA,IAClD,WAAWA,gBAAe,WAAWA,gBAAe,UAAU;AAC5D,uBAAiB,SAAS;AAAA,IAE5B,WAAWA,gBAAe,OAAO;AAC/B,UAAI,UAAU,WAAW,CAAC,UAAU,KAAK;AACvC,YAAI,MAAuC;AACzC,8BAAoB,WAAW,OAAO,KAAK;AAAA,QAC7C;AACA,kBAAU,MAAM,UAAU;AAAA,MAC5B;AACA,UAAI,UAAU,aAAa;AACzB,YAAI,MAAuC;AACzC,uBAAa,oCAAoC;AAAA,QACnD;AACA,iBAAS,WAAW,UAAU,WAAW;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,UAAU,kBAAkB,MAAM;AACpC,gBAAU,WAAW,UAAU,YAAY,CAAC;AAC5C,UAAI,UAAU,YAAY,UAAU,SAAS,SAAS,MAAM;AAC1D,YAAI,MAAuC;AACzC,8BAAoB,kBAAkB,gBAAgB;AAAA,QACxD;AACA,kBAAU,SAAS,QAAQ,UAAU;AAAA,MACvC;AAAA,IACF;AACA,2BAAuB,SAAS;AAAA,EAClC,CAAC;AAED,MAAI,OAAO,WAAW;AACpB,WAAO,YAAY,OAAO;AAAA,EAC5B;AACA,OAAK,yBAAyB,SAAU,eAAe;AACrD,QAAI,UAAU,OAAO,aAAa;AAClC,QAAI,SAAS;AACX,UAAI,CAAC,QAAQ,OAAO,GAAG;AACrB,kBAAU,CAAC,OAAO;AAAA,MACpB;AACA,WAAK,SAAS,SAAUC,SAAQ;AAC9B,+BAAuBA,OAAM;AAAA,MAC/B,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;;;AC5Me,SAAR,UAA2B,SAAS;AACzC,MAAI,eAAe,cAAc;AACjC,UAAQ,WAAW,SAAU,aAAa;AACxC,QAAI,QAAQ,YAAY,IAAI,OAAO;AAEnC,QAAI,OAAO;AACT,UAAI,gBAAgB,aAAa,IAAI,KAAK,KAAK,aAAa,IAAI,OAAO,CAAC,CAAC;AACzE,UAAI,OAAO,YAAY,QAAQ;AAC/B,UAAI,YAAY;AAAA;AAAA;AAAA,QAGd,sBAAsB,KAAK,mBAAmB,sBAAsB;AAAA,QACpE,sBAAsB,KAAK,mBAAmB,sBAAsB;AAAA,QACpE,kBAAkB,KAAK,mBAAmB,kBAAkB;AAAA,QAC5D,oBAAoB,KAAK,mBAAmB,oBAAoB;AAAA,QAChE,kBAAkB,KAAK,mBAAmB,kBAAkB;AAAA,QAC5D;AAAA,QACA;AAAA,MACF;AAEA,UAAI,CAAC,UAAU,oBAAoB,EAAE,UAAU,oBAAoB,UAAU,qBAAqB;AAChG;AAAA,MACF;AACA,oBAAc,UAAU,KAAK,mBAAmB,mBAAmB,cAAc,cAAc,SAAS,CAAC,EAAE,WAAW;AACtH,oBAAc,KAAK,SAAS;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,eAAa,KAAK,cAAc;AAClC;AACA,SAAS,eAAe,eAAe;AACrC,OAAK,eAAe,SAAU,iBAAiB,YAAY;AACzD,QAAI,YAAY,CAAC;AACjB,QAAI,YAAY,CAAC,KAAK,GAAG;AACzB,QAAI,OAAO,CAAC,gBAAgB,sBAAsB,gBAAgB,oBAAoB;AACtF,QAAI,aAAa,gBAAgB;AACjC,QAAI,mBAAmB,gBAAgB;AACvC,QAAI,gBAAgB,gBAAgB,YAAY,IAAI,eAAe,KAAK;AAGxE,eAAW,OAAO,MAAM,SAAU,IAAI,IAAI,WAAW;AACnD,UAAI,MAAM,WAAW,IAAI,gBAAgB,kBAAkB,SAAS;AAGpE,UAAI,MAAM,GAAG,GAAG;AACd,eAAO;AAAA,MACT;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,kBAAkB;AACpB,8BAAsB,WAAW,YAAY,SAAS;AAAA,MACxD,OAAO;AACL,kBAAU,WAAW,IAAI,gBAAgB,oBAAoB,SAAS;AAAA,MACxE;AAEA,UAAI,cAAc;AAClB,eAAS,IAAI,aAAa,GAAG,KAAK,GAAG,KAAK;AACxC,YAAI,YAAY,cAAc,CAAC;AAE/B,YAAI,CAAC,kBAAkB;AACrB,gCAAsB,UAAU,KAAK,WAAW,UAAU,oBAAoB,OAAO;AAAA,QACvF;AACA,YAAI,uBAAuB,GAAG;AAC5B,cAAI,MAAM,UAAU,KAAK,cAAc,UAAU,sBAAsB,mBAAmB;AAE1F,cAAI,kBAAkB,SACnB,kBAAkB,cAAc,MAAM,KAAK,kBAAkB,cAAc,MAAM,KAAK,kBAAkB,cAAc,OAAO,KAAK,MAAM,KACxI,kBAAkB,cAAc,OAAO,KAAK,MAAM,GACnD;AAIA,kBAAM,QAAQ,KAAK,GAAG;AACtB,0BAAc;AACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,gBAAU,CAAC,IAAI;AACf,gBAAU,CAAC,IAAI;AACf,aAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACH;;;ACnFA,IAAIC,SAAQ,UAAU;AACtB,IAAI,sBAAsB;AAAA,EACxB,WAAW,gBAAgB,oBAAoB,IAAI;AAAA,EACnD,WAAW,gBAAgB,oBAAoB,IAAI;AACrD;AACA,IAAI,kBAAkB;AAAA,EACpB,WAAW;AAAA,EACX,WAAW;AACb;AACA,SAAS,eAAe,aAAa,WAAW;AAC9C,MAAI,cAAc,YAAY,qBAAqB,oBAAoB,SAAS;AAChF,MAAI,CAAC,aAAa;AAChB,YAAQ,KAAK,yBAAyB,YAAY,IAAI;AACtD,WAAO,oBAAoB;AAAA,EAC7B;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,aAAa,WAAW;AAElD,MAAI,WAAW,YAAY,kBAAkB,gBAAgB,SAAS;AACtE,MAAI,CAAC,UAAU;AACb,YAAQ,KAAK,yBAAyB,YAAY,IAAI;AACtD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI,kBAAkB;AAAA,EACpB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,OAAO,SAAU,aAAa,SAAS;AACrC,QAAI,OAAO,YAAY,QAAQ;AAC/B,QAAI,YAAY,YAAY,yBAAyB;AAErD,QAAI,aAAa,YAAY,SAAS,SAAS;AAC/C,QAAI,WAAW,eAAe,aAAa,SAAS;AACpD,QAAI,cAAc,SAAS,UAAU;AACrC,QAAI,cAAc,WAAW,WAAW,OAAO;AAC/C,QAAI,aAAa;AACf,WAAK,UAAU,SAAS,WAAW;AACnC,kBAAY,QAAQ;AAAA,IACtB;AAEA,QAAI,WAAW,mBAAmB,aAAa,SAAS;AACxD,QAAI,QAAQ,YAAY,QAAQ;AAEhC,QAAI,gBAAgB,WAAW,KAAK,IAAI,QAAQ;AAChD,QAAI,eAAe,YAAY,SAAS,UAAU,YAAY,WAAW;AAEzE,QAAI,CAAC,YAAY,QAAQ,KAAK,iBAAiB,cAAc;AAI3D,UAAIC,gBAAe,YAAY;AAAA;AAAA,QAE/B,YAAY;AAAA,QAAM;AAAA,QAAM,QAAQ,eAAe;AAAA,MAAC;AAChD,UAAI,CAAC,YAAY,QAAQ,GAAG;AAC1B,oBAAY,QAAQ,IAAIA;AACxB,aAAK,UAAU,oBAAoB,IAAI;AAAA,MACzC;AACA,kBAAY,OAAO,YAAY,SAAS,UAAU,WAAW,YAAY,IAAI,IAAIA,gBAAe,YAAY;AAC5G,kBAAY,SAAS,YAAY,WAAW,UAAU,WAAW,YAAY,MAAM,IAAIA,gBAAe,YAAY;AAAA,IACpH;AACA,SAAK,UAAU,SAAS,WAAW;AACnC,SAAK,UAAU,YAAY,QAAQ;AAEnC,QAAI,CAAC,QAAQ,iBAAiB,WAAW,KAAK,eAAe;AAC3D,WAAK,UAAU,oBAAoB,KAAK;AACxC,aAAO;AAAA,QACL,UAAU,SAAUC,OAAM,KAAK;AAC7B,cAAI,aAAa,YAAY,cAAc,GAAG;AAC9C,cAAI,YAAY,OAAO,CAAC,GAAG,WAAW;AACtC,oBAAU,QAAQ,IAAI,cAAc,UAAU;AAC9C,UAAAA,MAAK,cAAc,KAAK,SAAS,SAAS;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,cAAc,IAAI,cAAM;AAC5B,IAAI,gBAAgB;AAAA,EAClB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,OAAO,SAAU,aAAa,SAAS;AACrC,QAAI,YAAY,qBAAqB,QAAQ,iBAAiB,WAAW,GAAG;AAC1E;AAAA,IACF;AACA,QAAI,OAAO,YAAY,QAAQ;AAC/B,QAAI,YAAY,YAAY,yBAAyB;AAErD,QAAI,WAAW,eAAe,aAAa,SAAS;AACpD,QAAI,WAAW,KAAK,UAAU,UAAU;AACxC,WAAO;AAAA,MACL,UAAU,KAAK,gBAAgB,SAAUA,OAAM,KAAK;AAElD,YAAI,UAAUA,MAAK,eAAe,GAAG;AACrC,YAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,sBAAY,SAAS,QAAQ,SAAS;AACtC,cAAI,QAAQ,SAAS,WAAW;AAChC,cAAI,cAAcA,MAAK,uBAAuB,KAAK,OAAO;AAC1D,iBAAO,aAAa,KAAK;AACzB,cAAI,YAAY,OAAO,OAAO;AAC5B,YAAAA,MAAK,cAAc,KAAK,SAAS,YAAY,OAAO,KAAK;AACzD,wBAAY,OAAO,MAAM,QAAQ;AAAA,UACnC;AACA,cAAI,YAAY,OAAO;AACrB,YAAAA,MAAK,cAAc,KAAK,oBAAoB,KAAK;AAAA,UACnD;AAAA,QACF;AAAA,MACF,IAAI;AAAA,IACN;AAAA,EACF;AACF;AAGA,IAAI,uBAAuB;AAAA,EACzB,kBAAkB;AAAA,EAClB,cAAc,SAAU,SAAS;AAG/B,QAAI,0BAA0B,cAAc;AAC5C,YAAQ,WAAW,SAAU,aAAa;AACxC,UAAI,UAAU,YAAY,WAAW;AACrC,UAAI,YAAY,gBAAgB,GAAG;AACjC;AAAA,MACF;AACA,UAAI,MAAM,YAAY,OAAO,MAAM;AACnC,UAAI,aAAa,wBAAwB,IAAI,GAAG;AAChD,UAAI,CAAC,YAAY;AACf,qBAAa,CAAC;AACd,gCAAwB,IAAI,KAAK,UAAU;AAAA,MAC7C;AACA,MAAAF,OAAM,WAAW,EAAE,QAAQ;AAAA,IAC7B,CAAC;AACD,YAAQ,WAAW,SAAU,aAAa;AACxC,UAAI,YAAY,gBAAgB,KAAK,QAAQ,iBAAiB,WAAW,GAAG;AAC1E;AAAA,MACF;AACA,UAAI,UAAU,YAAY,WAAW;AACrC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO,YAAY,QAAQ;AAC/B,UAAI,aAAaA,OAAM,WAAW,EAAE;AACpC,UAAI,YAAY,YAAY,yBAAyB;AACrD,UAAI,WAAW,mBAAmB,aAAa,SAAS;AACxD,WAAK,KAAK,SAAU,KAAK;AACvB,YAAI,SAAS,KAAK,YAAY,GAAG;AACjC,eAAO,MAAM,IAAI;AAAA,MACnB,CAAC;AAGD,cAAQ,KAAK,SAAU,QAAQ;AAC7B,YAAI,MAAM,OAAO,MAAM;AACvB,YAAI,cAAc,KAAK,cAAc,KAAK,kBAAkB;AAK5D,YAAI,aAAa;AACf,cAAI,YAAY,KAAK,uBAAuB,KAAK,OAAO;AACxD,cAAI,SAAS,QAAQ,QAAQ,MAAM,KAAK,SAAS;AACjD,cAAI,YAAY,QAAQ,MAAM;AAC9B,oBAAU,QAAQ,IAAI,YAAY,oBAAoB,QAAQ,YAAY,SAAS;AAAA,QACrF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;;;ACzKA,IAAI,KAAK,KAAK;AASC,SAAR,eAAgC,KAAK,MAAM;AAChD,SAAO,QAAQ,CAAC;AAChB,EAAO,SAAS,MAAM;AAAA,IACpB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa;AAAA,IACb,OAAO;AAAA,IACP,eAAe;AAAA,IACf,WAAW;AAAA,IACX,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,QAAQ,IAAY,cAAM;AAC9B,MAAI,OAAO,IAAY,aAAK;AAAA,IAC1B,OAAO;AAAA,MACL,MAAM,KAAK;AAAA,IACb;AAAA,IACA,QAAQ,KAAK;AAAA,IACb,GAAG;AAAA,EACL,CAAC;AACD,QAAM,IAAI,IAAI;AACd,MAAI,cAAc,IAAY,aAAK;AAAA,IACjC,OAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,UAAU,KAAK;AAAA,MACf,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,MAChB,YAAY,KAAK;AAAA,IACnB;AAAA,IACA,QAAQ,KAAK;AAAA,IACb,GAAG;AAAA,EACL,CAAC;AACD,MAAI,YAAY,IAAY,aAAK;AAAA,IAC/B,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA;AAAA,IACA,YAAY;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ,KAAK;AAAA,IACb,GAAG;AAAA,EACL,CAAC;AACD,QAAM,IAAI,SAAS;AACnB,MAAI;AACJ,MAAI,KAAK,aAAa;AACpB,UAAM,IAAY,YAAI;AAAA,MACpB,OAAO;AAAA,QACL,YAAY,CAAC,KAAK;AAAA,QAClB,UAAU,CAAC,KAAK,IAAI;AAAA,QACpB,GAAG,KAAK;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACL,QAAQ,KAAK;AAAA,QACb,SAAS;AAAA,QACT,WAAW,KAAK;AAAA,MAClB;AAAA,MACA,QAAQ,KAAK;AAAA,MACb,GAAG;AAAA,IACL,CAAC;AACD,QAAI,aAAa,IAAI,EAAE,KAAK,KAAM;AAAA,MAChC,UAAU,KAAK,IAAI;AAAA,IACrB,CAAC,EAAE,MAAM,eAAe;AACxB,QAAI,aAAa,IAAI,EAAE,KAAK,KAAM;AAAA,MAChC,YAAY,KAAK,IAAI;AAAA,IACvB,CAAC,EAAE,MAAM,GAAG,EAAE,MAAM,eAAe;AACnC,UAAM,IAAI,GAAG;AAAA,EACf;AAEA,QAAM,SAAS,WAAY;AACzB,QAAI,YAAY,YAAY,gBAAgB,EAAE;AAC9C,QAAI,IAAI,KAAK,cAAc,KAAK,gBAAgB;AAGhD,QAAI,MAAM,IAAI,SAAS,IAAI,IAAI,KAAK,KAAK,eAAe,YAAY,KAAK,KAAK,aAAa,KAAK,KAAK,eAAe,YAAY,IAAI,IAAI,YAAY,MAEjJ,KAAK,cAAc,IAAI,YAAY,MAEnC,YAAY,IAAI;AACnB,QAAI,KAAK,IAAI,UAAU,IAAI;AAC3B,SAAK,eAAe,IAAI,SAAS;AAAA,MAC/B;AAAA,MACA;AAAA,IACF,CAAC;AACD,cAAU,SAAS;AAAA,MACjB,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,OAAO,IAAI;AAAA,MACX,QAAQ,IAAI;AAAA,IACd,CAAC;AACD,SAAK,SAAS;AAAA,MACZ,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO,IAAI,SAAS;AAAA,MACpB,QAAQ,IAAI,UAAU;AAAA,IACxB,CAAC;AAAA,EACH;AACA,QAAM,OAAO;AACb,SAAO;AACT;;;AC7GA,IAAI;AAAA;AAAA,EAAyB,WAAY;AACvC,aAASG,WAAU,YAAY,KAAK,uBAAuB,gBAAgB;AAEzE,WAAK,gBAAgB,cAAc;AACnC,WAAK,aAAa;AAClB,WAAK,MAAM;AAKX,8BAAwB,KAAK,yBAAyB,sBAAsB,MAAM;AAClF,uBAAiB,KAAK,kBAAkB,eAAe,MAAM;AAC7D,WAAK,eAAe,sBAAsB,OAAO,cAAc;AAAA,IACjE;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,SAAS,SAAS;AAiB5D,cAAQ,YAAY,OAAO;AAQ3B,WAAK,cAAc,KAAK,SAAU,YAAY;AAC5C,YAAI,cAAc,WAAW;AAC7B,uBAAe,YAAY,MAAM;AAAA,MACnC,CAAC;AAAA,IACH;AAEA,IAAAA,WAAU,UAAU,iBAAiB,SAAU,MAAM,SAAS;AAE5D,UAAI,CAAC,KAAK,YAAY;AACpB;AAAA,MACF;AACA,UAAI,WAAW,KAAK,aAAa,IAAI,KAAK,WAAW,EAAE;AACvD,UAAI,OAAO,SAAS;AACpB,UAAI,cAAc,CAAC,WAAW,SAAS,uBAAuB,CAAC,QAAQ,KAAK,sBAAsB,KAAK,kBAAkB,SAAS;AAClI,UAAI,OAAO,cAAc,SAAS,OAAO;AACzC,UAAI,eAAe,QAAQ,KAAK;AAChC,UAAI,QAAQ,gBAAgB,OAAO,KAAK,KAAK,eAAe,IAAI,IAAI;AACpE,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,YAAY;AACtD,aAAO,KAAK,aAAa,IAAI,UAAU;AAAA,IACzC;AAQA,IAAAA,WAAU,UAAU,oBAAoB,SAAU,aAAa,MAAM;AACnE,UAAI,WAAW,KAAK,aAAa,IAAI,YAAY,GAAG;AACpD,UAAI,OAAO,YAAY,QAAQ;AAC/B,UAAI,UAAU,KAAK,MAAM;AAMzB,UAAI,oBAAoB,SAAS,sBAAsB,KAAK,4BAA4B,WAAW,SAAS;AAC5G,UAAI,QAAQ,YAAY,IAAI,OAAO,KAAK,WAAW,YAAY,IAAI,gBAAgB;AAGnF,UAAI,eAAe,YAAY,IAAI,sBAAsB,MAAM,QAAQ,UAAU;AACjF,kBAAY,kBAAkB,SAAS,UAAU;AAAA,QAC/C;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAAA,WAAU,UAAU,mBAAmB,SAAU,SAAS;AACxD,UAAI,YAAY;AAChB,UAAI,cAAc,UAAU,eAAe,cAAc;AACzD,cAAQ,WAAW,SAAU,aAAa;AACxC,YAAI,cAAc,YAAY,eAAe;AAC7C,YAAI,aAAa,YAAY;AAC7B,oBAAY,IAAI,YAAY;AAAA,UAC1B,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,WAAW,YAAY,wBAAwB;AAAA,UAC/C,oBAAoB,eAAe,EAAE,YAAY,sBAAsB,YAAY,mBAAmB;AAAA,UACtG,YAAY;AAAA,UACZ,MAAM,KAAK,MAAM,eAAe,GAAG;AAAA,UACnC,OAAO;AAAA,QACT,CAAC;AACD,kBAAU,MAAM,aAAa,YAAY,QAAQ;AAAA,MACnD,CAAC;AAAA,IACH;AACA,IAAAA,WAAU,UAAU,oBAAoB,WAAY;AAClD,UAAI,eAAe,KAAK;AACxB,UAAI,UAAU,KAAK,IAAI,SAAS;AAChC,UAAI,MAAM,KAAK;AACf,WAAK,KAAK,cAAc,SAAU,SAAS;AACzC,YAAI,SAAS,aAAa,IAAI,QAAQ,GAAG,KAAK,aAAa,IAAI,QAAQ,KAAK,CAAC,CAAC;AAC9E,YAAI,SAAS;AACb,YAAI,MAAuC;AAEzC,mBAAS;AAAA,QACX;AACA,eAAO,EAAE,QAAQ,SAAS,QAAQ,eAAe,MAAM;AACvD,gBAAQ,SAAS,KAAK,uBAAuB,SAAS,QAAQ,SAAS,GAAG;AAC1E,gBAAQ,gBAAgB,KAAK,wBAAwB,SAAS,QAAQ,SAAS,GAAG;AAAA,MACpF,GAAG,IAAI;AAAA,IACT;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,MAAM,OAAO,SAAS,KAAK;AACrE,UAAI,aAAa,KAAK;AACtB,UAAI,UAAU,WAAW;AACzB,cAAQ,QAAQ;AAChB,cAAQ,UAAU;AAClB,cAAQ,MAAM;AACd,iBAAW,UAAU,CAAC,KAAK;AAC3B,WAAK,MAAM,OAAO,UAAU;AAAA,IAC9B;AACA,IAAAA,WAAU,UAAU,4BAA4B,SAAU,SAAS,SAAS;AAE1E,WAAK,mBAAmB,KAAK,wBAAwB,SAAS,SAAS;AAAA,QACrE,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,IAAAA,WAAU,UAAU,qBAAqB,SAAU,SAAS,SAAS,KAAK;AACxE,WAAK,mBAAmB,KAAK,iBAAiB,SAAS,SAAS,GAAG;AAAA,IACrE;AACA,IAAAA,WAAU,UAAU,qBAAqB,SAAU,eAAe,SAAS,SAAS,KAAK;AACvF,YAAM,OAAO,CAAC;AACd,UAAI,aAAa;AACjB,UAAI,YAAY;AAChB,WAAK,eAAe,SAAU,cAAc,KAAK;AAC/C,YAAI,IAAI,cAAc,IAAI,eAAe,aAAa,YAAY;AAChE;AAAA,QACF;AACA,YAAI,qBAAqB,UAAU,cAAc,IAAI,aAAa,GAAG;AACrE,YAAI,gBAAgB,mBAAmB;AACvC,YAAI,cAAc,mBAAmB;AACrC,YAAI,aAAa;AACf,cAAI;AACJ,cAAI,eAAe,YAAY;AAC/B,uBAAa,KAAK,SAAU,MAAM;AAChC,gBAAI,aAAa,KAAK,IAAI,GAAG;AAC3B,mBAAK,MAAM;AACX,mCAAqB;AAAA,YACvB;AAAA,UACF,CAAC;AACD,gCAAsB,YAAY,MAAM;AACxC,oBAAU,cAAc,aAAa,OAAO;AAC5C,cAAI,gBAAgB,UAAU,eAAe,aAAa,IAAI,KAAK;AAKnE,uBAAa,KAAK,SAAU,MAAM;AAChC,iBAAK,QAAQ,aAAa;AAAA,UAC5B,CAAC;AACD,cAAI,YAAY,QAAQ,aAAa,GAAG;AACtC,yBAAa;AAAA,UACf;AAAA,QACF,WAAW,eAAe;AACxB,wBAAc,KAAK,SAAU,MAAM,YAAY;AAC7C,gBAAI,aAAa,KAAK,IAAI,GAAG;AAC3B,mBAAK,MAAM;AAAA,YACb;AACA,gBAAI,cAAc,UAAU,eAAe,MAAM,IAAI,KAAK;AAS1D,wBAAY,OAAO,CAAC,aAAa,oBAAoB,QAAQ,iBAAiB,KAAK,QAAQ,KAAK;AAChG,sBAAU,cAAc,MAAM,OAAO;AACrC,gBAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,2BAAa;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,eAAS,aAAaC,MAAK,MAAM;AAC/B,eAAOA,KAAI,aAAa,CAACA,KAAI,YAAYA,KAAI,SAAS,IAAI,KAAK,WAAW,EAAE;AAAA,MAC9E;AACA,WAAK,aAAa,cAAc,KAAK;AAAA,IACvC;AACA,IAAAD,WAAU,UAAU,qBAAqB,SAAU,SAAS;AAC1D,UAAI;AACJ,cAAQ,WAAW,SAAU,aAAa;AAExC,qBAAa,YAAY,SAAS,QAAQ,KAAK;AAAA,MACjD,CAAC;AACD,WAAK,aAAa,cAAc,KAAK;AAAA,IACvC;AACA,IAAAA,WAAU,UAAU,OAAO,WAAY;AAErC,WAAK,aAAa,KAAK,SAAU,UAAU;AACzC,YAAI,OAAO,SAAS;AACpB,WAAG;AACD,cAAI,KAAK,SAAS;AAChB,qBAAS,aAAa,KAAK;AAC3B;AAAA,UACF;AACA,iBAAO,KAAK,YAAY;AAAA,QAC1B,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,IAAAA,WAAU,UAAU,gBAAgB,SAAU,MAAM,SAAS;AAC3D,kBAAY,aAAa,KAAK,QAAQ,UAAU;AAAA,IAClD;AACA,IAAAA,WAAU,UAAU,yBAAyB,SAAU,cAAc,oBAAoB,SAAS,KAAK;AACrG,UAAI,YAAY;AAChB,UAAI,mBAAmB,mBAAmB;AAG1C,UAAI,mBAAmB,mBAAmB,gBAAgB,cAAc;AACxE,UAAIE,cAAa,aAAa;AAC9B,UAAI,kBAAkB,aAAa;AAInC,UAAI,aAAa,mBAAmB;AAClC,gBAAQ,cAAc,MAAM;AAAA,MAC9B,WAAWA,aAAY;AACrB,gBAAQ,oBAAoBA,aAAY,MAAM;AAAA,MAChD,WAAW,iBAAiB;AAC1B,wBAAgB,SAAS,GAAG,EAAE,KAAK,MAAM;AAAA,MAC3C;AACA,eAAS,OAAO,aAAa;AAC3B,YAAI,aAAa,YAAY;AAG7B,YAAI,OAAO,iBAAiB,IAAI,YAAY,oBAAoB,iBAAiB,IAAI,UAAU,KAAK,WAAW;AAAA,UAC7G,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,QACT,CAAC,CAAC;AACF,aAAK,UAAU;AAAA,UACb,OAAO;AAAA,UACP;AAAA,UACA;AAAA;AAAA,UAEA,gBAAgB,aAAa,YAAY,CAAC,aAAa;AAAA,UACvD,MAAM,aAAa;AAAA,UACnB,OAAO,aAAa;AAAA,UACpB;AAAA,QACF;AACA,kBAAU,MAAM,aAAa,IAAI;AAAA,MACnC;AAAA,IACF;AACA,IAAAF,WAAU,UAAU,0BAA0B,SAAU,cAAc,oBAAoB,SAAS,KAAK;AACtG,UAAI,YAAY;AAChB,UAAI,cAAc,mBAAmB,cAAc,mBAAmB,eAEnE,WAAW;AAAA,QACZ,OAAO;AAAA,MACT,CAAC;AACD,kBAAY,UAAU;AAAA,QACpB;AAAA,QACA;AAAA,QACA,cAAc,aAAa;AAAA,QAC3B;AAAA,MACF;AACA,UAAI,kBAAkB,YAAY;AAGlC,UAAI,kBAAkB,YAAY,eAAe,cAAc;AAC/D,UAAIE,cAAa,aAAa;AAC9B,UAAI,kBAAkB,aAAa;AACnC,UAAI,kBAAkB;AACtB,UAAI,yBAAyB;AAO7B,UAAI,SAAS;AACb,UAAI,MAAuC;AACzC,iBAAS;AAAA,MACX;AACA,aAAO,CAAC,aAAa,mBAAmB,MAAM;AAC9C,UAAIA,aAAY;AACd,gBAAQ,oBAAoBA,aAAY,UAAU;AAAA,MACpD,WAAW,iBAAiB;AAC1B,wBAAgB,SAAS,GAAG,EAAE,KAAK,UAAU;AAAA,MAC/C,OAKK;AACH,0BAAkB;AAClB,aAAK,QAAQ,UAAU,GAAG,UAAU;AAAA,MACtC;AACA,eAAS,WAAW,aAAa;AAC/B,YAAI,aAAa,YAAY;AAC7B,YAAI,OAAO,gBAAgB,IAAI,YAAY,mBAAmB,gBAAgB,IAAI,UAAU;AAAA;AAAA,SAG5F,yBAAyB,MAAM,WAAW;AAAA,UACxC,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,EAAE;AACH,aAAK,UAAU;AAAA,UACb,OAAO;AAAA,UACP;AAAA;AAAA;AAAA,QAGF;AAEA,aAAK,QAAQ;AACb,aAAK,UAAU;AACf,kBAAU,MAAM,aAAa,IAAI;AAAA,MACnC;AACA,UAAI,wBAAwB;AAC1B,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AACA,IAAAF,WAAU,UAAU,QAAQ,SAAU,aAAa,MAAM;AACvD,UAAI,aAAa,YAAY;AAC7B,UAAI,WAAW,KAAK,aAAa,IAAI,UAAU;AAC/C,OAAC,SAAS,SAAS,SAAS,OAAO;AACnC,eAAS,QAAQ,SAAS,KAAK,KAAK,IAAI;AACxC,eAAS,OAAO;AAChB,WAAK,kBAAkB,SAAS;AAChC,WAAK,aAAa;AAAA,IACpB;AACA,IAAAA,WAAU,mBAAmB,SAAU,cAAc,YAAY;AAC/D,UAAI,WAAW,YAAY,GAAG;AAC5B,uBAAe;AAAA,UACb,cAAc;AAAA,UACd,YAAY,iBAAiB,YAAY;AAAA,QAC3C;AAAA,MACF;AACA,mBAAa,MAAM,OAAO,cAAc;AACxC,qBAAe,aAAa,aAAa;AACzC,aAAO;AAAA,IACT;AACA;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,SAAS,iBAAiB,SAAS;AACjC,UAAQ,aAAa,QAAQ,SAAS,QAAQ,KAAK,QAAQ,OAAO;AACpE;AACA,SAAS,UAAU,SAAS;AAC1B,SAAO,QAAQ,mBAAmB;AACpC;AACA,SAAS,eAAe;AACtB,OAAK,MAAM,MAAM;AACjB,OAAK,cAAc,EAAE,MAAM;AAC7B;AACA,SAAS,cAAc;AACrB,OAAK,SAAS,KAAK,MAAM,MAAM;AACjC;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ,SAAS,QAAQ,KAAK,QAAQ,OAAO,IAAI;AACrG;AACA,SAAS,gBAAgB,SAAS;AAChC,MAAI,QAAQ,gBAAgB;AAC1B,YAAQ,KAAK,eAAe;AAAA,EAC9B;AACA,MAAI,eAAe,QAAQ,eAAe,iBAAiB,QAAQ,MAAM,QAAQ,OAAO,QAAQ,SAAS,QAAQ,KAAK,QAAQ,OAAO,CAAC;AACtI,SAAO,aAAa,SAAS,IAAI,IAAI,cAAc,SAAU,GAAG,KAAK;AACnE,WAAO,uBAAuB,GAAG;AAAA,EACnC,CAAC,IAAI;AACP;AACA,IAAI,2BAA2B,uBAAuB,CAAC;AACvD,SAAS,uBAAuB,gBAAgB;AAC9C,SAAO,SAAU,QAAQ,SAAS;AAChC,QAAI,OAAO,QAAQ;AACnB,QAAI,cAAc,QAAQ,aAAa,cAAc;AACrD,QAAI,eAAe,YAAY,UAAU;AACvC,eAAS,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,KAAK;AAC9C,oBAAY,SAAS,MAAM,CAAC;AAAA,MAC9B;AAAA,IACF,WAAW,eAAe,YAAY,UAAU;AAC9C,kBAAY,SAAS,QAAQ,IAAI;AAAA,IACnC;AAAA,EACF;AACF;AACA,SAAS,gBAAgB,SAAS;AAChC,SAAO,QAAQ,KAAK,MAAM;AAC5B;AAQA,SAAS,iBAAiB,YAAY;AACpC,eAAa;AACb,MAAI;AAEF,eAAW,aAAa,OAAO;AAAA,EACjC,SAAS,GAAG;AAAA,EAAC;AACb,SAAO;AACT;AACA,IAAI,cAAc,CAAC;AACnB,IAAI,UAAU,CAAC;AACf,IAAI;AACJ,YAAY,aAAa,cAAW;AACpC,YAAY,SAAS,oBAAY;AACjC,YAAY,mBAAmB,YAAY,sBAAsB,SAAU,MAAM;AAC/E,eAAa;AACf;AACA,YAAY,gBAAgB,SAAU,MAAM;AAC1C,MAAI,KAAK,aAAa,YAAY,KAAK,SAAS;AAC9C,iBAAa,KAAK;AAAA,EACpB;AACF;AACA,SAAS,YAAY,QAAQ,KAAK;AAEhC,WAAS,UAAU,IAAI,WAAW;AAEhC,WAAO,MAAM,IAAI;AAAA,EACnB;AAEF;AAEA,IAAO,oBAAQ;;;AC/bf,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC7J,IAAO,gBAAQ;AAAA,EACb,OAAO;AAAA,EACP,YAAY,CAAC,CAAC,WAAW,WAAW,SAAS,GAAG,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS,GAAG,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS,GAAG,QAAQ;AACnO;;;ACJA,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,aAAa,WAAY;AAC3B,SAAO;AAAA,IACL,UAAU;AAAA,MACR,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,QACT,OAAO,CAAC,0BAA0B,wBAAwB;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACrH,IAAI,QAAQ;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AAAA,EACP;AAAA,EACA,aAAa;AAAA,IACX,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA;AAAA,MAEL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,MACT,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,aAAa;AAAA,IACb,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,iBAAiB;AAAA,MACf,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,IACb,UAAU;AAAA,MACR,aAAa;AAAA,QACX,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU,WAAW;AAAA,EACrB,SAAS,WAAW;AAAA,EACpB,WAAW,WAAW;AAAA,EACtB,cAAc,WAAW;AAAA,EACzB,MAAM;AAAA,IACJ,QAAQ;AAAA,EACV;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,QACT,OAAO,CAAC,CAAC,GAAG,uBAAuB,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,WAAW;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,cAAc;AAAA;AAAA;AAAA,IAGhB;AAAA,EACF;AACF;AAEA,MAAM,aAAa,UAAU,OAAO;AACpC,IAAO,eAAQ;;;AChKf,IAAI;AAAA;AAAA,EAAgC,WAAY;AAC9C,aAASG,oBAAmB;AAAA,IAAC;AAC7B,IAAAA,kBAAiB,UAAU,iBAAiB,SAAU,OAAO;AAC3D,UAAI,WAAW,CAAC;AAChB,UAAI,YAAY,CAAC;AACjB,UAAI,aAAa,CAAC;AAElB,UAAW,SAAS,KAAK,GAAG;AAC1B,YAAI,cAAc,eAAe,KAAK;AAEtC,iBAAS,WAAW,YAAY,QAAQ;AACxC,iBAAS,UAAU,YAAY,OAAO;AAAA,MACxC,OAEK;AAGH,YAAI,aAAa,CAAC,SAAS,QAAQ,IAAI;AACvC,YAAI,aAAa;AAAA,UACf,MAAM;AAAA,UACN,WAAW;AAAA,UACX,UAAU;AAAA,QACZ;AACA,QAAO,KAAK,OAAO,SAAU,KAAK,KAAK;AACrC,cAAI,WAAW;AACf,mBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,gBAAI,aAAa,WAAW,CAAC;AAC7B,gBAAI,YAAY,IAAI,YAAY,UAAU;AAC1C,gBAAI,YAAY,KAAK,cAAc,IAAI,SAAS,WAAW,QAAQ;AACjE,kBAAI,WAAW,IAAI,MAAM,GAAG,SAAS;AAErC,kBAAI,aAAa,QAAQ;AACvB,yBAAS,WAAW;AACpB,yBAAS,WAAW,YAAY,CAAC,IAAI;AACrC,2BAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AACA,cAAI,WAAW,eAAe,GAAG,GAAG;AAClC,sBAAU,GAAG,IAAI;AACjB,uBAAW;AAAA,UACb;AACA,cAAI,CAAC,UAAU;AACb,uBAAW,GAAG,IAAI;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAAA,kBAAiB,UAAU,SAAS,SAAU,WAAW,OAAO;AAE9D,UAAI,YAAY,KAAK;AACrB,UAAI,CAAC,WAAW;AACd,eAAO;AAAA,MACT;AACA,UAAI,WAAW,UAAU;AACzB,UAAI,cAAc,UAAU;AAC5B,UAAI,QAAQ,UAAU;AACtB,UAAI,OAAO,UAAU;AAErB,UAAI,CAAC,SAAS,CAAC,MAAM;AACnB,eAAO;AAAA,MACT;AACA,UAAI,WAAW,MAAM;AACrB,UAAI,YAAY,MAAM;AACtB,aAAO,MAAM,UAAU,OAAO,UAAU,KAAK,MAAM,UAAU,OAAO,SAAS,KAAK,MAAM,UAAU,OAAO,SAAS,gBAAgB,KAAK,MAAM,UAAU,OAAO,MAAM,KAAK,MAAM,UAAU,OAAO,IAAI,KAAK,MAAM,WAAW,aAAa,MAAM,KAAK,MAAM,WAAW,aAAa,WAAW,KAAK,MAAM,WAAW,aAAa,UAAU,MAAM,CAAC,KAAK,yBAAyB,KAAK,sBAAsB,WAAW,MAAM,YAAY,UAAU,WAAW;AAC1b,eAAS,MAAMC,QAAO,MAAM,MAAM,YAAY;AAC5C,eAAOA,OAAM,IAAI,KAAK,QAAQ,KAAK,cAAc,IAAI,MAAMA,OAAM,IAAI;AAAA,MACvE;AAAA,IACF;AACA,IAAAD,kBAAiB,UAAU,eAAe,WAAY;AAEpD,WAAK,YAAY;AAAA,IACnB;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;;;ACjGF,IAAI,uBAAuB,CAAC,UAAU,cAAc,gBAAgB,cAAc;AAClF,IAAI,eAAe,qBAAqB,OAAO,CAAC,kBAAkB,CAAC;AAEnE,IAAI,mBAAmB;AAAA,EACrB,mBAAmB;AAAA;AAAA,EAEnB,kBAAkB;AAAA,EAClB,OAAO,SAAU,aAAa,SAAS;AACrC,QAAI,OAAO,YAAY,QAAQ;AAC/B,QAAI,YAAY,YAAY;AAC1B,WAAK,UAAU,cAAc,YAAY,UAAU;AAAA,IACrD;AACA,QAAI,CAAC,YAAY,iBAAiB;AAChC;AAAA,IACF;AACA,QAAI,gBAAgB,CAAC;AACrB,QAAI,kBAAkB,CAAC;AACvB,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,IAAI,qBAAqB,QAAQ,KAAK;AACpD,UAAI,iBAAiB,qBAAqB,CAAC;AAC3C,UAAI,MAAM,YAAY,IAAI,cAAc;AACxC,UAAI,WAAW,GAAG,GAAG;AACnB,sBAAc;AACd,wBAAgB,cAAc,IAAI;AAAA,MACpC,OAAO;AACL,sBAAc,cAAc,IAAI;AAAA,MAClC;AAAA,IACF;AACA,kBAAc,SAAS,cAAc,UAAU,YAAY;AAC3D,SAAK,UAAU,OAAO;AAAA,MACpB,YAAY,YAAY,cAAc,cAAc;AAAA,MACpD,kBAAkB,YAAY,IAAI,kBAAkB;AAAA,IACtD,GAAG,aAAa,CAAC;AAEjB,QAAI,QAAQ,iBAAiB,WAAW,GAAG;AACzC;AAAA,IACF;AACA,QAAI,gBAAgB,KAAK,eAAe;AACxC,aAAS,SAASE,OAAM,KAAK;AAC3B,UAAI,WAAW,YAAY,YAAY,GAAG;AAC1C,UAAI,SAAS,YAAY,cAAc,GAAG;AAC1C,eAASC,KAAI,GAAGA,KAAI,cAAc,QAAQA,MAAK;AAC7C,YAAIC,kBAAiB,cAAcD,EAAC;AACpC,QAAAD,MAAK,cAAc,KAAKE,iBAAgB,gBAAgBA,eAAc,EAAE,UAAU,MAAM,CAAC;AAAA,MAC3F;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU,cAAc,WAAW;AAAA,IACrC;AAAA,EACF;AACF;AACA,IAAI,iBAAiB;AAAA,EACnB,mBAAmB;AAAA;AAAA,EAEnB,kBAAkB;AAAA,EAClB,OAAO,SAAU,aAAa,SAAS;AACrC,QAAI,CAAC,YAAY,iBAAiB;AAChC;AAAA,IACF;AAEA,QAAI,QAAQ,iBAAiB,WAAW,GAAG;AACzC;AAAA,IACF;AACA,QAAI,OAAO,YAAY,QAAQ;AAC/B,aAAS,SAASF,OAAM,KAAK;AAC3B,UAAI,YAAYA,MAAK,aAAa,GAAG;AACrC,eAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,YAAI,iBAAiB,aAAa,CAAC;AACnC,YAAI,MAAM,UAAU,WAAW,gBAAgB,IAAI;AACnD,YAAI,OAAO,MAAM;AACf,UAAAA,MAAK,cAAc,KAAK,gBAAgB,GAAG;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU,KAAK,gBAAgB,WAAW;AAAA,IAC5C;AAAA,EACF;AACF;;;AC/EO,SAAS,sBAAsB,MAAM,WAAW,KAAK;AAC1D,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,UAAI,QAAQ,KAAK,cAAc,WAAW,OAAO;AACjD,aAAO,MAAM,KAAK,UAAU,UAAU,CAAC;AAAA,IACzC,KAAK;AACH,aAAO,KAAK,cAAc,WAAW,OAAO,EAAE;AAAA,IAChD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,KAAK,cAAc,WAAW,GAAG;AAAA,IAC1C;AACE,UAAI,MAAuC;AACzC,gBAAQ,KAAK,yBAAyB,GAAG;AAAA,MAC3C;AAAA,EACJ;AACF;AACO,SAAS,kBAAkB,MAAM,KAAK;AAC3C,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,UAAI,QAAQ,KAAK,UAAU,OAAO;AAClC,aAAO,MAAM,KAAK,UAAU,UAAU,CAAC;AAAA,IACzC,KAAK;AACH,aAAO,KAAK,UAAU,OAAO,EAAE;AAAA,IACjC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,KAAK,UAAU,GAAG;AAAA,IAC3B;AACE,UAAI,MAAuC;AACzC,gBAAQ,KAAK,yBAAyB,GAAG;AAAA,MAC3C;AAAA,EACJ;AACF;AACO,SAAS,sBAAsB,MAAM,WAAW,KAAK,OAAO;AACjE,UAAQ,KAAK;AAAA,IACX,KAAK;AAEH,UAAI,QAAQ,KAAK,uBAAuB,WAAW,OAAO;AAC1D,YAAM,KAAK,UAAU,UAAU,CAAC,IAAI;AAEpC,WAAK,cAAc,WAAW,oBAAoB,KAAK;AACvD;AAAA,IACF,KAAK;AACH,WAAK,uBAAuB,WAAW,OAAO,EAAE,UAAU;AAC1D;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,WAAK,cAAc,WAAW,KAAK,KAAK;AACxC;AAAA,IACF;AACE,UAAI,MAAuC;AACzC,gBAAQ,KAAK,yBAAyB,GAAG;AAAA,MAC3C;AAAA,EACJ;AACF;;;ACnDO,SAAS,6BAA6BG,aAAY,kBAAkB;AACzE,WAAS,iBAAiB,SAAS,SAAS;AAC1C,QAAI,gBAAgB,CAAC;AACrB,YAAQ,cAAc;AAAA,MACpB,UAAU;AAAA,MACV,SAASA;AAAA,MACT,OAAO;AAAA,IACT,GAAG,SAAU,aAAa;AACxB,oBAAc,KAAK,YAAY,WAAW;AAAA,IAC5C,CAAC;AACD,WAAO;AAAA,EACT;AACA,OAAK,CAAC,CAACA,cAAa,gBAAgB,cAAc,GAAG,CAACA,cAAa,UAAU,QAAQ,GAAG,CAACA,cAAa,YAAY,UAAU,CAAC,GAAG,SAAU,WAAW;AACnJ,qBAAiB,UAAU,CAAC,GAAG,SAAU,SAAS,SAAS,KAAK;AAC9D,gBAAU,OAAO,CAAC,GAAG,OAAO;AAC5B,UAAI,MAAuC;AACzC,4BAAoB,QAAQ,MAAM,UAAU,CAAC,CAAC;AAAA,MAChD;AACA,UAAI,eAAe,OAAO,SAAS;AAAA,QACjC,MAAM,UAAU,CAAC;AAAA,QACjB,aAAa,iBAAiB,SAAS,OAAO;AAAA,MAChD,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,+BAA+B,MAAM,cAAc,OAAO,SAAS,SAAS;AACnF,MAAI,kBAAkB,OAAO;AAC7B,MAAI,CAAC,MAAM,SAAS,eAAe,GAAG;AACpC,QAAI,MAAuC;AACzC,mBAAa,WAAW,kBAAkB,iBAAiB;AAAA,IAC7D;AACA,YAAQ,cAAc;AAAA,MACpB,UAAU;AAAA,MACV,SAAS;AAAA,IACX,GAAG,SAAU,aAAa;AACxB,UAAI,cAAc,YAAY;AAC9B,UAAI,cAAc,YAAY,OAAO;AACrC,UAAI,WAAW,QAAQ;AACvB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,SAAS,CAAC,EAAE,gBAAgB,aAAa;AAC3C,cAAI,OAAO,YAAY,QAAQ;AAC/B,cAAI,YAAY,eAAe,MAAM,QAAQ,iBAAiB;AAC9D,gBAAM,QAAQ,iBAAiB;AAAA,YAC7B,MAAM;AAAA,YACN,UAAU,YAAY;AAAA,YACtB,MAAM,QAAQ,SAAS,IAAI,KAAK,QAAQ,UAAU,CAAC,CAAC,IAAI,KAAK,QAAQ,SAAS;AAAA,YAC9E,UAAU,SAAS,WAAW,IAAI,cAAc,OAAO,CAAC,GAAG,WAAW;AAAA,UACxE,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACO,SAAS,yBAAyB,eAAe,OAAO,KAAK;AAClE,gBAAc,GAAG,iBAAiB,SAAU,QAAQ;AAClD,QAAI,UAAU,IAAI,SAAS;AAC3B,QAAI,OAAO,aAAa;AACtB,qCAA+B,OAAO,iBAAiB,OAAO,SAAS,MAAM;AAC7E,qCAA+B,OAAO,iBAAiB,OAAO,SAAS,MAAM;AAAA,IAC/E,WAAW,OAAO,eAAe,UAAU;AACzC,qCAA+B,OAAO,YAAY,OAAO,SAAS,MAAM;AACxE,qCAA+B,OAAO,YAAY,OAAO,SAAS,MAAM;AAAA,IAC1E,WAAW,OAAO,eAAe,YAAY;AAC3C,qCAA+B,OAAO,cAAc,OAAO,SAAS,MAAM;AAC1E,qCAA+B,OAAO,cAAc,OAAO,SAAS,MAAM;AAAA,IAC5E;AAAA,EACF,CAAC;AACH;;;ACxEO,SAAS,oBAAoB,QAAQ,KAAK,kBAAkB;AACjE,MAAI;AACJ,SAAO,QAAQ;AACb,QAAI,IAAI,MAAM,GAAG;AACf,cAAQ;AACR,UAAI,kBAAkB;AACpB;AAAA,MACF;AAAA,IACF;AACA,aAAS,OAAO,gBAAgB,OAAO;AAAA,EACzC;AACA,SAAO;AACT;;;ACvDA,IAAI,gBAAgB,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC;AAChD,IAAI,wBAAwB,OAAO,OAAO,mBAAmB;AAC7D,IAAI,UAAW,WAAY;AACvB,WAASC,WAAU;AACf,SAAK,MAAM,gBAAgB;AAAA,EAC/B;AACA,EAAAA,SAAQ,UAAU,MAAM,SAAU,KAAK;AACnC,WAAO,KAAK,OAAO,GAAG,EAAE,KAAK,GAAG;AAAA,EACpC;AACA,EAAAA,SAAQ,UAAU,MAAM,SAAU,KAAK,OAAO;AAC1C,QAAI,SAAS,KAAK,OAAO,GAAG;AAC5B,QAAI,uBAAuB;AACvB,aAAO,eAAe,QAAQ,KAAK,KAAK;AAAA,QACpC;AAAA,QACA,YAAY;AAAA,QACZ,cAAc;AAAA,MAClB,CAAC;AAAA,IACL,OACK;AACD,aAAO,KAAK,GAAG,IAAI;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,QAAQ,IAAI,SAAU,KAAK;AACzC,QAAI,KAAK,IAAI,GAAG,GAAG;AACf,aAAO,KAAK,OAAO,GAAG,EAAE,KAAK,GAAG;AAChC,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,MAAM,SAAU,KAAK;AACnC,WAAO,CAAC,CAAC,KAAK,OAAO,GAAG,EAAE,KAAK,GAAG;AAAA,EACtC;AACA,EAAAA,SAAQ,UAAU,SAAS,SAAU,KAAK;AACtC,QAAI,QAAQ,OAAO,GAAG,GAAG;AACrB,YAAM,UAAU,4CAA4C;AAAA,IAChE;AACA,WAAO;AAAA,EACX;AACA,SAAOA;AACX,EAAE;AACF,IAAO,kBAAQ;;;ACYf,IAAI,WAAmB,aAAK,OAAO;AAAA,EACjC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,WAAW,SAAU,MAAM,OAAO;AAChC,QAAI,KAAK,MAAM;AACf,QAAI,KAAK,MAAM;AACf,QAAI,QAAQ,MAAM,QAAQ;AAC1B,QAAI,SAAS,MAAM,SAAS;AAC5B,SAAK,OAAO,IAAI,KAAK,MAAM;AAC3B,SAAK,OAAO,KAAK,OAAO,KAAK,MAAM;AACnC,SAAK,OAAO,KAAK,OAAO,KAAK,MAAM;AACnC,SAAK,UAAU;AAAA,EACjB;AACF,CAAC;AAKD,IAAI,UAAkB,aAAK,OAAO;AAAA,EAChC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,WAAW,SAAU,MAAM,OAAO;AAChC,QAAI,KAAK,MAAM;AACf,QAAI,KAAK,MAAM;AACf,QAAI,QAAQ,MAAM,QAAQ;AAC1B,QAAI,SAAS,MAAM,SAAS;AAC5B,SAAK,OAAO,IAAI,KAAK,MAAM;AAC3B,SAAK,OAAO,KAAK,OAAO,EAAE;AAC1B,SAAK,OAAO,IAAI,KAAK,MAAM;AAC3B,SAAK,OAAO,KAAK,OAAO,EAAE;AAC1B,SAAK,UAAU;AAAA,EACjB;AACF,CAAC;AAKD,IAAI,MAAc,aAAK,OAAO;AAAA,EAC5B,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,WAAW,SAAU,MAAM,OAAO;AAChC,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM,QAAQ,IAAI;AAE1B,QAAI,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM;AAChC,QAAI,IAAI,IAAI;AAEZ,QAAI,KAAK,IAAI,KAAK,IAAI;AACtB,QAAI,KAAK,IAAI,IAAI,IAAI;AACrB,QAAI,QAAQ,KAAK,KAAK,KAAK,CAAC;AAE5B,QAAI,KAAK,KAAK,IAAI,KAAK,IAAI;AAC3B,QAAI,OAAO,KAAK,IAAI,KAAK;AACzB,QAAI,OAAO,KAAK,IAAI,KAAK;AACzB,QAAI,QAAQ,IAAI;AAChB,QAAI,SAAS,IAAI;AACjB,SAAK,OAAO,IAAI,IAAI,KAAK,EAAE;AAC3B,SAAK,IAAI,GAAG,IAAI,GAAG,KAAK,KAAK,OAAO,KAAK,KAAK,IAAI,KAAK;AACvD,SAAK,cAAc,IAAI,KAAK,OAAO,OAAO,KAAK,KAAK,OAAO,OAAO,GAAG,IAAI,QAAQ,GAAG,CAAC;AACrF,SAAK,cAAc,GAAG,IAAI,QAAQ,IAAI,KAAK,OAAO,OAAO,KAAK,KAAK,OAAO,OAAO,IAAI,IAAI,KAAK,EAAE;AAChG,SAAK,UAAU;AAAA,EACjB;AACF,CAAC;AAKD,IAAI,QAAgB,aAAK,OAAO;AAAA,EAC9B,MAAM;AAAA,EACN,OAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,WAAW,SAAU,KAAK,OAAO;AAC/B,QAAI,SAAS,MAAM;AACnB,QAAI,QAAQ,MAAM;AAClB,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM;AACd,QAAI,KAAK,QAAQ,IAAI;AACrB,QAAI,OAAO,GAAG,CAAC;AACf,QAAI,OAAO,IAAI,IAAI,IAAI,MAAM;AAC7B,QAAI,OAAO,GAAG,IAAI,SAAS,IAAI,CAAC;AAChC,QAAI,OAAO,IAAI,IAAI,IAAI,MAAM;AAC7B,QAAI,OAAO,GAAG,CAAC;AACf,QAAI,UAAU;AAAA,EAChB;AACF,CAAC;AAKD,IAAI,cAAc;AAAA,EAChB,MAAc;AAAA,EACd,MAAc;AAAA,EACd,WAAmB;AAAA,EACnB,QAAgB;AAAA,EAChB,QAAgB;AAAA,EAChB,SAAS;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AAAA,EACP,UAAU;AACZ;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM,SAAU,GAAG,GAAG,GAAG,GAAG,OAAO;AACjC,UAAM,KAAK;AACX,UAAM,KAAK,IAAI,IAAI;AACnB,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,IAAI,IAAI;AAAA,EACrB;AAAA,EACA,MAAM,SAAU,GAAG,GAAG,GAAG,GAAG,OAAO;AACjC,UAAM,IAAI;AACV,UAAM,IAAI;AACV,UAAM,QAAQ;AACd,UAAM,SAAS;AAAA,EACjB;AAAA,EACA,WAAW,SAAU,GAAG,GAAG,GAAG,GAAG,OAAO;AACtC,UAAM,IAAI;AACV,UAAM,IAAI;AACV,UAAM,QAAQ;AACd,UAAM,SAAS;AACf,UAAM,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AAAA,EAC7B;AAAA,EACA,QAAQ,SAAU,GAAG,GAAG,GAAG,GAAG,OAAO;AACnC,QAAI,OAAO,KAAK,IAAI,GAAG,CAAC;AACxB,UAAM,IAAI;AACV,UAAM,IAAI;AACV,UAAM,QAAQ;AACd,UAAM,SAAS;AAAA,EACjB;AAAA,EACA,QAAQ,SAAU,GAAG,GAAG,GAAG,GAAG,OAAO;AAEnC,UAAM,KAAK,IAAI,IAAI;AACnB,UAAM,KAAK,IAAI,IAAI;AACnB,UAAM,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AAAA,EAC7B;AAAA,EACA,SAAS,SAAU,GAAG,GAAG,GAAG,GAAG,OAAO;AACpC,UAAM,KAAK,IAAI,IAAI;AACnB,UAAM,KAAK,IAAI,IAAI;AACnB,UAAM,QAAQ;AACd,UAAM,SAAS;AAAA,EACjB;AAAA,EACA,KAAK,SAAU,GAAG,GAAG,GAAG,GAAG,OAAO;AAChC,UAAM,IAAI,IAAI,IAAI;AAClB,UAAM,IAAI,IAAI,IAAI;AAClB,UAAM,QAAQ;AACd,UAAM,SAAS;AAAA,EACjB;AAAA,EACA,OAAO,SAAU,GAAG,GAAG,GAAG,GAAG,OAAO;AAClC,UAAM,IAAI,IAAI,IAAI;AAClB,UAAM,IAAI,IAAI,IAAI;AAClB,UAAM,QAAQ;AACd,UAAM,SAAS;AAAA,EACjB;AAAA,EACA,UAAU,SAAU,GAAG,GAAG,GAAG,GAAG,OAAO;AACrC,UAAM,KAAK,IAAI,IAAI;AACnB,UAAM,KAAK,IAAI,IAAI;AACnB,UAAM,QAAQ;AACd,UAAM,SAAS;AAAA,EACjB;AACF;AACO,IAAI,qBAAqB,CAAC;AACjC,KAAK,aAAa,SAAU,MAAM,MAAM;AACtC,qBAAmB,IAAI,IAAI,IAAI,KAAK;AACtC,CAAC;AACD,IAAI,YAAoB,aAAK,OAAO;AAAA,EAClC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,YAAY;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,uBAAuB,SAAU,KAAK,QAAQ,MAAM;AAClD,QAAI,MAAM,sBAAsB,KAAK,QAAQ,IAAI;AACjD,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,MAAM,eAAe,SAAS,OAAO,aAAa,UAAU;AACvE,UAAI,IAAI,KAAK,IAAI,KAAK,SAAS;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,SAAU,KAAK,OAAO,UAAU;AACzC,QAAI,aAAa,MAAM;AACvB,QAAI,eAAe,QAAQ;AACzB,UAAI,cAAc,mBAAmB,UAAU;AAC/C,UAAI,CAAC,aAAa;AAEhB,qBAAa;AACb,sBAAc,mBAAmB,UAAU;AAAA,MAC7C;AACA,wBAAkB,UAAU,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,OAAO,MAAM,QAAQ,YAAY,KAAK;AAC5F,kBAAY,UAAU,KAAK,YAAY,OAAO,QAAQ;AAAA,IACxD;AAAA,EACF;AACF,CAAC;AAED,SAAS,mBAAmB,OAAO,YAAY;AAC7C,MAAI,KAAK,SAAS,SAAS;AACzB,QAAI,cAAc,KAAK;AACvB,QAAI,KAAK,gBAAgB;AACvB,kBAAY,SAAS;AACrB,kBAAY,OAAO,cAAc;AAEjC,kBAAY,YAAY;AAAA,IAC1B,WAAW,KAAK,MAAM,eAAe,QAAQ;AAC3C,kBAAY,SAAS;AAAA,IACvB,OAAO;AACL,kBAAY,OAAO;AAAA,IACrB;AACA,SAAK,WAAW;AAAA,EAClB;AACF;AAIO,SAAS,aAAa,YAAY,GAAG,GAAG,GAAG,GAAG,OAErD,YAAY;AAEV,MAAI,UAAU,WAAW,QAAQ,OAAO,MAAM;AAC9C,MAAI,SAAS;AACX,iBAAa,WAAW,OAAO,GAAG,CAAC,EAAE,YAAY,IAAI,WAAW,OAAO,CAAC;AAAA,EAC1E;AACA,MAAI;AACJ,MAAI,WAAW,QAAQ,UAAU,MAAM,GAAG;AACxC,iBAAqB,UAAU,WAAW,MAAM,CAAC,GAAG,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC,GAAG,aAAa,WAAW,OAAO;AAAA,EACnH,WAAW,WAAW,QAAQ,SAAS,MAAM,GAAG;AAC9C,iBAAqB,SAAS,WAAW,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC,GAAG,aAAa,WAAW,OAAO;AAAA,EACtH,OAAO;AACL,iBAAa,IAAI,UAAU;AAAA,MACzB,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAW,iBAAiB;AAE5B,aAAW,WAAW;AACtB,MAAI,OAAO;AACT,eAAW,SAAS,KAAK;AAAA,EAC3B;AACA,SAAO;AACT;AACO,SAAS,oBAAoB,YAAY;AAC9C,MAAI,CAAC,QAAQ,UAAU,GAAG;AACxB,iBAAa,CAAC,CAAC,YAAY,CAAC,UAAU;AAAA,EACxC;AACA,SAAO,CAAC,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;AAChD;AACO,SAAS,sBAAsB,cAAc,YAAY;AAC9D,MAAI,gBAAgB,MAAM;AACxB;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,mBAAe,CAAC,cAAc,YAAY;AAAA,EAC5C;AACA,SAAO,CAAC,aAAa,aAAa,CAAC,GAAG,WAAW,CAAC,CAAC,KAAK,GAAG,aAAa,UAAU,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,KAAK,CAAC;AAC1I;;;AC3RA,IAAI,WAAW,IAAI,gBAAQ;AAC3B,IAAI,aAAa,IAAI,YAAI,GAAG;AAC5B,IAAI,YAAY,CAAC,UAAU,cAAc,oBAAoB,SAAS,mBAAmB,cAAc,cAAc,gBAAgB,eAAe;AAO7I,SAAS,+BAA+B,aAAa,KAAK;AAC/D,MAAI,gBAAgB,QAAQ;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,MAAM,IAAI,oBAAoB;AAClC,MAAI,KAAK,IAAI,MAAM;AACnB,MAAI,QAAQ,GAAG,QAAQ,SAAS;AAChC,MAAI,YAAY,OAAO;AACrB,aAAS,QAAQ,EAAE,WAAW;AAAA,EAChC;AACA,MAAI,aAAa,SAAS,IAAI,WAAW;AACzC,MAAI,YAAY;AACd,WAAO;AAAA,EACT;AACA,MAAI,WAAW,SAAS,aAAa;AAAA,IACnC,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,cAAc;AAAA,IACd,eAAe;AAAA,EACjB,CAAC;AACD,MAAI,SAAS,oBAAoB,QAAQ;AACvC,aAAS,kBAAkB;AAAA,EAC7B;AACA,MAAI,UAAU;AAAA,IACZ,QAAQ;AAAA,EACV;AACA,oBAAkB,OAAO;AACzB,UAAQ,WAAW,SAAS;AAC5B,UAAQ,SAAS,QAAQ,SAAS,QAAQ,IAAI,IAAI;AAClD,WAAS,IAAI,aAAa,OAAO;AACjC,cAAY,QAAQ;AACpB,SAAO;AACP,WAAS,kBAAkBC,UAAS;AAClC,QAAIC,QAAO,CAAC,GAAG;AACf,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,UAAI,QAAQ,SAAS,UAAU,CAAC,CAAC;AACjC,UAAI,SAAS,QAAQ,CAAC,QAAQ,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,OAAO,UAAU,WAAW;AAC1G,qBAAa;AACb;AAAA,MACF;AACA,MAAAA,MAAK,KAAK,KAAK;AAAA,IACjB;AACA,QAAI;AACJ,QAAI,YAAY;AACd,iBAAWA,MAAK,KAAK,GAAG,KAAK,QAAQ,SAAS;AAC9C,UAAI,QAAQ,WAAW,IAAI,QAAQ;AACnC,UAAI,OAAO;AACT,gBAAQD,SAAQ,aAAa,QAAQA,SAAQ,QAAQ;AAAA,MACvD;AAAA,IACF;AACA,QAAI,aAAa,oBAAoB,SAAS,UAAU;AACxD,QAAI,aAAa,oBAAoB,SAAS,UAAU;AACxD,QAAI,cAAc,qBAAqB,SAAS,MAAM;AACtD,QAAI,oBAAoB,oBAAoB,UAAU;AACtD,QAAI,mBAAmB,oBAAoB,UAAU;AACrD,QAAI,SAAS,CAAC,SAAS,YAAY,aAAa;AAChD,QAAI,UAAU,SAAS;AAAA,MACrB,KAAK;AAAA,MACL,OAAO,CAAC;AAAA,MACR,KAAK;AAAA,MACL,UAAU,CAAC;AAAA,IACb;AACA,QAAI,QAAQ,eAAe;AAC3B,QAAI;AACJ,QAAI,QAAQ;AACV,aAAO,QAAQ,MAAM,QAAQ;AAC7B,aAAO,SAAS,MAAM,SAAS;AAC/B,YAAM,OAAO,WAAW,IAAI;AAAA,IAC9B;AACA,eAAW;AACX,QAAI,YAAY;AACd,iBAAW,IAAI,UAAU,UAAU,OAAO;AAAA,IAC5C;AACA,IAAAA,SAAQ,QAAQ;AAChB,IAAAA,SAAQ,aAAa;AACrB,IAAAA,SAAQ,WAAW,MAAM;AACzB,IAAAA,SAAQ,YAAY,MAAM;AAM1B,aAAS,iBAAiB;AAYxB,UAAI,QAAQ;AACZ,eAASE,KAAI,GAAG,OAAO,kBAAkB,QAAQA,KAAI,MAAM,EAAEA,IAAG;AAC9D,gBAAQ,uBAAuB,OAAO,kBAAkBA,EAAC,CAAC;AAAA,MAC5D;AACA,UAAI,gBAAgB;AACpB,eAASA,KAAI,GAAG,OAAO,YAAY,QAAQA,KAAI,MAAM,EAAEA,IAAG;AACxD,wBAAgB,uBAAuB,eAAe,YAAYA,EAAC,EAAE,MAAM;AAAA,MAC7E;AACA,eAAS;AACT,UAAI,SAAS,mBAAmB,kBAAkB,SAAS,YAAY;AACvE,UAAI,MAAuC;AACzC,YAAIC,QAAO,SAAU,UAAU;AAE7B,kBAAQ,KAAK,2CAA2C,WAAW,sCAAsC,WAAW,2GAA2G,WAAW,sCAAsC;AAAA,QAClR;AACA,YAAI,QAAQ,SAAS,cAAc;AACjC,UAAAA,MAAK,cAAc;AAAA,QACrB;AACA,YAAI,SAAS,SAAS,eAAe;AACnC,UAAAA,MAAK,eAAe;AAAA,QACtB;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,SAAS,YAAY,CAAC;AAAA,QACzD,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,QAAQ,SAAS,aAAa,CAAC;AAAA,MAC9D;AAAA,IACF;AACA,aAAS,aAAa;AACpB,UAAI,KAAK;AACP,YAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC/C,YAAI,SAAS,iBAAiB;AAC5B,cAAI,YAAY,SAAS;AACzB,cAAI,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAAA,QAChD;AAAA,MACF;AACA,UAAI,OAAO;AACX,eAASD,KAAI,GAAGA,KAAI,WAAW,QAAQ,EAAEA,IAAG;AAC1C,gBAAQ,WAAWA,EAAC;AAAA,MACtB;AACA,UAAI,QAAQ,GAAG;AAEb;AAAA,MACF;AACA,UAAI,IAAI,CAAC;AACT,UAAI,MAAM;AACV,UAAI,WAAW;AACf,UAAI,OAAO;AACX,aAAO,IAAI,MAAM,QAAQ;AACvB,YAAI,MAAM,MAAM,GAAG;AACjB,cAAI,YAAY,WAAW,IAAI,YAAY;AAC3C,cAAI,IAAI;AACR,cAAI,OAAO;AACX,cAAI,YAAY;AAChB,iBAAO,IAAI,MAAM,QAAQ,GAAG;AAC1B,gBAAI,OAAO;AACX,qBAASA,KAAI,GAAGA,KAAI,WAAW,IAAI,EAAE,QAAQ,EAAEA,IAAG;AAChD,sBAAQ,WAAW,IAAI,EAAEA,EAAC;AAAA,YAC5B;AACA,gBAAI,QAAQ,GAAG;AAEb;AAAA,YACF;AAEA,gBAAI,OAAO,MAAM,GAAG;AAClB,kBAAI,QAAQ,IAAI,SAAS,cAAc;AACvC,kBAAI,OAAO,IAAI,WAAW,IAAI,EAAE,IAAI,IAAI;AACxC,kBAAI,QAAQ,IAAI,WAAW,GAAG,IAAI;AAClC,kBAAI,QAAQ,WAAW,IAAI,EAAE,IAAI,IAAI,SAAS;AAC9C,kBAAI,SAAS,WAAW,GAAG,IAAI,SAAS;AACxC,kBAAI,YAAY,YAAY,IAAI,YAAY,SAAS,EAAE;AACvD,0BAAY,MAAM,OAAO,OAAO,QAAQ,YAAY,SAAS,EAAE,SAAS,CAAC;AAAA,YAC3E;AACA,iBAAK,WAAW,IAAI,EAAE,IAAI;AAC1B,cAAE;AACF,cAAE;AACF,gBAAI,SAAS,WAAW,IAAI,EAAE,QAAQ;AACpC,qBAAO;AAAA,YACT;AAAA,UACF;AACA,YAAE;AACF,cAAI,SAAS,WAAW,QAAQ;AAC9B,mBAAO;AAAA,UACT;AAAA,QACF;AACA,aAAK,WAAW,GAAG;AACnB,UAAE;AACF,UAAE;AACF,YAAI,QAAQ,WAAW,QAAQ;AAC7B,gBAAM;AAAA,QACR;AAAA,MACF;AACA,eAAS,YAAYE,IAAGC,IAAGC,QAAOC,SAAQ,YAAY;AACpD,YAAI,QAAQ,QAAQ,IAAI;AACxB,YAAI,SAAS,aAAa,YAAYH,KAAI,OAAOC,KAAI,OAAOC,SAAQ,OAAOC,UAAS,OAAO,SAAS,OAAO,SAAS,gBAAgB;AACpI,YAAI,OAAO;AACT,cAAI,cAAc,GAAG,QAAQ,iBAAiB,MAAM;AACpD,cAAI,aAAa;AACf,oBAAQ,SAAS,KAAK,WAAW;AAAA,UACnC;AAAA,QACF,OAAO;AAEL,sBAAY,KAAK,MAAM;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,SAAS,qBAAqB,QAAQ;AACpC,MAAI,CAAC,UAAU,OAAO,WAAW,GAAG;AAClC,WAAO,CAAC,CAAC,MAAM,CAAC;AAAA,EAClB;AACA,MAAI,SAAS,MAAM,GAAG;AACpB,WAAO,CAAC,CAAC,MAAM,CAAC;AAAA,EAClB;AACA,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,QAAI,CAAC,SAAS,OAAO,CAAC,CAAC,GAAG;AACxB,oBAAc;AACd;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa;AACf,WAAO,qBAAqB,CAAC,MAAM,CAAC;AAAA,EACtC;AACA,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,QAAI,SAAS,OAAO,CAAC,CAAC,GAAG;AACvB,aAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAAA,IACzB,OAAO;AACL,aAAO,KAAK,OAAO,CAAC,CAAC;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,oBAAoB,MAAM;AACjC,MAAI,CAAC,QAAQ,KAAK,WAAW,GAAG;AAC9B,WAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA,EAChB;AACA,MAAI,SAAS,IAAI,GAAG;AAClB,QAAI,YAAY,KAAK,KAAK,IAAI;AAC9B,WAAO,CAAC,CAAC,WAAW,SAAS,CAAC;AAAA,EAChC;AAKA,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,QAAI,CAAC,SAAS,KAAK,CAAC,CAAC,GAAG;AACtB,oBAAc;AACd;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa;AACf,WAAO,oBAAoB,CAAC,IAAI,CAAC;AAAA,EACnC;AACA,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,QAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,UAAI,YAAY,KAAK,KAAK,KAAK,CAAC,CAAC;AACjC,aAAO,KAAK,CAAC,WAAW,SAAS,CAAC;AAAA,IACpC,OAAO;AACL,UAAI,YAAY,IAAI,KAAK,CAAC,GAAG,SAAU,GAAG;AACxC,eAAO,KAAK,KAAK,CAAC;AAAA,MACpB,CAAC;AACD,UAAI,UAAU,SAAS,MAAM,GAAG;AAG9B,eAAO,KAAK,UAAU,OAAO,SAAS,CAAC;AAAA,MACzC,OAAO;AACL,eAAO,KAAK,SAAS;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,oBAAoB,MAAM;AACjC,MAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,WAAW,GAAG;AAC1D,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AACA,MAAI,SAAS,IAAI,GAAG;AAClB,QAAI,cAAc,KAAK,KAAK,IAAI;AAChC,WAAO,CAAC,aAAa,WAAW;AAAA,EAClC;AACA,MAAI,YAAY,IAAI,MAAM,SAAU,GAAG;AACrC,WAAO,KAAK,KAAK,CAAC;AAAA,EACpB,CAAC;AACD,SAAO,KAAK,SAAS,IAAI,UAAU,OAAO,SAAS,IAAI;AACzD;AASA,SAAS,oBAAoB,MAAM;AACjC,SAAO,IAAI,MAAM,SAAU,MAAM;AAC/B,WAAO,oBAAoB,IAAI;AAAA,EACjC,CAAC;AACH;AACA,SAAS,oBAAoB,MAAM;AACjC,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,mBAAe,KAAK,CAAC;AAAA,EACvB;AACA,MAAI,KAAK,SAAS,MAAM,GAAG;AAGzB,WAAO,cAAc;AAAA,EACvB;AACA,SAAO;AACT;;;AC3Ve,SAAR,YAA6B,SAAS,KAAK;AAChD,UAAQ,cAAc,SAAU,aAAa;AAC3C,QAAI,QAAQ,iBAAiB,WAAW,GAAG;AACzC;AAAA,IACF;AACA,QAAI,OAAO,YAAY,QAAQ;AAC/B,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,KAAK,SAAU,KAAK;AACvB,YAAIC,SAAQ,KAAK,cAAc,KAAK,OAAO;AAC3C,YAAIA,QAAO;AACT,cAAI,YAAY,KAAK,uBAAuB,KAAK,OAAO;AACxD,oBAAU,QAAQ,+BAA+BA,QAAO,GAAG;AAAA,QAC7D;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,QAAQ,KAAK,UAAU,OAAO;AAClC,QAAI,OAAO;AACT,UAAI,QAAQ,KAAK,UAAU,OAAO;AAClC,YAAM,QAAQ,+BAA+B,OAAO,GAAG;AAAA,IACzD;AAAA,EACF,CAAC;AACH;;;ACpBA,IAAI,YAAY,IAAI,iBAAS;AAC7B,IAAO,oBAAQ;;;ACCf,IAAI,aAAa,CAAC;AAEX,SAAS,aAAa,MAAM,MAAM;AACvC,MAAI,MAAuC;AACzC,QAAI,WAAW,IAAI,GAAG;AACpB,YAAM,sCAAsC,OAAO,GAAG;AAAA,IACxD;AAAA,EACF;AACA,aAAW,IAAI,IAAI;AACrB;AACO,SAAS,QAAQ,MAAM;AAC5B,MAAI,MAAuC;AACzC,QAAI,CAAC,WAAW,IAAI,GAAG;AACrB,YAAM,uBAAuB,OAAO,kBAAkB;AAAA,IACxD;AAAA,EACF;AACA,SAAO,WAAW,IAAI;AACxB;;;ACiBO,IAAI,UAAU;AACd,IAAI,eAAe;AAAA,EACxB,SAAS;AACX;AACA,IAAI,yBAAyB;AAC7B,IAAI,mCAAmC;AAGvC,IAAI,+BAA+B;AAGnC,IAAI,4BAA4B;AAChC,IAAI,6BAA6B;AACjC,IAAI,+BAA+B;AACnC,IAAI,yBAAyB;AAC7B,IAAI,qCAAqC;AACzC,IAAI,yBAAyB;AAC7B,IAAI,wBAAwB;AAC5B,IAAI,4BAA4B;AAIhC,IAAI,oCAAoC;AAGxC,IAAI,oCAAoC;AACxC,IAAI,wBAAwB;AAC5B,IAAI,uBAAuB;AAC3B,IAAI,wBAAwB;AACrB,IAAI,WAAW;AAAA,EACpB,WAAW;AAAA,IACT,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,EACT;AACF;AAMA,IAAI,sBAAsB;AAC1B,IAAI,iBAAiB;AACrB,IAAI,0BAA0B;AAC9B,IAAI,aAAa;AACjB,IAAI,qBAAqB;AACzB,IAAI,yBAAyB;AAC7B,IAAI,0BAA0B;AAC9B,IAAI,yBAAyB;AAG7B,SAAS,wCAAwC,QAAQ;AACvD,SAAO,WAAY;AACjB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IACzB;AACA,QAAI,KAAK,WAAW,GAAG;AACrB,sBAAgB,KAAK,EAAE;AACvB;AAAA,IACF;AACA,WAAO,+BAA+B,MAAM,QAAQ,IAAI;AAAA,EAC1D;AACF;AACA,SAAS,8CAA8C,QAAQ;AAC7D,SAAO,WAAY;AACjB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IACzB;AACA,WAAO,+BAA+B,MAAM,QAAQ,IAAI;AAAA,EAC1D;AACF;AACA,SAAS,+BAA+B,MAAM,QAAQ,MAAM;AAE1D,OAAK,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE,YAAY;AACzC,SAAO,iBAAS,UAAU,MAAM,EAAE,MAAM,MAAM,IAAI;AACpD;AACA,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACvB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,WAAOA;AAAA,EACT,EAAE,gBAAQ;AAAA;AACV,IAAI,qBAAqB,cAAc;AACvC,mBAAmB,KAAK,8CAA8C,IAAI;AAC1E,mBAAmB,MAAM,8CAA8C,KAAK;AAI5E,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAAA;AAAA,EAAuB,SAAU,QAAQ;AAC3C,cAAUC,UAAS,MAAM;AACzB,aAASA,SAAQ,KAEjBC,QAAO,MAAM;AACX,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,iBAAiB,CAAC,KAAK;AACzD,YAAM,eAAe,CAAC;AACtB,YAAM,aAAa,CAAC;AACpB,YAAM,mBAAmB,CAAC;AAC1B,YAAM,iBAAiB,CAAC;AAExB,YAAM,kBAAkB,CAAC;AACzB,aAAO,QAAQ,CAAC;AAEhB,UAAI,SAASA,MAAK,GAAG;AACnB,QAAAA,SAAQ,aAAaA,MAAK;AAAA,MAC5B;AACA,YAAM,OAAO;AACb,UAAI,kBAAkB;AACtB,UAAI,uBAAuB;AAC3B,UAAI,sBAAsB;AAC1B,UAAI,MAAuC;AACzC,YAAI;AAAA;AAAA,UACJ,YAAI,kBAAkB,SAAS;AAAA;AAC/B,YAAI,MAAM;AACR,4BAAkB,UAAU,KAAK,gCAAgC,eAAe;AAChF,iCAAuB,UAAU,KAAK,oCAAoC,oBAAoB;AAC9F,gCAAsB,UAAU,KAAK,sCAAsC,mBAAmB;AAAA,QAChG;AAAA,MACF;AACA,UAAI,KAAK,KAAK;AACZ,QAAQ,sBAAsB,SAAU,IAAI;AAC1C,cAAI,SAAS,UAAU,EAAE;AACzB,cAAI,YAAY,OAAO;AACvB,cAAI,aAAa,MAAM;AACrB;AAAA,UACF;AACA,cAAI,UAAU,cAAc;AAC5B,kBAAQ,IAAI,gBAAgB,OAAO,WAAW;AAC9C,kBAAQ,IAAI,cAAc,SAAS;AACnC,iBAAO,WAAW,QAAQ,IAAI,YAAY,OAAO,OAAO;AACxD,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,UAAI,KAAK,MAAM,MAAc,KAAK,KAAK;AAAA,QACrC,UAAU,KAAK,YAAY;AAAA,QAC3B,kBAAkB,KAAK;AAAA,QACvB,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,QACb,KAAK,KAAK;AAAA,QACV,cAAc,UAAU,KAAK,cAAc,mBAAmB;AAAA,QAC9D,kBAAkB,UAAU,KAAK,kBAAkB,oBAAoB;AAAA,QACvE,aAAa,KAAK;AAAA,MACpB,CAAC;AACD,YAAM,OAAO,KAAK;AAElB,YAAM,oBAAoB,SAAS,KAAK,GAAG,OAAO,EAAE,GAAG,EAAE;AACzD,MAAAA,SAAQ,MAAMA,MAAK;AACnB,MAAAA,UAAS,qBAAeA,QAAO,IAAI;AACnC,YAAM,SAASA;AACf,YAAM,UAAU,mBAAmB,KAAK,UAAU,WAAW;AAC7D,YAAM,eAAe,IAAI,yBAAwB;AACjD,UAAI,MAAM,MAAM,OAAO,mBAAmB,KAAK;AAE/C,eAAS,iBAAiB,GAAG,GAAG;AAC9B,eAAO,EAAE,SAAS,EAAE;AAAA,MACtB;AACA,WAAQ,aAAa,gBAAgB;AACrC,WAAQ,oBAAoB,gBAAgB;AAC5C,YAAM,aAAa,IAAI,kBAAU,OAAO,KAAK,oBAAoB,WAAW;AAC5E,YAAM,iBAAiB,IAAI,cAAc;AAEzC,YAAM,YAAY;AAElB,YAAM,SAAS,KAAK,MAAM,QAAQ,KAAK;AACvC,SAAG,UAAU,GAAG,SAAS,MAAM,UAAU,KAAK;AAC9C,wBAAkB,IAAI,KAAK;AAC3B,qBAAe,IAAI,KAAK;AAExB,qBAAe,KAAK;AACpB,aAAO;AAAA,IACT;AACA,IAAAD,SAAQ,UAAU,WAAW,WAAY;AACvC,UAAI,KAAK,WAAW;AAClB;AAAA,MACF;AACA,yBAAmB,IAAI;AACvB,UAAI,YAAY,KAAK;AAErB,UAAI,KAAK,cAAc,GAAG;AACxB,YAAI,SAAS,KAAK,cAAc,EAAE;AAClC,aAAK,mBAAmB,IAAI;AAC5B,YAAI;AACF,kBAAQ,IAAI;AACZ,wBAAc,OAAO,KAAK,MAAM,MAAM,KAAK,cAAc,EAAE,YAAY;AAAA,QACzE,SAAS,GAAG;AACV,eAAK,mBAAmB,IAAI;AAC5B,eAAK,cAAc,IAAI;AACvB,gBAAM;AAAA,QACR;AAOA,aAAK,IAAI,MAAM;AACf,aAAK,mBAAmB,IAAI;AAC5B,aAAK,cAAc,IAAI;AACvB,4BAAoB,KAAK,MAAM,MAAM;AACrC,4BAAoB,KAAK,MAAM,MAAM;AAAA,MACvC,WAES,UAAU,YAAY;AAE7B,YAAI,aAAa;AACjB,YAAI,UAAU,KAAK;AACnB,YAAI,MAAM,KAAK;AACf,kBAAU,aAAa;AACvB,WAAG;AACD,cAAI,YAAY,CAAC,oBAAI,KAAK;AAC1B,oBAAU,mBAAmB,OAAO;AAEpC,oBAAU,0BAA0B,OAAO;AAC3C,4BAAkB,MAAM,OAAO;AAO/B,oBAAU,mBAAmB,OAAO;AACpC,uBAAa,MAAM,KAAK,QAAQ,KAAK,UAAU,CAAC,CAAC;AACjD,wBAAc,CAAC,oBAAI,KAAK,IAAI;AAAA,QAC9B,SAAS,aAAa,KAAK,UAAU;AAErC,YAAI,CAAC,UAAU,YAAY;AACzB,eAAK,IAAI,MAAM;AAAA,QACjB;AAAA,MAGF;AAAA,IACF;AAEA,IAAAA,SAAQ,UAAU,SAAS,WAAY;AACrC,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,SAAQ,UAAU,QAAQ,WAAY;AACpC,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,SAAQ,UAAU,QAAQ,WAAY;AACpC,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,SAAQ,UAAU,QAAQ,WAAY;AACpC,aAAO,KAAK;AAAA,IACd;AAEA,IAAAA,SAAQ,UAAU,YAAY,SAAU,QAAQ,UAAU,YAAY;AACpE,UAAI,KAAK,mBAAmB,GAAG;AAC7B,YAAI,MAAuC;AACzC,gBAAM,uDAAuD;AAAA,QAC/D;AACA;AAAA,MACF;AACA,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,SAAS,QAAQ,GAAG;AACtB,qBAAa,SAAS;AACtB,iBAAS,SAAS;AAClB,uBAAe,SAAS;AACxB,wBAAgB,SAAS;AACzB,mBAAW,SAAS;AAAA,MACtB;AACA,WAAK,mBAAmB,IAAI;AAC5B,UAAI,CAAC,KAAK,UAAU,UAAU;AAC5B,YAAI,gBAAgB,IAAI,sBAAc,KAAK,IAAI;AAC/C,YAAIC,SAAQ,KAAK;AACjB,YAAI,UAAU,KAAK,SAAS,IAAI,eAAY;AAC5C,gBAAQ,YAAY,KAAK;AACzB,gBAAQ,MAAM,KAAK;AACnB,gBAAQ,KAAK,MAAM,MAAM,MAAMA,QAAO,KAAK,SAAS,aAAa;AAAA,MACnE;AACA,WAAK,OAAO,UAAU,QAAQ;AAAA,QAC5B;AAAA,MACF,GAAG,uBAAuB;AAC1B,UAAI,eAAe;AAAA,QACjB,kBAAkB;AAAA,QAClB,eAAe;AAAA,MACjB;AACA,UAAI,YAAY;AACd,aAAK,cAAc,IAAI;AAAA,UACrB;AAAA,UACA;AAAA,QACF;AACA,aAAK,mBAAmB,IAAI;AAG5B,aAAK,MAAM,EAAE,OAAO;AAAA,MACtB,OAAO;AACL,YAAI;AACF,kBAAQ,IAAI;AACZ,wBAAc,OAAO,KAAK,MAAM,MAAM,YAAY;AAAA,QACpD,SAAS,GAAG;AACV,eAAK,cAAc,IAAI;AACvB,eAAK,mBAAmB,IAAI;AAC5B,gBAAM;AAAA,QACR;AAGA,YAAI,CAAC,KAAK,MAAM;AAEd,eAAK,IAAI,MAAM;AAAA,QACjB;AACA,aAAK,cAAc,IAAI;AACvB,aAAK,mBAAmB,IAAI;AAC5B,4BAAoB,KAAK,MAAM,MAAM;AACrC,4BAAoB,KAAK,MAAM,MAAM;AAAA,MACvC;AAAA,IACF;AAIA,IAAAD,SAAQ,UAAU,WAAW,WAAY;AACvC,mBAAa,iDAAiD;AAAA,IAChE;AAEA,IAAAA,SAAQ,UAAU,WAAW,WAAY;AACvC,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,SAAQ,UAAU,YAAY,WAAY;AACxC,aAAO,KAAK,UAAU,KAAK,OAAO,UAAU;AAAA,IAC9C;AACA,IAAAA,SAAQ,UAAU,WAAW,WAAY;AACvC,aAAO,KAAK,IAAI,SAAS;AAAA,IAC3B;AACA,IAAAA,SAAQ,UAAU,YAAY,WAAY;AACxC,aAAO,KAAK,IAAI,UAAU;AAAA,IAC5B;AACA,IAAAA,SAAQ,UAAU,sBAAsB,WAAY;AAClD,aAAO,KAAK,IAAI,QAAQ,OACU,YAAI,mBAAmB,OAAO,oBAAoB;AAAA,IACtF;AAKA,IAAAA,SAAQ,UAAU,oBAAoB,SAAU,MAAM;AACpD,UAAI,MAAuC;AACzC,4BAAoB,qBAAqB,gBAAgB;AAAA,MAC3D;AACA,aAAO,KAAK,eAAe,IAAI;AAAA,IACjC;AACA,IAAAA,SAAQ,UAAU,iBAAiB,SAAU,MAAM;AACjD,aAAO,QAAQ,CAAC;AAChB,UAAI,UAAU,KAAK,IAAI;AACvB,UAAI,MAAuC;AACzC,YAAI,QAAQ,SAAS,UAAU;AAC7B,gBAAM,IAAI,MAAM,yDAAyD;AAAA,QAC3E;AAAA,MACF;AACA,aAAO,QAAQ,kBAAkB;AAAA,QAC/B,iBAAiB,KAAK,mBAAmB,KAAK,OAAO,IAAI,iBAAiB;AAAA,QAC1E,YAAY,KAAK,cAAc,KAAK,oBAAoB;AAAA,MAC1D,CAAC;AAAA,IACH;AACA,IAAAA,SAAQ,UAAU,oBAAoB,SAAU,MAAM;AACpD,aAAO,QAAQ,CAAC;AAChB,UAAI,UAAU,KAAK,IAAI;AACvB,UAAI,MAAuC;AACzC,YAAI,QAAQ,SAAS,OAAO;AAC1B,gBAAM,IAAI,MAAM,yDAAyD;AAAA,QAC3E;AAAA,MACF;AACA,aAAO,QAAQ,eAAe;AAAA,QAC5B,YAAY,KAAK;AAAA,MACnB,CAAC;AAAA,IACH;AAIA,IAAAA,SAAQ,UAAU,gBAAgB,WAAY;AAC5C,UAAI,CAAC,YAAI,cAAc;AACrB;AAAA,MACF;AACA,UAAI,KAAK,KAAK;AACd,UAAI,OAAO,GAAG,QAAQ,eAAe;AAErC,WAAK,MAAM,SAAU,IAAI;AACvB,WAAG,cAAc,MAAM,IAAI;AAAA,MAC7B,CAAC;AACD,aAAO,GAAG,QAAQ,UAAU;AAAA,IAC9B;AACA,IAAAA,SAAQ,UAAU,aAAa,SAAU,MAAM;AAC7C,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,aAAO,QAAQ,CAAC;AAChB,UAAI,oBAAoB,KAAK;AAC7B,UAAI,UAAU,KAAK;AACnB,UAAI,yBAAyB,CAAC;AAC9B,UAAI,OAAO;AACX,WAAK,mBAAmB,SAAU,eAAe;AAC/C,gBAAQ,cAAc;AAAA,UACpB,UAAU;AAAA,QACZ,GAAG,SAAU,WAAW;AACtB,cAAI,OAAO,KAAK,eAAe,UAAU,QAAQ;AACjD,cAAI,CAAC,KAAK,MAAM,QAAQ;AACtB,mCAAuB,KAAK,IAAI;AAChC,iBAAK,MAAM,SAAS;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,UAAI,MAAM,KAAK,IAAI,QAAQ,QAAQ,MAAM,QAAQ,KAAK,cAAc,IAAI,KAAK,eAAe,IAAI,EAAE,UAAU,YAAY,QAAQ,KAAK,QAAQ,MAAM;AACnJ,WAAK,wBAAwB,SAAU,MAAM;AAC3C,aAAK,MAAM,SAAS;AAAA,MACtB,CAAC;AACD,aAAO;AAAA,IACT;AACA,IAAAA,SAAQ,UAAU,sBAAsB,SAAU,MAAM;AACtD,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,SAAS;AAC1B,UAAI,UAAU,KAAK;AACnB,UAAI,UAAU,KAAK;AACnB,UAAI,UAAU,KAAK;AACnB,UAAI,aAAa;AACjB,UAAI,gBAAgB,OAAO,GAAG;AAC5B,YAAI,SAAS;AACb,YAAI,QAAQ;AACZ,YAAI,UAAU,CAAC;AACf,YAAI,WAAW,CAAC;AAChB,YAAI,eAAe,CAAC;AACpB,YAAI,QAAQ,QAAQ,KAAK,cAAc,KAAK,oBAAoB;AAChE,aAAK,WAAW,SAAU,OAAO,IAAI;AACnC,cAAI,MAAM,UAAU,SAAS;AAC3B,gBAAI,SAAS,QAAQ,MAAM,MAAM,EAAE,QAAQ,UAAU,EAAE,YAAY,MAAM,eAAe,MAAM,IAAI,CAAC;AACnG,gBAAI,eAAe,MAAM,OAAO,EAAE,sBAAsB;AACxD,qBAAS,QAAQ,aAAa,MAAM,MAAM;AAC1C,oBAAQ,QAAQ,aAAa,KAAK,KAAK;AACvC,sBAAU,QAAQ,aAAa,OAAO,OAAO;AAC7C,uBAAW,QAAQ,aAAa,QAAQ,QAAQ;AAChD,yBAAa,KAAK;AAAA,cAChB,KAAK;AAAA,cACL,MAAM,aAAa;AAAA,cACnB,KAAK,aAAa;AAAA,YACpB,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,kBAAU;AACV,iBAAS;AACT,mBAAW;AACX,oBAAY;AACZ,YAAI,QAAQ,UAAU;AACtB,YAAI,SAAS,WAAW;AACxB,YAAI,eAAe,YAAY,aAAa;AAC5C,YAAI,OAAe,KAAK,cAAc;AAAA,UACpC,UAAU,QAAQ,QAAQ;AAAA,QAC5B,CAAC;AACD,aAAK,OAAO;AAAA,UACV;AAAA,UACA;AAAA,QACF,CAAC;AACD,YAAI,OAAO;AACT,cAAI,YAAY;AAChB,eAAK,cAAc,SAAU,MAAM;AACjC,gBAAI,IAAI,KAAK,OAAO;AACpB,gBAAI,IAAI,KAAK,MAAM;AACnB,yBAAa,6BAA6B,IAAI,MAAM,IAAI,QAAQ,KAAK,MAAM;AAAA,UAC7E,CAAC;AACD,eAAK,QAAQ,WAAW,EAAE,YAAY;AACtC,cAAI,KAAK,0BAA0B;AACjC,iBAAK,QAAQ,mBAAmB,KAAK,wBAAwB;AAAA,UAC/D;AACA,eAAK,mBAAmB;AACxB,iBAAO,KAAK,QAAQ,UAAU;AAAA,QAChC,OAAO;AAEL,cAAI,KAAK,0BAA0B;AACjC,iBAAK,IAAI,IAAY,aAAK;AAAA,cACxB,OAAO;AAAA,gBACL,GAAG;AAAA,gBACH,GAAG;AAAA,gBACH;AAAA,gBACA;AAAA,cACF;AAAA,cACA,OAAO;AAAA,gBACL,MAAM,KAAK;AAAA,cACb;AAAA,YACF,CAAC,CAAC;AAAA,UACJ;AACA,eAAK,cAAc,SAAU,MAAM;AACjC,gBAAI,MAAM,IAAY,cAAM;AAAA,cAC1B,OAAO;AAAA,gBACL,GAAG,KAAK,OAAO,QAAQ;AAAA,gBACvB,GAAG,KAAK,MAAM,QAAQ;AAAA,gBACtB,OAAO,KAAK;AAAA,cACd;AAAA,YACF,CAAC;AACD,iBAAK,IAAI,GAAG;AAAA,UACd,CAAC;AACD,eAAK,mBAAmB;AACxB,iBAAO,aAAa,UAAU,YAAY,QAAQ,KAAK,QAAQ,MAAM;AAAA,QACvE;AAAA,MACF,OAAO;AACL,eAAO,KAAK,WAAW,IAAI;AAAA,MAC7B;AAAA,IACF;AACA,IAAAA,SAAQ,UAAU,iBAAiB,SAAU,QAAQ,OAAO;AAC1D,aAAO,eAAe,MAAM,kBAAkB,QAAQ,KAAK;AAAA,IAC7D;AACA,IAAAA,SAAQ,UAAU,mBAAmB,SAAU,QAAQ,OAAO;AAC5D,aAAO,eAAe,MAAM,oBAAoB,QAAQ,KAAK;AAAA,IAC/D;AAMA,IAAAA,SAAQ,UAAU,eAAe,SAAU,QAAQ,OAAO;AACxD,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,UAAI,UAAU,KAAK;AACnB,UAAI;AACJ,UAAI,aAAuB,YAAY,SAAS,MAAM;AACtD,WAAK,YAAY,SAAU,QAAQ,KAAK;AACtC,YAAI,QAAQ,QAAQ,KAAK,KAAK,KAAK,QAAQ,SAAU,OAAO;AAC1D,cAAI,WAAW,MAAM;AACrB,cAAI,YAAY,SAAS,cAAc;AACrC,qBAAS,UAAU,CAAC,CAAC,SAAS,aAAa,KAAK;AAAA,UAClD,WAAW,QAAQ,gBAAgB;AACjC,gBAAI,OAAO,KAAK,WAAW,MAAM,QAAQ;AACzC,gBAAI,QAAQ,KAAK,cAAc;AAC7B,uBAAS,UAAU,KAAK,aAAa,OAAO,KAAK;AAAA,YACnD,OAAO;AACL,kBAAI,MAAuC;AACzC,qBAAK,MAAM,QAAQ,OAAO,qDAAqD,0CAA0C;AAAA,cAC3H;AAAA,YACF;AAAA,UACF,OAAO;AACL,gBAAI,MAAuC;AACzC,mBAAK,MAAM,iCAAiC;AAAA,YAC9C;AAAA,UACF;AAAA,QACF,GAAG,IAAI;AAAA,MACT,GAAG,IAAI;AACP,aAAO,CAAC,CAAC;AAAA,IACX;AAgBA,IAAAA,SAAQ,UAAU,YAAY,SAAU,QAAQ,YAAY;AAC1D,UAAI,UAAU,KAAK;AACnB,UAAI,eAAyB,YAAY,SAAS,QAAQ;AAAA,QACxD,iBAAiB;AAAA,MACnB,CAAC;AACD,UAAI,cAAc,aAAa;AAC/B,UAAI,MAAuC;AACzC,YAAI,CAAC,aAAa;AAChB,eAAK,oCAAoC;AAAA,QAC3C;AAAA,MACF;AACA,UAAI,OAAO,YAAY,QAAQ;AAC/B,UAAI,kBAAkB,aAAa,eAAe,iBAAiB,IAAI,aAAa,kBAAkB,aAAa,eAAe,WAAW,IAAI,KAAK,gBAAgB,aAAa,SAAS,IAAI;AAChM,aAAO,mBAAmB,OAAO,sBAAsB,MAAM,iBAAiB,UAAU,IAAI,kBAAkB,MAAM,UAAU;AAAA,IAChI;AAIA,IAAAA,SAAQ,UAAU,0BAA0B,SAAU,gBAAgB;AACpE,aAAO,KAAK,eAAe,eAAe,QAAQ;AAAA,IACpD;AAIA,IAAAA,SAAQ,UAAU,uBAAuB,SAAU,aAAa;AAC9D,aAAO,KAAK,WAAW,YAAY,QAAQ;AAAA,IAC7C;AACA,IAAAA,SAAQ,UAAU,cAAc,WAAY;AAC1C,UAAI,QAAQ;AACZ,WAAK,mBAAmB,SAAU,SAAS;AACzC,YAAI,UAAU,SAAU,GAAG;AACzB,cAAI,UAAU,MAAM,SAAS;AAC7B,cAAI,KAAK,EAAE;AACX,cAAI;AACJ,cAAI,cAAc,YAAY;AAE9B,cAAI,aAAa;AACf,qBAAS,CAAC;AAAA,UACZ,OAAO;AACL,kBAAM,oBAAoB,IAAI,SAAU,QAAQ;AAC9C,kBAAI,SAAS,UAAU,MAAM;AAC7B,kBAAI,UAAU,OAAO,aAAa,MAAM;AACtC,oBAAI,YAAY,OAAO,aAAa,QAAQ,iBAAiB,OAAO,WAAW;AAC/E,yBAAS,aAAa,UAAU,cAAc,OAAO,WAAW,OAAO,UAAU,EAAE,KAAK,CAAC;AACzF,uBAAO;AAAA,cACT,WAES,OAAO,WAAW;AACzB,yBAAS,OAAO,CAAC,GAAG,OAAO,SAAS;AACpC,uBAAO;AAAA,cACT;AAAA,YACF,GAAG,IAAI;AAAA,UACT;AAQA,cAAI,QAAQ;AACV,gBAAI,gBAAgB,OAAO;AAC3B,gBAAI,iBAAiB,OAAO;AAM5B,gBAAI,kBAAkB,cAAc,kBAAkB,eAAe,kBAAkB,YAAY;AACjG,8BAAgB;AAChB,+BAAiB,OAAO;AAAA,YAC1B;AACA,gBAAI,QAAQ,iBAAiB,kBAAkB,QAAQ,QAAQ,aAAa,eAAe,cAAc;AACzG,gBAAI,OAAO,SAAS,MAAM,MAAM,aAAa,WAAW,eAAe,gBAAgB,EAAE,MAAM,QAAQ;AACvG,gBAAI,MAAuC;AAIzC,kBAAI,CAAC,eAAe,EAAE,SAAS,OAAO;AACpC,qBAAK,0CAA0C;AAAA,cACjD;AAAA,YACF;AACA,mBAAO,QAAQ;AACf,mBAAO,OAAO;AACd,kBAAM,iBAAiB,YAAY;AAAA,cACjC,UAAU;AAAA,cACV,aAAa;AAAA,cACb;AAAA,cACA;AAAA,YACF;AACA,kBAAM,QAAQ,SAAS,MAAM;AAAA,UAC/B;AAAA,QACF;AAMA,gBAAQ,uBAAuB;AAC/B,cAAM,IAAI,GAAG,SAAS,SAAS,KAAK;AAAA,MACtC,CAAC;AACD,WAAK,gBAAgB,SAAU,YAAY,WAAW;AACpD,cAAM,eAAe,GAAG,WAAW,SAAU,OAAO;AAClD,eAAK,QAAQ,WAAW,KAAK;AAAA,QAC/B,GAAG,KAAK;AAAA,MACV,CAAC;AAGD,WAAK,CAAC,eAAe,GAAG,SAAU,WAAW;AAC3C,cAAM,eAAe,GAAG,WAAW,SAAU,OAAO;AAClD,eAAK,QAAQ,WAAW,KAAK;AAAA,QAC/B,GAAG,KAAK;AAAA,MACV,CAAC;AACD,+BAAyB,KAAK,gBAAgB,MAAM,KAAK,IAAI;AAAA,IAC/D;AACA,IAAAA,SAAQ,UAAU,aAAa,WAAY;AACzC,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,SAAQ,UAAU,QAAQ,WAAY;AACpC,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,WAAK,UAAU;AAAA,QACb,QAAQ,CAAC;AAAA,MACX,GAAG,IAAI;AAAA,IACT;AACA,IAAAA,SAAQ,UAAU,UAAU,WAAY;AACtC,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,WAAK,YAAY;AACjB,UAAI,MAAM,KAAK,OAAO;AACtB,UAAI,KAAK;AACP,QAAU,aAAa,KAAK,OAAO,GAAG,mBAAmB,EAAE;AAAA,MAC7D;AACA,UAAI,QAAQ;AACZ,UAAI,MAAM,MAAM;AAChB,UAAI,UAAU,MAAM;AACpB,WAAK,MAAM,kBAAkB,SAAU,WAAW;AAChD,kBAAU,QAAQ,SAAS,GAAG;AAAA,MAChC,CAAC;AACD,WAAK,MAAM,cAAc,SAAUE,QAAO;AACxC,QAAAA,OAAM,QAAQ,SAAS,GAAG;AAAA,MAC5B,CAAC;AAED,YAAM,IAAI,QAAQ;AAGlB,YAAM,OAAO,MAAM,SAAS,MAAM,aAAa,MAAM,iBAAiB,MAAM,eAAe,MAAM,mBAAmB,MAAM,aAAa,MAAM,OAAO,MAAM,MAAM,MAAM,oBAAoB,MAAM,SAAS,MAAM,eAAe,MAAM,iBAAiB;AACrP,aAAO,UAAU,MAAM,EAAE;AAAA,IAC3B;AAIA,IAAAF,SAAQ,UAAU,SAAS,SAAU,MAAM;AACzC,UAAI,KAAK,mBAAmB,GAAG;AAC7B,YAAI,MAAuC;AACzC,gBAAM,oDAAoD;AAAA,QAC5D;AACA;AAAA,MACF;AACA,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,WAAK,IAAI,OAAO,IAAI;AACpB,UAAI,UAAU,KAAK;AAEnB,WAAK,cAAc,KAAK,WAAW,OAAO;AAC1C,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,UAAI,cAAc,QAAQ,YAAY,OAAO;AAC7C,UAAI,SAAS,QAAQ,KAAK;AAI1B,UAAI,KAAK,cAAc,GAAG;AACxB,YAAI,UAAU,MAAM;AAClB,mBAAS,KAAK,cAAc,EAAE;AAAA,QAChC;AACA,sBAAc;AACd,aAAK,cAAc,IAAI;AAAA,MACzB;AACA,WAAK,mBAAmB,IAAI;AAC5B,UAAI;AACF,uBAAe,QAAQ,IAAI;AAC3B,sBAAc,OAAO,KAAK,MAAM;AAAA,UAC9B,MAAM;AAAA,UACN,WAAW,OAAO;AAAA;AAAA,YAEhB,UAAU;AAAA,UACZ,GAAG,QAAQ,KAAK,SAAS;AAAA,QAC3B,CAAC;AAAA,MACH,SAAS,GAAG;AACV,aAAK,mBAAmB,IAAI;AAC5B,cAAM;AAAA,MACR;AACA,WAAK,mBAAmB,IAAI;AAC5B,0BAAoB,KAAK,MAAM,MAAM;AACrC,0BAAoB,KAAK,MAAM,MAAM;AAAA,IACvC;AACA,IAAAA,SAAQ,UAAU,cAAc,SAAU,MAAM,KAAK;AACnD,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,UAAI,SAAS,IAAI,GAAG;AAClB,cAAM;AACN,eAAO;AAAA,MACT;AACA,aAAO,QAAQ;AACf,WAAK,YAAY;AACjB,UAAI,CAAC,eAAe,IAAI,GAAG;AACzB,YAAI,MAAuC;AACzC,eAAK,qBAAqB,OAAO,cAAc;AAAA,QACjD;AACA;AAAA,MACF;AACA,UAAI,KAAK,eAAe,IAAI,EAAE,KAAK,MAAM,GAAG;AAC5C,UAAI,KAAK,KAAK;AACd,WAAK,aAAa;AAClB,SAAG,IAAI,EAAE;AAAA,IACX;AAIA,IAAAA,SAAQ,UAAU,cAAc,WAAY;AAC1C,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,WAAK,cAAc,KAAK,IAAI,OAAO,KAAK,UAAU;AAClD,WAAK,aAAa;AAAA,IACpB;AACA,IAAAA,SAAQ,UAAU,sBAAsB,SAAU,UAAU;AAC1D,UAAI,UAAU,OAAO,CAAC,GAAG,QAAQ;AACjC,cAAQ,OAAO,eAAe,SAAS,IAAI;AAC3C,aAAO;AAAA,IACT;AAUA,IAAAA,SAAQ,UAAU,iBAAiB,SAAU,SAAS,KAAK;AACzD,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,UAAI,CAAC,SAAS,GAAG,GAAG;AAClB,cAAM;AAAA,UACJ,QAAQ,CAAC,CAAC;AAAA,QACZ;AAAA,MACF;AACA,UAAI,CAAC,QAAQ,QAAQ,IAAI,GAAG;AAC1B;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,MACF;AAEA,UAAI,KAAK,mBAAmB,GAAG;AAC7B,aAAK,gBAAgB,KAAK,OAAO;AACjC;AAAA,MACF;AACA,UAAI,SAAS,IAAI;AACjB,uBAAiB,KAAK,MAAM,SAAS,MAAM;AAC3C,UAAI,QAAQ,IAAI;AAChB,UAAI,OAAO;AACT,aAAK,IAAI,MAAM;AAAA,MACjB,WAAW,UAAU,SAAS,YAAI,QAAQ,QAAQ;AAMhD,aAAK,kBAAkB;AAAA,MACzB;AACA,0BAAoB,KAAK,MAAM,MAAM;AACrC,0BAAoB,KAAK,MAAM,MAAM;AAAA,IACvC;AACA,IAAAA,SAAQ,UAAU,oBAAoB,WAAY;AAChD,wBAAU,QAAQ,uBAAuB,KAAK,QAAQ,KAAK,MAAM;AAAA;AAAA;AAAA,QAG/D,eAAe,CAAC;AAAA,MAClB,CAAC;AAAA,IACH;AACA,IAAAA,SAAQ,UAAU,aAAa,SAAU,QAAQ;AAC/C,UAAI,KAAK,WAAW;AAClB,wBAAgB,KAAK,EAAE;AACvB;AAAA,MACF;AACA,UAAI,cAAc,OAAO;AACzB,UAAI,UAAU,KAAK,SAAS;AAC5B,UAAI,cAAc,QAAQ,iBAAiB,WAAW;AACtD,UAAI,MAAuC;AACzC,eAAO,OAAO,QAAQ,WAAW;AAAA,MACnC;AACA,kBAAY,WAAW,MAAM;AAQ7B,WAAK,WAAW,aAAa;AAC7B,WAAK,MAAM,EAAE,OAAO;AAAA,IACtB;AAGA,IAAAA,SAAQ,gBAAgB,WAAY;AAClC,gBAAU,SAAU,OAAO;AACzB,YAAI,YAAY,MAAM;AACtB,kBAAU,iBAAiB,MAAM,MAAM;AACvC,kBAAU,kBAAkB;AAC5B,oBAAY,OAAO,IAAI;AACvB,oBAAY,OAAO,KAAK;AACxB,kBAAU,KAAK;AAAA,MACjB;AAIA,oBAAc,SAAU,OAAO,aAAa;AAC1C,YAAI,UAAU,MAAM;AACpB,YAAI,YAAY,MAAM;AACtB,YAAI,WAAW,cAAc,MAAM,mBAAmB,MAAM;AAC5D,YAAI,UAAU,cAAc,MAAM,iBAAiB,MAAM;AACzD,YAAI,KAAK,MAAM;AACf,YAAI,MAAM,MAAM;AAChB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,mBAAS,CAAC,EAAE,UAAU;AAAA,QACxB;AACA,sBAAc,QAAQ,cAAc,SAAU,eAAe,OAAO;AAClE,4BAAkB,YAAY,UAAU,KAAK;AAAA,QAC/C,CAAC,IAAI,QAAQ,WAAW,SAAS;AACjC,iBAAS,UAAU,OAAO;AAOxB,cAAI,iBAAiB,MAAM;AAE3B,gBAAM,mBAAmB;AAEzB,cAAI,SAAS,SAAS,MAAM,KAAK,MAAM,MAAM;AAC7C,cAAIG,QAAO,CAAC,kBAAkB,QAAQ,MAAM;AAC5C,cAAI,CAACA,OAAM;AACT,gBAAI,YAAY,eAAe,MAAM,IAAI;AACzC,gBAAI,QAAQ,cAAcC,mBAAc,SAAS,UAAU,MAAM,UAAU,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAM9E,cAAU,SAAS,UAAU,GAAG;AAAA;AAChC,gBAAI,MAAuC;AACzC,qBAAO,OAAO,UAAU,MAAM,kBAAkB;AAAA,YAClD;AACA,YAAAD,QAAO,IAAI,MAAM;AACjB,YAAAA,MAAK,KAAK,SAAS,GAAG;AACtB,oBAAQ,MAAM,IAAIA;AAClB,qBAAS,KAAKA,KAAI;AAClB,eAAG,IAAIA,MAAK,KAAK;AAAA,UACnB;AACA,gBAAM,WAAWA,MAAK,OAAO;AAC7B,UAAAA,MAAK,UAAU;AACf,UAAAA,MAAK,UAAU;AACf,UAAAA,MAAK,MAAM,oBAAoB;AAAA,YAC7B,UAAU,MAAM;AAAA,YAChB,OAAO,MAAM;AAAA,UACf;AACA,WAAC,eAAe,UAAU,YAAYA,OAAM,OAAO,SAAS,GAAG;AAAA,QACjE;AACA,iBAAS,IAAI,GAAG,IAAI,SAAS,UAAS;AACpC,cAAI,OAAO,SAAS,CAAC;AACrB,cAAI,CAAC,KAAK,SAAS;AACjB,aAAC,eAAe,KAAK,WAAW,QAAQ;AACxC,eAAG,OAAO,KAAK,KAAK;AACpB,iBAAK,QAAQ,SAAS,GAAG;AACzB,qBAAS,OAAO,GAAG,CAAC;AACpB,gBAAI,QAAQ,KAAK,IAAI,MAAM,MAAM;AAC/B,qBAAO,QAAQ,KAAK,IAAI;AAAA,YAC1B;AACA,iBAAK,OAAO,KAAK,MAAM,oBAAoB;AAAA,UAC7C,OAAO;AACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,uBAAiB,SAAU,OAAO,QAAQ,SAAS,UAAU,SAAS;AACpE,YAAI,UAAU,MAAM;AACpB,gBAAQ,iBAAiB,OAAO;AAEhC,YAAI,CAAC,UAAU;AAIb,eAAK,CAAC,EAAE,OAAO,MAAM,gBAAgB,EAAE,OAAO,MAAM,YAAY,GAAG,QAAQ;AAC3E;AAAA,QACF;AACA,YAAI,QAAQ,CAAC;AACb,cAAM,WAAW,IAAI,IAAI,QAAQ,WAAW,IAAI;AAChD,cAAM,WAAW,OAAO,IAAI,QAAQ,WAAW,OAAO;AACtD,cAAM,WAAW,MAAM,IAAI,QAAQ,WAAW,MAAM;AACpD,YAAI,YAAY;AAAA,UACd;AAAA,UACA;AAAA,QACF;AACA,oBAAY,UAAU,UAAU;AAChC,YAAI,kBAAkB,QAAQ;AAC9B,YAAI;AACJ,YAAI,mBAAmB,MAAM;AAC3B,+BAAqB,cAAc;AACnC,eAAe,iBAAiB,eAAe,GAAG,SAAU,IAAI;AAC9D,gBAAI,UAAoB,oBAAoB,IAAI,IAAI;AACpD,gBAAI,WAAW,MAAM;AACnB,iCAAmB,IAAI,SAAS,IAAI;AAAA,YACtC;AAAA,UACF,CAAC;AAAA,QACH;AAEA,mBAAW,QAAQ,cAAc,WAAW,SAAU,OAAO;AAC3D,cAAI,aAAa,sBAAsB,mBAAmB,IAAI,MAAM,EAAE,KAAK;AAC3E,cAAI,YAAY;AACd;AAAA,UACF;AACA;AACA,cAAI,kBAAkB,OAAO,GAAG;AAC9B,gBAAI,iBAAiB,gBAAa;AAChC,kBAAI,QAAQ,SAAS,yBAAyB,CAAC,QAAQ,WAAW,CAAC,MAAM,IAAI,CAAC,YAAY,UAAU,CAAC,GAAG;AACtG,+CAA+B,OAAO,SAAS,MAAM,IAAI;AAAA,cAC3D;AAAA,YACF,OAAO;AACL,kBAAI,KAAK,iCAAiC,MAAM,UAAU,MAAM,gBAAgB,QAAQ,MAAM,MAAM,IAAI,GACtG,YAAY,GAAG,WACf,cAAc,GAAG;AACnB,kBAAI,QAAQ,SAAS,yBAAyB,aAAa,CAAC,QAAQ,SAAS;AAC3E,8BAAc,MAAM,UAAU,MAAM,gBAAgB,MAAM,IAAI;AAAA,cAChE;AAKA,kBAAI,aAAa;AACf,qBAAK,aAAa,SAAU,YAAY;AACtC,0BAAQ,SAAS,wBAAwB,cAAc,UAAU,IAAI,cAAc,UAAU;AAAA,gBAC/F,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF,WAAW,sBAAsB,OAAO,GAAG;AAEzC,gBAAI,iBAAiB,gBAAa;AAChC,yCAA2B,OAAO,SAAS,MAAM,IAAI;AACrD,2CAA6B,KAAK;AAClC,iCAAmB,KAAK;AAAA,YAC1B;AAAA,UACF;AAAA,QACF,GAAG,KAAK;AACR,mBAAW,QAAQ,cAAc,WAAW,SAAU,OAAO;AAC3D,cAAI,aAAa,sBAAsB,mBAAmB,IAAI,MAAM,EAAE,KAAK;AAC3E,cAAI,YAAY;AACd;AAAA,UACF;AACA;AACA,mBAAS,MAAM,aAAa,WAAW,eAAe,gBAAgB,EAAE,MAAM,QAAQ,CAAC;AAAA,QACzF,GAAG,KAAK;AACR,iBAAS,SAAS,MAAM;AACtB,kBAAQ,KAAK,WAAW,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE,KAAK,SAAS,SAAS,MAAM,MAAM,OAAO;AAAA,QACjG;AAAA,MACF;AACA,sBAAgB;AAAA,QACd,kBAAkB,SAAU,SAAS;AACnC,kBAAQ,IAAI;AACZ,wBAAc,OAAO,KAAK,MAAM,SAAS;AAAA;AAAA;AAAA;AAAA,YAIvC,eAAe,QAAQ,aAAa;AAAA,UACtC,CAAC;AAAA,QACH;AAAA,QACA,QAAQ,SAAU,SAAS,cAAc;AACvC,cAAI,UAAU,KAAK;AACnB,cAAI,MAAM,KAAK;AACf,cAAI,KAAK,KAAK;AACd,cAAI,cAAc,KAAK;AACvB,cAAI,YAAY,KAAK;AAErB,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AACA,kBAAQ,iBAAiB,OAAO;AAChC,oBAAU,YAAY,SAAS,OAAO;AACtC,oBAAU,mBAAmB,OAAO;AAMpC,sBAAY,OAAO,SAAS,GAAG;AAC/B,oBAAU,0BAA0B,SAAS,OAAO;AAIpD,4BAAkB,MAAM,OAAO;AAK/B,sBAAY,OAAO,SAAS,GAAG;AAC/B,4BAAkB,OAAO;AACzB,oBAAU,mBAAmB,SAAS,OAAO;AAC7C,iBAAO,MAAM,SAAS,KAAK,SAAS,YAAY;AAEhD,cAAIE,mBAAkB,QAAQ,IAAI,iBAAiB,KAAK;AACxD,cAAI,WAAW,QAAQ,IAAI,UAAU;AACrC,aAAG,mBAAmBA,gBAAe;AAErC,cAAI,YAAY,QAAQ,aAAa,QAAQ;AAC3C,eAAG,YAAY,QAAQ;AAAA,UACzB;AACA,4BAAU,QAAQ,eAAe,SAAS,GAAG;AAAA,QAC/C;AAAA,QACA,iBAAiB,SAAU,SAAS;AAClC,cAAI,QAAQ;AACZ,cAAI,UAAU,KAAK;AACnB,cAAI,MAAM,KAAK;AAEf,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AACA,kBAAQ,iBAAiB,OAAO;AAEhC,cAAI,qBAAqB,CAAC;AAC1B,kBAAQ,cAAc,SAAU,eAAe,gBAAgB;AAC7D,gBAAI,kBAAkB,UAAU;AAC9B;AAAA,YACF;AACA,gBAAI,gBAAgB,MAAM,wBAAwB,cAAc;AAChE,gBAAI,iBAAiB,cAAc,SAAS;AAC1C,kBAAI,cAAc,iBAAiB;AACjC,oBAAI,SAAS,cAAc,gBAAgB,gBAAgB,SAAS,KAAK,OAAO;AAChF,0BAAU,OAAO,UAAU,mBAAmB,KAAK,aAAa;AAAA,cAClE,OAAO;AACL,mCAAmB,KAAK,aAAa;AAAA,cACvC;AAAA,YACF;AAAA,UACF,CAAC;AACD,cAAI,iBAAiB,cAAc;AACnC,kBAAQ,WAAW,SAAU,aAAa;AACxC,gBAAI,YAAY,MAAM,WAAW,YAAY,QAAQ;AACrD,gBAAI,UAAU,iBAAiB;AAC7B,kBAAI,SAAS,UAAU,gBAAgB,aAAa,SAAS,KAAK,OAAO;AACzE,wBAAU,OAAO,UAAU,eAAe,IAAI,YAAY,KAAK,CAAC;AAAA,YAClE,OAAO;AACL,6BAAe,IAAI,YAAY,KAAK,CAAC;AAAA,YACvC;AAAA,UACF,CAAC;AACD,4BAAkB,OAAO;AAGzB,eAAK,WAAW,mBAAmB,SAAS,SAAS;AAAA,YACnD,UAAU;AAAA,YACV,UAAU;AAAA,UACZ,CAAC;AAGD,uBAAa,MAAM,SAAS,KAAK,SAAS,CAAC,GAAG,cAAc;AAC5D,4BAAU,QAAQ,eAAe,SAAS,GAAG;AAAA,QAC/C;AAAA,QACA,YAAY,SAAU,SAAS;AAC7B,cAAI,UAAU,KAAK;AAEnB,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AACA,kBAAQ,iBAAiB,OAAO;AAChC,wBAAU,iBAAiB,SAAS,YAAY;AAChD,4BAAkB,OAAO;AAEzB,eAAK,WAAW,mBAAmB,SAAS,SAAS;AAAA,YACnD,UAAU;AAAA,UACZ,CAAC;AACD,iBAAO,MAAM,SAAS,KAAK,MAAM,SAAS,CAAC,CAAC;AAC5C,4BAAU,QAAQ,eAAe,SAAS,KAAK,IAAI;AAAA,QACrD;AAAA,QACA,cAAc,SAAU,SAAS;AAE/B,cAAI,QAAQ;AACZ,cAAI,UAAU,KAAK;AAEnB,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AACA,kBAAQ,iBAAiB,OAAO;AAEhC,kBAAQ,WAAW,SAAU,aAAa;AACxC,wBAAY,QAAQ,EAAE,eAAe;AAAA,UACvC,CAAC;AAED,wBAAU,iBAAiB,SAAS,cAAc;AAClD,4BAAkB,OAAO;AAEzB,eAAK,WAAW,mBAAmB,SAAS,SAAS;AAAA,YACnD,YAAY;AAAA,YACZ,UAAU;AAAA,UACZ,CAAC;AACD,kBAAQ,cAAc,SAAU,eAAe,gBAAgB;AAC7D,gBAAI,kBAAkB,UAAU;AAC9B,kBAAI,gBAAgB,MAAM,wBAAwB,cAAc;AAChE,+BAAiB,cAAc,WAAW,cAAc,aAAa,gBAAgB,SAAS,MAAM,MAAM,OAAO;AAAA,YACnH;AAAA,UACF,CAAC;AACD,kBAAQ,WAAW,SAAU,aAAa;AACxC,gBAAI,YAAY,MAAM,WAAW,YAAY,QAAQ;AACrD,sBAAU,aAAa,aAAa,SAAS,MAAM,MAAM,OAAO;AAAA,UAClE,CAAC;AACD,4BAAU,QAAQ,eAAe,SAAS,KAAK,IAAI;AAAA,QACrD;AAAA,QACA,cAAc,SAAU,SAAS;AAC/B,wBAAc,OAAO,KAAK,MAAM,OAAO;AAAA,QACzC;AAAA,MACF;AACA,uBAAiB,SAAU,OAAO,YAAY,QAAQ,OAAO;AAC3D,YAAI,MAAM,WAAW;AACnB,0BAAgB,MAAM,EAAE;AACxB;AAAA,QACF;AACA,YAAI,UAAU,MAAM;AACpB,YAAI,eAAe,MAAM,aAAa,qBAAqB;AAC3D,YAAI;AACJ,YAAI,eAAyB,YAAY,SAAS,MAAM;AACxD,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,cAAI,WAAW,aAAa,CAAC;AAC7B,cAAI,SAAS,UAAU,MAAM,SAAS,SAAS,UAAU,EAAE,SAAS,cAAc,KAAK,MAAM,MAAM;AACjG,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,MAAuC;AACzC,eAAK,wCAAwC,aAAa,6BAA6B;AAAA,QACzF;AAAA,MACF;AACA,0BAAoB,SAAU,OAAO,SAAS;AAC5C,YAAI,YAAY,MAAM;AACtB,YAAI,YAAY,MAAM;AACtB,gBAAQ,WAAW,SAAU,aAAa;AACxC,oBAAU,kBAAkB,aAAa,UAAU,YAAY,QAAQ,CAAC;AAAA,QAC1E,CAAC;AAAA,MACH;AACA,yBAAmB,SAAU,SAAS,QAAQ;AAC5C,YAAI,QAAQ;AACZ,YAAI,UAAU,KAAK,SAAS;AAC5B,YAAI,cAAc,QAAQ;AAC1B,YAAI,gBAAgB,QAAQ;AAC5B,YAAI,aAAa,QAAQ,WAAW;AACpC,YAAI,aAAa,WAAW;AAC5B,YAAI,cAAc,WAAW,UAAU,UAAU,MAAM,GAAG;AAC1D,YAAI,eAAe,WAAW,IAAI;AAClC,YAAI,UAAU,WAAW,CAAC,KAAK,QAAQ,eAAe,WAAW,CAAC,CAAC;AACnE,aAAK,mBAAmB,IAAI;AAC5B,YAAI,WAAW,CAAC,OAAO;AACvB,YAAI,UAAU;AAEd,YAAI,QAAQ,OAAO;AACjB,oBAAU;AACV,qBAAW,IAAI,QAAQ,OAAO,SAAU,MAAM;AAC5C,mBAAO,SAAS,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO;AACzC,iBAAK,QAAQ;AACb,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,YAAI,gBAAgB,CAAC;AACrB,YAAI;AACJ,YAAI,iBAAiB,sBAAsB,OAAO;AAClD,YAAI,aAAa,kBAAkB,OAAO;AAE1C,YAAI,YAAY;AACd,uBAAa,KAAK,IAAI;AAAA,QACxB;AACA,aAAK,UAAU,SAAU,WAAW;AAElC,qBAAW,WAAW,OAAO,WAAW,MAAM,QAAQ,MAAM,IAAI;AAEhE,qBAAW,YAAY,OAAO,CAAC,GAAG,SAAS;AAE3C,mBAAS,OAAO,WAAW,SAAS,SAAS;AAC7C,wBAAc,KAAK,QAAQ;AAE3B,cAAI,YAAY;AACd,gBAAI,KAAe,eAAe,OAAO,GACvC,iBAAiB,GAAG,gBACpB,oBAAoB,GAAG;AACzB,gBAAI,oBAAoB,oBAAoB,eAAe,KAAK,EAAE,CAAC,IAAI;AACvE,2BAAe,OAAO,cAAc,WAAW,iBAAiB;AAChE,+BAAmB,KAAK;AAAA,UAC1B,WAAW,gBAAgB;AAGzB,2BAAe,OAAO,cAAc,WAAW,QAAQ;AACvD,+BAAmB,KAAK;AAAA,UAC1B,WAAW,SAAS;AAClB,2BAAe,OAAO,cAAc,WAAW,QAAQ,MAAM,QAAQ,GAAG;AAAA,UAC1E;AAAA,QACF,CAAC;AACD,YAAI,iBAAiB,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC,SAAS;AACzE,cAAI;AAEF,gBAAI,KAAK,cAAc,GAAG;AACxB,sBAAQ,IAAI;AACZ,4BAAc,OAAO,KAAK,MAAM,OAAO;AACvC,mBAAK,cAAc,IAAI;AAAA,YACzB,OAAO;AACL,4BAAc,YAAY,EAAE,KAAK,MAAM,OAAO;AAAA,YAChD;AAAA,UACF,SAAS,GAAG;AACV,iBAAK,mBAAmB,IAAI;AAC5B,kBAAM;AAAA,UACR;AAAA,QACF;AAEA,YAAI,SAAS;AACX,qBAAW;AAAA,YACT,MAAM,WAAW,SAAS;AAAA,YAC1B;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,qBAAW,cAAc,CAAC;AAAA,QAC5B;AACA,aAAK,mBAAmB,IAAI;AAC5B,YAAI,CAAC,QAAQ;AACX,cAAI,gBAAgB,KAAK;AACzB,wBAAc,QAAQ,SAAS,MAAM,QAAQ;AAE7C,cAAI,gBAAgB;AAClB,gBAAI,SAAS;AAAA,cACX,MAAM;AAAA,cACN;AAAA,cACA,UAAU,sBAAsB,OAAO;AAAA,cACvC,aAAa,QAAQ,eAAe;AAAA,cACpC,YAAY,QAAQ;AAAA,cACpB,mBAAmB;AAAA,YACrB;AACA,0BAAc,QAAQ,OAAO,MAAM,MAAM;AAAA,UAC3C;AAAA,QACF;AAAA,MACF;AACA,4BAAsB,SAAU,QAAQ;AACtC,YAAI,iBAAiB,KAAK;AAC1B,eAAO,eAAe,QAAQ;AAC5B,cAAI,UAAU,eAAe,MAAM;AACnC,2BAAiB,KAAK,MAAM,SAAS,MAAM;AAAA,QAC7C;AAAA,MACF;AACA,4BAAsB,SAAU,QAAQ;AACtC,SAAC,UAAU,KAAK,QAAQ,SAAS;AAAA,MACnC;AAaA,0BAAoB,SAAU,IAAI,OAAO;AACvC,WAAG,GAAG,YAAY,SAAU,QAAQ;AAClC,gBAAM,QAAQ,YAAY,MAAM;AAKhC;AAAA;AAAA;AAAA;AAAA,YAIA,GAAG,UAAU,WAAW,KAAK,CAAC,MAAM,cAAc,KAAK,CAAC,MAAM,WAAW,cAAc,CAAC,MAAM,gBAAgB;AAAA,YAAQ;AACpH,kBAAM,QAAQ,UAAU;AAAA,UAC1B;AAAA,QACF,CAAC;AAAA,MACH;AACA,uBAAiB,SAAU,IAAI,OAAO;AACpC,WAAG,GAAG,aAAa,SAAU,GAAG;AAC9B,cAAI,KAAK,EAAE;AACX,cAAI,aAAa,oBAAoB,IAAI,oBAAoB;AAC7D,cAAI,YAAY;AACd,6CAAiC,YAAY,GAAG,MAAM,IAAI;AAC1D,+BAAmB,KAAK;AAAA,UAC1B;AAAA,QACF,CAAC,EAAE,GAAG,YAAY,SAAU,GAAG;AAC7B,cAAI,KAAK,EAAE;AACX,cAAI,aAAa,oBAAoB,IAAI,oBAAoB;AAC7D,cAAI,YAAY;AACd,4CAAgC,YAAY,GAAG,MAAM,IAAI;AACzD,+BAAmB,KAAK;AAAA,UAC1B;AAAA,QACF,CAAC,EAAE,GAAG,SAAS,SAAU,GAAG;AAC1B,cAAI,KAAK,EAAE;AACX,cAAI,aAAa,oBAAoB,IAAI,SAAU,QAAQ;AACzD,mBAAO,UAAU,MAAM,EAAE,aAAa;AAAA,UACxC,GAAG,IAAI;AACP,cAAI,YAAY;AACd,gBAAI,aAAa,WAAW,WAAW,aAAa;AACpD,gBAAI,SAAS,UAAU,UAAU;AACjC,kBAAM,KAAK,eAAe;AAAA,cACxB,MAAM;AAAA,cACN,UAAU,OAAO;AAAA,cACjB,iBAAiB,OAAO;AAAA,cACxB,aAAa,OAAO;AAAA,cACpB,aAAa;AAAA,YACf,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AACA,eAAS,kBAAkB,SAAS;AAClC,gBAAQ,kBAAkB;AAC1B,gBAAQ,WAAW,SAAU,aAAa;AACxC,sBAAY,kBAAkB;AAAA,QAChC,CAAC;AAAA,MACH;AACA;AAEA,eAAS,gBAAgB,SAAS;AAChC;AACA,YAAI,mBAAmB,CAAC;AACxB,YAAI,gBAAgB,CAAC;AACrB,YAAI,oBAAoB;AACxB,gBAAQ,cAAc,SAAU,eAAe,gBAAgB;AAC7D,cAAI,SAAS,eAAe,IAAI,QAAQ,KAAK;AAC7C,cAAI,IAAI,eAAe,IAAI,GAAG,KAAK;AACnC,cAAI,YAAY,eAAe,aAAa;AAC5C,8BAAoB,qBAAqB,CAAC,CAAC;AAC3C,WAAC,kBAAkB,WAAW,gBAAgB,kBAAkB,KAAK;AAAA,YACnE;AAAA,YACA;AAAA,YACA,KAAK,eAAe;AAAA,YACpB,MAAM;AAAA,YACN,KAAK;AAAA,UACP,CAAC;AAAA,QACH,CAAC;AACD,YAAI,mBAAmB;AAErB,cAAI,UAAU,iBAAiB,OAAO,aAAa;AACnD,cAAI;AACJ,cAAI;AACJ,eAAQ,SAAS,SAAU,GAAG,GAAG;AAC/B,gBAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,qBAAO,EAAE,IAAI,EAAE;AAAA,YACjB;AACA,mBAAO,EAAE,SAAS,EAAE;AAAA,UACtB,CAAC;AACD,eAAK,SAAS,SAAU,MAAM;AAC5B,gBAAI,iBAAiB,QAAQ,aAAa,KAAK,MAAM,KAAK,GAAG;AAC7D,gBAAI,SAAS,KAAK;AAClB,gBAAI,MAAM,KAAK;AACf,gBAAI,sBAAsB,MAAM;AAC9B,uBAAS,KAAK,IAAI,oBAAoB,MAAM;AAAA,YAC9C;AACA,gBAAI,KAAK;AACP,kBAAI,WAAW,sBAAsB,QAAQ,iBAAiB;AAC5D;AAAA,cACF;AACA,gCAAkB;AAAA,YACpB,WAAW,iBAAiB;AAC1B,kBAAI,WAAW,oBAAoB;AACjC;AAAA,cACF;AACA,gCAAkB;AAAA,YACpB;AACA,iCAAqB;AACrB,2BAAe,UAAU,MAAM;AAAA,UACjC,CAAC;AAAA,QACH;AAAA,MACF;AACA,eAAS,SAAU,OAAO,SAAS,KAAK,SAAS,cAAc;AAC7D,wBAAgB,OAAO;AACvB,yBAAiB,OAAO,SAAS,KAAK,SAAS,YAAY;AAC3D,aAAK,MAAM,cAAc,SAAU,OAAO;AACxC,gBAAM,UAAU;AAAA,QAClB,CAAC;AACD,qBAAa,OAAO,SAAS,KAAK,SAAS,YAAY;AAEvD,aAAK,MAAM,cAAc,SAAU,OAAO;AACxC,cAAI,CAAC,MAAM,SAAS;AAClB,kBAAM,OAAO,SAAS,GAAG;AAAA,UAC3B;AAAA,QACF,CAAC;AAAA,MACH;AACA,yBAAmB,SAAU,OAAO,SAAS,KAAK,SAAS,cAAc,WAAW;AAClF,aAAK,aAAa,MAAM,kBAAkB,SAAU,eAAe;AACjE,cAAI,iBAAiB,cAAc;AACnC,sBAAY,gBAAgB,aAAa;AACzC,wBAAc,OAAO,gBAAgB,SAAS,KAAK,OAAO;AAC1D,kBAAQ,gBAAgB,aAAa;AACrC,uBAAa,gBAAgB,aAAa;AAAA,QAC5C,CAAC;AAAA,MACH;AAIA,qBAAe,SAAU,OAAO,SAAS,KAAK,SAAS,cAAc,UAAU;AAE7E,YAAI,YAAY,MAAM;AACtB,uBAAe,OAAO,gBAAgB,CAAC,GAAG;AAAA,UACxC,eAAe,QAAQ,UAAU;AAAA,QACnC,CAAC;AAED,0BAAU,QAAQ,uBAAuB,SAAS,KAAK,YAAY;AACnE,YAAI,aAAa;AACjB,gBAAQ,WAAW,SAAU,aAAa;AACxC,cAAI,YAAY,MAAM,WAAW,YAAY,QAAQ;AACrD,oBAAU,UAAU;AACpB,cAAI,aAAa,UAAU;AAC3B,oBAAU,cAAc,YAAY,OAAO;AAE3C,sBAAY,aAAa,SAAS;AAClC,cAAI,YAAY,SAAS,IAAI,YAAY,GAAG,GAAG;AAC7C,uBAAW,MAAM;AAAA,UACnB;AACA,cAAI,WAAW,QAAQ,UAAU,eAAe,UAAU,CAAC,GAAG;AAC5D,yBAAa;AAAA,UACf;AACA,oBAAU,MAAM,SAAS,CAAC,CAAC,YAAY,IAAI,QAAQ;AAInD,sBAAY,aAAa,SAAS;AAClC,uCAA6B,WAAW;AAAA,QAC1C,CAAC;AACD,kBAAU,aAAa,cAAc,UAAU;AAC/C,0BAAU,QAAQ,uBAAuB,SAAS,KAAK,YAAY;AAEnE,0BAAU,QAAQ,qBAAqB,SAAS,KAAK,YAAY;AACjE,gBAAQ,WAAW,SAAU,aAAa;AACxC,cAAI,YAAY,MAAM,WAAW,YAAY,QAAQ;AAErD,kBAAQ,aAAa,SAAS;AAG9B,uBAAa,aAAa,SAAS;AAAA,QACrC,CAAC;AAED,+BAAuB,OAAO,OAAO;AACrC,0BAAU,QAAQ,sBAAsB,SAAS,KAAK,YAAY;AAAA,MACpE;AACA,2BAAqB,SAAU,OAAO;AACpC,cAAM,uBAAuB,IAAI;AAEjC,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AACA,2BAAqB,SAAU,OAAO;AACpC,YAAI,CAAC,MAAM,uBAAuB,GAAG;AACnC;AAAA,QACF;AACA,cAAM,MAAM,EAAE,QAAQ,SAAS,SAAU,IAAI;AAE3C,cAAY,iBAAiB,EAAE,GAAG;AAChC;AAAA,UACF;AACA,6BAAmB,EAAE;AAAA,QACvB,CAAC;AACD,cAAM,uBAAuB,IAAI;AAAA,MACnC;AACA,eAAS,mBAAmB,IAAI;AAC9B,YAAI,YAAY,CAAC;AACjB,YAAI,YAAY,GAAG;AAEnB,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAI,YAAY,UAAU,CAAC;AAC3B,cAAI,EAAE,cAAc,cAAc,cAAc,UAAU,cAAc,WAAW;AACjF,sBAAU,KAAK,SAAS;AAAA,UAC1B;AAAA,QACF;AAEA,YAAI,GAAG,YAAY,GAAG,OAAO,QAAQ;AACnC,oBAAU,KAAK,QAAQ;AAAA,QACzB;AACA,YAAI,GAAG,eAAe,wBAAwB,GAAG,OAAO,UAAU;AAChE,oBAAU,KAAK,UAAU;AAAA,QAC3B,WAAW,GAAG,eAAe,oBAAoB,GAAG,OAAO,MAAM;AAC/D,oBAAU,KAAK,MAAM;AAAA,QACvB;AACA,WAAG,UAAU,SAAS;AAAA,MACxB;AACA,eAAS,uBAAuB,OAAO,SAAS;AAC9C,YAAI,KAAK,MAAM;AACf,YAAI,UAAU,GAAG;AACjB,YAAI,UAAU;AACd,gBAAQ,SAAS,SAAU,IAAI;AAC7B,cAAI,CAAC,GAAG,SAAS;AACf;AAAA,UACF;AAAA,QACF,CAAC;AACD,YAAI,UAAU,QAAQ,IAAI,qBAAqB,KAAK,CAAC,YAAI,QAAQ,CAAC,YAAI,QAAQ;AAC5E,kBAAQ,WAAW,SAAU,aAAa;AACxC,gBAAI,YAAY,wBAAwB;AACtC;AAAA,YACF;AACA,gBAAI,YAAY,MAAM,WAAW,YAAY,QAAQ;AACrD,gBAAI,UAAU,SAAS;AACrB,wBAAU,aAAa,SAAU,IAAI;AACnC,oBAAI,GAAG,OAAO,UAAU;AACtB,qBAAG,OAAO,SAAS,aAAa;AAAA,gBAClC;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA;AAIA,eAAS,YAAY,aAAa,WAAW;AAC3C,YAAI,YAAY,YAAY,IAAI,WAAW,KAAK;AAChD,kBAAU,aAAa,SAAU,IAAI;AAEnC,cAAI,CAAC,GAAG,SAAS;AAEf,eAAG,MAAM,QAAQ;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AACA;AACA,eAAS,QAAQ,OAAO,MAAM;AAC5B,YAAI,MAAM,cAAc;AACtB;AAAA,QACF;AACA,YAAI,IAAI,MAAM,IAAI,GAAG,KAAK;AAC1B,YAAI,SAAS,MAAM,IAAI,QAAQ,KAAK;AAEpC,aAAK,aAAa,SAAU,IAAI;AAC9B,oBAAU,IAAI,GAAG,QAAQ,SAAS;AAElC,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA;AACA,eAAS,UAAU,IAAI,GAAG,QAAQ,OAAO;AAEvC,YAAI,QAAQ,GAAG,eAAe;AAC9B,YAAI,YAAY,GAAG,iBAAiB;AACpC,YAAI,UAAU,GAAG;AACjB,YAAI,SAAS;AAEX,cAAI,WAAW,GAAG,YAAY;AAC9B,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,oBAAQ,KAAK,IAAI,UAAU,SAAS,CAAC,GAAG,GAAG,QAAQ,KAAK,GAAG,KAAK;AAAA,UAClE;AAAA,QACF,OAAO;AAEL,aAAG,IAAI;AACP,aAAG,SAAS;AACZ,kBAAQ,KAAK,IAAI,GAAG,IAAI,KAAK;AAAA,QAC/B;AAEA,YAAI,OAAO;AACT,gBAAM,IAAI;AACV,gBAAM,SAAS;AAGf,mBAAS,KAAK,MAAM,MAAM,KAAK,QAAQ;AAAA,QACzC;AACA,YAAI,WAAW;AACb,cAAI,sBAAsB,GAAG;AAC7B,oBAAU,IAAI;AACd,oBAAU,SAAS;AACnB,mBAAS,KAAK,MAAM,UAAU,KAAK,SAAS,uBAAuB,oBAAoB,YAAY,IAAI;AAAA,QACzG;AACA,eAAO;AAAA,MACT;AAGA,eAAS,YAAY,OAAO,MAAM;AAChC,aAAK,aAAa,SAAU,IAAI;AAE9B,cAAY,iBAAiB,EAAE,GAAG;AAChC;AAAA,UACF;AACA,cAAI,cAAc,GAAG,eAAe;AACpC,cAAI,YAAY,GAAG,iBAAiB;AACpC,cAAI,GAAG,iBAAiB;AACtB,eAAG,kBAAkB;AAAA,UACvB;AACA,cAAI,eAAe,YAAY,iBAAiB;AAC9C,wBAAY,kBAAkB;AAAA,UAChC;AACA,cAAI,aAAa,UAAU,iBAAiB;AAC1C,sBAAU,kBAAkB;AAAA,UAC9B;AAEA,cAAI,GAAG,SAAS,GAAG;AACjB,eAAG,aAAa,GAAG;AACnB,eAAG,YAAY;AAAA,UACjB,WAAW,GAAG,YAAY;AACxB,eAAG,aAAa;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,MACH;AACA,eAAS,aAAa,OAAO,MAAM;AACjC,YAAI,sBAAsB,MAAM,SAAS,gBAAgB;AACzD,YAAI,kBAAkB,MAAM,mBAAmB;AAC/C,YAAI,WAAW,oBAAoB,IAAI,UAAU;AACjD,YAAI,kBAAkB,WAAW,IAAI;AAAA,UACnC;AAAA,UACA,OAAO,oBAAoB,IAAI,OAAO;AAAA,UACtC,QAAQ,oBAAoB,IAAI,QAAQ;AAAA;AAAA,QAE1C,IAAI;AACJ,aAAK,aAAa,SAAU,IAAI;AAC9B,cAAI,GAAG,UAAU,GAAG,OAAO,UAAU;AAEnC,gBAAY,iBAAiB,EAAE,GAAG;AAChC;AAAA,YACF;AACA,gBAAI,cAAsB,cAAM;AAC9B,6BAAe,EAAE;AAAA,YACnB;AAGA,gBAAI,GAAG,SAAS;AACd,kBAAI,aAAa,GAAG;AAEpB,kBAAI,YAAY;AACd,mBAAG,UAAU,UAAU;AAAA,cACzB;AAAA,YACF;AAEA,gBAAI,iBAAiB;AACnB,iBAAG,kBAAkB;AACrB,kBAAI,cAAc,GAAG,eAAe;AACpC,kBAAI,YAAY,GAAG,iBAAiB;AAEpC,kBAAI,aAAa;AACf,4BAAY,kBAAkB;AAAA,cAChC;AACA,kBAAI,WAAW;AACb,0BAAU,kBAAkB;AAAA,cAC9B;AAAA,YACF;AAEA,gBAAI,GAAG,SAAS;AACd,iCAAmB,EAAE;AAAA,YACvB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AACA;AACA,2BAAqB,SAAU,OAAO;AACpC,eAAO;AAAA,SAAmB,SAAUC,SAAQ;AAC1C,oBAAU,SAASA,OAAM;AACzB,mBAAS,UAAU;AACjB,mBAAOA,YAAW,QAAQA,QAAO,MAAM,MAAM,SAAS,KAAK;AAAA,UAC7D;AACA,kBAAQ,UAAU,uBAAuB,WAAY;AACnD,mBAAO,MAAM,aAAa,qBAAqB;AAAA,UACjD;AACA,kBAAQ,UAAU,wBAAwB,SAAU,IAAI;AACtD,mBAAO,IAAI;AACT,kBAAI,YAAY,GAAG;AACnB,kBAAI,aAAa,MAAM;AACrB,uBAAO,MAAM,OAAO,aAAa,UAAU,UAAU,UAAU,KAAK;AAAA,cACtE;AACA,mBAAK,GAAG;AAAA,YACV;AAAA,UACF;AACA,kBAAQ,UAAU,gBAAgB,SAAU,IAAI,gBAAgB;AAC9D,0BAAc,IAAI,cAAc;AAChC,+BAAmB,KAAK;AAAA,UAC1B;AACA,kBAAQ,UAAU,gBAAgB,SAAU,IAAI,gBAAgB;AAC9D,0BAAc,IAAI,cAAc;AAChC,+BAAmB,KAAK;AAAA,UAC1B;AACA,kBAAQ,UAAU,YAAY,SAAU,IAAI;AAC1C,sBAAU,EAAE;AACZ,+BAAmB,KAAK;AAAA,UAC1B;AACA,kBAAQ,UAAU,YAAY,SAAU,IAAI;AAC1C,sBAAU,EAAE;AACZ,+BAAmB,KAAK;AAAA,UAC1B;AACA,kBAAQ,UAAU,cAAc,SAAU,IAAI;AAC5C,wBAAY,EAAE;AACd,+BAAmB,KAAK;AAAA,UAC1B;AACA,kBAAQ,UAAU,cAAc,SAAU,IAAI;AAC5C,wBAAY,EAAE;AACd,+BAAmB,KAAK;AAAA,UAC1B;AACA,kBAAQ,UAAU,WAAW,WAAY;AACvC,mBAAO,MAAM,SAAS;AAAA,UACxB;AACA,kBAAQ,UAAU,0BAA0B,SAAU,gBAAgB;AACpE,mBAAO,MAAM,wBAAwB,cAAc;AAAA,UACrD;AACA,kBAAQ,UAAU,uBAAuB,SAAU,aAAa;AAC9D,mBAAO,MAAM,qBAAqB,WAAW;AAAA,UAC/C;AACA,iBAAO;AAAA,QACT,EAAE,oBAAY,GAAG,KAAK;AAAA,MACxB;AACA,sBAAgB,SAAU,OAAO;AAC/B,iBAAS,4BAA4B,QAAQ,QAAQ;AACnD,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAI,aAAa,OAAO,CAAC;AACzB,uBAAW,kBAAkB,IAAI;AAAA,UACnC;AAAA,QACF;AACA,aAAK,gBAAgB,SAAU,YAAY,WAAW;AACpD,gBAAM,eAAe,GAAG,WAAW,SAAU,OAAO;AAClD,gBAAI,gBAAgB,MAAM,KAAK,KAAK,MAAM,kBAAkB,MAAM,wBAAwB;AACxF,kBAAI,SAAS,MAAM,eAAe;AAChC;AAAA,cACF;AACA,kBAAI,WAAW,MAAM,oBAAoB,KAAK;AAC9C,kBAAI,gBAAgB,CAAC;AACrB,mBAAK,WAAW,SAAU,YAAY;AACpC,oBAAI,eAAe,SAAS,WAAW,UAAU,MAAM,OAAO;AAC5D,gCAAc,KAAK,UAAU;AAAA,gBAC/B;AAAA,cACF,CAAC;AACD,0CAA4B,eAAe,sBAAsB;AACjE,mBAAK,eAAe,SAAU,YAAY;AACxC,oBAAI,WAAW,kBAAkB,MAAM,yBAAyB;AAC9D,6BAAW,eAAe,QAAQ;AAAA,gBACpC;AAAA,cACF,CAAC;AACD,0CAA4B,eAAe,sBAAsB;AAAA,YACnE;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,EAAE;AACF,WAAON;AAAA,EACT,EAAE,gBAAQ;AAAA;AACV,IAAI,eAAe,QAAQ;AAC3B,aAAa,KAAK,wCAAwC,IAAI;AAC9D,aAAa,MAAM,wCAAwC,KAAK;AAKhE,aAAa,MAAM,SAAU,WAAW,IAAI,KAAK;AAC/C,MAAI,OAAO;AACX,eAAa,4BAA4B;AACzC,WAAS,UAAU;AACjB,QAAI,QAAQ,CAAC;AACb,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,YAAM,EAAE,IAAI,UAAU,EAAE;AAAA,IAC1B;AACA,UAAM,GAAG,SAAS,GAAG,MAAM,MAAM,KAAK;AAEtC,SAAK,IAAI,WAAW,OAAO;AAAA,EAC7B;AACA;AAEA,OAAK,GAAG,KAAK,MAAM,WAAW,SAAS,GAAG;AAC5C;AACA,IAAI,oBAAoB,CAAC,SAAS,YAAY,aAAa,YAAY,aAAa,aAAa,WAAW,aAAa,aAAa;AACtI,SAAS,gBAAgB,IAAI;AAC3B,MAAI,MAAuC;AACzC,SAAK,cAAc,KAAK,oBAAoB;AAAA,EAC9C;AACF;AACA,IAAI,UAAU,CAAC;AAIf,IAAI,iBAAiB,CAAC;AACtB,IAAI,qBAAqB,CAAC;AAC1B,IAAI,0BAA0B,CAAC;AAC/B,IAAI,cAAc,CAAC;AACnB,IAAI,eAAe,CAAC;AACpB,IAAI,iBAAiB,CAAC;AACtB,IAAI,YAAY,CAAC;AACjB,IAAI,kBAAkB,CAAC;AACvB,IAAI,SAAS,CAAC,oBAAI,KAAK,IAAI;AAC3B,IAAI,cAAc,CAAC,oBAAI,KAAK,IAAI;AAChC,IAAI,oBAAoB;AAWjB,SAASO,MAAK,KAAKN,QAAO,MAAM;AACrC,MAAI,WAAW,EAAE,QAAQ,KAAK;AAC9B,MAAI,UAAU;AACZ,QAAI,MAAuC;AACzC,UAAI,CAAC,KAAK;AACR,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AAAA,IACF;AACA,QAAI,gBAAgB,iBAAiB,GAAG;AACxC,QAAI,eAAe;AACjB,UAAI,MAAuC;AACzC,aAAK,2DAA2D;AAAA,MAClE;AACA,aAAO;AAAA,IACT;AACA,QAAI,MAAuC;AACzC,UAAI,MAAM,GAAG,KAAK,IAAI,SAAS,YAAY,MAAM,aAAa,CAAC,IAAI,gBAAgB,CAAC,QAAQ,KAAK,SAAS,SAAS,CAAC,IAAI,iBAAiB,CAAC,QAAQ,KAAK,UAAU,QAAQ;AACvK,aAAK,iLAAiM;AAAA,MACxM;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,IAAI,QAAQ,KAAKA,QAAO,IAAI;AACxC,QAAM,KAAK,QAAQ;AACnB,YAAU,MAAM,EAAE,IAAI;AACtB,cAAsB,aAAa,KAAK,mBAAmB,MAAM,EAAE;AACnE,gBAAc,KAAK;AACnB,oBAAU,QAAQ,aAAa,KAAK;AACpC,SAAO;AACT;AAkBO,SAAS,QAAQ,SAAS;AAE/B,MAAI,QAAQ,OAAO,GAAG;AACpB,QAAI,SAAS;AACb,cAAU;AAEV,SAAK,QAAQ,SAAU,OAAO;AAC5B,UAAI,MAAM,SAAS,MAAM;AACvB,kBAAU,MAAM;AAAA,MAClB;AAAA,IACF,CAAC;AACD,cAAU,WAAW,OAAO;AAC5B,SAAK,QAAQ,SAAU,OAAO;AAC5B,YAAM,QAAQ;AAAA,IAChB,CAAC;AAAA,EACH;AACA,kBAAgB,OAAO,IAAI;AAC3B,SAAO;AACT;AACO,SAAS,WAAW,SAAS;AAClC,kBAAgB,OAAO,IAAI;AAC7B;AAKO,IAAI,aAAa;AAIjB,SAAS,QAAQ,OAAO;AAC7B,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,UAAU,KAAK;AAAA,EACzB,WAAW,EAAE,iBAAiB,UAAU;AAEtC,YAAQ,iBAAiB,KAAK;AAAA,EAChC;AACA,MAAI,iBAAiB,WAAW,CAAC,MAAM,WAAW,GAAG;AACnD,UAAM,QAAQ;AAAA,EAChB;AACF;AACO,SAAS,iBAAiB,KAAK;AACpC,SAAO,UAAoB,aAAa,KAAK,iBAAiB,CAAC;AACjE;AACO,SAAS,gBAAgB,KAAK;AACnC,SAAO,UAAU,GAAG;AACtB;AAIO,SAAS,cAAc,MAAMA,QAAO;AACzC,eAAa,IAAI,IAAIA;AACvB;AAIO,SAAS,qBAAqB,kBAAkB;AACrD,MAAI,QAAQ,yBAAyB,gBAAgB,IAAI,GAAG;AAC1D,4BAAwB,KAAK,gBAAgB;AAAA,EAC/C;AACF;AACO,SAAS,kBAAkB,UAAU,WAAW;AACrD,oBAAkB,oBAAoB,UAAU,WAAW,0BAA0B;AACvF;AAKO,SAAS,iBAAiB,cAAc;AAC7C,0BAAwB,aAAa,YAAY;AACnD;AAKO,SAAS,mBAAmB,gBAAgB;AACjD,0BAAwB,eAAe,cAAc;AACvD;AACO,SAAS,wBAAwB,MAAM,IAAI;AAChD,oBAAU,GAAG,MAAM,EAAE;AACvB;AACO,SAAS,eAAe,YAAY,WAAW,QAAQ;AAC5D,MAAI,WAAW,SAAS,GAAG;AACzB,aAAS;AACT,gBAAY;AAAA,EACd;AACA,MAAI,aAAa,SAAS,UAAU,IAAI,WAAW,OAAO,CAAC,YAAY,aAAa;AAAA,IAClF,OAAO;AAAA,EACT,CAAC,EAAE,CAAC;AAEJ,aAAW,SAAS,WAAW,SAAS,YAAY,YAAY;AAChE,cAAY,WAAW;AACvB,MAAI,eAAe,SAAS,GAAG;AAE7B;AAAA,EACF;AAEA,SAAO,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,SAAS,CAAC;AAChE,MAAI,CAAC,QAAQ,UAAU,GAAG;AACxB,YAAQ,UAAU,IAAI;AAAA,MACpB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,iBAAe,SAAS,IAAI;AAC9B;AACO,SAAS,yBAAyB,MAAM,iBAAiB;AAC9D,2BAAwB,SAAS,MAAM,eAAe;AACxD;AAMO,SAAS,8BAA8B,MAAM;AAClD,MAAI,kBAAkB,yBAAwB,IAAI,IAAI;AACtD,MAAI,iBAAiB;AACnB,WAAO,gBAAgB,oBAAoB,gBAAgB,kBAAkB,IAAI,gBAAgB,WAAW,MAAM;AAAA,EACpH;AACF;AAEA,SAAS,eAAe,UAAU,YAAY;AAC5C,oBAAkB,aAAa,UAAU,YAAY,wBAAwB,QAAQ;AACvF;AACA,SAAS,eAAe,UAAU,YAAY;AAC5C,oBAAkB,aAAa,UAAU,YAAY,uBAAuB,QAAQ;AACtF;AAEA,IAAI,kBAAkB,CAAC;AACvB,SAAS,kBAAkB,YAAY,UAAU,IAAI,iBAAiB,YAAY;AAChF,MAAI,WAAW,QAAQ,KAAK,SAAS,QAAQ,GAAG;AAC9C,SAAK;AACL,eAAW;AAAA,EACb;AACA,MAAI,MAAuC;AACzC,QAAI,MAAM,QAAQ,KAAK,YAAY,MAAM;AACvC,YAAM,IAAI,MAAM,kBAAkB;AAAA,IACpC;AAEA,SAAK,YAAY,SAAU,MAAM;AAC/B,aAAO,KAAK,UAAU,EAAE;AAAA,IAC1B,CAAC;AAAA,EACH;AAEA,MAAI,QAAQ,iBAAiB,EAAE,KAAK,GAAG;AACrC;AAAA,EACF;AACA,kBAAgB,KAAK,EAAE;AACvB,MAAI,eAAe,kBAAU,iBAAiB,IAAI,UAAU;AAC5D,eAAa,SAAS;AACtB,eAAa,QAAQ;AACrB,aAAW,KAAK,YAAY;AAC9B;AACO,SAAS,gBAAgB,MAAM,WAAW;AAC/C,iBAAe,IAAI,IAAI;AACzB;AAiBO,SAAS,iBAAiB,SAAS;AACxC,MAAI,MAAuC;AACzC,iBAAa,+EAA+E;AAAA,EAC9F;AACA,iBAAe;AAAA,IACb,cAAc;AAAA,EAChB,CAAC;AACH;AAKO,SAAS,YAAY,SAAS,SAAS,cAAc;AAC1D,MAAIO,eAAc,QAAQ,aAAa;AACvC,EAAAA,gBAAeA,aAAY,SAAS,SAAS,YAAY;AAC3D;AACO,SAAS,OAAO,SAAS;AAC9B,MAAIC,UAAS,QAAQ,QAAQ;AAC7B,SAAOA,WAAUA,QAAO,OAAO;AACjC;AACO,IAAI,oBAAoB;AAe/B,eAAe,wBAAwB,eAAe;AACtD,eAAe,mCAAmC,aAAa;AAC/D,eAAe,mCAAmC,oBAAoB;AACtE,eAAe,wBAAwB,gBAAgB;AACvD,eAAe,mCAAmC,cAAc;AAChE,eAAe,uBAAuB,WAAK;AAC3C,qBAAqB,oBAAc;AACnC,kBAAkB,8BAA8B,SAAS;AACzD,gBAAgB,WAAW,cAAc;AAEzC,eAAe;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV,GAAG,IAAI;AACP,eAAe;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV,GAAG,IAAI;AACP,eAAe;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV,GAAG,IAAI;AACP,eAAe;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV,GAAG,IAAI;AACP,eAAe;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV,GAAG,IAAI;AAEP,cAAc,SAAS,aAAU;AACjC,cAAc,QAAQ,YAAS;AAGxB,IAAI,WAAW,CAAC;;;ACnrEvB,IAAI;AAAA;AAAA,EAAkC,WAAY;AAChD,aAASC,oBAAmB,QAAQ,YAAY;AAC9C,WAAK,UAAU;AACf,WAAK,UAAU;AAAA,IACjB;AACA,IAAAA,oBAAmB,UAAU,MAAM,WAAY;AAC7C,aAAO;AAAA;AAAA,QAEL,gBAAgB,KAAK,uBAAuB;AAAA,QAC5C,QAAQ,KAAK;AAAA,MACf;AAAA,IACF;AAQA,IAAAA,oBAAmB,UAAU,yBAAyB,WAAY;AAChE,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,kBAAkB,KAAK,UAAU,KAAK,QAAQ,yBAAyB,IAAI,CAAC;AAAA,MACnF;AACA,aAAO,KAAK;AAAA,IACd;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEK,SAAS,oBAAoB,MAAM,QAAQ;AAChD,MAAI,UAAU,CAAC;AACf,MAAI,SAAS,QAAQ,SAAS,CAAC;AAC/B,MAAI,sBAAsB,cAAc;AACxC,MAAI,iBAAiB,CAAC;AACtB,MAAI,mBAAmB,CAAC;AACxB,MAAI,mBAAmB,CAAC;AACxB,OAAK,KAAK,YAAY,SAAU,SAAS;AACvC,QAAI,UAAU,KAAK,iBAAiB,OAAO;AAC3C,QAAI,WAAW,QAAQ;AACvB,QAAI,UAAU;AACZ,UAAI,MAAuC;AACzC,eAAO,kBAAkB,IAAI,QAAQ,KAAK,IAAI;AAAA,MAChD;AACA,UAAI,gBAAgB,QAAQ;AAC5B,2BAAqB,QAAQ,QAAQ,EAAE,aAAa,IAAI;AACxD,UAAI,CAAC,QAAQ,cAAc;AACzB,4BAAoB,IAAI,UAAU,CAAC;AAKnC,YAAI,gBAAgB,QAAQ,IAAI,GAAG;AACjC,yBAAe,CAAC,IAAI;AAAA,QACtB;AAGA,6BAAqB,kBAAkB,QAAQ,EAAE,aAAa,IAAI,KAAK,kBAAkB,QAAQ,IAAI;AAAA,MACvG;AACA,UAAI,QAAQ,gBAAgB;AAC1B,yBAAiB,KAAK,OAAO;AAAA,MAC/B;AAAA,IACF;AACA,sBAAkB,KAAK,SAAU,GAAG,UAAU;AAC5C,UAAI,YAAY,qBAAqB,QAAQ,QAAQ;AACrD,UAAI,WAAW,QAAQ,UAAU,QAAQ;AACzC,UAAI,YAAY,QAAQ,aAAa,OAAO;AAC1C,kBAAU,QAAQ,IAAI,QAAQ;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,kBAAkB,CAAC;AACvB,MAAI,yBAAyB,CAAC;AAC9B,sBAAoB,KAAK,SAAU,GAAG,UAAU;AAC9C,QAAI,SAAS,OAAO,QAAQ;AAC5B,2BAAuB,QAAQ,IAAI,OAAO,CAAC;AAG3C,sBAAkB,gBAAgB,OAAO,MAAM;AAAA,EACjD,CAAC;AACD,UAAQ,kBAAkB;AAC1B,UAAQ,wBAAwB,IAAI,iBAAiB,SAAU,SAAS;AACtE,WAAO,KAAK,iBAAiB,OAAO,EAAE;AAAA,EACxC,CAAC;AACD,UAAQ,yBAAyB;AACjC,MAAI,cAAc,OAAO;AAGzB,MAAI,eAAe,YAAY,QAAQ;AACrC,qBAAiB,YAAY,MAAM;AAAA,EACrC;AACA,MAAI,gBAAgB,OAAO;AAC3B,MAAI,iBAAiB,cAAc,QAAQ;AACzC,uBAAmB,cAAc,MAAM;AAAA,EACzC,WAAW,CAAC,iBAAiB,QAAQ;AACnC,uBAAmB,eAAe,MAAM;AAAA,EAC1C;AACA,SAAO,iBAAiB;AACxB,SAAO,mBAAmB;AAC1B,UAAQ,aAAa,IAAI,mBAAmB,kBAAkB,MAAM;AACpE,SAAO;AACT;AACA,SAAS,qBAAqB,QAAQ,KAAK;AACzC,MAAI,CAAC,OAAO,eAAe,GAAG,GAAG;AAC/B,WAAO,GAAG,IAAI,CAAC;AAAA,EACjB;AACA,SAAO,OAAO,GAAG;AACnB;AAEO,SAAS,uBAAuB,UAAU;AAC/C,SAAO,aAAa,aAAa,YAAY,aAAa,SAAS,SAAS;AAC9E;AACA,SAAS,gBAAgB,SAAS;AAGhC,SAAO,EAAE,YAAY,aAAa,YAAY;AAChD;;;ACnHA,IAAI;AAAA;AAAA,EAAqC,2BAAY;AAInD,aAASC,uBAAsB,KAAK;AAkClC,WAAK,YAAY,CAAC;AAClB,UAAI,OAAO,MAAM;AACf,QAAO,OAAO,MAAM,GAAG;AAAA,MACzB;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEF,IAAO,gCAAQ;;;AC5Cf,IAAIC,SAAQ,UAAU;AACtB,IAAI,eAAe;AAAA,EACjB,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,MAAM;AACR;AAYA,IAAI;AAAA;AAAA,EAAgC,WAAY;AAC9C,aAASC,kBAAiB,KAAK;AAC7B,WAAK,aAAa,IAAI;AACtB,WAAK,cAAc,IAAI;AACvB,WAAK,SAAS,IAAI;AAClB,WAAK,gBAAgB,IAAI;AACzB,WAAK,kBAAkB,IAAI,gBAAgB;AAAA,IAC7C;AACA,IAAAA,kBAAiB,UAAU,qBAAqB,WAAY;AAC1D,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,kBAAiB,UAAU,oBAAoB,SAAU,kBAAkB;AACzE,WAAK,cAAc;AACnB,UAAI,CAAC,kBAAkB;AACrB;AAAA,MACF;AACA,UAAI,CAAC,KAAK,aAAa;AACrB,aAAK,cAAc,uBAAuB,KAAK,MAAM;AAAA,MACvD;AAAA,IACF;AAQA,IAAAA,kBAAiB,UAAU,0BAA0B,SAAU,SAAS;AACtE,aAAO,UAAU,KAAK,YAAY,IAAI,OAAO,GAAG,EAAE;AAAA,IACpD;AAMA,IAAAA,kBAAiB,UAAU,qBAAqB,SAAU,UAAU;AAClE,UAAI,mBAAmB,KAAK,OAAO;AACnC,UAAI,kBAAkB;AACpB,eAAO,iBAAiB,QAAQ;AAAA,MAClC;AAAA,IACF;AACA,IAAAA,kBAAiB,UAAU,kBAAkB,WAAY;AACvD,UAAI,WAAW,KAAK;AACpB,UAAI,yBAAyB,yBAAyB,KAAK,MAAM;AACjE,UAAI,iBAAiB,CAAC,2BAA2B,QAAQ;AAGzD,UAAI,UAAU;AACd,UAAI,OAAO,CAAC;AACZ,eAAS,aAAa,GAAG,eAAe,GAAG,aAAa,UAAU,cAAc;AAC9E,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,cAAc;AAClB,YAAI,eAAe,KAAK,WAAW,YAAY;AAE/C,YAAI,gBAAgB,aAAa,kBAAkB,YAAY;AAC7D,qBAAW,yBAAyB,aAAa,OAAO;AACxD,iBAAO,aAAa;AACpB,wBAAc,aAAa;AAC3B;AAAA,QACF,OAAO;AACL,cAAI,eAAe,KAAK,mBAAmB,UAAU;AACrD,cAAI,cAAc;AAChB,uBAAW,yBAAyB,aAAa,OAAO;AACxD,mBAAO,aAAa;AAAA,UACtB;AAAA,QACF;AACA,aAAK,KAAK;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAOD,YAAI,0BAA0B,YAAY,SAGtC,CAAC,gBAAgB,CAAC,aAAa,qBAAqB;AACtD,qBAAW,iBAET,SAAS,QAAQ,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI,IAEjD;AAAA,QACJ;AACA,mBAAW;AACX,mBAAW,aAAa,IAAI,KAAK;AACjC,YAAI,aAAa;AACf,qBAAW,YAAY;AAAA,QACzB;AACA,mBAAW;AAAA,MACb;AAIA,UAAI,SAAS,KAAK;AAClB,UAAI,OAAO,CAAC,OAAO,gBAAgB,OAAO,YAAY,OAAO,EAAE,KAAK,IAAI;AACxE,aAAO;AAAA,QACL,YAAY;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AACA,IAAAA,kBAAiB,UAAU,2BAA2B,WAAY;AAChE,UAAI,SAAS,CAAC;AACd,eAAS,aAAa,GAAG,eAAe,GAAG,aAAa,KAAK,eAAe,cAAc;AACxF,YAAI,SAAS;AACb,YAAI,eAAe,KAAK,WAAW,YAAY;AAE/C,YAAI,gBAAgB,aAAa,kBAAkB,YAAY;AAC7D,cAAI,CAAC,aAAa,oBAAoB;AACpC,qBAAS,aAAa;AAAA,UACxB;AACA;AAAA,QACF,OAAO;AACL,cAAI,eAAe,KAAK,mBAAmB,UAAU;AACrD,cAAI,cAAc;AAChB,qBAAS,aAAa;AAAA,UACxB;AAAA,QACF;AACA,eAAO,KAAK,MAAM;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AACA,IAAAA,kBAAiB,UAAU,6BAA6B,SAAU,QAAQ;AACxE,WAAK,WAAW,KAAK,MAAM;AAC3B,aAAO,qBAAqB;AAC5B,WAAK;AAIL,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEK,SAAS,mBAAmB,QAAQ;AACzC,SAAO,kBAAkB;AAC3B;AACO,SAAS,iBAAiB,SAAS;AACxC,MAAI,iBAAiB,cAAc;AACnC,WAAS,IAAI,GAAG,KAAK,WAAW,CAAC,GAAG,QAAQ,KAAK;AAC/C,QAAI,gBAAgB,QAAQ,CAAC;AAC7B,QAAI,cAAc,SAAS,aAAa,IAAI,cAAc,OAAO;AACjE,QAAI,eAAe,QAAQ,eAAe,IAAI,WAAW,KAAK,MAAM;AAClE,qBAAe,IAAI,aAAa,CAAC;AAAA,IACnC;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,uBAAuB,QAAQ;AAC7C,MAAI,cAAcC,OAAM,MAAM;AAC9B,SAAO,YAAY,eAAe,YAAY,aAAa,iBAAiB,OAAO,gBAAgB;AACrG;AACO,SAAS,2BAA2B,UAAU;AACnD,SAAO,WAAW;AACpB;;;ACvKA,IAAIC,YAAkB;AACtB,IAAIC,OAAa;AACjB,IAAI,iBAAiB,OAAO,eAAe,cAAc,QAAQ;AAGjE,IAAI,YAAY;AAChB,IAAI,kBAAkB;AAEtB,IAAI,0BAA0B,CAAC,iBAAiB,aAAa,WAAW,uBAAuB,eAAe,cAAc,YAAY,mBAAmB,eAAe,aAAa,kBAAkB;AACzM,IAAI,mBAAmB,CAAC,oBAAoB;AAI5C,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAAA;AAAA,EAA0B,WAAY;AAMxC,aAASC,YAAW,iBAAiB,WAAW;AAC9C,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,YAAY,CAAC;AAClB,WAAK,UAAU,CAAC;AAKhB,WAAK,UAAU,CAAC;AAEhB,WAAK,UAAU,CAAC;AAEhB,WAAK,eAAe,CAAC;AAErB,WAAK,eAAe,CAAC;AAErB,WAAK,cAAc,CAAC;AAEpB,WAAK,qBAAqB,CAAC;AAC3B,WAAK,mBAAmB,CAAC;AAKzB,WAAK,gBAAgB;AAGrB,WAAK,uBAAuB,CAAC,gBAAgB,cAAc,kBAAkB,KAAK;AAElF,WAAK,oBAAoB,CAAC,cAAc,aAAa;AACrD,WAAK,qBAAqB,CAAC,cAAc,gBAAgB;AACzD,UAAI;AACJ,UAAI,oBAAoB;AACxB,UAAI,mBAAmB,eAAe,GAAG;AACvC,qBAAa,gBAAgB;AAC7B,aAAK,cAAc,gBAAgB,mBAAmB;AACtD,aAAK,UAAU;AAAA,MACjB,OAAO;AACL,4BAAoB;AACpB,qBAAa;AAAA,MACf;AACA,mBAAa,cAAc,CAAC,KAAK,GAAG;AACpC,UAAI,iBAAiB,CAAC;AACtB,UAAI,iBAAiB,CAAC;AACtB,UAAI,qBAAqB,CAAC;AAC1B,UAAI,cAAc;AAClB,UAAI,WAAW,CAAC;AAChB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAE1C,YAAI,eAAe,WAAW,CAAC;AAC/B,YAAI,gBAAuB,SAAS,YAAY,IAAI,IAAI,8BAAsB;AAAA,UAC5E,MAAM;AAAA,QACR,CAAC,IAAI,EAAE,wBAAwB,iCAAyB,IAAI,8BAAsB,YAAY,IAAI;AAClG,YAAI,gBAAgB,cAAc;AAClC,sBAAc,OAAO,cAAc,QAAQ;AAC3C,YAAI,CAAC,cAAc,UAAU;AAC3B,wBAAc,WAAW;AACzB,wBAAc,gBAAgB;AAAA,QAChC;AACA,YAAI,YAAY,cAAc,YAAY,cAAc,aAAa,CAAC;AACtE,uBAAe,KAAK,aAAa;AACjC,uBAAe,aAAa,IAAI;AAChC,YAAI,SAAS,aAAa,KAAK,MAAM;AACnC,wBAAc;AAAA,QAChB;AACA,YAAI,cAAc,uBAAuB;AACvC,6BAAmB,aAAa,IAAI,CAAC;AAAA,QACvC;AACA,YAAI,UAAU,aAAa,GAAG;AAC5B,eAAK,cAAc;AAAA,QACrB;AACA,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,YAAY;AAAA,QACnB;AACA,YAAI,MAAuC;AACzC,UAAO,OAAO,qBAAqB,cAAc,iBAAiB,CAAC;AAAA,QACrE;AACA,YAAI,mBAAmB;AACrB,wBAAc,gBAAgB;AAAA,QAChC;AAAA,MACF;AACA,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,sBAAsB,WAAW;AACtC,WAAK,YAAY;AACjB,WAAK,sBAAsB;AAC3B,UAAI,KAAK,aAAa;AACpB,YAAI,iBAAiB,KAAK,gBAAuB,cAAc;AAC/D,QAAO,KAAK,gBAAgB,SAAU,SAAS;AAC7C,yBAAe,IAAI,eAAe,OAAO,EAAE,eAAe,OAAO;AAAA,QACnE,CAAC;AAAA,MACH;AAAA,IACF;AAkBA,IAAAA,YAAW,UAAU,eAAe,SAAU,KAAK;AACjD,UAAI,SAAS,KAAK,mBAAmB,GAAG;AACxC,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AACA,eAAS;AACT,UAAI,CAAC,KAAK,aAAa;AACrB,eAAO,KAAK,WAAW,MAAM;AAAA,MAC/B;AAGA,UAAI,UAAU,KAAK,cAAc,IAAI,MAAM;AAC3C,UAAI,WAAW,MAAM;AACnB,eAAO;AAAA,MACT;AACA,UAAI,eAAe,KAAK,QAAQ,mBAAmB,MAAM;AACzD,UAAI,cAAc;AAChB,eAAO,aAAa;AAAA,MACtB;AAAA,IACF;AAKA,IAAAA,YAAW,UAAU,oBAAoB,SAAU,KAAK;AACtD,UAAI,SAAS,KAAK,mBAAmB,GAAG;AACxC,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,MAAM;AACf,eAAO;AAAA,MACT;AACA,UAAI,UAAU,KAAK,YAAY,GAAG;AAClC,aAAO,UAAU,QAAQ,gBAAgB,KAAK,cAAc,KAAK,QAAQ,wBAAwB,GAAG,IAAI;AAAA,IAC1G;AAoBA,IAAAA,YAAW,UAAU,qBAAqB,SAAU,KAAK;AACvD,UAAW,SAAS,GAAG,KAEpB,OAAO,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,YAAY,GAAG,MAAM,CAAC,KAAK,eAAe,KAAK,QAAQ,wBAAwB,GAAG,IAAI,IAAI;AAC/H,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,oBAAoB,SAAU,KAAK;AACtD,UAAI,SAAS,KAAK,kBAAkB,GAAG;AACvC,UAAI,MAAuC;AACzC,YAAI,UAAU,MAAM;AAClB,gBAAM,IAAI,MAAM,uBAAuB,GAAG;AAAA,QAC5C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAOA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,KAAK;AAErD,aAAO,KAAK,YAAY,KAAK,aAAa,GAAG,CAAC;AAAA,IAChD;AACA,IAAAA,YAAW,UAAU,wBAAwB,SAAU,aAAa;AAClE,UAAI,iBAAiB,KAAK;AAC1B,WAAK,cAAc,cAAc,SAAU,SAAS;AAClD,eAAO,eAAe,eAAe,OAAO,IAAI,eAAe,OAAO,IAAI;AAAA,MAC5E,IAAI,SAAU,SAAS;AACrB,eAAO,eAAe,OAAO;AAAA,MAC/B;AAAA,IACF;AAIA,IAAAA,YAAW,UAAU,uBAAuB,WAAY;AACtD,aAAO,KAAK,YAAY,gBAAgB,MAAM;AAAA,IAChD;AACA,IAAAA,YAAW,UAAU,eAAe,SAAU,UAAU,KAAK;AAC3D,UAAI,oBAAoB,KAAK;AAC7B,UAAI,OAAO,MAAM;AACf,eAAO,kBAAkB,uBAAuB,QAAQ;AAAA,MAC1D;AACA,UAAI,OAAO,kBAAkB,OAAO,QAAQ;AAC5C,aAAO,OAAO,KAAK,GAAG,IAAI;AAAA,IAC5B;AACA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,UAAU;AAC1D,UAAI,oBAAoB,KAAK;AAC7B,UAAI,OAAO,kBAAkB,OAAO,QAAQ;AAC5C,cAAQ,QAAQ,CAAC,GAAG,MAAM;AAAA,IAC5B;AACA,IAAAA,YAAW,UAAU,WAAW,WAAY;AAC1C,aAAO,KAAK;AAAA,IACd;AAUA,IAAAA,YAAW,UAAU,WAAW,SAAU,MAAM,UAAU,gBAAgB;AACxE,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI,gBAAgB,mBAAW;AAC7B,gBAAQ;AAAA,MACV;AACA,UAAI,CAAC,OAAO;AACV,YAAI,aAAa,KAAK;AACtB,YAAI,WAAW,iBAAiB,IAAI,KAAY,YAAY,IAAI,IAAI,IAAI,oBAAoB,MAAM,WAAW,MAAM,IAAI;AACvH,gBAAQ,IAAI,kBAAU;AACtB,YAAI,iBAAiBD,KAAI,YAAY,SAAU,SAAS;AACtD,iBAAO;AAAA,YACL,MAAM,MAAM,UAAU,OAAO,EAAE;AAAA,YAC/B,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AACD,cAAM,SAAS,UAAU,gBAAgB,cAAc;AAAA,MACzD;AACA,WAAK,SAAS;AAEd,WAAK,aAAa,YAAY,CAAC,GAAG,MAAM;AACxC,WAAK,UAAU,CAAC;AAChB,WAAK,mBAAmB,CAAC;AACzB,WAAK,QAAQ,GAAG,MAAM,MAAM,CAAC;AAG7B,WAAK,cAAc,oBAAoB,MAAM,KAAK,OAAO;AACzD,WAAK,aAAa,KAAK,YAAY;AAAA,IACrC;AAIA,IAAAC,YAAW,UAAU,aAAa,SAAU,MAAM;AAChD,UAAI,QAAQ,KAAK,OAAO,WAAW,IAAI;AACvC,WAAK,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,IACjC;AAgBA,IAAAA,YAAW,UAAU,eAAe,SAAU,QAAQ,OAAO;AAC3D,UAAI,KAAK,KAAK,OAAO,aAAa,QAAQ,MAAM,MAAM,GACpD,QAAQ,GAAG,OACX,MAAM,GAAG;AACX,UAAI,uBAAuB,KAAK,sBAAsB;AACtD,WAAK,mBAAmB;AACxB,UAAI,OAAO;AACT,iBAAS,MAAM,OAAO,MAAM,KAAK,OAAO;AACtC,cAAI,YAAY,MAAM;AACtB,eAAK,UAAU,GAAG,IAAI,MAAM,SAAS;AACrC,cAAI,sBAAsB;AACxB,2BAAe,MAAM,GAAG;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,qBAAqB,WAAY;AACpD,UAAI,QAAQ,KAAK;AACjB,UAAI,aAAa,KAAK;AACtB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAI,UAAU,KAAK,UAAU,WAAW,CAAC,CAAC;AAC1C,YAAI,QAAQ,aAAa;AACvB,gBAAM,mBAAmB,QAAQ,eAAe,QAAQ,WAAW;AAAA,QACrE;AAAA,MACF;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,wBAAwB,WAAY;AACvD,UAAI,WAAW,KAAK,OAAO,YAAY;AACvC,aAAO,KAAK,aAAa,QAAQ,SAAS,UAAU,EAAE,iBAAiB,6BAA6B,CAAC,SAAS;AAAA,IAChH;AACA,IAAAA,YAAW,UAAU,UAAU,SAAU,OAAO,KAAK;AACnD,UAAI,SAAS,KAAK;AAChB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK;AACjB,UAAI,WAAW,MAAM,YAAY;AACjC,WAAK,mBAAmB;AACxB,UAAI,WAAW,KAAK;AACpB,UAAI,SAAS,KAAK;AAClB,UAAI,eAAe,SAAS,UAAU,EAAE;AACxC,UAAI,mBAAmB,iBAAiB;AASxC,UAAI,oBAAoB,CAAC,SAAS,MAAM;AACtC,YAAI,iBAAiB,CAAC;AACtB,iBAAS,MAAM,OAAO,MAAM,KAAK,OAAO;AAEtC,cAAI,WAAW,SAAS,QAAQ,KAAK,cAAc;AACnD,cAAI,CAAC,KAAK,iBAAiB,iBAAiB,QAAQ,GAAG;AACrD,iBAAK,gBAAgB;AAAA,UACvB;AACA,cAAI,UAAU;AACZ,gBAAI,WAAW,SAAS;AACxB,gBAAI,SAAS,GAAG,KAAK,QAAQ,YAAY,MAAM;AAC7C,uBAAS,GAAG,IAAI,oBAAoB,UAAU,IAAI;AAAA,YACpD;AACA,gBAAI,SAAS,SAAS;AACtB,gBAAI,OAAO,GAAG,KAAK,QAAQ,UAAU,MAAM;AACzC,qBAAO,GAAG,IAAI,oBAAoB,QAAQ,IAAI;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,sBAAsB,GAAG;AAChC,iBAAS,MAAM,OAAO,MAAM,KAAK,OAAO;AACtC,yBAAe,MAAM,GAAG;AAAA,QAC1B;AAAA,MACF;AACA,2BAAqB,IAAI;AAAA,IAC3B;AAeA,IAAAA,YAAW,UAAU,uBAAuB,SAAU,KAAK;AACzD,aAAO,KAAK,mBAAmB,GAAG,KAAK,KAAK,OAAO,cAAc,KAAK,kBAAkB,GAAG,CAAC;AAAA,IAC9F;AAKA,IAAAA,YAAW,UAAU,uBAAuB,SAAU,QAAQ,KAAK;AACjE,YAAM,KAAK,aAAa,GAAG;AAC3B,WAAK,mBAAmB,GAAG,IAAI,OAAO,MAAM;AAAA,IAC9C;AACA,IAAAA,YAAW,UAAU,qBAAqB,SAAU,KAAK;AACvD,aAAO,KAAK,iBAAiB,GAAG;AAAA,IAClC;AACA,IAAAA,YAAW,UAAU,qBAAqB,SAAU,KAAK,OAAO;AAC9D,MAAAF,UAAS,GAAG,IAAW,OAAO,KAAK,kBAAkB,GAAG,IAAI,KAAK,iBAAiB,GAAG,IAAI;AAAA,IAC3F;AAOA,IAAAE,YAAW,UAAU,UAAU,SAAU,KAAK;AAC5C,UAAI,WAAW,KAAK,YAAY,GAAG;AACnC,UAAI,OAAO,KAAK,UAAU,QAAQ;AAClC,UAAI,QAAQ,QAAQ,KAAK,eAAe,MAAM;AAC5C,eAAO,mBAAmB,MAAM,KAAK,aAAa,QAAQ;AAAA,MAC5D;AACA,UAAI,QAAQ,MAAM;AAChB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,UAAU,eAAe,SAAU,QAAQ,KAAK;AACzD,UAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,GAAG;AACzC,UAAI,cAAc,KAAK,OAAO,eAAe,MAAM;AACnD,UAAI,aAAa;AACf,eAAO,YAAY,WAAW,OAAO;AAAA,MACvC;AACA,aAAO;AAAA,IACT;AAOA,IAAAA,YAAW,UAAU,QAAQ,SAAU,KAAK;AAC1C,aAAO,MAAM,MAAM,KAAK,YAAY,GAAG,CAAC;AAAA,IAC1C;AACA,IAAAA,YAAW,UAAU,QAAQ,WAAY;AACvC,aAAO,KAAK,OAAO,MAAM;AAAA,IAC3B;AAMA,IAAAA,YAAW,UAAU,MAAM,SAAU,KAAK,KAAK;AAC7C,UAAI,QAAQ,KAAK;AACjB,UAAI,UAAU,KAAK,UAAU,GAAG;AAChC,UAAI,SAAS;AACX,eAAO,MAAM,IAAI,QAAQ,eAAe,GAAG;AAAA,MAC7C;AAAA,IACF;AAIA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,KAAK,QAAQ;AAC1D,UAAI,QAAQ,KAAK;AACjB,UAAI,UAAU,KAAK,UAAU,GAAG;AAChC,UAAI,SAAS;AACX,eAAO,MAAM,cAAc,QAAQ,eAAe,MAAM;AAAA,MAC1D;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,aAAa,WAAY;AAC5C,aAAO,KAAK,OAAO,WAAW;AAAA,IAChC;AACA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,KAAK;AAClD,aAAO,KAAK,OAAO,cAAc,KAAK,kBAAkB,GAAG,CAAC;AAAA,IAC9D;AACA,IAAAA,YAAW,UAAU,SAAS,SAAU,KAAK;AAC3C,aAAO,KAAK,OAAO,OAAO,KAAK,kBAAkB,GAAG,CAAC;AAAA,IACvD;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,KAAK;AAC9C,aAAO,KAAK,OAAO,UAAU,KAAK,kBAAkB,GAAG,CAAC;AAAA,IAC1D;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,YAAY,KAAK;AAC1D,UAAI,QAAQ;AACZ,UAAI,QAAQ,KAAK;AACjB,aAAc,QAAQ,UAAU,IAAI,MAAM,UAAUD,KAAI,YAAY,SAAU,KAAK;AACjF,eAAO,MAAM,kBAAkB,GAAG;AAAA,MACpC,CAAC,GAAG,GAAG,IAAI,MAAM,UAAU,UAAU;AAAA,IACvC;AAKA,IAAAC,YAAW,UAAU,WAAW,SAAU,KAAK;AAC7C,UAAI,wBAAwB,KAAK,YAAY;AAC7C,eAAS,IAAI,GAAG,MAAM,sBAAsB,QAAQ,IAAI,KAAK,KAAK;AAIhE,YAAI,MAAM,KAAK,OAAO,IAAI,sBAAsB,CAAC,GAAG,GAAG,CAAC,GAAG;AACzD,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,YAAW,UAAU,cAAc,SAAU,MAAM;AACjD,eAAS,IAAI,GAAG,MAAM,KAAK,OAAO,MAAM,GAAG,IAAI,KAAK,KAAK;AACvD,YAAI,KAAK,QAAQ,CAAC,MAAM,MAAM;AAC5B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,KAAK;AAChD,aAAO,KAAK,OAAO,YAAY,GAAG;AAAA,IACpC;AACA,IAAAA,YAAW,UAAU,kBAAkB,SAAU,UAAU;AACzD,aAAO,KAAK,OAAO,gBAAgB,QAAQ;AAAA,IAC7C;AAQA,IAAAA,YAAW,UAAU,aAAa,SAAU,KAAK,OAAO;AACtD,UAAI,kBAAkB,OAAO,KAAK,oBAAoB,GAAG;AACzD,UAAI,MAAuC;AACzC,YAAI,CAAC,iBAAiB;AACpB,gBAAM,IAAI,MAAM,sBAAsB;AAAA,QACxC;AAAA,MACF;AACA,UAAI,WAAW,gBAAgB,KAAK;AACpC,UAAI,YAAY,QAAQ,MAAM,QAAQ,GAAG;AACvC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AASA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,KAAK,OAAO,aAAa;AACzE,aAAO,KAAK,OAAO,iBAAiB,KAAK,kBAAkB,GAAG,GAAG,OAAO,WAAW;AAAA,IACrF;AACA,IAAAA,YAAW,UAAU,OAAO,SAAU,MAAM,IAAI,KAAK;AACnD;AAEA,UAAW,WAAW,IAAI,GAAG;AAC3B,cAAM;AACN,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,OAAO,OAAO;AAClB,UAAI,aAAaD,KAAI,oBAAoB,IAAI,GAAG,KAAK,mBAAmB,IAAI;AAC5E,WAAK,OAAO,KAAK,YAAY,OAAc,KAAK,IAAI,IAAI,IAAI,EAAE;AAAA,IAChE;AACA,IAAAC,YAAW,UAAU,aAAa,SAAU,MAAM,IAAI,KAAK;AACzD;AAEA,UAAW,WAAW,IAAI,GAAG;AAC3B,cAAM;AACN,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,OAAO,OAAO;AAClB,UAAI,aAAaD,KAAI,oBAAoB,IAAI,GAAG,KAAK,mBAAmB,IAAI;AAC5E,WAAK,SAAS,KAAK,OAAO,OAAO,YAAY,OAAc,KAAK,IAAI,IAAI,IAAI,EAAE;AAC9E,aAAO;AAAA,IACT;AAKA,IAAAC,YAAW,UAAU,cAAc,SAAU,OAAO;AAClD;AAEA,UAAI,QAAQ;AACZ,UAAI,aAAa,CAAC;AAClB,UAAI,OAAc,KAAK,KAAK;AAC5B,UAAI,aAAa,CAAC;AAClB,MAAO,KAAK,MAAM,SAAU,KAAK;AAC/B,YAAI,SAAS,MAAM,kBAAkB,GAAG;AACxC,mBAAW,MAAM,IAAI,MAAM,GAAG;AAC9B,mBAAW,KAAK,MAAM;AAAA,MACxB,CAAC;AACD,WAAK,SAAS,KAAK,OAAO,YAAY,UAAU;AAChD,aAAO;AAAA,IACT;AAEA,IAAAA,YAAW,UAAU,WAAW,SAAU,MAAM,IAAI,KAAK;AACvD;AAEA,UAAW,WAAW,IAAI,GAAG;AAC3B,cAAM;AACN,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AAEA,YAAM,OAAO;AACb,UAAI,SAAS,CAAC;AACd,WAAK,KAAK,MAAM,WAAY;AAC1B,eAAO,KAAK,MAAM,GAAG,MAAM,MAAM,SAAS,CAAC;AAAA,MAC7C,GAAG,GAAG;AACN,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,UAAU,MAAM,SAAU,MAAM,IAAI,KAAK,WAAW;AAC7D;AAGA,UAAI,OAAO,OAAO,aAAa;AAC/B,UAAI,aAAaD,KAAI,oBAAoB,IAAI,GAAG,KAAK,mBAAmB,IAAI;AAC5E,UAAI,OAAO,yBAAyB,IAAI;AACxC,WAAK,SAAS,KAAK,OAAO,IAAI,YAAY,OAAc,KAAK,IAAI,IAAI,IAAI,EAAE;AAC3E,aAAO;AAAA,IACT;AACA,IAAAC,YAAW,UAAU,SAAS,SAAU,MAAM,IAAI,KAAK,WAAW;AAChE,UAAI,QAAQ;AAEZ,UAAI,OAAO,OAAO,aAAa;AAC/B,UAAI,MAAuC;AACzC,QAAO,KAAK,oBAAoB,IAAI,GAAG,SAAU,KAAK;AACpD,cAAI,UAAU,MAAM,iBAAiB,GAAG;AACxC,cAAI,CAAC,QAAQ,oBAAoB;AAC/B,oBAAQ,MAAM,8CAA8C;AAAA,UAC9D;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,aAAaD,KAAI,oBAAoB,IAAI,GAAG,KAAK,mBAAmB,IAAI;AAK5E,WAAK,OAAO,OAAO,YAAY,OAAc,KAAK,IAAI,IAAI,IAAI,EAAE;AAAA,IAClE;AAKA,IAAAC,YAAW,UAAU,aAAa,SAAU,WAAW,MAAM,aAAa,aAAa;AACrF,UAAI,OAAO,yBAAyB,IAAI;AACxC,WAAK,SAAS,KAAK,OAAO,WAAW,KAAK,kBAAkB,SAAS,GAAG,MAAM,aAAa,WAAW;AACtG,aAAO;AAAA,IACT;AAMA,IAAAA,YAAW,UAAU,iBAAiB,SAAU,gBAAgB,MAAM;AACpE,UAAI,OAAO,yBAAyB,IAAI;AACxC,WAAK,SAAS,KAAK,OAAO,eAAe,KAAK,kBAAkB,cAAc,GAAG,IAAI;AACrF,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,UAAU,iBAAiB,SAAU,KAAK;AACnD,aAAO,KAAK,OAAO,eAAe,GAAG;AAAA,IACvC;AAKA,IAAAA,YAAW,UAAU,eAAe,SAAU,KAAK;AACjD,UAAI,YAAY,KAAK;AACrB,UAAI,WAAW,KAAK,eAAe,GAAG;AACtC,aAAO,IAAI,cAAM,UAAU,WAAW,aAAa,UAAU,OAAO;AAAA,IACtE;AAIA,IAAAA,YAAW,UAAU,OAAO,SAAU,WAAW;AAC/C,UAAI,WAAW;AACf,aAAO,IAAI,mBAAW,YAAY,UAAU,SAAS,EAAE,WAAW,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE,WAAW,GAAG,SAAU,KAAK;AACrH,eAAO,MAAM,WAAW,GAAG;AAAA,MAC7B,GAAG,SAAU,KAAK;AAChB,eAAO,MAAM,UAAU,GAAG;AAAA,MAC5B,CAAC;AAAA,IACH;AAIA,IAAAA,YAAW,UAAU,YAAY,SAAU,KAAK;AAC9C,UAAI,SAAS,KAAK;AAClB,aAAO,UAAU,OAAO,GAAG;AAAA,IAC7B;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,OAAO,KAAK;AACrD,WAAK,UAAU,KAAK,WAAW,CAAC;AAChC,UAAIF,UAAS,KAAK,GAAG;AACnB,QAAO,OAAO,KAAK,SAAS,KAAK;AAAA,MACnC,OAAO;AACL,aAAK,QAAQ,KAAK,IAAI;AAAA,MACxB;AAAA,IACF;AAKA,IAAAE,YAAW,UAAU,gBAAgB,SAAU,KAAK,KAAK;AACvD,UAAI,aAAa,KAAK,aAAa,GAAG;AACtC,UAAI,MAAM,cAAc,WAAW,GAAG;AACtC,UAAI,OAAO,MAAM;AAEf,eAAO,KAAK,UAAU,GAAG;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,YAAW,UAAU,gBAAgB,WAAY;AAC/C,aAAO,KAAK,aAAa,SAAS;AAAA,IACpC;AAKA,IAAAA,YAAW,UAAU,yBAAyB,SAAU,KAAK,KAAK;AAChE,UAAI,cAAc,KAAK;AACvB,UAAI,aAAa,YAAY,GAAG;AAChC,UAAI,CAAC,YAAY;AACf,qBAAa,YAAY,GAAG,IAAI,CAAC;AAAA,MACnC;AACA,UAAI,MAAM,WAAW,GAAG;AACxB,UAAI,OAAO,MAAM;AACf,cAAM,KAAK,UAAU,GAAG;AAExB,YAAW,QAAQ,GAAG,GAAG;AACvB,gBAAM,IAAI,MAAM;AAAA,QAClB,WAAWF,UAAS,GAAG,GAAG;AACxB,gBAAa,OAAO,CAAC,GAAG,GAAG;AAAA,QAC7B;AACA,mBAAW,GAAG,IAAI;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AAEA,IAAAE,YAAW,UAAU,gBAAgB,SAAU,KAAK,KAAK,OAAO;AAC9D,UAAI,aAAa,KAAK,aAAa,GAAG,KAAK,CAAC;AAC5C,WAAK,aAAa,GAAG,IAAI;AACzB,UAAIF,UAAS,GAAG,GAAG;AACjB,QAAO,OAAO,YAAY,GAAG;AAAA,MAC/B,OAAO;AACL,mBAAW,GAAG,IAAI;AAAA,MACpB;AAAA,IACF;AAIA,IAAAE,YAAW,UAAU,iBAAiB,WAAY;AAChD,WAAK,UAAU,CAAC;AAChB,WAAK,eAAe,CAAC;AAAA,IACvB;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,KAAK,KAAK;AACnD,MAAAF,UAAS,GAAG,IAAW,OAAO,KAAK,SAAS,GAAG,IAAI,KAAK,QAAQ,GAAG,IAAI;AAAA,IACzE;AAIA,IAAAE,YAAW,UAAU,YAAY,SAAU,KAAK;AAC9C,aAAO,KAAK,QAAQ,GAAG;AAAA,IACzB;AAIA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,KAAK;AAClD,aAAO,KAAK,aAAa,GAAG;AAAA,IAC9B;AAIA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,KAAK,QAAQC,QAAO;AACjE,WAAK,aAAa,GAAG,IAAIA,SAAe,OAAO,KAAK,aAAa,GAAG,KAAK,CAAC,GAAG,MAAM,IAAI;AAAA,IACzF;AAIA,IAAAD,YAAW,UAAU,mBAAmB,WAAY;AAClD,WAAK,aAAa,SAAS;AAAA,IAC7B;AAIA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,KAAK,IAAI;AACzD,UAAI,cAAc,KAAK,aAAa,KAAK,UAAU;AACnD,sBAAgB,aAAa,KAAK,UAAU,KAAK,EAAE;AACnD,WAAK,YAAY,GAAG,IAAI;AAAA,IAC1B;AACA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,KAAK;AACrD,aAAO,KAAK,YAAY,GAAG;AAAA,IAC7B;AACA,IAAAA,YAAW,UAAU,oBAAoB,SAAU,IAAI,SAAS;AAC9D,MAAO,KAAK,KAAK,aAAa,SAAU,IAAI,KAAK;AAC/C,YAAI,IAAI;AACN,gBAAM,GAAG,KAAK,SAAS,IAAI,GAAG;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AAKA,IAAAA,YAAW,UAAU,eAAe,SAAU,MAAM;AAClD,UAAI,CAAC,MAAM;AACT,eAAO,IAAIA,YAAW,KAAK,UAAU,KAAK,UAAUD,KAAI,KAAK,YAAY,KAAK,aAAa,IAAI,GAAG,KAAK,SAAS;AAAA,MAClH;AACA,yBAAmB,MAAM,IAAI;AAC7B,WAAK,SAAS,KAAK;AACnB,aAAO;AAAA,IACT;AAIA,IAAAC,YAAW,UAAU,aAAa,SAAU,YAAY,gBAAgB;AACtE,UAAI,iBAAiB,KAAK,UAAU;AACpC,UAAI,CAAQ,WAAW,cAAc,GAAG;AACtC;AAAA,MACF;AACA,WAAK,mBAAmB,KAAK,oBAAoB,CAAC;AAClD,WAAK,iBAAiB,KAAK,UAAU;AACrC,WAAK,UAAU,IAAI,WAAY;AAC7B,YAAI,MAAM,eAAe,MAAM,MAAM,SAAS;AAC9C,eAAO,eAAe,MAAM,MAAM,CAAC,GAAG,EAAE,OAAc,MAAM,SAAS,CAAC,CAAC;AAAA,MACzE;AAAA,IACF;AAIA,IAAAA,YAAW,gBAAgB,WAAY;AACrC,6BAAuB,SAAU,MAAM;AACrC,YAAI,qBAAqB,KAAK;AAC9B,QAAO,KAAK,oBAAoB,SAAU,iBAAiB,KAAK;AAC9D,cAAI,UAAU,KAAK,UAAU,GAAG;AAEhC,cAAI,cAAc,QAAQ;AAC1B,cAAI,QAAQ,KAAK;AACjB,cAAI,aAAa;AACf,8BAAkB,mBAAmB,GAAG,IAAI,IAAI,eAAe,YAAY,WAAW,MAAM;AAG5F,qBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,8BAAgB,CAAC,IAAI;AAAA,YACvB;AACA,qBAAS,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,KAAK;AAEtC,8BAAgB,MAAM,IAAI,QAAQ,eAAe,CAAC,CAAC,IAAI;AAAA,YACzD;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AACA,2BAAqB,SAAU,MAAM,QAAQ,KAAK;AAChD,eAAO,oBAAoB,KAAK,aAAa,QAAQ,GAAG,GAAG,IAAI;AAAA,MACjE;AAIA,cAAQ,SAAU,MAAM,UAAU;AAChC,YAAI,KAAK,KAAK,QAAQ,QAAQ;AAC9B,YAAI,MAAM,QAAQ,KAAK,aAAa,MAAM;AACxC,eAAK,mBAAmB,MAAM,KAAK,WAAW,QAAQ;AAAA,QACxD;AACA,YAAI,MAAM,MAAM;AACd,eAAK,YAAY;AAAA,QACnB;AACA,eAAO;AAAA,MACT;AACA,4BAAsB,SAAU,YAAY;AAC1C,YAAI,CAAQ,QAAQ,UAAU,GAAG;AAC/B,uBAAa,cAAc,OAAO,CAAC,UAAU,IAAI,CAAC;AAAA,QACpD;AACA,eAAO;AAAA,MACT;AAIA,iCAA2B,SAAU,UAAU;AAC7C,YAAI,OAAO,IAAIA,YAAW,SAAS,UAAU,SAAS,UAAUD,KAAI,SAAS,YAAY,SAAS,aAAa,QAAQ,GAAG,SAAS,SAAS;AAE5I,2BAAmB,MAAM,QAAQ;AACjC,eAAO;AAAA,MACT;AACA,2BAAqB,SAAU,QAAQ,QAAQ;AAC7C,QAAO,KAAK,wBAAwB,OAAO,OAAO,oBAAoB,CAAC,CAAC,GAAG,SAAU,UAAU;AAC7F,cAAI,OAAO,eAAe,QAAQ,GAAG;AACnC,mBAAO,QAAQ,IAAI,OAAO,QAAQ;AAAA,UACpC;AAAA,QACF,CAAC;AACD,eAAO,mBAAmB,OAAO;AACjC,QAAO,KAAK,kBAAkB,SAAU,UAAU;AAChD,iBAAO,QAAQ,IAAW,MAAM,OAAO,QAAQ,CAAC;AAAA,QAClD,CAAC;AACD,eAAO,mBAA0B,OAAO,CAAC,GAAG,OAAO,gBAAgB;AAAA,MACrE;AACA,uBAAiB,SAAU,MAAM,KAAK;AACpC,YAAI,WAAW,KAAK;AACpB,YAAI,SAAS,KAAK;AAClB,YAAI,aAAa,KAAK;AACtB,YAAI,WAAW,KAAK;AACpB,YAAI,OAAO,SAAS,GAAG;AACvB,YAAI,KAAK,OAAO,GAAG;AACnB,YAAI,QAAQ,QAAQ,cAAc,MAAM;AACtC,mBAAS,GAAG,IAAI,OAAO,mBAAmB,MAAM,YAAY,GAAG;AAAA,QACjE;AACA,YAAI,MAAM,QAAQ,YAAY,MAAM;AAClC,iBAAO,GAAG,IAAI,KAAK,mBAAmB,MAAM,UAAU,GAAG;AAAA,QAC3D;AACA,YAAI,MAAM,QAAQ,QAAQ,MAAM;AAC9B,cAAI,kBAAkB,KAAK;AAC3B,cAAI,QAAQ,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,KAAK,KAAK;AACnE,eAAK;AACL,cAAI,QAAQ,GAAG;AACb,kBAAM,WAAW;AAAA,UACnB;AACA,iBAAO,GAAG,IAAI;AAAA,QAChB;AAAA,MACF;AAAA,IACF,EAAE;AACF,WAAOC;AAAA,EACT,EAAE;AAAA;AACF,IAAO,qBAAQ;;;ACv6Bf,IAAI,aAAa,CAAC;AAClB,IAAI,qBAAqB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,eAAeE;AAAA,EACf,aAAa;AAAA,EACb,WAAW;AAAA;AAAA,EAEX,wBAAwB,SAAU,qBAAqB;AACrD,sBAAe,cAAc,mBAAmB;AAAA,EAClD;AAAA,EACA,uBAAuB,SAAU,oBAAoB;AACnD,IAAAA,mBAAc,cAAc,kBAAkB;AAAA,EAChD;AAAA,EACA,qBAAqB,SAAU,kBAAkB;AAC/C,mBAAY,cAAc,gBAAgB;AAAA,EAC5C;AAAA,EACA,mBAAmB,SAAU,gBAAgB;AAC3C,kBAAU,cAAc,cAAc;AAAA,EACxC;AAAA,EACA,0BAA0B,SAAU,eAAe,WAAW;AAC5D,sBAAe,yBAAyB,eAAe,SAAS;AAAA,EAClE;AAAA,EACA,iBAAiB,SAAU,aAAa,aAAa;AACnD,oBAAgB,aAAa,WAAW;AAAA,EAC1C;AACF;AACO,SAAS,IAAI,KAAK;AACvB,MAAI,QAAQ,GAAG,GAAG;AAEhB,SAAK,KAAK,SAAU,WAAW;AAC7B,UAAI,SAAS;AAAA,IACf,CAAC;AACD;AAAA,EACF;AACA,MAAI,QAAQ,YAAY,GAAG,KAAK,GAAG;AACjC;AAAA,EACF;AACA,aAAW,KAAK,GAAG;AACnB,MAAI,WAAW,GAAG,GAAG;AACnB,UAAM;AAAA,MACJ,SAAS;AAAA,IACX;AAAA,EACF;AACA,MAAI,QAAQ,kBAAkB;AAChC;;;AC5CO,SAAS,gBAAgB,aAAa,iBAAiB,KAAK;AACjE,QAAM,OAAO,CAAC;AACd,MAAI,UAAU,IAAI;AAClB,MAAI,wBAAwB,IAAI;AAChC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,wBAAwB,eAAe,GAAG;AAC5C,0BAAsB;AAAA,EACxB,OAAO;AACL,aAAS,gBAAgB;AACzB,0BAAsB,OAAO;AAC7B,YAAQ,gBAAgB;AAAA,EAC1B;AAEA,MAAI,WAAW,CAAC,EAAE,eAAe,YAAY,IAAI,OAAO;AACxD,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,OAAK,qBAAqB,SAAU,eAAe,OAAO;AACxD,QAAI,SAAS,aAAa,GAAG;AAC3B,0BAAoB,KAAK,IAAI,gBAAgB;AAAA,QAC3C,MAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,YAAY,CAAC,cAAc,cAAc;AAE3C,UAAI,CAAC,WAAW,CAAC,oBAAoB,cAAc,aAAa;AAC9D,2BAAmB;AAAA,MACrB;AAEA,UAAI,CAAC,kBAAkB,cAAc,SAAS,aAAa,cAAc,SAAS,WAAW,CAAC,yBAAyB,0BAA0B,cAAc,WAAW;AACxK,yBAAiB;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,kBAAkB,CAAC,WAAW,CAAC,kBAAkB;AAGnD,cAAU;AAAA,EACZ;AAIA,MAAI,gBAAgB;AAIlB,2BAAuB,uBAAuB,YAAY;AAC1D,2BAAuB,uBAAuB,YAAY;AAE1D,QAAI,kBAAkB;AACpB,uBAAiB,wBAAwB;AAAA,IAC3C;AACA,QAAI,uBAAuB,eAAe;AAC1C,QAAI,iBAAiB,eAAe;AACpC,QAAI,yBAAyB;AAC7B,SAAK,qBAAqB,SAAU,eAAe;AACjD,UAAI,cAAc,aAAa,sBAAsB;AACnD;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,6BAA6B;AAAA,MAC/B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,eAAe;AAAA,MACf,MAAM;AAAA,MACN,cAAc;AAAA,MACd,oBAAoB;AAAA,MACpB,eAAe,oBAAoB;AAAA,IACrC;AACA,QAAI,6BAA6B;AAAA,MAC/B,MAAM;AAAA;AAAA;AAAA,MAGN,UAAU;AAAA,MACV,eAAe,yBAAyB;AAAA,MACxC,MAAM;AAAA,MACN,cAAc;AAAA,MACd,oBAAoB;AAAA,MACpB,eAAe,oBAAoB,SAAS;AAAA,IAC9C;AACA,QAAI,QAAQ;AACV,UAAI,OAAO;AACT,mCAA2B,gBAAgB,MAAM,2BAA2B,sBAAsB,cAAc;AAChH,mCAA2B,gBAAgB,MAAM,2BAA2B,sBAAsB,cAAc;AAAA,MAClH;AACA,aAAO,2BAA2B,0BAA0B;AAC5D,aAAO,2BAA2B,0BAA0B;AAAA,IAC9D,OAAO;AACL,0BAAoB,KAAK,0BAA0B;AACnD,0BAAoB,KAAK,0BAA0B;AAAA,IACrD;AAAA,EACF;AACA,SAAO;AAAA,IACL,kBAAkB,kBAAkB,eAAe;AAAA,IACnD,oBAAoB,oBAAoB,iBAAiB;AAAA,IACzD,kBAAkB;AAAA,IAClB;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,wBAAwB,iBAAiB;AAChD,SAAO,CAAC,mBAAmB,gBAAgB,MAAM;AACnD;AACO,SAAS,mBAAmB,MAAM,YAAY;AAGnD,SAAO,CAAC,CAAC,cAAc,eAAe,KAAK,mBAAmB,kBAAkB;AAClF;AACO,SAAS,oBAAoB,MAAM,WAAW;AACnD,SAAO,mBAAmB,MAAM,SAAS,IAAI,KAAK,mBAAmB,sBAAsB,IAAI;AACjG;", "names": ["ComponentView", "Component_default", "inner", "ChartView", "GlobalModel", "theme", "optionPreprocessorFuncs", "cmpts", "i", "cmpt", "ExtensionAPI", "CoordinateSystemManager", "OptionManager", "optionPreprocessorFuncs", "each", "isObject", "seriesType", "option", "inner", "colorPalette", "data", "Scheduler", "opt", "seriesType", "ECEventProcessor", "query", "data", "i", "symbolPropName", "seriesType", "WeakMap", "pattern", "keys", "i", "warn", "x", "y", "width", "height", "decal", "MessageCenter", "<PERSON><PERSON><PERSON>", "theme", "chart", "view", "Component_default", "backgroundColor", "_super", "init", "registerMap", "getMap", "DimensionUserOuput", "SeriesDimensionDefine", "inner", "SeriesDataSchema", "inner", "isObject", "map", "SeriesData", "merge", "Component_default"]}