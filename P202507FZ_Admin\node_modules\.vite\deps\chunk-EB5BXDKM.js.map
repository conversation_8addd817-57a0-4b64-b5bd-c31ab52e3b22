{"version": 3, "sources": ["../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/data/helper/createDimensions.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/model/referHelper.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/chart/helper/createSeriesData.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nimport SeriesDimensionDefine from '../SeriesDimensionDefine.js';\nimport { createHashMap, defaults, each, extend, isObject, isString } from 'zrender/lib/core/util.js';\nimport { createSourceFromSeriesDataOption, isSourceInstance } from '../Source.js';\nimport { CtorInt32Array } from '../DataStore.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { BE_ORDINAL, guessOrdinal } from './sourceHelper.js';\nimport { createDimNameMap, ensureSourceDimNameMap, SeriesDataSchema, shouldOmitUnusedDimensions } from './SeriesDataSchema.js';\n/**\n * For outside usage compat (like echarts-gl are using it).\n */\nexport function createDimensions(source, opt) {\n  return prepareSeriesDataSchema(source, opt).dimensions;\n}\n/**\n * This method builds the relationship between:\n * + \"what the coord sys or series requires (see `coordDimensions`)\",\n * + \"what the user defines (in `encode` and `dimensions`, see `opt.dimensionsDefine` and `opt.encodeDefine`)\"\n * + \"what the data source provids (see `source`)\".\n *\n * Some guess strategy will be adapted if user does not define something.\n * If no 'value' dimension specified, the first no-named dimension will be\n * named as 'value'.\n *\n * @return The results are always sorted by `storeDimIndex` asc.\n */\nexport default function prepareSeriesDataSchema(\n// TODO: TYPE completeDimensions type\nsource, opt) {\n  if (!isSourceInstance(source)) {\n    source = createSourceFromSeriesDataOption(source);\n  }\n  opt = opt || {};\n  var sysDims = opt.coordDimensions || [];\n  var dimsDef = opt.dimensionsDefine || source.dimensionsDefine || [];\n  var coordDimNameMap = createHashMap();\n  var resultList = [];\n  var dimCount = getDimCount(source, sysDims, dimsDef, opt.dimensionsCount);\n  // Try to ignore unused dimensions if sharing a high dimension datastore\n  // 30 is an experience value.\n  var omitUnusedDimensions = opt.canOmitUnusedDimensions && shouldOmitUnusedDimensions(dimCount);\n  var isUsingSourceDimensionsDef = dimsDef === source.dimensionsDefine;\n  var dataDimNameMap = isUsingSourceDimensionsDef ? ensureSourceDimNameMap(source) : createDimNameMap(dimsDef);\n  var encodeDef = opt.encodeDefine;\n  if (!encodeDef && opt.encodeDefaulter) {\n    encodeDef = opt.encodeDefaulter(source, dimCount);\n  }\n  var encodeDefMap = createHashMap(encodeDef);\n  var indicesMap = new CtorInt32Array(dimCount);\n  for (var i = 0; i < indicesMap.length; i++) {\n    indicesMap[i] = -1;\n  }\n  function getResultItem(dimIdx) {\n    var idx = indicesMap[dimIdx];\n    if (idx < 0) {\n      var dimDefItemRaw = dimsDef[dimIdx];\n      var dimDefItem = isObject(dimDefItemRaw) ? dimDefItemRaw : {\n        name: dimDefItemRaw\n      };\n      var resultItem = new SeriesDimensionDefine();\n      var userDimName = dimDefItem.name;\n      if (userDimName != null && dataDimNameMap.get(userDimName) != null) {\n        // Only if `series.dimensions` is defined in option\n        // displayName, will be set, and dimension will be displayed vertically in\n        // tooltip by default.\n        resultItem.name = resultItem.displayName = userDimName;\n      }\n      dimDefItem.type != null && (resultItem.type = dimDefItem.type);\n      dimDefItem.displayName != null && (resultItem.displayName = dimDefItem.displayName);\n      var newIdx = resultList.length;\n      indicesMap[dimIdx] = newIdx;\n      resultItem.storeDimIndex = dimIdx;\n      resultList.push(resultItem);\n      return resultItem;\n    }\n    return resultList[idx];\n  }\n  if (!omitUnusedDimensions) {\n    for (var i = 0; i < dimCount; i++) {\n      getResultItem(i);\n    }\n  }\n  // Set `coordDim` and `coordDimIndex` by `encodeDefMap` and normalize `encodeDefMap`.\n  encodeDefMap.each(function (dataDimsRaw, coordDim) {\n    var dataDims = normalizeToArray(dataDimsRaw).slice();\n    // Note: It is allowed that `dataDims.length` is `0`, e.g., options is\n    // `{encode: {x: -1, y: 1}}`. Should not filter anything in\n    // this case.\n    if (dataDims.length === 1 && !isString(dataDims[0]) && dataDims[0] < 0) {\n      encodeDefMap.set(coordDim, false);\n      return;\n    }\n    var validDataDims = encodeDefMap.set(coordDim, []);\n    each(dataDims, function (resultDimIdxOrName, idx) {\n      // The input resultDimIdx can be dim name or index.\n      var resultDimIdx = isString(resultDimIdxOrName) ? dataDimNameMap.get(resultDimIdxOrName) : resultDimIdxOrName;\n      if (resultDimIdx != null && resultDimIdx < dimCount) {\n        validDataDims[idx] = resultDimIdx;\n        applyDim(getResultItem(resultDimIdx), coordDim, idx);\n      }\n    });\n  });\n  // Apply templates and default order from `sysDims`.\n  var availDimIdx = 0;\n  each(sysDims, function (sysDimItemRaw) {\n    var coordDim;\n    var sysDimItemDimsDef;\n    var sysDimItemOtherDims;\n    var sysDimItem;\n    if (isString(sysDimItemRaw)) {\n      coordDim = sysDimItemRaw;\n      sysDimItem = {};\n    } else {\n      sysDimItem = sysDimItemRaw;\n      coordDim = sysDimItem.name;\n      var ordinalMeta = sysDimItem.ordinalMeta;\n      sysDimItem.ordinalMeta = null;\n      sysDimItem = extend({}, sysDimItem);\n      sysDimItem.ordinalMeta = ordinalMeta;\n      // `coordDimIndex` should not be set directly.\n      sysDimItemDimsDef = sysDimItem.dimsDef;\n      sysDimItemOtherDims = sysDimItem.otherDims;\n      sysDimItem.name = sysDimItem.coordDim = sysDimItem.coordDimIndex = sysDimItem.dimsDef = sysDimItem.otherDims = null;\n    }\n    var dataDims = encodeDefMap.get(coordDim);\n    // negative resultDimIdx means no need to mapping.\n    if (dataDims === false) {\n      return;\n    }\n    dataDims = normalizeToArray(dataDims);\n    // dimensions provides default dim sequences.\n    if (!dataDims.length) {\n      for (var i = 0; i < (sysDimItemDimsDef && sysDimItemDimsDef.length || 1); i++) {\n        while (availDimIdx < dimCount && getResultItem(availDimIdx).coordDim != null) {\n          availDimIdx++;\n        }\n        availDimIdx < dimCount && dataDims.push(availDimIdx++);\n      }\n    }\n    // Apply templates.\n    each(dataDims, function (resultDimIdx, coordDimIndex) {\n      var resultItem = getResultItem(resultDimIdx);\n      // Coordinate system has a higher priority on dim type than source.\n      if (isUsingSourceDimensionsDef && sysDimItem.type != null) {\n        resultItem.type = sysDimItem.type;\n      }\n      applyDim(defaults(resultItem, sysDimItem), coordDim, coordDimIndex);\n      if (resultItem.name == null && sysDimItemDimsDef) {\n        var sysDimItemDimsDefItem = sysDimItemDimsDef[coordDimIndex];\n        !isObject(sysDimItemDimsDefItem) && (sysDimItemDimsDefItem = {\n          name: sysDimItemDimsDefItem\n        });\n        resultItem.name = resultItem.displayName = sysDimItemDimsDefItem.name;\n        resultItem.defaultTooltip = sysDimItemDimsDefItem.defaultTooltip;\n      }\n      // FIXME refactor, currently only used in case: {otherDims: {tooltip: false}}\n      sysDimItemOtherDims && defaults(resultItem.otherDims, sysDimItemOtherDims);\n    });\n  });\n  function applyDim(resultItem, coordDim, coordDimIndex) {\n    if (VISUAL_DIMENSIONS.get(coordDim) != null) {\n      resultItem.otherDims[coordDim] = coordDimIndex;\n    } else {\n      resultItem.coordDim = coordDim;\n      resultItem.coordDimIndex = coordDimIndex;\n      coordDimNameMap.set(coordDim, true);\n    }\n  }\n  // Make sure the first extra dim is 'value'.\n  var generateCoord = opt.generateCoord;\n  var generateCoordCount = opt.generateCoordCount;\n  var fromZero = generateCoordCount != null;\n  generateCoordCount = generateCoord ? generateCoordCount || 1 : 0;\n  var extra = generateCoord || 'value';\n  function ifNoNameFillWithCoordName(resultItem) {\n    if (resultItem.name == null) {\n      // Duplication will be removed in the next step.\n      resultItem.name = resultItem.coordDim;\n    }\n  }\n  // Set dim `name` and other `coordDim` and other props.\n  if (!omitUnusedDimensions) {\n    for (var resultDimIdx = 0; resultDimIdx < dimCount; resultDimIdx++) {\n      var resultItem = getResultItem(resultDimIdx);\n      var coordDim = resultItem.coordDim;\n      if (coordDim == null) {\n        // TODO no need to generate coordDim for isExtraCoord?\n        resultItem.coordDim = genCoordDimName(extra, coordDimNameMap, fromZero);\n        resultItem.coordDimIndex = 0;\n        // Series specified generateCoord is using out.\n        if (!generateCoord || generateCoordCount <= 0) {\n          resultItem.isExtraCoord = true;\n        }\n        generateCoordCount--;\n      }\n      ifNoNameFillWithCoordName(resultItem);\n      if (resultItem.type == null && (guessOrdinal(source, resultDimIdx) === BE_ORDINAL.Must\n      // Consider the case:\n      // {\n      //    dataset: {source: [\n      //        ['2001', 123],\n      //        ['2002', 456],\n      //        ...\n      //        ['The others', 987],\n      //    ]},\n      //    series: {type: 'pie'}\n      // }\n      // The first column should better be treated as a \"ordinal\" although it\n      // might not be detected as an \"ordinal\" by `guessOrdinal`.\n      || resultItem.isExtraCoord && (resultItem.otherDims.itemName != null || resultItem.otherDims.seriesName != null))) {\n        resultItem.type = 'ordinal';\n      }\n    }\n  } else {\n    each(resultList, function (resultItem) {\n      // PENDING: guessOrdinal or let user specify type: 'ordinal' manually?\n      ifNoNameFillWithCoordName(resultItem);\n    });\n    // Sort dimensions: there are some rule that use the last dim as label,\n    // and for some latter travel process easier.\n    resultList.sort(function (item0, item1) {\n      return item0.storeDimIndex - item1.storeDimIndex;\n    });\n  }\n  removeDuplication(resultList);\n  return new SeriesDataSchema({\n    source: source,\n    dimensions: resultList,\n    fullDimensionCount: dimCount,\n    dimensionOmitted: omitUnusedDimensions\n  });\n}\nfunction removeDuplication(result) {\n  var duplicationMap = createHashMap();\n  for (var i = 0; i < result.length; i++) {\n    var dim = result[i];\n    var dimOriginalName = dim.name;\n    var count = duplicationMap.get(dimOriginalName) || 0;\n    if (count > 0) {\n      // Starts from 0.\n      dim.name = dimOriginalName + (count - 1);\n    }\n    count++;\n    duplicationMap.set(dimOriginalName, count);\n  }\n}\n// ??? TODO\n// Originally detect dimCount by data[0]. Should we\n// optimize it to only by sysDims and dimensions and encode.\n// So only necessary dims will be initialized.\n// But\n// (1) custom series should be considered. where other dims\n// may be visited.\n// (2) sometimes user need to calculate bubble size or use visualMap\n// on other dimensions besides coordSys needed.\n// So, dims that is not used by system, should be shared in data store?\nfunction getDimCount(source, sysDims, dimsDef, optDimCount) {\n  // Note that the result dimCount should not small than columns count\n  // of data, otherwise `dataDimNameMap` checking will be incorrect.\n  var dimCount = Math.max(source.dimensionsDetectedCount || 1, sysDims.length, dimsDef.length, optDimCount || 0);\n  each(sysDims, function (sysDimItem) {\n    var sysDimItemDimsDef;\n    if (isObject(sysDimItem) && (sysDimItemDimsDef = sysDimItem.dimsDef)) {\n      dimCount = Math.max(dimCount, sysDimItemDimsDef.length);\n    }\n  });\n  return dimCount;\n}\nfunction genCoordDimName(name, map, fromZero) {\n  if (fromZero || map.hasKey(name)) {\n    var i = 0;\n    while (map.hasKey(name + i)) {\n      i++;\n    }\n    name += i;\n  }\n  map.set(name, true);\n  return name;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * Helper for model references.\n * There are many manners to refer axis/coordSys.\n */\n// TODO\n// merge relevant logic to this file?\n// check: \"modelHelper\" of tooltip and \"BrushTargetManager\".\nimport { createHashMap, retrieve, each } from 'zrender/lib/core/util.js';\nimport { SINGLE_REFERRING } from '../util/model.js';\n/**\n * @class\n * For example:\n * {\n *     coordSysName: 'cartesian2d',\n *     coordSysDims: ['x', 'y', ...],\n *     axisMap: HashMap({\n *         x: xAxisModel,\n *         y: yAxisModel\n *     }),\n *     categoryAxisMap: HashMap({\n *         x: xAxisModel,\n *         y: undefined\n *     }),\n *     // The index of the first category axis in `coordSysDims`.\n *     // `null/undefined` means no category axis exists.\n *     firstCategoryDimIndex: 1,\n *     // To replace user specified encode.\n * }\n */\nvar CoordSysInfo = /** @class */function () {\n  function CoordSysInfo(coordSysName) {\n    this.coordSysDims = [];\n    this.axisMap = createHashMap();\n    this.categoryAxisMap = createHashMap();\n    this.coordSysName = coordSysName;\n  }\n  return CoordSysInfo;\n}();\nexport function getCoordSysInfoBySeries(seriesModel) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var result = new CoordSysInfo(coordSysName);\n  var fetch = fetchers[coordSysName];\n  if (fetch) {\n    fetch(seriesModel, result, result.axisMap, result.categoryAxisMap);\n    return result;\n  }\n}\nvar fetchers = {\n  cartesian2d: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var xAxisModel = seriesModel.getReferringComponents('xAxis', SINGLE_REFERRING).models[0];\n    var yAxisModel = seriesModel.getReferringComponents('yAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!xAxisModel) {\n        throw new Error('xAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('xAxisId'), 0) + '\" not found');\n      }\n      if (!yAxisModel) {\n        throw new Error('yAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('yAxisId'), 0) + '\" not found');\n      }\n    }\n    result.coordSysDims = ['x', 'y'];\n    axisMap.set('x', xAxisModel);\n    axisMap.set('y', yAxisModel);\n    if (isCategory(xAxisModel)) {\n      categoryAxisMap.set('x', xAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(yAxisModel)) {\n      categoryAxisMap.set('y', yAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  singleAxis: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var singleAxisModel = seriesModel.getReferringComponents('singleAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!singleAxisModel) {\n        throw new Error('singleAxis should be specified.');\n      }\n    }\n    result.coordSysDims = ['single'];\n    axisMap.set('single', singleAxisModel);\n    if (isCategory(singleAxisModel)) {\n      categoryAxisMap.set('single', singleAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n  },\n  polar: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var polarModel = seriesModel.getReferringComponents('polar', SINGLE_REFERRING).models[0];\n    var radiusAxisModel = polarModel.findAxisModel('radiusAxis');\n    var angleAxisModel = polarModel.findAxisModel('angleAxis');\n    if (process.env.NODE_ENV !== 'production') {\n      if (!angleAxisModel) {\n        throw new Error('angleAxis option not found');\n      }\n      if (!radiusAxisModel) {\n        throw new Error('radiusAxis option not found');\n      }\n    }\n    result.coordSysDims = ['radius', 'angle'];\n    axisMap.set('radius', radiusAxisModel);\n    axisMap.set('angle', angleAxisModel);\n    if (isCategory(radiusAxisModel)) {\n      categoryAxisMap.set('radius', radiusAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(angleAxisModel)) {\n      categoryAxisMap.set('angle', angleAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  geo: function (seriesModel, result, axisMap, categoryAxisMap) {\n    result.coordSysDims = ['lng', 'lat'];\n  },\n  parallel: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var ecModel = seriesModel.ecModel;\n    var parallelModel = ecModel.getComponent('parallel', seriesModel.get('parallelIndex'));\n    var coordSysDims = result.coordSysDims = parallelModel.dimensions.slice();\n    each(parallelModel.parallelAxisIndex, function (axisIndex, index) {\n      var axisModel = ecModel.getComponent('parallelAxis', axisIndex);\n      var axisDim = coordSysDims[index];\n      axisMap.set(axisDim, axisModel);\n      if (isCategory(axisModel)) {\n        categoryAxisMap.set(axisDim, axisModel);\n        if (result.firstCategoryDimIndex == null) {\n          result.firstCategoryDimIndex = index;\n        }\n      }\n    });\n  }\n};\nfunction isCategory(axisModel) {\n  return axisModel.get('type') === 'category';\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { getDataItemValue } from '../../util/model.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport { getCoordSysInfoBySeries } from '../../model/referHelper.js';\nimport { createSourceFromSeriesDataOption } from '../../data/Source.js';\nimport { enableDataStack } from '../../data/helper/dataStackHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\nimport { SOURCE_FORMAT_ORIGINAL } from '../../util/types.js';\nfunction getCoordSysDimDefs(seriesModel, coordSysInfo) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var registeredCoordSys = CoordinateSystem.get(coordSysName);\n  var coordSysDimDefs;\n  if (coordSysInfo && coordSysInfo.coordSysDims) {\n    coordSysDimDefs = zrUtil.map(coordSysInfo.coordSysDims, function (dim) {\n      var dimInfo = {\n        name: dim\n      };\n      var axisModel = coordSysInfo.axisMap.get(dim);\n      if (axisModel) {\n        var axisType = axisModel.get('type');\n        dimInfo.type = getDimensionTypeByAxis(axisType);\n      }\n      return dimInfo;\n    });\n  }\n  if (!coordSysDimDefs) {\n    // Get dimensions from registered coordinate system\n    coordSysDimDefs = registeredCoordSys && (registeredCoordSys.getDimensionsInfo ? registeredCoordSys.getDimensionsInfo() : registeredCoordSys.dimensions.slice()) || ['x', 'y'];\n  }\n  return coordSysDimDefs;\n}\nfunction injectOrdinalMeta(dimInfoList, createInvertedIndices, coordSysInfo) {\n  var firstCategoryDimIndex;\n  var hasNameEncode;\n  coordSysInfo && zrUtil.each(dimInfoList, function (dimInfo, dimIndex) {\n    var coordDim = dimInfo.coordDim;\n    var categoryAxisModel = coordSysInfo.categoryAxisMap.get(coordDim);\n    if (categoryAxisModel) {\n      if (firstCategoryDimIndex == null) {\n        firstCategoryDimIndex = dimIndex;\n      }\n      dimInfo.ordinalMeta = categoryAxisModel.getOrdinalMeta();\n      if (createInvertedIndices) {\n        dimInfo.createInvertedIndices = true;\n      }\n    }\n    if (dimInfo.otherDims.itemName != null) {\n      hasNameEncode = true;\n    }\n  });\n  if (!hasNameEncode && firstCategoryDimIndex != null) {\n    dimInfoList[firstCategoryDimIndex].otherDims.itemName = 0;\n  }\n  return firstCategoryDimIndex;\n}\n/**\n * Caution: there are side effects to `sourceManager` in this method.\n * Should better only be called in `Series['getInitialData']`.\n */\nfunction createSeriesData(sourceRaw, seriesModel, opt) {\n  opt = opt || {};\n  var sourceManager = seriesModel.getSourceManager();\n  var source;\n  var isOriginalSource = false;\n  if (sourceRaw) {\n    isOriginalSource = true;\n    source = createSourceFromSeriesDataOption(sourceRaw);\n  } else {\n    source = sourceManager.getSource();\n    // Is series.data. not dataset.\n    isOriginalSource = source.sourceFormat === SOURCE_FORMAT_ORIGINAL;\n  }\n  var coordSysInfo = getCoordSysInfoBySeries(seriesModel);\n  var coordSysDimDefs = getCoordSysDimDefs(seriesModel, coordSysInfo);\n  var useEncodeDefaulter = opt.useEncodeDefaulter;\n  var encodeDefaulter = zrUtil.isFunction(useEncodeDefaulter) ? useEncodeDefaulter : useEncodeDefaulter ? zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordSysDimDefs, seriesModel) : null;\n  var createDimensionOptions = {\n    coordDimensions: coordSysDimDefs,\n    generateCoord: opt.generateCoord,\n    encodeDefine: seriesModel.getEncode(),\n    encodeDefaulter: encodeDefaulter,\n    canOmitUnusedDimensions: !isOriginalSource\n  };\n  var schema = prepareSeriesDataSchema(source, createDimensionOptions);\n  var firstCategoryDimIndex = injectOrdinalMeta(schema.dimensions, opt.createInvertedIndices, coordSysInfo);\n  var store = !isOriginalSource ? sourceManager.getSharedDataStore(schema) : null;\n  var stackCalculationInfo = enableDataStack(seriesModel, {\n    schema: schema,\n    store: store\n  });\n  var data = new SeriesData(schema, seriesModel);\n  data.setCalculationInfo(stackCalculationInfo);\n  var dimValueGetter = firstCategoryDimIndex != null && isNeedCompleteOrdinalData(source) ? function (itemOpt, dimName, dataIndex, dimIndex) {\n    // Use dataIndex as ordinal value in categoryAxis\n    return dimIndex === firstCategoryDimIndex ? dataIndex : this.defaultDimValueGetter(itemOpt, dimName, dataIndex, dimIndex);\n  } : null;\n  data.hasItemOption = false;\n  data.initData(\n  // Try to reuse the data store in sourceManager if using dataset.\n  isOriginalSource ? source : store, null, dimValueGetter);\n  return data;\n}\nfunction isNeedCompleteOrdinalData(source) {\n  if (source.sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var sampleItem = firstDataNotNull(source.data || []);\n    return !zrUtil.isArray(getDataItemValue(sampleItem));\n  }\n}\nfunction firstDataNotNull(arr) {\n  var i = 0;\n  while (i < arr.length && arr[i] == null) {\n    i++;\n  }\n  return arr[i];\n}\nexport default createSeriesData;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDO,SAAS,iBAAiB,QAAQ,KAAK;AAC5C,SAAO,wBAAwB,QAAQ,GAAG,EAAE;AAC9C;AAae,SAAR,wBAEP,QAAQ,KAAK;AACX,MAAI,CAAC,iBAAiB,MAAM,GAAG;AAC7B,aAAS,iCAAiC,MAAM;AAAA,EAClD;AACA,QAAM,OAAO,CAAC;AACd,MAAI,UAAU,IAAI,mBAAmB,CAAC;AACtC,MAAI,UAAU,IAAI,oBAAoB,OAAO,oBAAoB,CAAC;AAClE,MAAI,kBAAkB,cAAc;AACpC,MAAI,aAAa,CAAC;AAClB,MAAI,WAAW,YAAY,QAAQ,SAAS,SAAS,IAAI,eAAe;AAGxE,MAAI,uBAAuB,IAAI,2BAA2B,2BAA2B,QAAQ;AAC7F,MAAI,6BAA6B,YAAY,OAAO;AACpD,MAAI,iBAAiB,6BAA6B,uBAAuB,MAAM,IAAI,iBAAiB,OAAO;AAC3G,MAAI,YAAY,IAAI;AACpB,MAAI,CAAC,aAAa,IAAI,iBAAiB;AACrC,gBAAY,IAAI,gBAAgB,QAAQ,QAAQ;AAAA,EAClD;AACA,MAAI,eAAe,cAAc,SAAS;AAC1C,MAAI,aAAa,IAAI,eAAe,QAAQ;AAC5C,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,eAAW,CAAC,IAAI;AAAA,EAClB;AACA,WAAS,cAAc,QAAQ;AAC7B,QAAI,MAAM,WAAW,MAAM;AAC3B,QAAI,MAAM,GAAG;AACX,UAAI,gBAAgB,QAAQ,MAAM;AAClC,UAAI,aAAa,SAAS,aAAa,IAAI,gBAAgB;AAAA,QACzD,MAAM;AAAA,MACR;AACA,UAAIA,cAAa,IAAI,8BAAsB;AAC3C,UAAI,cAAc,WAAW;AAC7B,UAAI,eAAe,QAAQ,eAAe,IAAI,WAAW,KAAK,MAAM;AAIlE,QAAAA,YAAW,OAAOA,YAAW,cAAc;AAAA,MAC7C;AACA,iBAAW,QAAQ,SAASA,YAAW,OAAO,WAAW;AACzD,iBAAW,eAAe,SAASA,YAAW,cAAc,WAAW;AACvE,UAAI,SAAS,WAAW;AACxB,iBAAW,MAAM,IAAI;AACrB,MAAAA,YAAW,gBAAgB;AAC3B,iBAAW,KAAKA,WAAU;AAC1B,aAAOA;AAAA,IACT;AACA,WAAO,WAAW,GAAG;AAAA,EACvB;AACA,MAAI,CAAC,sBAAsB;AACzB,aAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,oBAAc,CAAC;AAAA,IACjB;AAAA,EACF;AAEA,eAAa,KAAK,SAAU,aAAaC,WAAU;AACjD,QAAI,WAAW,iBAAiB,WAAW,EAAE,MAAM;AAInD,QAAI,SAAS,WAAW,KAAK,CAAC,SAAS,SAAS,CAAC,CAAC,KAAK,SAAS,CAAC,IAAI,GAAG;AACtE,mBAAa,IAAIA,WAAU,KAAK;AAChC;AAAA,IACF;AACA,QAAI,gBAAgB,aAAa,IAAIA,WAAU,CAAC,CAAC;AACjD,SAAK,UAAU,SAAU,oBAAoB,KAAK;AAEhD,UAAIC,gBAAe,SAAS,kBAAkB,IAAI,eAAe,IAAI,kBAAkB,IAAI;AAC3F,UAAIA,iBAAgB,QAAQA,gBAAe,UAAU;AACnD,sBAAc,GAAG,IAAIA;AACrB,iBAAS,cAAcA,aAAY,GAAGD,WAAU,GAAG;AAAA,MACrD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,MAAI,cAAc;AAClB,OAAK,SAAS,SAAU,eAAe;AACrC,QAAIA;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS,aAAa,GAAG;AAC3B,MAAAA,YAAW;AACX,mBAAa,CAAC;AAAA,IAChB,OAAO;AACL,mBAAa;AACb,MAAAA,YAAW,WAAW;AACtB,UAAI,cAAc,WAAW;AAC7B,iBAAW,cAAc;AACzB,mBAAa,OAAO,CAAC,GAAG,UAAU;AAClC,iBAAW,cAAc;AAEzB,0BAAoB,WAAW;AAC/B,4BAAsB,WAAW;AACjC,iBAAW,OAAO,WAAW,WAAW,WAAW,gBAAgB,WAAW,UAAU,WAAW,YAAY;AAAA,IACjH;AACA,QAAI,WAAW,aAAa,IAAIA,SAAQ;AAExC,QAAI,aAAa,OAAO;AACtB;AAAA,IACF;AACA,eAAW,iBAAiB,QAAQ;AAEpC,QAAI,CAAC,SAAS,QAAQ;AACpB,eAASE,KAAI,GAAGA,MAAK,qBAAqB,kBAAkB,UAAU,IAAIA,MAAK;AAC7E,eAAO,cAAc,YAAY,cAAc,WAAW,EAAE,YAAY,MAAM;AAC5E;AAAA,QACF;AACA,sBAAc,YAAY,SAAS,KAAK,aAAa;AAAA,MACvD;AAAA,IACF;AAEA,SAAK,UAAU,SAAUD,eAAc,eAAe;AACpD,UAAIF,cAAa,cAAcE,aAAY;AAE3C,UAAI,8BAA8B,WAAW,QAAQ,MAAM;AACzD,QAAAF,YAAW,OAAO,WAAW;AAAA,MAC/B;AACA,eAAS,SAASA,aAAY,UAAU,GAAGC,WAAU,aAAa;AAClE,UAAID,YAAW,QAAQ,QAAQ,mBAAmB;AAChD,YAAI,wBAAwB,kBAAkB,aAAa;AAC3D,SAAC,SAAS,qBAAqB,MAAM,wBAAwB;AAAA,UAC3D,MAAM;AAAA,QACR;AACA,QAAAA,YAAW,OAAOA,YAAW,cAAc,sBAAsB;AACjE,QAAAA,YAAW,iBAAiB,sBAAsB;AAAA,MACpD;AAEA,6BAAuB,SAASA,YAAW,WAAW,mBAAmB;AAAA,IAC3E,CAAC;AAAA,EACH,CAAC;AACD,WAAS,SAASA,aAAYC,WAAU,eAAe;AACrD,QAAI,kBAAkB,IAAIA,SAAQ,KAAK,MAAM;AAC3C,MAAAD,YAAW,UAAUC,SAAQ,IAAI;AAAA,IACnC,OAAO;AACL,MAAAD,YAAW,WAAWC;AACtB,MAAAD,YAAW,gBAAgB;AAC3B,sBAAgB,IAAIC,WAAU,IAAI;AAAA,IACpC;AAAA,EACF;AAEA,MAAI,gBAAgB,IAAI;AACxB,MAAI,qBAAqB,IAAI;AAC7B,MAAI,WAAW,sBAAsB;AACrC,uBAAqB,gBAAgB,sBAAsB,IAAI;AAC/D,MAAI,QAAQ,iBAAiB;AAC7B,WAAS,0BAA0BD,aAAY;AAC7C,QAAIA,YAAW,QAAQ,MAAM;AAE3B,MAAAA,YAAW,OAAOA,YAAW;AAAA,IAC/B;AAAA,EACF;AAEA,MAAI,CAAC,sBAAsB;AACzB,aAAS,eAAe,GAAG,eAAe,UAAU,gBAAgB;AAClE,UAAI,aAAa,cAAc,YAAY;AAC3C,UAAI,WAAW,WAAW;AAC1B,UAAI,YAAY,MAAM;AAEpB,mBAAW,WAAW,gBAAgB,OAAO,iBAAiB,QAAQ;AACtE,mBAAW,gBAAgB;AAE3B,YAAI,CAAC,iBAAiB,sBAAsB,GAAG;AAC7C,qBAAW,eAAe;AAAA,QAC5B;AACA;AAAA,MACF;AACA,gCAA0B,UAAU;AACpC,UAAI,WAAW,QAAQ,SAAS,aAAa,QAAQ,YAAY,MAAM,WAAW,QAa/E,WAAW,iBAAiB,WAAW,UAAU,YAAY,QAAQ,WAAW,UAAU,cAAc,QAAQ;AACjH,mBAAW,OAAO;AAAA,MACpB;AAAA,IACF;AAAA,EACF,OAAO;AACL,SAAK,YAAY,SAAUA,aAAY;AAErC,gCAA0BA,WAAU;AAAA,IACtC,CAAC;AAGD,eAAW,KAAK,SAAU,OAAO,OAAO;AACtC,aAAO,MAAM,gBAAgB,MAAM;AAAA,IACrC,CAAC;AAAA,EACH;AACA,oBAAkB,UAAU;AAC5B,SAAO,IAAI,iBAAiB;AAAA,IAC1B;AAAA,IACA,YAAY;AAAA,IACZ,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,EACpB,CAAC;AACH;AACA,SAAS,kBAAkB,QAAQ;AACjC,MAAI,iBAAiB,cAAc;AACnC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,MAAM,OAAO,CAAC;AAClB,QAAI,kBAAkB,IAAI;AAC1B,QAAI,QAAQ,eAAe,IAAI,eAAe,KAAK;AACnD,QAAI,QAAQ,GAAG;AAEb,UAAI,OAAO,mBAAmB,QAAQ;AAAA,IACxC;AACA;AACA,mBAAe,IAAI,iBAAiB,KAAK;AAAA,EAC3C;AACF;AAWA,SAAS,YAAY,QAAQ,SAAS,SAAS,aAAa;AAG1D,MAAI,WAAW,KAAK,IAAI,OAAO,2BAA2B,GAAG,QAAQ,QAAQ,QAAQ,QAAQ,eAAe,CAAC;AAC7G,OAAK,SAAS,SAAU,YAAY;AAClC,QAAI;AACJ,QAAI,SAAS,UAAU,MAAM,oBAAoB,WAAW,UAAU;AACpE,iBAAW,KAAK,IAAI,UAAU,kBAAkB,MAAM;AAAA,IACxD;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,gBAAgB,MAAMI,MAAK,UAAU;AAC5C,MAAI,YAAYA,KAAI,OAAO,IAAI,GAAG;AAChC,QAAI,IAAI;AACR,WAAOA,KAAI,OAAO,OAAO,CAAC,GAAG;AAC3B;AAAA,IACF;AACA,YAAQ;AAAA,EACV;AACA,EAAAA,KAAI,IAAI,MAAM,IAAI;AAClB,SAAO;AACT;;;ACzPA,IAAI;AAAA;AAAA,EAA4B,2BAAY;AAC1C,aAASC,cAAa,cAAc;AAClC,WAAK,eAAe,CAAC;AACrB,WAAK,UAAU,cAAc;AAC7B,WAAK,kBAAkB,cAAc;AACrC,WAAK,eAAe;AAAA,IACtB;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACK,SAAS,wBAAwB,aAAa;AACnD,MAAI,eAAe,YAAY,IAAI,kBAAkB;AACrD,MAAI,SAAS,IAAI,aAAa,YAAY;AAC1C,MAAI,QAAQ,SAAS,YAAY;AACjC,MAAI,OAAO;AACT,UAAM,aAAa,QAAQ,OAAO,SAAS,OAAO,eAAe;AACjE,WAAO;AAAA,EACT;AACF;AACA,IAAI,WAAW;AAAA,EACb,aAAa,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AACpE,QAAI,aAAa,YAAY,uBAAuB,SAAS,gBAAgB,EAAE,OAAO,CAAC;AACvF,QAAI,aAAa,YAAY,uBAAuB,SAAS,gBAAgB,EAAE,OAAO,CAAC;AACvF,QAAI,MAAuC;AACzC,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,YAAY,SAAS,YAAY,IAAI,YAAY,GAAG,YAAY,IAAI,SAAS,GAAG,CAAC,IAAI,aAAa;AAAA,MACpH;AACA,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,YAAY,SAAS,YAAY,IAAI,YAAY,GAAG,YAAY,IAAI,SAAS,GAAG,CAAC,IAAI,aAAa;AAAA,MACpH;AAAA,IACF;AACA,WAAO,eAAe,CAAC,KAAK,GAAG;AAC/B,YAAQ,IAAI,KAAK,UAAU;AAC3B,YAAQ,IAAI,KAAK,UAAU;AAC3B,QAAI,WAAW,UAAU,GAAG;AAC1B,sBAAgB,IAAI,KAAK,UAAU;AACnC,aAAO,wBAAwB;AAAA,IACjC;AACA,QAAI,WAAW,UAAU,GAAG;AAC1B,sBAAgB,IAAI,KAAK,UAAU;AACnC,aAAO,yBAAyB,SAAS,OAAO,wBAAwB;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,YAAY,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AACnE,QAAI,kBAAkB,YAAY,uBAAuB,cAAc,gBAAgB,EAAE,OAAO,CAAC;AACjG,QAAI,MAAuC;AACzC,UAAI,CAAC,iBAAiB;AACpB,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AAAA,IACF;AACA,WAAO,eAAe,CAAC,QAAQ;AAC/B,YAAQ,IAAI,UAAU,eAAe;AACrC,QAAI,WAAW,eAAe,GAAG;AAC/B,sBAAgB,IAAI,UAAU,eAAe;AAC7C,aAAO,wBAAwB;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AAC9D,QAAI,aAAa,YAAY,uBAAuB,SAAS,gBAAgB,EAAE,OAAO,CAAC;AACvF,QAAI,kBAAkB,WAAW,cAAc,YAAY;AAC3D,QAAI,iBAAiB,WAAW,cAAc,WAAW;AACzD,QAAI,MAAuC;AACzC,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,4BAA4B;AAAA,MAC9C;AACA,UAAI,CAAC,iBAAiB;AACpB,cAAM,IAAI,MAAM,6BAA6B;AAAA,MAC/C;AAAA,IACF;AACA,WAAO,eAAe,CAAC,UAAU,OAAO;AACxC,YAAQ,IAAI,UAAU,eAAe;AACrC,YAAQ,IAAI,SAAS,cAAc;AACnC,QAAI,WAAW,eAAe,GAAG;AAC/B,sBAAgB,IAAI,UAAU,eAAe;AAC7C,aAAO,wBAAwB;AAAA,IACjC;AACA,QAAI,WAAW,cAAc,GAAG;AAC9B,sBAAgB,IAAI,SAAS,cAAc;AAC3C,aAAO,yBAAyB,SAAS,OAAO,wBAAwB;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,KAAK,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AAC5D,WAAO,eAAe,CAAC,OAAO,KAAK;AAAA,EACrC;AAAA,EACA,UAAU,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AACjE,QAAI,UAAU,YAAY;AAC1B,QAAI,gBAAgB,QAAQ,aAAa,YAAY,YAAY,IAAI,eAAe,CAAC;AACrF,QAAI,eAAe,OAAO,eAAe,cAAc,WAAW,MAAM;AACxE,SAAK,cAAc,mBAAmB,SAAU,WAAW,OAAO;AAChE,UAAI,YAAY,QAAQ,aAAa,gBAAgB,SAAS;AAC9D,UAAI,UAAU,aAAa,KAAK;AAChC,cAAQ,IAAI,SAAS,SAAS;AAC9B,UAAI,WAAW,SAAS,GAAG;AACzB,wBAAgB,IAAI,SAAS,SAAS;AACtC,YAAI,OAAO,yBAAyB,MAAM;AACxC,iBAAO,wBAAwB;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,WAAW,WAAW;AAC7B,SAAO,UAAU,IAAI,MAAM,MAAM;AACnC;;;ACxHA,SAAS,mBAAmB,aAAa,cAAc;AACrD,MAAI,eAAe,YAAY,IAAI,kBAAkB;AACrD,MAAI,qBAAqB,yBAAiB,IAAI,YAAY;AAC1D,MAAI;AACJ,MAAI,gBAAgB,aAAa,cAAc;AAC7C,sBAAyB,IAAI,aAAa,cAAc,SAAU,KAAK;AACrE,UAAI,UAAU;AAAA,QACZ,MAAM;AAAA,MACR;AACA,UAAI,YAAY,aAAa,QAAQ,IAAI,GAAG;AAC5C,UAAI,WAAW;AACb,YAAI,WAAW,UAAU,IAAI,MAAM;AACnC,gBAAQ,OAAO,uBAAuB,QAAQ;AAAA,MAChD;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,CAAC,iBAAiB;AAEpB,sBAAkB,uBAAuB,mBAAmB,oBAAoB,mBAAmB,kBAAkB,IAAI,mBAAmB,WAAW,MAAM,MAAM,CAAC,KAAK,GAAG;AAAA,EAC9K;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,aAAa,uBAAuB,cAAc;AAC3E,MAAI;AACJ,MAAI;AACJ,kBAAuB,KAAK,aAAa,SAAU,SAAS,UAAU;AACpE,QAAI,WAAW,QAAQ;AACvB,QAAI,oBAAoB,aAAa,gBAAgB,IAAI,QAAQ;AACjE,QAAI,mBAAmB;AACrB,UAAI,yBAAyB,MAAM;AACjC,gCAAwB;AAAA,MAC1B;AACA,cAAQ,cAAc,kBAAkB,eAAe;AACvD,UAAI,uBAAuB;AACzB,gBAAQ,wBAAwB;AAAA,MAClC;AAAA,IACF;AACA,QAAI,QAAQ,UAAU,YAAY,MAAM;AACtC,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,MAAI,CAAC,iBAAiB,yBAAyB,MAAM;AACnD,gBAAY,qBAAqB,EAAE,UAAU,WAAW;AAAA,EAC1D;AACA,SAAO;AACT;AAKA,SAAS,iBAAiB,WAAW,aAAa,KAAK;AACrD,QAAM,OAAO,CAAC;AACd,MAAI,gBAAgB,YAAY,iBAAiB;AACjD,MAAI;AACJ,MAAI,mBAAmB;AACvB,MAAI,WAAW;AACb,uBAAmB;AACnB,aAAS,iCAAiC,SAAS;AAAA,EACrD,OAAO;AACL,aAAS,cAAc,UAAU;AAEjC,uBAAmB,OAAO,iBAAiB;AAAA,EAC7C;AACA,MAAI,eAAe,wBAAwB,WAAW;AACtD,MAAI,kBAAkB,mBAAmB,aAAa,YAAY;AAClE,MAAI,qBAAqB,IAAI;AAC7B,MAAI,kBAAyB,WAAW,kBAAkB,IAAI,qBAAqB,qBAA4B,MAAM,iCAAiC,iBAAiB,WAAW,IAAI;AACtL,MAAI,yBAAyB;AAAA,IAC3B,iBAAiB;AAAA,IACjB,eAAe,IAAI;AAAA,IACnB,cAAc,YAAY,UAAU;AAAA,IACpC;AAAA,IACA,yBAAyB,CAAC;AAAA,EAC5B;AACA,MAAI,SAAS,wBAAwB,QAAQ,sBAAsB;AACnE,MAAI,wBAAwB,kBAAkB,OAAO,YAAY,IAAI,uBAAuB,YAAY;AACxG,MAAI,QAAQ,CAAC,mBAAmB,cAAc,mBAAmB,MAAM,IAAI;AAC3E,MAAI,uBAAuB,gBAAgB,aAAa;AAAA,IACtD;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,OAAO,IAAI,mBAAW,QAAQ,WAAW;AAC7C,OAAK,mBAAmB,oBAAoB;AAC5C,MAAI,iBAAiB,yBAAyB,QAAQ,0BAA0B,MAAM,IAAI,SAAU,SAAS,SAAS,WAAW,UAAU;AAEzI,WAAO,aAAa,wBAAwB,YAAY,KAAK,sBAAsB,SAAS,SAAS,WAAW,QAAQ;AAAA,EAC1H,IAAI;AACJ,OAAK,gBAAgB;AACrB,OAAK;AAAA;AAAA,IAEL,mBAAmB,SAAS;AAAA,IAAO;AAAA,IAAM;AAAA,EAAc;AACvD,SAAO;AACT;AACA,SAAS,0BAA0B,QAAQ;AACzC,MAAI,OAAO,iBAAiB,wBAAwB;AAClD,QAAI,aAAa,iBAAiB,OAAO,QAAQ,CAAC,CAAC;AACnD,WAAO,CAAQ,QAAQ,iBAAiB,UAAU,CAAC;AAAA,EACrD;AACF;AACA,SAAS,iBAAiB,KAAK;AAC7B,MAAI,IAAI;AACR,SAAO,IAAI,IAAI,UAAU,IAAI,CAAC,KAAK,MAAM;AACvC;AAAA,EACF;AACA,SAAO,IAAI,CAAC;AACd;AACA,IAAO,2BAAQ;", "names": ["resultItem", "coordDim", "resultDimIdx", "i", "map", "CoordSysInfo"]}