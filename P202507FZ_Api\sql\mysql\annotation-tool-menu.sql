-- 标注工具菜单 SQL
-- 添加标注工具菜单项到系统中

-- 插入工具目录菜单（如果不存在）
INSERT INTO system_menu (
    name, permission, type, sort, parent_id,
    path, icon, component, component_name, status, 
    visible, keep_alive, always_show, creator, create_time
) 
SELECT '工具管理', '', 1, 999, 0, '/tools', 'ep:tools', NULL, NULL, 0, 
       b'1', b'1', b'1', 'admin', NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM system_menu WHERE name = '工具管理' AND parent_id = 0
);

-- 获取工具管理目录的ID
SET @tools_parent_id = (SELECT id FROM system_menu WHERE name = '工具管理' AND parent_id = 0 LIMIT 1);

-- 插入标注工具菜单
INSERT INTO system_menu (
    name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator, create_time
) VALUES (
    '标注工具', 'tools:annotation:query', 2, 1, @tools_parent_id,
    'annotation', 'ep:edit', 'tools/annotation/index', 'ToolsAnnotation', 0,
    b'1', b'1', b'1', 'admin', NOW()
);

-- 获取标注工具菜单的ID
SET @annotation_menu_id = LAST_INSERT_ID();

-- 插入标注工具相关的按钮权限
INSERT INTO system_menu (
    name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator, create_time
) VALUES 
(
    '标注工具查询', 'tools:annotation:query', 3, 1, @annotation_menu_id,
    '', '', '', NULL, 0,
    b'1', b'1', b'1', 'admin', NOW()
),
(
    '标注工具访问', 'tools:annotation:access', 3, 2, @annotation_menu_id,
    '', '', '', NULL, 0,
    b'1', b'1', b'1', 'admin', NOW()
);

-- 提示信息
SELECT '标注工具菜单添加完成！' as message;
SELECT '请在系统管理 -> 菜单管理中查看新添加的菜单项' as note;
SELECT '菜单路径: 工具管理 -> 标注工具' as menu_path;
