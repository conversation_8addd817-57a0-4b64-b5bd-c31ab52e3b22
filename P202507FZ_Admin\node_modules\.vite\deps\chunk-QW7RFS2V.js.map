{"version": 3, "sources": ["../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/node_modules/element-plus/es/components/autocomplete/style/css.mjs"], "sourcesContent": ["import '../../base/style/css.mjs';\nimport 'element-plus/theme-chalk/el-autocomplete.css';\nimport '../../input/style/css.mjs';\nimport '../../scrollbar/style/css.mjs';\nimport '../../popper/style/css.mjs';\n//# sourceMappingURL=css.mjs.map\n"], "mappings": ";AACA,OAAO;", "names": []}