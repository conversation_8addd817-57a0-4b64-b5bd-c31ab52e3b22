import {
  some
} from "./chunk-YTJ5ESGD.js";

// node_modules/.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/util/ModelUtil.js
function is(element, type) {
  var bo = getBusinessObject(element);
  return bo && typeof bo.$instanceOf === "function" && bo.$instanceOf(type);
}
function isAny(element, types) {
  return some(types, function(t) {
    return is(element, t);
  });
}
function getBusinessObject(element) {
  return element && element.businessObject || element;
}
function getDi(element) {
  return element && element.di;
}

export {
  is,
  isAny,
  getBusinessObject,
  getDi
};
//# sourceMappingURL=chunk-FNF472WR.js.map
