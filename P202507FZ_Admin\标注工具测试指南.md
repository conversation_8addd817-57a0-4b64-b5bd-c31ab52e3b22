# 标注工具测试指南

## 快速测试步骤

### 1. 数据库配置
执行以下SQL脚本添加菜单：
```sql
-- 在MySQL中执行
source P202507FZ_Api/sql/mysql/annotation-tool-menu.sql;
```

### 2. 重启服务
重启后端Spring Boot应用以刷新菜单缓存。

### 3. 权限配置
1. 登录管理后台
2. 进入"系统管理" -> "角色管理"
3. 编辑管理员角色，在菜单权限中勾选：
   - 工具管理
   - 标注工具
   - 标注工具查询
   - 标注工具访问

### 4. 访问测试
1. 刷新浏览器页面
2. 在左侧菜单中应该能看到"工具管理"目录
3. 展开后点击"标注工具"
4. 页面应该显示iframe并尝试加载外部系统

## 预期结果

### 成功情况
- 左侧菜单显示"工具管理" -> "标注工具"
- 点击后页面正常加载
- iframe显示外部标注工具界面
- 页面头部显示"重新加载"和"新窗口打开"按钮

### 可能的问题

#### 1. 菜单不显示
**原因**: 权限未分配或缓存未刷新
**解决**: 
- 检查角色权限配置
- 重启后端服务
- 清除浏览器缓存

#### 2. iframe加载失败
**原因**: 外部系统不可访问或跨域限制
**解决**:
- 检查网络连接
- 确认外部系统 `http://***************:19003` 是否可访问
- 查看浏览器控制台错误信息

#### 3. 页面空白
**原因**: 组件路径配置错误
**解决**:
- 确认文件路径 `P202507FZ_Admin/src/views/tools/annotation/index.vue` 存在
- 检查组件名称 `ToolsAnnotation` 是否正确

## 功能验证清单

- [ ] 菜单项正确显示
- [ ] 页面能够正常访问
- [ ] iframe开始加载（显示加载动画）
- [ ] 外部系统正常显示（如果网络可达）
- [ ] "重新加载"按钮功能正常
- [ ] "新窗口打开"按钮功能正常
- [ ] 错误处理正常（如果外部系统不可达）
- [ ] 响应式设计在移动端正常

## 调试信息

### 浏览器控制台
正常情况下应该看到：
```
标注工具加载成功
```

错误情况下可能看到：
```
标注工具加载失败
标注工具加载超时
```

### 网络检查
可以通过以下方式检查外部系统：
```bash
curl -I http://***************:19003
```

### 数据库验证
检查菜单是否正确插入：
```sql
SELECT id, name, path, component, parent_id 
FROM system_menu 
WHERE name IN ('工具管理', '标注工具') 
ORDER BY parent_id, sort;
```

## 自定义配置

### 修改外部系统URL
编辑文件：`P202507FZ_Admin/src/views/tools/annotation/index.vue`
```javascript
// 修改这一行
const annotationUrl = 'http://your-annotation-system-url'
```

### 修改菜单图标
在SQL脚本中修改icon字段：
```sql
-- 工具管理目录图标
'ep:tools'

-- 标注工具菜单图标  
'ep:edit'
```

### 调整超时时间
在Vue组件中修改：
```javascript
// 当前是15秒，可以根据需要调整
}, 15000) // 15秒超时
```

## 联系支持
如果遇到问题，请提供：
1. 浏览器控制台错误信息
2. 网络请求状态
3. 数据库菜单数据
4. 后端服务日志
