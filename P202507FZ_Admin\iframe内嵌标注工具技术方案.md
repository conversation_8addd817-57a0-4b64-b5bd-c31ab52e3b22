# iframe内嵌标注工具技术方案

## 🎯 解决方案概述

通过Vite代理服务器来绕过CSRF验证，实现标注工具在iframe中的正常使用。

## 🔧 技术实现

### 1. Vite代理配置 (vite.config.ts)

```typescript
proxy: {
  '/annotation-tool': {
    target: 'http://***************:19003',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/annotation-tool/, ''),
    configure: (proxy, options) => {
      proxy.on('proxyReq', (proxyReq, req, res) => {
        // 设置请求头，绕过CSRF检查
        proxyReq.setHeader('Referer', 'http://***************:19003/');
        proxyReq.setHeader('Origin', 'http://***************:19003');
        
        // 转发cookie
        if (req.headers.cookie) {
          proxyReq.setHeader('Cookie', req.headers.cookie);
        }
      });
      
      proxy.on('proxyRes', (proxyRes, req, res) => {
        // 移除X-Frame-Options限制
        delete proxyRes.headers['x-frame-options'];
        proxyRes.headers['x-frame-options'] = 'SAMEORIGIN';
        
        // 修改cookie的SameSite属性
        const setCookie = proxyRes.headers['set-cookie'];
        if (setCookie) {
          proxyRes.headers['set-cookie'] = setCookie.map(cookie => {
            return cookie.replace(/SameSite=\w+/gi, 'SameSite=None')
                         .replace(/Secure/gi, '');
          });
        }
      });
    }
  }
}
```

### 2. 前端页面实现

**关键特性：**
- 使用代理路径 `/annotation-tool` 而不是直接URL
- 连接状态指示器
- 刷新和新窗口打开功能
- 简洁的工具栏界面

**核心代码：**
```vue
<iframe
  ref="annotationFrame"
  src="/annotation-tool"
  style="width: 100%; height: calc(100vh - 160px);"
  @load="onFrameLoad"
  @error="onFrameError"
/>
```

## 🛡️ 安全机制

### CSRF绕过原理
1. **同源请求**: 代理使请求看起来来自同一域名
2. **正确的Referer**: 设置为目标系统的域名
3. **Cookie转发**: 自动转发认证cookie
4. **Origin伪装**: 设置正确的Origin头

### 安全考虑
- 仅在开发环境使用此代理
- 生产环境建议外部系统配置正确的CORS和CSRF策略
- 代理仅转发必要的请求头

## 📊 工作流程

```mermaid
sequenceDiagram
    participant Browser as 浏览器
    participant Vite as Vite代理
    participant External as 外部标注系统
    
    Browser->>Vite: 请求 /annotation-tool
    Vite->>Vite: 重写请求头 (Referer, Origin)
    Vite->>External: 转发到 ***************:19003
    External->>Vite: 返回响应 + Set-Cookie
    Vite->>Vite: 修改响应头 (X-Frame-Options, SameSite)
    Vite->>Browser: 返回修改后的响应
    Browser->>Browser: 在iframe中正常显示
```

## 🔍 故障排除

### 常见问题

**1. 代理不生效**
- 确认已重启开发服务器
- 检查vite.config.ts语法是否正确
- 查看控制台是否有代理错误信息

**2. 仍然有CSRF错误**
- 检查Network标签，确认请求路径为 `/annotation-tool/...`
- 确认Referer和Origin头是否正确设置
- 检查cookie是否正确转发

**3. iframe显示空白**
- 检查外部系统是否正常运行
- 确认网络连接是否正常
- 查看浏览器控制台错误信息

### 调试方法

**1. 检查代理请求**
```javascript
// 在浏览器控制台执行
fetch('/annotation-tool/api/test')
  .then(response => console.log('代理工作正常'))
  .catch(error => console.error('代理失败:', error))
```

**2. 查看请求头**
在Network标签中检查：
- Request Headers中的Referer和Origin
- Response Headers中的X-Frame-Options
- Cookie的设置和转发

## 📈 性能优化

### 缓存策略
- 静态资源通过代理缓存
- API请求保持实时性
- 合理设置超时时间

### 连接管理
- 自动重连机制
- 连接状态监控
- 错误恢复处理

## 🚀 部署建议

### 开发环境
- 使用当前的代理方案
- 便于调试和开发

### 生产环境
建议外部系统配置：
```python
# Django settings.py
CSRF_TRUSTED_ORIGINS = ['https://your-admin-domain.com']
CORS_ALLOWED_ORIGINS = ['https://your-admin-domain.com']
X_FRAME_OPTIONS = 'SAMEORIGIN'
```

## 📋 检查清单

部署前确认：
- [ ] vite.config.ts代理配置正确
- [ ] 开发服务器已重启
- [ ] iframe页面路径使用 `/annotation-tool`
- [ ] 连接状态指示器工作正常
- [ ] 可以正常登录和使用标注功能
- [ ] 浏览器控制台无CSRF错误

## 🔄 更新记录

- **v1.0**: 初始实现，基本的代理转发
- **v1.1**: 添加CSRF绕过机制
- **v1.2**: 优化cookie处理和安全头设置
- **v1.3**: 添加连接状态监控和错误处理

## 📞 技术支持

如遇问题，请提供：
1. 浏览器控制台完整错误信息
2. Network标签的请求响应详情
3. 开发服务器启动日志
4. 外部系统的错误日志（如可获取）
