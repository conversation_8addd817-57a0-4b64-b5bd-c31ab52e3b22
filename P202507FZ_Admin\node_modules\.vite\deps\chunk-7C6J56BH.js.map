{"version": 3, "sources": ["../../.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/util/DiUtil.js"], "sourcesContent": ["import {\n  is,\n  getBusinessObject,\n  getDi\n} from './ModelUtil';\n\nimport {\n  some\n} from 'min-dash';\n\n/**\n * @typedef {import('../model/Types').Element} Element\n * @typedef {import('../model/Types').ModdleElement} ModdleElement\n */\n\n/**\n * @param {Element} element\n * @param {ModdleElement} [di]\n *\n * @return {boolean}\n */\nexport function isExpanded(element, di) {\n\n  if (is(element, 'bpmn:CallActivity')) {\n    return false;\n  }\n\n  if (is(element, 'bpmn:SubProcess')) {\n    di = di || getDi(element);\n\n    if (di && is(di, 'bpmndi:BPMNPlane')) {\n      return true;\n    }\n\n    return di && !!di.isExpanded;\n  }\n\n  if (is(element, 'bpmn:Participant')) {\n    return !!getBusinessObject(element).processRef;\n  }\n\n  return true;\n}\n\n/**\n * @param {Element} element\n *\n * @return {boolean}\n */\nexport function isHorizontal(element) {\n\n  if (!is(element, 'bpmn:Participant') && !is(element, 'bpmn:Lane')) {\n    return undefined;\n  }\n\n  var isHorizontal = getDi(element).isHorizontal;\n\n  if (isHorizontal === undefined) {\n    return true;\n  }\n\n  return isHorizontal;\n}\n\n/**\n * @param {Element} element\n *\n * @return {boolean}\n */\nexport function isInterrupting(element) {\n  return element && getBusinessObject(element).isInterrupting !== false;\n}\n\n/**\n * @param {Element} element\n *\n * @return {boolean}\n */\nexport function isEventSubProcess(element) {\n  return element && !!getBusinessObject(element).triggeredByEvent;\n}\n\n/**\n * @param {Element} element\n * @param {string} eventType\n *\n * @return {boolean}\n */\nexport function hasEventDefinition(element, eventType) {\n  var eventDefinitions = getBusinessObject(element).eventDefinitions;\n\n  return some(eventDefinitions, function(event) {\n    return is(event, eventType);\n  });\n}\n\n/**\n * @param {Element} element\n *\n * @return {boolean}\n */\nexport function hasErrorEventDefinition(element) {\n  return hasEventDefinition(element, 'bpmn:ErrorEventDefinition');\n}\n\n/**\n * @param {Element} element\n *\n * @return {boolean}\n */\nexport function hasEscalationEventDefinition(element) {\n  return hasEventDefinition(element, 'bpmn:EscalationEventDefinition');\n}\n\n/**\n * @param {Element} element\n *\n * @return {boolean}\n */\nexport function hasCompensateEventDefinition(element) {\n  return hasEventDefinition(element, 'bpmn:CompensateEventDefinition');\n}\n"], "mappings": ";;;;;;;;;;AAqBO,SAAS,WAAW,SAAS,IAAI;AAEtC,MAAI,GAAG,SAAS,mBAAmB,GAAG;AACpC,WAAO;AAAA,EACT;AAEA,MAAI,GAAG,SAAS,iBAAiB,GAAG;AAClC,SAAK,MAAM,MAAM,OAAO;AAExB,QAAI,MAAM,GAAG,IAAI,kBAAkB,GAAG;AACpC,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,CAAC,CAAC,GAAG;AAAA,EACpB;AAEA,MAAI,GAAG,SAAS,kBAAkB,GAAG;AACnC,WAAO,CAAC,CAAC,kBAAkB,OAAO,EAAE;AAAA,EACtC;AAEA,SAAO;AACT;AAOO,SAAS,aAAa,SAAS;AAEpC,MAAI,CAAC,GAAG,SAAS,kBAAkB,KAAK,CAAC,GAAG,SAAS,WAAW,GAAG;AACjE,WAAO;AAAA,EACT;AAEA,MAAIA,gBAAe,MAAM,OAAO,EAAE;AAElC,MAAIA,kBAAiB,QAAW;AAC9B,WAAO;AAAA,EACT;AAEA,SAAOA;AACT;AAOO,SAAS,eAAe,SAAS;AACtC,SAAO,WAAW,kBAAkB,OAAO,EAAE,mBAAmB;AAClE;AAOO,SAAS,kBAAkB,SAAS;AACzC,SAAO,WAAW,CAAC,CAAC,kBAAkB,OAAO,EAAE;AACjD;AAQO,SAAS,mBAAmB,SAAS,WAAW;AACrD,MAAI,mBAAmB,kBAAkB,OAAO,EAAE;AAElD,SAAO,KAAK,kBAAkB,SAAS,OAAO;AAC5C,WAAO,GAAG,OAAO,SAAS;AAAA,EAC5B,CAAC;AACH;AAOO,SAAS,wBAAwB,SAAS;AAC/C,SAAO,mBAAmB,SAAS,2BAA2B;AAChE;AAOO,SAAS,6BAA6B,SAAS;AACpD,SAAO,mBAAmB,SAAS,gCAAgC;AACrE;AAOO,SAAS,6BAA6B,SAAS;AACpD,SAAO,mBAAmB,SAAS,gCAAgC;AACrE;", "names": ["isHorizontal"]}