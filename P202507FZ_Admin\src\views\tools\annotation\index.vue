<template>
  <ContentWrap>
    <div class="annotation-tool-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>标注工具</h2>
        <p class="page-description">数据标注和处理工具</p>
        <div class="header-actions">
          <el-button type="primary" @click="reloadIframe" :loading="loading">
            <Icon icon="ep:refresh" class="mr-5px" />
            重新加载
          </el-button>
          <el-button type="success" @click="openInNewTab">
            <Icon icon="ep:link" class="mr-5px" />
            新窗口打开
          </el-button>
        </div>
      </div>

      <!-- 连接状态提示 -->
      <div v-if="!loading && !error" class="connection-info">
        <el-alert
          title="连接信息"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>正在连接到标注工具: <strong>{{ annotationUrl }}</strong></p>
            <p>如果长时间无法加载，请尝试以下解决方案：</p>
            <ul>
              <li>检查网络连接是否正常</li>
              <li>确认标注工具服务是否运行</li>
              <li>使用"新窗口打开"功能直接访问</li>
            </ul>
          </template>
        </el-alert>
      </div>

      <!-- 嵌套的iframe -->
      <div class="iframe-container" v-loading="loading" element-loading-text="正在加载标注工具...">
        <iframe
          ref="annotationIframe"
          :src="currentUrl"
          frameborder="0"
          width="100%"
          height="100%"
          @load="onIframeLoad"
          @error="onIframeError"
          allowfullscreen
          sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-top-navigation"
          referrerpolicy="no-referrer-when-downgrade"
        ></iframe>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-overlay">
        <el-result
          icon="warning"
          title="连接失败"
          :sub-title="errorMessage"
        >
          <template #extra>
            <div class="error-actions">
              <el-button type="primary" @click="reloadIframe" :loading="loading">
                <Icon icon="ep:refresh" class="mr-5px" />
                重新加载
              </el-button>
              <el-button type="success" @click="openInNewTab">
                <Icon icon="ep:link" class="mr-5px" />
                新窗口打开
              </el-button>
              <el-button @click="showTroubleshooting = !showTroubleshooting">
                <Icon icon="ep:question-filled" class="mr-5px" />
                故障排除
              </el-button>
            </div>

            <!-- 故障排除信息 -->
            <el-collapse v-if="showTroubleshooting" class="troubleshooting">
              <el-collapse-item title="常见问题解决方案" name="1">
                <div class="troubleshooting-content">
                  <h4>1. 跨域安全策略问题</h4>
                  <p>• 外部系统需要配置正确的CORS策略</p>
                  <p>• 建议使用HTTPS协议访问</p>

                  <h4>2. 网络连接问题</h4>
                  <p>• 检查 {{ annotationUrl }} 是否可访问</p>
                  <p>• 确认防火墙设置</p>

                  <h4>3. 浏览器安全限制</h4>
                  <p>• 尝试在新窗口中直接打开</p>
                  <p>• 检查浏览器是否阻止了不安全内容</p>
                </div>
              </el-collapse-item>
            </el-collapse>
          </template>
        </el-result>
      </div>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
defineOptions({ name: 'ToolsAnnotation' })

// 标注工具的URL
const annotationUrl = 'http://***************:19003'

// 响应式数据
const loading = ref(true)
const error = ref(false)
const showTroubleshooting = ref(false)
const errorMessage = ref('无法加载标注工具，请检查网络连接或联系管理员')
const annotationIframe = ref<HTMLIFrameElement>()

// 当前使用的URL（支持添加参数）
const currentUrl = computed(() => {
  return annotationUrl + '?embedded=true&t=' + Date.now()
})

/** iframe加载完成 */
const onIframeLoad = () => {
  loading.value = false
  error.value = false
  showTroubleshooting.value = false
  console.log('标注工具加载成功')
}

/** iframe加载错误 */
const onIframeError = (event?: Event) => {
  loading.value = false
  error.value = true

  // 根据错误类型设置不同的错误消息
  if (event) {
    errorMessage.value = '网络连接失败，无法访问标注工具服务'
  } else {
    errorMessage.value = '加载超时，标注工具服务可能暂时不可用'
  }

  console.error('标注工具加载失败:', event)
}

/** 重新加载iframe */
const reloadIframe = () => {
  if (annotationIframe.value) {
    loading.value = true
    error.value = false
    showTroubleshooting.value = false
    errorMessage.value = '无法加载标注工具，请检查网络连接或联系管理员'

    // 添加时间戳避免缓存
    const url = annotationUrl + '?embedded=true&reload=true&t=' + Date.now()
    annotationIframe.value.src = url

    console.log('重新加载标注工具:', url)
  }
}

/** 在新窗口打开 */
const openInNewTab = () => {
  window.open(annotationUrl, '_blank')
}

/** 组件挂载时的处理 */
onMounted(() => {
  // 设置iframe加载超时检测
  const timeout = setTimeout(() => {
    if (loading.value) {
      console.warn('标注工具加载超时')
      onIframeError()
    }
  }, 15000) // 15秒超时

  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearTimeout(timeout)
  })
})
</script>

<style scoped>
.annotation-tool-container {
  position: relative;
  height: calc(100vh - 200px);
  min-height: 600px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 24px;
  border-bottom: 1px solid #e8e9eb;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.connection-info {
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e8e9eb;
}

.connection-info ul {
  margin: 8px 0 0 20px;
  padding: 0;
}

.connection-info li {
  margin: 4px 0;
  color: #606266;
  font-size: 13px;
}

.iframe-container {
  position: relative;
  height: calc(100% - 100px);
  width: 100%;
  background: #f8f9fa;
}

.iframe-container iframe {
  display: block;
  border: none;
  background: #fff;
  transition: opacity 0.3s ease;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  z-index: 10;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 16px;
}

.troubleshooting {
  margin-top: 20px;
  text-align: left;
  max-width: 600px;
}

.troubleshooting-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.troubleshooting-content h4 {
  margin: 16px 0 8px 0;
  color: #409eff;
  font-size: 14px;
}

.troubleshooting-content h4:first-child {
  margin-top: 0;
}

.troubleshooting-content p {
  margin: 4px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .annotation-tool-container {
    height: calc(100vh - 160px);
    min-height: 500px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 20px;
  }

  .page-header h2 {
    font-size: 18px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .iframe-container {
    height: calc(100% - 120px);
  }
}

/* 加载动画优化 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.9);
}

:deep(.el-loading-spinner) {
  margin-top: -25px;
}

:deep(.el-loading-text) {
  color: #409eff;
  font-size: 14px;
  margin-top: 15px;
}
</style>
