<template>
  <ContentWrap :bodyStyle="{ padding: '0px' }" class="!mb-0">
    <iframe
      id="annotationFrame"
      src="http://***************:19003"
      style="width: 100%; height: 100vh"
      frameborder="0"
    >
    </iframe>
  </ContentWrap>
</template>

<script lang="ts" setup>
defineOptions({ name: 'ToolsAnnotation' })
</script>

<style scoped>
.annotation-tool-container {
  position: relative;
  height: calc(100vh - 200px);
  min-height: 600px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 24px;
  border-bottom: 1px solid #e8e9eb;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.connection-info {
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e8e9eb;
}

.connection-info ul {
  margin: 8px 0 0 20px;
  padding: 0;
}

.connection-info li {
  margin: 4px 0;
  color: #606266;
  font-size: 13px;
}

.iframe-container {
  position: relative;
  height: calc(100% - 100px);
  width: 100%;
  background: #f8f9fa;
}

.iframe-container iframe {
  display: block;
  border: none;
  background: #fff;
  transition: opacity 0.3s ease;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  z-index: 10;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 16px;
}

.troubleshooting {
  margin-top: 20px;
  text-align: left;
  max-width: 600px;
}

.troubleshooting-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.troubleshooting-content h4 {
  margin: 16px 0 8px 0;
  color: #409eff;
  font-size: 14px;
}

.troubleshooting-content h4:first-child {
  margin-top: 0;
}

.troubleshooting-content p {
  margin: 4px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .annotation-tool-container {
    height: calc(100vh - 160px);
    min-height: 500px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 20px;
  }

  .page-header h2 {
    font-size: 18px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .iframe-container {
    height: calc(100% - 120px);
  }
}

/* 加载动画优化 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.9);
}

:deep(.el-loading-spinner) {
  margin-top: -25px;
}

:deep(.el-loading-text) {
  color: #409eff;
  font-size: 14px;
  margin-top: 15px;
}
</style>
