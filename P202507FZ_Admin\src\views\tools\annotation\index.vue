<template>
  <ContentWrap>
    <div class="annotation-tool-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>标注工具</h2>
        <p class="page-description">数据标注和处理工具</p>
        <div class="header-actions">
          <el-button type="primary" @click="reloadIframe" :loading="loading">
            <Icon icon="ep:refresh" class="mr-5px" />
            重新加载
          </el-button>
          <el-button type="success" @click="openInNewTab">
            <Icon icon="ep:link" class="mr-5px" />
            新窗口打开
          </el-button>
        </div>
      </div>

      <!-- 嵌套的iframe -->
      <div class="iframe-container" v-loading="loading" element-loading-text="正在加载标注工具...">
        <iframe
          ref="annotationIframe"
          :src="annotationUrl"
          frameborder="0"
          width="100%"
          height="100%"
          @load="onIframeLoad"
          @error="onIframeError"
          allowfullscreen
          sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
        ></iframe>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-overlay">
        <el-result
          icon="warning"
          title="加载失败"
          sub-title="无法加载标注工具，请检查网络连接或联系管理员"
        >
          <template #extra>
            <el-button type="primary" @click="reloadIframe">重新加载</el-button>
            <el-button @click="openInNewTab">新窗口打开</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
defineOptions({ name: 'ToolsAnnotation' })

// 标注工具的URL
const annotationUrl = 'http://***************:19003'

// 响应式数据
const loading = ref(true)
const error = ref(false)
const annotationIframe = ref<HTMLIFrameElement>()

/** iframe加载完成 */
const onIframeLoad = () => {
  loading.value = false
  error.value = false
  console.log('标注工具加载成功')
}

/** iframe加载错误 */
const onIframeError = () => {
  loading.value = false
  error.value = true
  console.error('标注工具加载失败')
}

/** 重新加载iframe */
const reloadIframe = () => {
  if (annotationIframe.value) {
    loading.value = true
    error.value = false
    // 添加时间戳避免缓存
    const url = annotationUrl + '?t=' + Date.now()
    annotationIframe.value.src = url
  }
}

/** 在新窗口打开 */
const openInNewTab = () => {
  window.open(annotationUrl, '_blank')
}

/** 组件挂载时的处理 */
onMounted(() => {
  // 设置iframe加载超时检测
  const timeout = setTimeout(() => {
    if (loading.value) {
      console.warn('标注工具加载超时')
      onIframeError()
    }
  }, 15000) // 15秒超时

  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearTimeout(timeout)
  })
})
</script>

<style scoped>
.annotation-tool-container {
  position: relative;
  height: calc(100vh - 200px);
  min-height: 600px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 24px;
  border-bottom: 1px solid #e8e9eb;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.iframe-container {
  position: relative;
  height: calc(100% - 100px);
  width: 100%;
  background: #f8f9fa;
}

.iframe-container iframe {
  display: block;
  border: none;
  background: #fff;
  transition: opacity 0.3s ease;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .annotation-tool-container {
    height: calc(100vh - 160px);
    min-height: 500px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 20px;
  }

  .page-header h2 {
    font-size: 18px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .iframe-container {
    height: calc(100% - 120px);
  }
}

/* 加载动画优化 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.9);
}

:deep(.el-loading-spinner) {
  margin-top: -25px;
}

:deep(.el-loading-text) {
  color: #409eff;
  font-size: 14px;
  margin-top: 15px;
}
</style>
</template>
