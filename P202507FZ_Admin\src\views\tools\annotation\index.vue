<template>
  <ContentWrap :bodyStyle="{ padding: '0px' }" class="!mb-0">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h3>标注工具</h3>
        <span class="status-indicator" :class="{ 'connected': isConnected }">
          {{ isConnected ? '已连接' : '连接中...' }}
        </span>
      </div>
      <div class="toolbar-right">
        <el-button size="small" @click="reloadFrame">
          <Icon icon="ep:refresh" class="mr-5px" />
          刷新
        </el-button>
        <el-button size="small" @click="openInNewTab">
          <Icon icon="ep:link" class="mr-5px" />
          新窗口打开
        </el-button>
      </div>
    </div>

    <!-- iframe嵌套 -->
    <iframe
      ref="annotationFrame"
      :src="proxyUrl"
      style="width: 100%; height: calc(100vh - 160px); border: none;"
      frameborder="0"
      @load="onFrameLoad"
      @error="onFrameError"
    >
    </iframe>
  </ContentWrap>
</template>

<script lang="ts" setup>
defineOptions({ name: 'ToolsAnnotation' })

// 标注工具URL
const annotationUrl = 'http://***************:19003'
const proxyUrl = '/annotation-tool'  // 使用代理路径

// 响应式数据
const isConnected = ref(false)
const annotationFrame = ref<HTMLIFrameElement>()

// iframe加载完成
const onFrameLoad = () => {
  isConnected.value = true
  console.log('标注工具加载成功')
}

// iframe加载错误
const onFrameError = () => {
  isConnected.value = false
  console.error('标注工具加载失败')
}

// 刷新iframe
const reloadFrame = () => {
  if (annotationFrame.value) {
    isConnected.value = false
    annotationFrame.value.src = proxyUrl + '?t=' + Date.now()
  }
}

// 在新窗口打开标注工具
const openInNewTab = () => {
  window.open(annotationUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')
}

// 组件挂载时初始化
onMounted(() => {
  // 设置连接超时检测
  setTimeout(() => {
    if (!isConnected.value) {
      console.warn('标注工具连接超时')
    }
  }, 10000)
})
</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e8e9eb;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-left h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.status-indicator {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  background: #f56c6c;
  color: white;
  transition: all 0.3s;
}

.status-indicator.connected {
  background: #67c23a;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}
</style>