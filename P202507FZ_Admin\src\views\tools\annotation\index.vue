<template>
  <ContentWrap>
    <!-- 提示信息 -->
    <div class="csrf-notice">
      <el-alert
        title="标注工具访问提示"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>由于CSRF安全策略限制，建议在新窗口中打开标注工具以获得最佳体验。</p>
          <el-button type="primary" @click="openInNewTab" style="margin-top: 8px;">
            <Icon icon="ep:link" class="mr-5px" />
            在新窗口中打开标注工具
          </el-button>
        </template>
      </el-alert>
    </div>

    <!-- iframe嵌套 -->
    <div class="iframe-wrapper">
      <iframe
        id="annotationFrame"
        src="http://***************:19003"
        style="width: 100%; height: 70vh; border: 1px solid #dcdfe6; border-radius: 4px;"
        frameborder="0"
      >
      </iframe>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
defineOptions({ name: 'ToolsAnnotation' })

// 标注工具URL
const annotationUrl = 'http://***************:19003'

// 在新窗口打开标注工具
const openInNewTab = () => {
  window.open(annotationUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')
}
</script>

<style scoped>
.csrf-notice {
  margin-bottom: 16px;
}

.iframe-wrapper {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
}
</style>