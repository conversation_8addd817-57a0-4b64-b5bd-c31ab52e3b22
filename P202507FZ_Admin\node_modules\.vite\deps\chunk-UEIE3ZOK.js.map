{"version": 3, "sources": ["../../.pnpm/diagram-js@14.11.3/node_modules/diagram-js/lib/features/resize/ResizeUtil.js", "../../.pnpm/bpmn-js@17.11.1/node_modules/bpmn-js/lib/features/modeling/util/LaneUtil.js"], "sourcesContent": ["import {\n  filter,\n  isNumber\n} from 'min-dash';\n\nvar max = Math.max,\n    min = Math.min;\n\nvar DEFAULT_CHILD_BOX_PADDING = 20;\n\nimport {\n  getBBox\n} from '../../util/Elements';\n\nimport {\n  asTRBL,\n  asBounds\n} from '../../layout/LayoutUtil';\n\n/**\n * @typedef {import('../../core/Types').ElementLike} Element\n * @typedef {import('../../core/Types').ShapeLike} Shape\n *\n * @typedef {import('../../util/Types').Direction} Direction\n * @typedef {import('../../util/Types').Point} Point\n * @typedef {import('../../util/Types').Rect} Rect\n * @typedef {import('../../util/Types').RectTRBL} RectTRBL\n */\n\n/**\n * Substract a TRBL from another\n *\n * @param {RectTRBL} trblA\n * @param {RectTRBL} trblB\n *\n * @return {RectTRBL}\n */\nexport function substractTRBL(trblA, trblB) {\n  return {\n    top: trblA.top - trblB.top,\n    right: trblA.right - trblB.right,\n    bottom: trblA.bottom - trblB.bottom,\n    left: trblA.left - trblB.left\n  };\n}\n\n/**\n * Resize the given bounds by the specified delta from a given anchor point.\n *\n * @param {Rect} bounds the bounding box that should be resized\n * @param {Direction} direction in which the element is resized (nw, ne, se, sw)\n * @param {Point} delta of the resize operation\n *\n * @return {Rect} resized bounding box\n */\nexport function resizeBounds(bounds, direction, delta) {\n  var dx = delta.x,\n      dy = delta.y;\n\n  var newBounds = {\n    x: bounds.x,\n    y: bounds.y,\n    width: bounds.width,\n    height: bounds.height\n  };\n\n  if (direction.indexOf('n') !== -1) {\n    newBounds.y = bounds.y + dy;\n    newBounds.height = bounds.height - dy;\n  } else if (direction.indexOf('s') !== -1) {\n    newBounds.height = bounds.height + dy;\n  }\n\n  if (direction.indexOf('e') !== -1) {\n    newBounds.width = bounds.width + dx;\n  } else if (direction.indexOf('w') !== -1) {\n    newBounds.x = bounds.x + dx;\n    newBounds.width = bounds.width - dx;\n  }\n\n  return newBounds;\n}\n\n\n/**\n * Resize the given bounds by applying the passed\n * { top, right, bottom, left } delta.\n *\n * @param {Rect} bounds\n * @param {RectTRBL} resize\n *\n * @return {Rect}\n */\nexport function resizeTRBL(bounds, resize) {\n  return {\n    x: bounds.x + (resize.left || 0),\n    y: bounds.y + (resize.top || 0),\n    width: bounds.width - (resize.left || 0) + (resize.right || 0),\n    height: bounds.height - (resize.top || 0) + (resize.bottom || 0)\n  };\n}\n\n\nexport function reattachPoint(bounds, newBounds, point) {\n\n  var sx = bounds.width / newBounds.width,\n      sy = bounds.height / newBounds.height;\n\n  return {\n    x: Math.round((newBounds.x + newBounds.width / 2)) - Math.floor(((bounds.x + bounds.width / 2) - point.x) / sx),\n    y: Math.round((newBounds.y + newBounds.height / 2)) - Math.floor(((bounds.y + bounds.height / 2) - point.y) / sy)\n  };\n}\n\n\nfunction applyConstraints(attr, trbl, resizeConstraints) {\n\n  var value = trbl[attr],\n      minValue = resizeConstraints.min && resizeConstraints.min[attr],\n      maxValue = resizeConstraints.max && resizeConstraints.max[attr];\n\n  if (isNumber(minValue)) {\n    value = (/top|left/.test(attr) ? min : max)(value, minValue);\n  }\n\n  if (isNumber(maxValue)) {\n    value = (/top|left/.test(attr) ? max : min)(value, maxValue);\n  }\n\n  return value;\n}\n\nexport function ensureConstraints(currentBounds, resizeConstraints) {\n\n  if (!resizeConstraints) {\n    return currentBounds;\n  }\n\n  var currentTrbl = asTRBL(currentBounds);\n\n  return asBounds({\n    top: applyConstraints('top', currentTrbl, resizeConstraints),\n    right: applyConstraints('right', currentTrbl, resizeConstraints),\n    bottom: applyConstraints('bottom', currentTrbl, resizeConstraints),\n    left: applyConstraints('left', currentTrbl, resizeConstraints)\n  });\n}\n\n\nexport function getMinResizeBounds(direction, currentBounds, minDimensions, childrenBounds) {\n\n  var currentBox = asTRBL(currentBounds);\n\n  var minBox = {\n    top: /n/.test(direction) ? currentBox.bottom - minDimensions.height : currentBox.top,\n    left: /w/.test(direction) ? currentBox.right - minDimensions.width : currentBox.left,\n    bottom: /s/.test(direction) ? currentBox.top + minDimensions.height : currentBox.bottom,\n    right: /e/.test(direction) ? currentBox.left + minDimensions.width : currentBox.right\n  };\n\n  var childrenBox = childrenBounds ? asTRBL(childrenBounds) : minBox;\n\n  var combinedBox = {\n    top: min(minBox.top, childrenBox.top),\n    left: min(minBox.left, childrenBox.left),\n    bottom: max(minBox.bottom, childrenBox.bottom),\n    right: max(minBox.right, childrenBox.right)\n  };\n\n  return asBounds(combinedBox);\n}\n\nfunction asPadding(mayBePadding, defaultValue) {\n  if (typeof mayBePadding !== 'undefined') {\n    return mayBePadding;\n  } else {\n    return DEFAULT_CHILD_BOX_PADDING;\n  }\n}\n\nexport function addPadding(bbox, padding) {\n  var left, right, top, bottom;\n\n  if (typeof padding === 'object') {\n    left = asPadding(padding.left);\n    right = asPadding(padding.right);\n    top = asPadding(padding.top);\n    bottom = asPadding(padding.bottom);\n  } else {\n    left = right = top = bottom = asPadding(padding);\n  }\n\n  return {\n    x: bbox.x - left,\n    y: bbox.y - top,\n    width: bbox.width + left + right,\n    height: bbox.height + top + bottom\n  };\n}\n\n\n/**\n * Is the given element part of the resize\n * targets min boundary box?\n *\n * This is the default implementation which excludes\n * connections and labels.\n *\n * @param {Element} element\n */\nfunction isBBoxChild(element) {\n\n  // exclude connections\n  if (element.waypoints) {\n    return false;\n  }\n\n  // exclude labels\n  if (element.type === 'label') {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Return children bounding computed from a shapes children\n * or a list of prefiltered children.\n *\n * @param {Shape|Shape[]} shapeOrChildren\n * @param {RectTRBL|number} padding\n *\n * @return {Rect}\n */\nexport function computeChildrenBBox(shapeOrChildren, padding) {\n\n  var elements;\n\n  // compute based on shape\n  if (shapeOrChildren.length === undefined) {\n\n    // grab all the children that are part of the\n    // parents children box\n    elements = filter(shapeOrChildren.children, isBBoxChild);\n\n  } else {\n    elements = shapeOrChildren;\n  }\n\n  if (elements.length) {\n    return addPadding(getBBox(elements), padding);\n  }\n}\n", "import { is } from '../../../util/ModelUtil';\n\nimport {\n  getParent\n} from './ModelingUtil';\n\nimport {\n  isHorizontal\n} from '../../../util/DiUtil';\n\nimport {\n  asTRBL\n} from 'diagram-js/lib/layout/LayoutUtil';\n\nimport {\n  substractTRBL,\n  resizeTRBL\n} from 'diagram-js/lib/features/resize/ResizeUtil';\n\n/**\n * @typedef {import('../../../model/Types').Shape} Shape\n *\n * @typedef {import('diagram-js/lib/util/Types').Rect} Rect\n */\n\nvar abs = Math.abs;\n\n\nfunction getTRBLResize(oldBounds, newBounds) {\n  return substractTRBL(asTRBL(newBounds), asTRBL(oldBounds));\n}\n\n\nvar LANE_PARENTS = [\n  'bpmn:Participant',\n  'bpmn:Process',\n  'bpmn:SubProcess'\n];\n\nexport var LANE_INDENTATION = 30;\n\n\n/**\n * Return all lanes that are children of the given shape.\n *\n * @param  {Shape} shape\n * @param  {Shape[]} [collectedShapes]\n *\n * @return {Shape[]}\n */\nexport function collectLanes(shape, collectedShapes) {\n\n  collectedShapes = collectedShapes || [];\n\n  shape.children.filter(function(s) {\n    if (is(s, 'bpmn:Lane')) {\n      collectLanes(s, collectedShapes);\n\n      collectedShapes.push(s);\n    }\n  });\n\n  return collectedShapes;\n}\n\n\n/**\n * Return all lanes that are direct children of the given shape.\n *\n * @param {Shape} shape\n *\n * @return {Shape[]}\n */\nexport function getChildLanes(shape) {\n  return shape.children.filter(function(c) {\n    return is(c, 'bpmn:Lane');\n  });\n}\n\n\n/**\n * Return the parent shape of the given lane.\n *\n * @param {Shape} shape\n *\n * @return {Shape}\n */\nexport function getLanesRoot(shape) {\n  return getParent(shape, LANE_PARENTS) || shape;\n}\n\n\n/**\n * Compute the required resize operations for lanes\n * adjacent to the given shape, assuming it will be\n * resized to the given new bounds.\n *\n * @param {Shape} shape\n * @param {Rect} newBounds\n *\n * @return { {\n *   shape: Shape;\n *   newBounds: Rect;\n * }[] }\n */\nexport function computeLanesResize(shape, newBounds) {\n\n  var rootElement = getLanesRoot(shape);\n\n  var initialShapes = is(rootElement, 'bpmn:Process') ? [] : [ rootElement ];\n\n  var allLanes = collectLanes(rootElement, initialShapes),\n      shapeTrbl = asTRBL(shape),\n      shapeNewTrbl = asTRBL(newBounds),\n      trblResize = getTRBLResize(shape, newBounds),\n      resizeNeeded = [];\n\n  var isHorizontalLane = isHorizontal(shape);\n\n  allLanes.forEach(function(other) {\n\n    if (other === shape) {\n      return;\n    }\n\n    var topResize = isHorizontalLane ? 0 : trblResize.top,\n        rightResize = isHorizontalLane ? trblResize.right : 0,\n        bottomResize = isHorizontalLane ? 0 : trblResize.bottom,\n        leftResize = isHorizontalLane ? trblResize.left : 0;\n\n    var otherTrbl = asTRBL(other);\n\n    if (trblResize.top) {\n      if (abs(otherTrbl.bottom - shapeTrbl.top) < 10) {\n        bottomResize = shapeNewTrbl.top - otherTrbl.bottom;\n      }\n\n      if (abs(otherTrbl.top - shapeTrbl.top) < 5) {\n        topResize = shapeNewTrbl.top - otherTrbl.top;\n      }\n    }\n\n    if (trblResize.left) {\n      if (abs(otherTrbl.right - shapeTrbl.left) < 10) {\n        rightResize = shapeNewTrbl.left - otherTrbl.right;\n      }\n\n      if (abs(otherTrbl.left - shapeTrbl.left) < 5) {\n        leftResize = shapeNewTrbl.left - otherTrbl.left;\n      }\n    }\n\n    if (trblResize.bottom) {\n      if (abs(otherTrbl.top - shapeTrbl.bottom) < 10) {\n        topResize = shapeNewTrbl.bottom - otherTrbl.top;\n      }\n\n      if (abs(otherTrbl.bottom - shapeTrbl.bottom) < 5) {\n        bottomResize = shapeNewTrbl.bottom - otherTrbl.bottom;\n      }\n    }\n\n    if (trblResize.right) {\n      if (abs(otherTrbl.left - shapeTrbl.right) < 10) {\n        leftResize = shapeNewTrbl.right - otherTrbl.left;\n      }\n\n      if (abs(otherTrbl.right - shapeTrbl.right) < 5) {\n        rightResize = shapeNewTrbl.right - otherTrbl.right;\n      }\n    }\n\n    if (topResize || rightResize || bottomResize || leftResize) {\n\n      resizeNeeded.push({\n        shape: other,\n        newBounds: resizeTRBL(other, {\n          top: topResize,\n          right: rightResize,\n          bottom: bottomResize,\n          left: leftResize\n        })\n      });\n    }\n\n  });\n\n  return resizeNeeded;\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAKA,IAAI,MAAM,KAAK;AAAf,IACI,MAAM,KAAK;AAEf,IAAI,4BAA4B;AA6BzB,SAAS,cAAc,OAAO,OAAO;AAC1C,SAAO;AAAA,IACL,KAAK,MAAM,MAAM,MAAM;AAAA,IACvB,OAAO,MAAM,QAAQ,MAAM;AAAA,IAC3B,QAAQ,MAAM,SAAS,MAAM;AAAA,IAC7B,MAAM,MAAM,OAAO,MAAM;AAAA,EAC3B;AACF;AAWO,SAAS,aAAa,QAAQ,WAAW,OAAO;AACrD,MAAI,KAAK,MAAM,GACX,KAAK,MAAM;AAEf,MAAI,YAAY;AAAA,IACd,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,IACV,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,EACjB;AAEA,MAAI,UAAU,QAAQ,GAAG,MAAM,IAAI;AACjC,cAAU,IAAI,OAAO,IAAI;AACzB,cAAU,SAAS,OAAO,SAAS;AAAA,EACrC,WAAW,UAAU,QAAQ,GAAG,MAAM,IAAI;AACxC,cAAU,SAAS,OAAO,SAAS;AAAA,EACrC;AAEA,MAAI,UAAU,QAAQ,GAAG,MAAM,IAAI;AACjC,cAAU,QAAQ,OAAO,QAAQ;AAAA,EACnC,WAAW,UAAU,QAAQ,GAAG,MAAM,IAAI;AACxC,cAAU,IAAI,OAAO,IAAI;AACzB,cAAU,QAAQ,OAAO,QAAQ;AAAA,EACnC;AAEA,SAAO;AACT;AAYO,SAAS,WAAW,QAAQ,QAAQ;AACzC,SAAO;AAAA,IACL,GAAG,OAAO,KAAK,OAAO,QAAQ;AAAA,IAC9B,GAAG,OAAO,KAAK,OAAO,OAAO;AAAA,IAC7B,OAAO,OAAO,SAAS,OAAO,QAAQ,MAAM,OAAO,SAAS;AAAA,IAC5D,QAAQ,OAAO,UAAU,OAAO,OAAO,MAAM,OAAO,UAAU;AAAA,EAChE;AACF;AAeA,SAAS,iBAAiB,MAAM,MAAM,mBAAmB;AAEvD,MAAI,QAAQ,KAAK,IAAI,GACjB,WAAW,kBAAkB,OAAO,kBAAkB,IAAI,IAAI,GAC9D,WAAW,kBAAkB,OAAO,kBAAkB,IAAI,IAAI;AAElE,MAAI,SAAS,QAAQ,GAAG;AACtB,aAAS,WAAW,KAAK,IAAI,IAAI,MAAM,KAAK,OAAO,QAAQ;AAAA,EAC7D;AAEA,MAAI,SAAS,QAAQ,GAAG;AACtB,aAAS,WAAW,KAAK,IAAI,IAAI,MAAM,KAAK,OAAO,QAAQ;AAAA,EAC7D;AAEA,SAAO;AACT;AAEO,SAAS,kBAAkB,eAAe,mBAAmB;AAElE,MAAI,CAAC,mBAAmB;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,OAAO,aAAa;AAEtC,SAAO,SAAS;AAAA,IACd,KAAK,iBAAiB,OAAO,aAAa,iBAAiB;AAAA,IAC3D,OAAO,iBAAiB,SAAS,aAAa,iBAAiB;AAAA,IAC/D,QAAQ,iBAAiB,UAAU,aAAa,iBAAiB;AAAA,IACjE,MAAM,iBAAiB,QAAQ,aAAa,iBAAiB;AAAA,EAC/D,CAAC;AACH;AAGO,SAAS,mBAAmB,WAAW,eAAe,eAAe,gBAAgB;AAE1F,MAAI,aAAa,OAAO,aAAa;AAErC,MAAI,SAAS;AAAA,IACX,KAAK,IAAI,KAAK,SAAS,IAAI,WAAW,SAAS,cAAc,SAAS,WAAW;AAAA,IACjF,MAAM,IAAI,KAAK,SAAS,IAAI,WAAW,QAAQ,cAAc,QAAQ,WAAW;AAAA,IAChF,QAAQ,IAAI,KAAK,SAAS,IAAI,WAAW,MAAM,cAAc,SAAS,WAAW;AAAA,IACjF,OAAO,IAAI,KAAK,SAAS,IAAI,WAAW,OAAO,cAAc,QAAQ,WAAW;AAAA,EAClF;AAEA,MAAI,cAAc,iBAAiB,OAAO,cAAc,IAAI;AAE5D,MAAI,cAAc;AAAA,IAChB,KAAK,IAAI,OAAO,KAAK,YAAY,GAAG;AAAA,IACpC,MAAM,IAAI,OAAO,MAAM,YAAY,IAAI;AAAA,IACvC,QAAQ,IAAI,OAAO,QAAQ,YAAY,MAAM;AAAA,IAC7C,OAAO,IAAI,OAAO,OAAO,YAAY,KAAK;AAAA,EAC5C;AAEA,SAAO,SAAS,WAAW;AAC7B;AAEA,SAAS,UAAU,cAAc,cAAc;AAC7C,MAAI,OAAO,iBAAiB,aAAa;AACvC,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEO,SAAS,WAAW,MAAM,SAAS;AACxC,MAAI,MAAM,OAAO,KAAK;AAEtB,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO,UAAU,QAAQ,IAAI;AAC7B,YAAQ,UAAU,QAAQ,KAAK;AAC/B,UAAM,UAAU,QAAQ,GAAG;AAC3B,aAAS,UAAU,QAAQ,MAAM;AAAA,EACnC,OAAO;AACL,WAAO,QAAQ,MAAM,SAAS,UAAU,OAAO;AAAA,EACjD;AAEA,SAAO;AAAA,IACL,GAAG,KAAK,IAAI;AAAA,IACZ,GAAG,KAAK,IAAI;AAAA,IACZ,OAAO,KAAK,QAAQ,OAAO;AAAA,IAC3B,QAAQ,KAAK,SAAS,MAAM;AAAA,EAC9B;AACF;AAYA,SAAS,YAAY,SAAS;AAG5B,MAAI,QAAQ,WAAW;AACrB,WAAO;AAAA,EACT;AAGA,MAAI,QAAQ,SAAS,SAAS;AAC5B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,oBAAoB,iBAAiB,SAAS;AAE5D,MAAI;AAGJ,MAAI,gBAAgB,WAAW,QAAW;AAIxC,eAAW,OAAO,gBAAgB,UAAU,WAAW;AAAA,EAEzD,OAAO;AACL,eAAW;AAAA,EACb;AAEA,MAAI,SAAS,QAAQ;AACnB,WAAO,WAAW,QAAQ,QAAQ,GAAG,OAAO;AAAA,EAC9C;AACF;;;ACnOA,IAAI,MAAM,KAAK;AAGf,SAAS,cAAc,WAAW,WAAW;AAC3C,SAAO,cAAc,OAAO,SAAS,GAAG,OAAO,SAAS,CAAC;AAC3D;AAGA,IAAI,eAAe;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AACF;AAEO,IAAI,mBAAmB;AAWvB,SAAS,aAAa,OAAO,iBAAiB;AAEnD,oBAAkB,mBAAmB,CAAC;AAEtC,QAAM,SAAS,OAAO,SAAS,GAAG;AAChC,QAAI,GAAG,GAAG,WAAW,GAAG;AACtB,mBAAa,GAAG,eAAe;AAE/B,sBAAgB,KAAK,CAAC;AAAA,IACxB;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAUO,SAAS,cAAc,OAAO;AACnC,SAAO,MAAM,SAAS,OAAO,SAAS,GAAG;AACvC,WAAO,GAAG,GAAG,WAAW;AAAA,EAC1B,CAAC;AACH;AAUO,SAAS,aAAa,OAAO;AAClC,SAAO,UAAU,OAAO,YAAY,KAAK;AAC3C;AAgBO,SAAS,mBAAmB,OAAO,WAAW;AAEnD,MAAI,cAAc,aAAa,KAAK;AAEpC,MAAI,gBAAgB,GAAG,aAAa,cAAc,IAAI,CAAC,IAAI,CAAE,WAAY;AAEzE,MAAI,WAAW,aAAa,aAAa,aAAa,GAClD,YAAY,OAAO,KAAK,GACxB,eAAe,OAAO,SAAS,GAC/B,aAAa,cAAc,OAAO,SAAS,GAC3C,eAAe,CAAC;AAEpB,MAAI,mBAAmB,aAAa,KAAK;AAEzC,WAAS,QAAQ,SAAS,OAAO;AAE/B,QAAI,UAAU,OAAO;AACnB;AAAA,IACF;AAEA,QAAI,YAAY,mBAAmB,IAAI,WAAW,KAC9C,cAAc,mBAAmB,WAAW,QAAQ,GACpD,eAAe,mBAAmB,IAAI,WAAW,QACjD,aAAa,mBAAmB,WAAW,OAAO;AAEtD,QAAI,YAAY,OAAO,KAAK;AAE5B,QAAI,WAAW,KAAK;AAClB,UAAI,IAAI,UAAU,SAAS,UAAU,GAAG,IAAI,IAAI;AAC9C,uBAAe,aAAa,MAAM,UAAU;AAAA,MAC9C;AAEA,UAAI,IAAI,UAAU,MAAM,UAAU,GAAG,IAAI,GAAG;AAC1C,oBAAY,aAAa,MAAM,UAAU;AAAA,MAC3C;AAAA,IACF;AAEA,QAAI,WAAW,MAAM;AACnB,UAAI,IAAI,UAAU,QAAQ,UAAU,IAAI,IAAI,IAAI;AAC9C,sBAAc,aAAa,OAAO,UAAU;AAAA,MAC9C;AAEA,UAAI,IAAI,UAAU,OAAO,UAAU,IAAI,IAAI,GAAG;AAC5C,qBAAa,aAAa,OAAO,UAAU;AAAA,MAC7C;AAAA,IACF;AAEA,QAAI,WAAW,QAAQ;AACrB,UAAI,IAAI,UAAU,MAAM,UAAU,MAAM,IAAI,IAAI;AAC9C,oBAAY,aAAa,SAAS,UAAU;AAAA,MAC9C;AAEA,UAAI,IAAI,UAAU,SAAS,UAAU,MAAM,IAAI,GAAG;AAChD,uBAAe,aAAa,SAAS,UAAU;AAAA,MACjD;AAAA,IACF;AAEA,QAAI,WAAW,OAAO;AACpB,UAAI,IAAI,UAAU,OAAO,UAAU,KAAK,IAAI,IAAI;AAC9C,qBAAa,aAAa,QAAQ,UAAU;AAAA,MAC9C;AAEA,UAAI,IAAI,UAAU,QAAQ,UAAU,KAAK,IAAI,GAAG;AAC9C,sBAAc,aAAa,QAAQ,UAAU;AAAA,MAC/C;AAAA,IACF;AAEA,QAAI,aAAa,eAAe,gBAAgB,YAAY;AAE1D,mBAAa,KAAK;AAAA,QAChB,OAAO;AAAA,QACP,WAAW,WAAW,OAAO;AAAA,UAC3B,KAAK;AAAA,UACL,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EAEF,CAAC;AAED,SAAO;AACT;", "names": []}