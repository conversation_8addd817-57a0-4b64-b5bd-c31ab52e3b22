{"version": 3, "sources": ["../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/canvas/Layer.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/canvas/Painter.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/renderer/installCanvasRenderer.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport * as util from '../core/util.js';\nimport { devicePixelRatio } from '../config.js';\nimport Eventful from '../core/Eventful.js';\nimport { getCanvasGradient } from './helper.js';\nimport { createCanvasPattern } from './graphic.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { platformApi } from '../core/platform.js';\nfunction createDom(id, painter, dpr) {\n    var newDom = platformApi.createCanvas();\n    var width = painter.getWidth();\n    var height = painter.getHeight();\n    var newDomStyle = newDom.style;\n    if (newDomStyle) {\n        newDomStyle.position = 'absolute';\n        newDomStyle.left = '0';\n        newDomStyle.top = '0';\n        newDomStyle.width = width + 'px';\n        newDomStyle.height = height + 'px';\n        newDom.setAttribute('data-zr-dom-id', id);\n    }\n    newDom.width = width * dpr;\n    newDom.height = height * dpr;\n    return newDom;\n}\n;\nvar Layer = (function (_super) {\n    __extends(Layer, _super);\n    function Layer(id, painter, dpr) {\n        var _this = _super.call(this) || this;\n        _this.motionBlur = false;\n        _this.lastFrameAlpha = 0.7;\n        _this.dpr = 1;\n        _this.virtual = false;\n        _this.config = {};\n        _this.incremental = false;\n        _this.zlevel = 0;\n        _this.maxRepaintRectCount = 5;\n        _this.__dirty = true;\n        _this.__firstTimePaint = true;\n        _this.__used = false;\n        _this.__drawIndex = 0;\n        _this.__startIndex = 0;\n        _this.__endIndex = 0;\n        _this.__prevStartIndex = null;\n        _this.__prevEndIndex = null;\n        var dom;\n        dpr = dpr || devicePixelRatio;\n        if (typeof id === 'string') {\n            dom = createDom(id, painter, dpr);\n        }\n        else if (util.isObject(id)) {\n            dom = id;\n            id = dom.id;\n        }\n        _this.id = id;\n        _this.dom = dom;\n        var domStyle = dom.style;\n        if (domStyle) {\n            util.disableUserSelect(dom);\n            dom.onselectstart = function () { return false; };\n            domStyle.padding = '0';\n            domStyle.margin = '0';\n            domStyle.borderWidth = '0';\n        }\n        _this.painter = painter;\n        _this.dpr = dpr;\n        return _this;\n    }\n    Layer.prototype.getElementCount = function () {\n        return this.__endIndex - this.__startIndex;\n    };\n    Layer.prototype.afterBrush = function () {\n        this.__prevStartIndex = this.__startIndex;\n        this.__prevEndIndex = this.__endIndex;\n    };\n    Layer.prototype.initContext = function () {\n        this.ctx = this.dom.getContext('2d');\n        this.ctx.dpr = this.dpr;\n    };\n    Layer.prototype.setUnpainted = function () {\n        this.__firstTimePaint = true;\n    };\n    Layer.prototype.createBackBuffer = function () {\n        var dpr = this.dpr;\n        this.domBack = createDom('back-' + this.id, this.painter, dpr);\n        this.ctxBack = this.domBack.getContext('2d');\n        if (dpr !== 1) {\n            this.ctxBack.scale(dpr, dpr);\n        }\n    };\n    Layer.prototype.createRepaintRects = function (displayList, prevList, viewWidth, viewHeight) {\n        if (this.__firstTimePaint) {\n            this.__firstTimePaint = false;\n            return null;\n        }\n        var mergedRepaintRects = [];\n        var maxRepaintRectCount = this.maxRepaintRectCount;\n        var full = false;\n        var pendingRect = new BoundingRect(0, 0, 0, 0);\n        function addRectToMergePool(rect) {\n            if (!rect.isFinite() || rect.isZero()) {\n                return;\n            }\n            if (mergedRepaintRects.length === 0) {\n                var boundingRect = new BoundingRect(0, 0, 0, 0);\n                boundingRect.copy(rect);\n                mergedRepaintRects.push(boundingRect);\n            }\n            else {\n                var isMerged = false;\n                var minDeltaArea = Infinity;\n                var bestRectToMergeIdx = 0;\n                for (var i = 0; i < mergedRepaintRects.length; ++i) {\n                    var mergedRect = mergedRepaintRects[i];\n                    if (mergedRect.intersect(rect)) {\n                        var pendingRect_1 = new BoundingRect(0, 0, 0, 0);\n                        pendingRect_1.copy(mergedRect);\n                        pendingRect_1.union(rect);\n                        mergedRepaintRects[i] = pendingRect_1;\n                        isMerged = true;\n                        break;\n                    }\n                    else if (full) {\n                        pendingRect.copy(rect);\n                        pendingRect.union(mergedRect);\n                        var aArea = rect.width * rect.height;\n                        var bArea = mergedRect.width * mergedRect.height;\n                        var pendingArea = pendingRect.width * pendingRect.height;\n                        var deltaArea = pendingArea - aArea - bArea;\n                        if (deltaArea < minDeltaArea) {\n                            minDeltaArea = deltaArea;\n                            bestRectToMergeIdx = i;\n                        }\n                    }\n                }\n                if (full) {\n                    mergedRepaintRects[bestRectToMergeIdx].union(rect);\n                    isMerged = true;\n                }\n                if (!isMerged) {\n                    var boundingRect = new BoundingRect(0, 0, 0, 0);\n                    boundingRect.copy(rect);\n                    mergedRepaintRects.push(boundingRect);\n                }\n                if (!full) {\n                    full = mergedRepaintRects.length >= maxRepaintRectCount;\n                }\n            }\n        }\n        for (var i = this.__startIndex; i < this.__endIndex; ++i) {\n            var el = displayList[i];\n            if (el) {\n                var shouldPaint = el.shouldBePainted(viewWidth, viewHeight, true, true);\n                var prevRect = el.__isRendered && ((el.__dirty & REDRAW_BIT) || !shouldPaint)\n                    ? el.getPrevPaintRect()\n                    : null;\n                if (prevRect) {\n                    addRectToMergePool(prevRect);\n                }\n                var curRect = shouldPaint && ((el.__dirty & REDRAW_BIT) || !el.__isRendered)\n                    ? el.getPaintRect()\n                    : null;\n                if (curRect) {\n                    addRectToMergePool(curRect);\n                }\n            }\n        }\n        for (var i = this.__prevStartIndex; i < this.__prevEndIndex; ++i) {\n            var el = prevList[i];\n            var shouldPaint = el && el.shouldBePainted(viewWidth, viewHeight, true, true);\n            if (el && (!shouldPaint || !el.__zr) && el.__isRendered) {\n                var prevRect = el.getPrevPaintRect();\n                if (prevRect) {\n                    addRectToMergePool(prevRect);\n                }\n            }\n        }\n        var hasIntersections;\n        do {\n            hasIntersections = false;\n            for (var i = 0; i < mergedRepaintRects.length;) {\n                if (mergedRepaintRects[i].isZero()) {\n                    mergedRepaintRects.splice(i, 1);\n                    continue;\n                }\n                for (var j = i + 1; j < mergedRepaintRects.length;) {\n                    if (mergedRepaintRects[i].intersect(mergedRepaintRects[j])) {\n                        hasIntersections = true;\n                        mergedRepaintRects[i].union(mergedRepaintRects[j]);\n                        mergedRepaintRects.splice(j, 1);\n                    }\n                    else {\n                        j++;\n                    }\n                }\n                i++;\n            }\n        } while (hasIntersections);\n        this._paintRects = mergedRepaintRects;\n        return mergedRepaintRects;\n    };\n    Layer.prototype.debugGetPaintRects = function () {\n        return (this._paintRects || []).slice();\n    };\n    Layer.prototype.resize = function (width, height) {\n        var dpr = this.dpr;\n        var dom = this.dom;\n        var domStyle = dom.style;\n        var domBack = this.domBack;\n        if (domStyle) {\n            domStyle.width = width + 'px';\n            domStyle.height = height + 'px';\n        }\n        dom.width = width * dpr;\n        dom.height = height * dpr;\n        if (domBack) {\n            domBack.width = width * dpr;\n            domBack.height = height * dpr;\n            if (dpr !== 1) {\n                this.ctxBack.scale(dpr, dpr);\n            }\n        }\n    };\n    Layer.prototype.clear = function (clearAll, clearColor, repaintRects) {\n        var dom = this.dom;\n        var ctx = this.ctx;\n        var width = dom.width;\n        var height = dom.height;\n        clearColor = clearColor || this.clearColor;\n        var haveMotionBLur = this.motionBlur && !clearAll;\n        var lastFrameAlpha = this.lastFrameAlpha;\n        var dpr = this.dpr;\n        var self = this;\n        if (haveMotionBLur) {\n            if (!this.domBack) {\n                this.createBackBuffer();\n            }\n            this.ctxBack.globalCompositeOperation = 'copy';\n            this.ctxBack.drawImage(dom, 0, 0, width / dpr, height / dpr);\n        }\n        var domBack = this.domBack;\n        function doClear(x, y, width, height) {\n            ctx.clearRect(x, y, width, height);\n            if (clearColor && clearColor !== 'transparent') {\n                var clearColorGradientOrPattern = void 0;\n                if (util.isGradientObject(clearColor)) {\n                    var shouldCache = clearColor.global || (clearColor.__width === width\n                        && clearColor.__height === height);\n                    clearColorGradientOrPattern = shouldCache\n                        && clearColor.__canvasGradient\n                        || getCanvasGradient(ctx, clearColor, {\n                            x: 0,\n                            y: 0,\n                            width: width,\n                            height: height\n                        });\n                    clearColor.__canvasGradient = clearColorGradientOrPattern;\n                    clearColor.__width = width;\n                    clearColor.__height = height;\n                }\n                else if (util.isImagePatternObject(clearColor)) {\n                    clearColor.scaleX = clearColor.scaleX || dpr;\n                    clearColor.scaleY = clearColor.scaleY || dpr;\n                    clearColorGradientOrPattern = createCanvasPattern(ctx, clearColor, {\n                        dirty: function () {\n                            self.setUnpainted();\n                            self.painter.refresh();\n                        }\n                    });\n                }\n                ctx.save();\n                ctx.fillStyle = clearColorGradientOrPattern || clearColor;\n                ctx.fillRect(x, y, width, height);\n                ctx.restore();\n            }\n            if (haveMotionBLur) {\n                ctx.save();\n                ctx.globalAlpha = lastFrameAlpha;\n                ctx.drawImage(domBack, x, y, width, height);\n                ctx.restore();\n            }\n        }\n        ;\n        if (!repaintRects || haveMotionBLur) {\n            doClear(0, 0, width, height);\n        }\n        else if (repaintRects.length) {\n            util.each(repaintRects, function (rect) {\n                doClear(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n            });\n        }\n    };\n    return Layer;\n}(Eventful));\nexport default Layer;\n", "import { devicePixelRatio } from '../config.js';\nimport * as util from '../core/util.js';\nimport Layer from './Layer.js';\nimport requestAnimationFrame from '../animation/requestAnimationFrame.js';\nimport env from '../core/env.js';\nimport { brush, brushSingle } from './graphic.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { getSize } from './helper.js';\nvar HOVER_LAYER_ZLEVEL = 1e5;\nvar CANVAS_ZLEVEL = 314159;\nvar EL_AFTER_INCREMENTAL_INC = 0.01;\nvar INCREMENTAL_INC = 0.001;\nfunction isLayerValid(layer) {\n    if (!layer) {\n        return false;\n    }\n    if (layer.__builtin__) {\n        return true;\n    }\n    if (typeof (layer.resize) !== 'function'\n        || typeof (layer.refresh) !== 'function') {\n        return false;\n    }\n    return true;\n}\nfunction createRoot(width, height) {\n    var domRoot = document.createElement('div');\n    domRoot.style.cssText = [\n        'position:relative',\n        'width:' + width + 'px',\n        'height:' + height + 'px',\n        'padding:0',\n        'margin:0',\n        'border-width:0'\n    ].join(';') + ';';\n    return domRoot;\n}\nvar CanvasPainter = (function () {\n    function CanvasPainter(root, storage, opts, id) {\n        this.type = 'canvas';\n        this._zlevelList = [];\n        this._prevDisplayList = [];\n        this._layers = {};\n        this._layerConfig = {};\n        this._needsManuallyCompositing = false;\n        this.type = 'canvas';\n        var singleCanvas = !root.nodeName\n            || root.nodeName.toUpperCase() === 'CANVAS';\n        this._opts = opts = util.extend({}, opts || {});\n        this.dpr = opts.devicePixelRatio || devicePixelRatio;\n        this._singleCanvas = singleCanvas;\n        this.root = root;\n        var rootStyle = root.style;\n        if (rootStyle) {\n            util.disableUserSelect(root);\n            root.innerHTML = '';\n        }\n        this.storage = storage;\n        var zlevelList = this._zlevelList;\n        this._prevDisplayList = [];\n        var layers = this._layers;\n        if (!singleCanvas) {\n            this._width = getSize(root, 0, opts);\n            this._height = getSize(root, 1, opts);\n            var domRoot = this._domRoot = createRoot(this._width, this._height);\n            root.appendChild(domRoot);\n        }\n        else {\n            var rootCanvas = root;\n            var width = rootCanvas.width;\n            var height = rootCanvas.height;\n            if (opts.width != null) {\n                width = opts.width;\n            }\n            if (opts.height != null) {\n                height = opts.height;\n            }\n            this.dpr = opts.devicePixelRatio || 1;\n            rootCanvas.width = width * this.dpr;\n            rootCanvas.height = height * this.dpr;\n            this._width = width;\n            this._height = height;\n            var mainLayer = new Layer(rootCanvas, this, this.dpr);\n            mainLayer.__builtin__ = true;\n            mainLayer.initContext();\n            layers[CANVAS_ZLEVEL] = mainLayer;\n            mainLayer.zlevel = CANVAS_ZLEVEL;\n            zlevelList.push(CANVAS_ZLEVEL);\n            this._domRoot = root;\n        }\n    }\n    CanvasPainter.prototype.getType = function () {\n        return 'canvas';\n    };\n    CanvasPainter.prototype.isSingleCanvas = function () {\n        return this._singleCanvas;\n    };\n    CanvasPainter.prototype.getViewportRoot = function () {\n        return this._domRoot;\n    };\n    CanvasPainter.prototype.getViewportRootOffset = function () {\n        var viewportRoot = this.getViewportRoot();\n        if (viewportRoot) {\n            return {\n                offsetLeft: viewportRoot.offsetLeft || 0,\n                offsetTop: viewportRoot.offsetTop || 0\n            };\n        }\n    };\n    CanvasPainter.prototype.refresh = function (paintAll) {\n        var list = this.storage.getDisplayList(true);\n        var prevList = this._prevDisplayList;\n        var zlevelList = this._zlevelList;\n        this._redrawId = Math.random();\n        this._paintList(list, prevList, paintAll, this._redrawId);\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            var layer = this._layers[z];\n            if (!layer.__builtin__ && layer.refresh) {\n                var clearColor = i === 0 ? this._backgroundColor : null;\n                layer.refresh(clearColor);\n            }\n        }\n        if (this._opts.useDirtyRect) {\n            this._prevDisplayList = list.slice();\n        }\n        return this;\n    };\n    CanvasPainter.prototype.refreshHover = function () {\n        this._paintHoverList(this.storage.getDisplayList(false));\n    };\n    CanvasPainter.prototype._paintHoverList = function (list) {\n        var len = list.length;\n        var hoverLayer = this._hoverlayer;\n        hoverLayer && hoverLayer.clear();\n        if (!len) {\n            return;\n        }\n        var scope = {\n            inHover: true,\n            viewWidth: this._width,\n            viewHeight: this._height\n        };\n        var ctx;\n        for (var i = 0; i < len; i++) {\n            var el = list[i];\n            if (el.__inHover) {\n                if (!hoverLayer) {\n                    hoverLayer = this._hoverlayer = this.getLayer(HOVER_LAYER_ZLEVEL);\n                }\n                if (!ctx) {\n                    ctx = hoverLayer.ctx;\n                    ctx.save();\n                }\n                brush(ctx, el, scope, i === len - 1);\n            }\n        }\n        if (ctx) {\n            ctx.restore();\n        }\n    };\n    CanvasPainter.prototype.getHoverLayer = function () {\n        return this.getLayer(HOVER_LAYER_ZLEVEL);\n    };\n    CanvasPainter.prototype.paintOne = function (ctx, el) {\n        brushSingle(ctx, el);\n    };\n    CanvasPainter.prototype._paintList = function (list, prevList, paintAll, redrawId) {\n        if (this._redrawId !== redrawId) {\n            return;\n        }\n        paintAll = paintAll || false;\n        this._updateLayerStatus(list);\n        var _a = this._doPaintList(list, prevList, paintAll), finished = _a.finished, needsRefreshHover = _a.needsRefreshHover;\n        if (this._needsManuallyCompositing) {\n            this._compositeManually();\n        }\n        if (needsRefreshHover) {\n            this._paintHoverList(list);\n        }\n        if (!finished) {\n            var self_1 = this;\n            requestAnimationFrame(function () {\n                self_1._paintList(list, prevList, paintAll, redrawId);\n            });\n        }\n        else {\n            this.eachLayer(function (layer) {\n                layer.afterBrush && layer.afterBrush();\n            });\n        }\n    };\n    CanvasPainter.prototype._compositeManually = function () {\n        var ctx = this.getLayer(CANVAS_ZLEVEL).ctx;\n        var width = this._domRoot.width;\n        var height = this._domRoot.height;\n        ctx.clearRect(0, 0, width, height);\n        this.eachBuiltinLayer(function (layer) {\n            if (layer.virtual) {\n                ctx.drawImage(layer.dom, 0, 0, width, height);\n            }\n        });\n    };\n    CanvasPainter.prototype._doPaintList = function (list, prevList, paintAll) {\n        var _this = this;\n        var layerList = [];\n        var useDirtyRect = this._opts.useDirtyRect;\n        for (var zi = 0; zi < this._zlevelList.length; zi++) {\n            var zlevel = this._zlevelList[zi];\n            var layer = this._layers[zlevel];\n            if (layer.__builtin__\n                && layer !== this._hoverlayer\n                && (layer.__dirty || paintAll)) {\n                layerList.push(layer);\n            }\n        }\n        var finished = true;\n        var needsRefreshHover = false;\n        var _loop_1 = function (k) {\n            var layer = layerList[k];\n            var ctx = layer.ctx;\n            var repaintRects = useDirtyRect\n                && layer.createRepaintRects(list, prevList, this_1._width, this_1._height);\n            var start = paintAll ? layer.__startIndex : layer.__drawIndex;\n            var useTimer = !paintAll && layer.incremental && Date.now;\n            var startTime = useTimer && Date.now();\n            var clearColor = layer.zlevel === this_1._zlevelList[0]\n                ? this_1._backgroundColor : null;\n            if (layer.__startIndex === layer.__endIndex) {\n                layer.clear(false, clearColor, repaintRects);\n            }\n            else if (start === layer.__startIndex) {\n                var firstEl = list[start];\n                if (!firstEl.incremental || !firstEl.notClear || paintAll) {\n                    layer.clear(false, clearColor, repaintRects);\n                }\n            }\n            if (start === -1) {\n                console.error('For some unknown reason. drawIndex is -1');\n                start = layer.__startIndex;\n            }\n            var i;\n            var repaint = function (repaintRect) {\n                var scope = {\n                    inHover: false,\n                    allClipped: false,\n                    prevEl: null,\n                    viewWidth: _this._width,\n                    viewHeight: _this._height\n                };\n                for (i = start; i < layer.__endIndex; i++) {\n                    var el = list[i];\n                    if (el.__inHover) {\n                        needsRefreshHover = true;\n                    }\n                    _this._doPaintEl(el, layer, useDirtyRect, repaintRect, scope, i === layer.__endIndex - 1);\n                    if (useTimer) {\n                        var dTime = Date.now() - startTime;\n                        if (dTime > 15) {\n                            break;\n                        }\n                    }\n                }\n                if (scope.prevElClipPaths) {\n                    ctx.restore();\n                }\n            };\n            if (repaintRects) {\n                if (repaintRects.length === 0) {\n                    i = layer.__endIndex;\n                }\n                else {\n                    var dpr = this_1.dpr;\n                    for (var r = 0; r < repaintRects.length; ++r) {\n                        var rect = repaintRects[r];\n                        ctx.save();\n                        ctx.beginPath();\n                        ctx.rect(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n                        ctx.clip();\n                        repaint(rect);\n                        ctx.restore();\n                    }\n                }\n            }\n            else {\n                ctx.save();\n                repaint();\n                ctx.restore();\n            }\n            layer.__drawIndex = i;\n            if (layer.__drawIndex < layer.__endIndex) {\n                finished = false;\n            }\n        };\n        var this_1 = this;\n        for (var k = 0; k < layerList.length; k++) {\n            _loop_1(k);\n        }\n        if (env.wxa) {\n            util.each(this._layers, function (layer) {\n                if (layer && layer.ctx && layer.ctx.draw) {\n                    layer.ctx.draw();\n                }\n            });\n        }\n        return {\n            finished: finished,\n            needsRefreshHover: needsRefreshHover\n        };\n    };\n    CanvasPainter.prototype._doPaintEl = function (el, currentLayer, useDirtyRect, repaintRect, scope, isLast) {\n        var ctx = currentLayer.ctx;\n        if (useDirtyRect) {\n            var paintRect = el.getPaintRect();\n            if (!repaintRect || paintRect && paintRect.intersect(repaintRect)) {\n                brush(ctx, el, scope, isLast);\n                el.setPrevPaintRect(paintRect);\n            }\n        }\n        else {\n            brush(ctx, el, scope, isLast);\n        }\n    };\n    CanvasPainter.prototype.getLayer = function (zlevel, virtual) {\n        if (this._singleCanvas && !this._needsManuallyCompositing) {\n            zlevel = CANVAS_ZLEVEL;\n        }\n        var layer = this._layers[zlevel];\n        if (!layer) {\n            layer = new Layer('zr_' + zlevel, this, this.dpr);\n            layer.zlevel = zlevel;\n            layer.__builtin__ = true;\n            if (this._layerConfig[zlevel]) {\n                util.merge(layer, this._layerConfig[zlevel], true);\n            }\n            else if (this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC]) {\n                util.merge(layer, this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC], true);\n            }\n            if (virtual) {\n                layer.virtual = virtual;\n            }\n            this.insertLayer(zlevel, layer);\n            layer.initContext();\n        }\n        return layer;\n    };\n    CanvasPainter.prototype.insertLayer = function (zlevel, layer) {\n        var layersMap = this._layers;\n        var zlevelList = this._zlevelList;\n        var len = zlevelList.length;\n        var domRoot = this._domRoot;\n        var prevLayer = null;\n        var i = -1;\n        if (layersMap[zlevel]) {\n            if (process.env.NODE_ENV !== 'production') {\n                util.logError('ZLevel ' + zlevel + ' has been used already');\n            }\n            return;\n        }\n        if (!isLayerValid(layer)) {\n            if (process.env.NODE_ENV !== 'production') {\n                util.logError('Layer of zlevel ' + zlevel + ' is not valid');\n            }\n            return;\n        }\n        if (len > 0 && zlevel > zlevelList[0]) {\n            for (i = 0; i < len - 1; i++) {\n                if (zlevelList[i] < zlevel\n                    && zlevelList[i + 1] > zlevel) {\n                    break;\n                }\n            }\n            prevLayer = layersMap[zlevelList[i]];\n        }\n        zlevelList.splice(i + 1, 0, zlevel);\n        layersMap[zlevel] = layer;\n        if (!layer.virtual) {\n            if (prevLayer) {\n                var prevDom = prevLayer.dom;\n                if (prevDom.nextSibling) {\n                    domRoot.insertBefore(layer.dom, prevDom.nextSibling);\n                }\n                else {\n                    domRoot.appendChild(layer.dom);\n                }\n            }\n            else {\n                if (domRoot.firstChild) {\n                    domRoot.insertBefore(layer.dom, domRoot.firstChild);\n                }\n                else {\n                    domRoot.appendChild(layer.dom);\n                }\n            }\n        }\n        layer.painter || (layer.painter = this);\n    };\n    CanvasPainter.prototype.eachLayer = function (cb, context) {\n        var zlevelList = this._zlevelList;\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            cb.call(context, this._layers[z], z);\n        }\n    };\n    CanvasPainter.prototype.eachBuiltinLayer = function (cb, context) {\n        var zlevelList = this._zlevelList;\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            var layer = this._layers[z];\n            if (layer.__builtin__) {\n                cb.call(context, layer, z);\n            }\n        }\n    };\n    CanvasPainter.prototype.eachOtherLayer = function (cb, context) {\n        var zlevelList = this._zlevelList;\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            var layer = this._layers[z];\n            if (!layer.__builtin__) {\n                cb.call(context, layer, z);\n            }\n        }\n    };\n    CanvasPainter.prototype.getLayers = function () {\n        return this._layers;\n    };\n    CanvasPainter.prototype._updateLayerStatus = function (list) {\n        this.eachBuiltinLayer(function (layer, z) {\n            layer.__dirty = layer.__used = false;\n        });\n        function updatePrevLayer(idx) {\n            if (prevLayer) {\n                if (prevLayer.__endIndex !== idx) {\n                    prevLayer.__dirty = true;\n                }\n                prevLayer.__endIndex = idx;\n            }\n        }\n        if (this._singleCanvas) {\n            for (var i_1 = 1; i_1 < list.length; i_1++) {\n                var el = list[i_1];\n                if (el.zlevel !== list[i_1 - 1].zlevel || el.incremental) {\n                    this._needsManuallyCompositing = true;\n                    break;\n                }\n            }\n        }\n        var prevLayer = null;\n        var incrementalLayerCount = 0;\n        var prevZlevel;\n        var i;\n        for (i = 0; i < list.length; i++) {\n            var el = list[i];\n            var zlevel = el.zlevel;\n            var layer = void 0;\n            if (prevZlevel !== zlevel) {\n                prevZlevel = zlevel;\n                incrementalLayerCount = 0;\n            }\n            if (el.incremental) {\n                layer = this.getLayer(zlevel + INCREMENTAL_INC, this._needsManuallyCompositing);\n                layer.incremental = true;\n                incrementalLayerCount = 1;\n            }\n            else {\n                layer = this.getLayer(zlevel + (incrementalLayerCount > 0 ? EL_AFTER_INCREMENTAL_INC : 0), this._needsManuallyCompositing);\n            }\n            if (!layer.__builtin__) {\n                util.logError('ZLevel ' + zlevel + ' has been used by unkown layer ' + layer.id);\n            }\n            if (layer !== prevLayer) {\n                layer.__used = true;\n                if (layer.__startIndex !== i) {\n                    layer.__dirty = true;\n                }\n                layer.__startIndex = i;\n                if (!layer.incremental) {\n                    layer.__drawIndex = i;\n                }\n                else {\n                    layer.__drawIndex = -1;\n                }\n                updatePrevLayer(i);\n                prevLayer = layer;\n            }\n            if ((el.__dirty & REDRAW_BIT) && !el.__inHover) {\n                layer.__dirty = true;\n                if (layer.incremental && layer.__drawIndex < 0) {\n                    layer.__drawIndex = i;\n                }\n            }\n        }\n        updatePrevLayer(i);\n        this.eachBuiltinLayer(function (layer, z) {\n            if (!layer.__used && layer.getElementCount() > 0) {\n                layer.__dirty = true;\n                layer.__startIndex = layer.__endIndex = layer.__drawIndex = 0;\n            }\n            if (layer.__dirty && layer.__drawIndex < 0) {\n                layer.__drawIndex = layer.__startIndex;\n            }\n        });\n    };\n    CanvasPainter.prototype.clear = function () {\n        this.eachBuiltinLayer(this._clearLayer);\n        return this;\n    };\n    CanvasPainter.prototype._clearLayer = function (layer) {\n        layer.clear();\n    };\n    CanvasPainter.prototype.setBackgroundColor = function (backgroundColor) {\n        this._backgroundColor = backgroundColor;\n        util.each(this._layers, function (layer) {\n            layer.setUnpainted();\n        });\n    };\n    CanvasPainter.prototype.configLayer = function (zlevel, config) {\n        if (config) {\n            var layerConfig = this._layerConfig;\n            if (!layerConfig[zlevel]) {\n                layerConfig[zlevel] = config;\n            }\n            else {\n                util.merge(layerConfig[zlevel], config, true);\n            }\n            for (var i = 0; i < this._zlevelList.length; i++) {\n                var _zlevel = this._zlevelList[i];\n                if (_zlevel === zlevel || _zlevel === zlevel + EL_AFTER_INCREMENTAL_INC) {\n                    var layer = this._layers[_zlevel];\n                    util.merge(layer, layerConfig[zlevel], true);\n                }\n            }\n        }\n    };\n    CanvasPainter.prototype.delLayer = function (zlevel) {\n        var layers = this._layers;\n        var zlevelList = this._zlevelList;\n        var layer = layers[zlevel];\n        if (!layer) {\n            return;\n        }\n        layer.dom.parentNode.removeChild(layer.dom);\n        delete layers[zlevel];\n        zlevelList.splice(util.indexOf(zlevelList, zlevel), 1);\n    };\n    CanvasPainter.prototype.resize = function (width, height) {\n        if (!this._domRoot.style) {\n            if (width == null || height == null) {\n                return;\n            }\n            this._width = width;\n            this._height = height;\n            this.getLayer(CANVAS_ZLEVEL).resize(width, height);\n        }\n        else {\n            var domRoot = this._domRoot;\n            domRoot.style.display = 'none';\n            var opts = this._opts;\n            var root = this.root;\n            width != null && (opts.width = width);\n            height != null && (opts.height = height);\n            width = getSize(root, 0, opts);\n            height = getSize(root, 1, opts);\n            domRoot.style.display = '';\n            if (this._width !== width || height !== this._height) {\n                domRoot.style.width = width + 'px';\n                domRoot.style.height = height + 'px';\n                for (var id in this._layers) {\n                    if (this._layers.hasOwnProperty(id)) {\n                        this._layers[id].resize(width, height);\n                    }\n                }\n                this.refresh(true);\n            }\n            this._width = width;\n            this._height = height;\n        }\n        return this;\n    };\n    CanvasPainter.prototype.clearLayer = function (zlevel) {\n        var layer = this._layers[zlevel];\n        if (layer) {\n            layer.clear();\n        }\n    };\n    CanvasPainter.prototype.dispose = function () {\n        this.root.innerHTML = '';\n        this.root =\n            this.storage =\n                this._domRoot =\n                    this._layers = null;\n    };\n    CanvasPainter.prototype.getRenderedCanvas = function (opts) {\n        opts = opts || {};\n        if (this._singleCanvas && !this._compositeManually) {\n            return this._layers[CANVAS_ZLEVEL].dom;\n        }\n        var imageLayer = new Layer('image', this, opts.pixelRatio || this.dpr);\n        imageLayer.initContext();\n        imageLayer.clear(false, opts.backgroundColor || this._backgroundColor);\n        var ctx = imageLayer.ctx;\n        if (opts.pixelRatio <= this.dpr) {\n            this.refresh();\n            var width_1 = imageLayer.dom.width;\n            var height_1 = imageLayer.dom.height;\n            this.eachLayer(function (layer) {\n                if (layer.__builtin__) {\n                    ctx.drawImage(layer.dom, 0, 0, width_1, height_1);\n                }\n                else if (layer.renderToCanvas) {\n                    ctx.save();\n                    layer.renderToCanvas(ctx);\n                    ctx.restore();\n                }\n            });\n        }\n        else {\n            var scope = {\n                inHover: false,\n                viewWidth: this._width,\n                viewHeight: this._height\n            };\n            var displayList = this.storage.getDisplayList(true);\n            for (var i = 0, len = displayList.length; i < len; i++) {\n                var el = displayList[i];\n                brush(ctx, el, scope, i === len - 1);\n            }\n        }\n        return imageLayer.dom;\n    };\n    CanvasPainter.prototype.getWidth = function () {\n        return this._width;\n    };\n    CanvasPainter.prototype.getHeight = function () {\n        return this._height;\n    };\n    return CanvasPainter;\n}());\nexport default CanvasPainter;\n;\n", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport CanvasPainter from 'zrender/lib/canvas/Painter.js';\nexport function install(registers) {\n  registers.registerPainter('canvas', CanvasPainter);\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,SAAS,UAAU,IAAI,SAAS,KAAK;AACjC,MAAI,SAAS,YAAY,aAAa;AACtC,MAAI,QAAQ,QAAQ,SAAS;AAC7B,MAAI,SAAS,QAAQ,UAAU;AAC/B,MAAI,cAAc,OAAO;AACzB,MAAI,aAAa;AACb,gBAAY,WAAW;AACvB,gBAAY,OAAO;AACnB,gBAAY,MAAM;AAClB,gBAAY,QAAQ,QAAQ;AAC5B,gBAAY,SAAS,SAAS;AAC9B,WAAO,aAAa,kBAAkB,EAAE;AAAA,EAC5C;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,SAAS,SAAS;AACzB,SAAO;AACX;AAEA,IAAI,QAAS,SAAU,QAAQ;AAC3B,YAAUA,QAAO,MAAM;AACvB,WAASA,OAAM,IAAI,SAAS,KAAK;AAC7B,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,aAAa;AACnB,UAAM,iBAAiB;AACvB,UAAM,MAAM;AACZ,UAAM,UAAU;AAChB,UAAM,SAAS,CAAC;AAChB,UAAM,cAAc;AACpB,UAAM,SAAS;AACf,UAAM,sBAAsB;AAC5B,UAAM,UAAU;AAChB,UAAM,mBAAmB;AACzB,UAAM,SAAS;AACf,UAAM,cAAc;AACpB,UAAM,eAAe;AACrB,UAAM,aAAa;AACnB,UAAM,mBAAmB;AACzB,UAAM,iBAAiB;AACvB,QAAI;AACJ,UAAM,OAAO;AACb,QAAI,OAAO,OAAO,UAAU;AACxB,YAAM,UAAU,IAAI,SAAS,GAAG;AAAA,IACpC,WACc,SAAS,EAAE,GAAG;AACxB,YAAM;AACN,WAAK,IAAI;AAAA,IACb;AACA,UAAM,KAAK;AACX,UAAM,MAAM;AACZ,QAAI,WAAW,IAAI;AACnB,QAAI,UAAU;AACV,MAAK,kBAAkB,GAAG;AAC1B,UAAI,gBAAgB,WAAY;AAAE,eAAO;AAAA,MAAO;AAChD,eAAS,UAAU;AACnB,eAAS,SAAS;AAClB,eAAS,cAAc;AAAA,IAC3B;AACA,UAAM,UAAU;AAChB,UAAM,MAAM;AACZ,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,kBAAkB,WAAY;AAC1C,WAAO,KAAK,aAAa,KAAK;AAAA,EAClC;AACA,EAAAA,OAAM,UAAU,aAAa,WAAY;AACrC,SAAK,mBAAmB,KAAK;AAC7B,SAAK,iBAAiB,KAAK;AAAA,EAC/B;AACA,EAAAA,OAAM,UAAU,cAAc,WAAY;AACtC,SAAK,MAAM,KAAK,IAAI,WAAW,IAAI;AACnC,SAAK,IAAI,MAAM,KAAK;AAAA,EACxB;AACA,EAAAA,OAAM,UAAU,eAAe,WAAY;AACvC,SAAK,mBAAmB;AAAA,EAC5B;AACA,EAAAA,OAAM,UAAU,mBAAmB,WAAY;AAC3C,QAAI,MAAM,KAAK;AACf,SAAK,UAAU,UAAU,UAAU,KAAK,IAAI,KAAK,SAAS,GAAG;AAC7D,SAAK,UAAU,KAAK,QAAQ,WAAW,IAAI;AAC3C,QAAI,QAAQ,GAAG;AACX,WAAK,QAAQ,MAAM,KAAK,GAAG;AAAA,IAC/B;AAAA,EACJ;AACA,EAAAA,OAAM,UAAU,qBAAqB,SAAU,aAAa,UAAU,WAAW,YAAY;AACzF,QAAI,KAAK,kBAAkB;AACvB,WAAK,mBAAmB;AACxB,aAAO;AAAA,IACX;AACA,QAAI,qBAAqB,CAAC;AAC1B,QAAI,sBAAsB,KAAK;AAC/B,QAAI,OAAO;AACX,QAAI,cAAc,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC7C,aAAS,mBAAmB,MAAM;AAC9B,UAAI,CAAC,KAAK,SAAS,KAAK,KAAK,OAAO,GAAG;AACnC;AAAA,MACJ;AACA,UAAI,mBAAmB,WAAW,GAAG;AACjC,YAAI,eAAe,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC9C,qBAAa,KAAK,IAAI;AACtB,2BAAmB,KAAK,YAAY;AAAA,MACxC,OACK;AACD,YAAI,WAAW;AACf,YAAI,eAAe;AACnB,YAAI,qBAAqB;AACzB,iBAASC,KAAI,GAAGA,KAAI,mBAAmB,QAAQ,EAAEA,IAAG;AAChD,cAAI,aAAa,mBAAmBA,EAAC;AACrC,cAAI,WAAW,UAAU,IAAI,GAAG;AAC5B,gBAAI,gBAAgB,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC/C,0BAAc,KAAK,UAAU;AAC7B,0BAAc,MAAM,IAAI;AACxB,+BAAmBA,EAAC,IAAI;AACxB,uBAAW;AACX;AAAA,UACJ,WACS,MAAM;AACX,wBAAY,KAAK,IAAI;AACrB,wBAAY,MAAM,UAAU;AAC5B,gBAAI,QAAQ,KAAK,QAAQ,KAAK;AAC9B,gBAAI,QAAQ,WAAW,QAAQ,WAAW;AAC1C,gBAAI,cAAc,YAAY,QAAQ,YAAY;AAClD,gBAAI,YAAY,cAAc,QAAQ;AACtC,gBAAI,YAAY,cAAc;AAC1B,6BAAe;AACf,mCAAqBA;AAAA,YACzB;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,MAAM;AACN,6BAAmB,kBAAkB,EAAE,MAAM,IAAI;AACjD,qBAAW;AAAA,QACf;AACA,YAAI,CAAC,UAAU;AACX,cAAI,eAAe,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC9C,uBAAa,KAAK,IAAI;AACtB,6BAAmB,KAAK,YAAY;AAAA,QACxC;AACA,YAAI,CAAC,MAAM;AACP,iBAAO,mBAAmB,UAAU;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,IAAI,KAAK,cAAc,IAAI,KAAK,YAAY,EAAE,GAAG;AACtD,UAAI,KAAK,YAAY,CAAC;AACtB,UAAI,IAAI;AACJ,YAAI,cAAc,GAAG,gBAAgB,WAAW,YAAY,MAAM,IAAI;AACtE,YAAI,WAAW,GAAG,iBAAkB,GAAG,UAAU,cAAe,CAAC,eAC3D,GAAG,iBAAiB,IACpB;AACN,YAAI,UAAU;AACV,6BAAmB,QAAQ;AAAA,QAC/B;AACA,YAAI,UAAU,gBAAiB,GAAG,UAAU,cAAe,CAAC,GAAG,gBACzD,GAAG,aAAa,IAChB;AACN,YAAI,SAAS;AACT,6BAAmB,OAAO;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,IAAI,KAAK,kBAAkB,IAAI,KAAK,gBAAgB,EAAE,GAAG;AAC9D,UAAI,KAAK,SAAS,CAAC;AACnB,UAAI,cAAc,MAAM,GAAG,gBAAgB,WAAW,YAAY,MAAM,IAAI;AAC5E,UAAI,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAG,cAAc;AACrD,YAAI,WAAW,GAAG,iBAAiB;AACnC,YAAI,UAAU;AACV,6BAAmB,QAAQ;AAAA,QAC/B;AAAA,MACJ;AAAA,IACJ;AACA,QAAI;AACJ,OAAG;AACC,yBAAmB;AACnB,eAAS,IAAI,GAAG,IAAI,mBAAmB,UAAS;AAC5C,YAAI,mBAAmB,CAAC,EAAE,OAAO,GAAG;AAChC,6BAAmB,OAAO,GAAG,CAAC;AAC9B;AAAA,QACJ;AACA,iBAAS,IAAI,IAAI,GAAG,IAAI,mBAAmB,UAAS;AAChD,cAAI,mBAAmB,CAAC,EAAE,UAAU,mBAAmB,CAAC,CAAC,GAAG;AACxD,+BAAmB;AACnB,+BAAmB,CAAC,EAAE,MAAM,mBAAmB,CAAC,CAAC;AACjD,+BAAmB,OAAO,GAAG,CAAC;AAAA,UAClC,OACK;AACD;AAAA,UACJ;AAAA,QACJ;AACA;AAAA,MACJ;AAAA,IACJ,SAAS;AACT,SAAK,cAAc;AACnB,WAAO;AAAA,EACX;AACA,EAAAD,OAAM,UAAU,qBAAqB,WAAY;AAC7C,YAAQ,KAAK,eAAe,CAAC,GAAG,MAAM;AAAA,EAC1C;AACA,EAAAA,OAAM,UAAU,SAAS,SAAU,OAAO,QAAQ;AAC9C,QAAI,MAAM,KAAK;AACf,QAAI,MAAM,KAAK;AACf,QAAI,WAAW,IAAI;AACnB,QAAI,UAAU,KAAK;AACnB,QAAI,UAAU;AACV,eAAS,QAAQ,QAAQ;AACzB,eAAS,SAAS,SAAS;AAAA,IAC/B;AACA,QAAI,QAAQ,QAAQ;AACpB,QAAI,SAAS,SAAS;AACtB,QAAI,SAAS;AACT,cAAQ,QAAQ,QAAQ;AACxB,cAAQ,SAAS,SAAS;AAC1B,UAAI,QAAQ,GAAG;AACX,aAAK,QAAQ,MAAM,KAAK,GAAG;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,OAAM,UAAU,QAAQ,SAAU,UAAU,YAAY,cAAc;AAClE,QAAI,MAAM,KAAK;AACf,QAAI,MAAM,KAAK;AACf,QAAI,QAAQ,IAAI;AAChB,QAAI,SAAS,IAAI;AACjB,iBAAa,cAAc,KAAK;AAChC,QAAI,iBAAiB,KAAK,cAAc,CAAC;AACzC,QAAI,iBAAiB,KAAK;AAC1B,QAAI,MAAM,KAAK;AACf,QAAI,OAAO;AACX,QAAI,gBAAgB;AAChB,UAAI,CAAC,KAAK,SAAS;AACf,aAAK,iBAAiB;AAAA,MAC1B;AACA,WAAK,QAAQ,2BAA2B;AACxC,WAAK,QAAQ,UAAU,KAAK,GAAG,GAAG,QAAQ,KAAK,SAAS,GAAG;AAAA,IAC/D;AACA,QAAI,UAAU,KAAK;AACnB,aAAS,QAAQ,GAAG,GAAGE,QAAOC,SAAQ;AAClC,UAAI,UAAU,GAAG,GAAGD,QAAOC,OAAM;AACjC,UAAI,cAAc,eAAe,eAAe;AAC5C,YAAI,8BAA8B;AAClC,YAAS,iBAAiB,UAAU,GAAG;AACnC,cAAI,cAAc,WAAW,UAAW,WAAW,YAAYD,UACxD,WAAW,aAAaC;AAC/B,wCAA8B,eACvB,WAAW,oBACX,kBAAkB,KAAK,YAAY;AAAA,YAClC,GAAG;AAAA,YACH,GAAG;AAAA,YACH,OAAOD;AAAA,YACP,QAAQC;AAAA,UACZ,CAAC;AACL,qBAAW,mBAAmB;AAC9B,qBAAW,UAAUD;AACrB,qBAAW,WAAWC;AAAA,QAC1B,WACc,qBAAqB,UAAU,GAAG;AAC5C,qBAAW,SAAS,WAAW,UAAU;AACzC,qBAAW,SAAS,WAAW,UAAU;AACzC,wCAA8B,oBAAoB,KAAK,YAAY;AAAA,YAC/D,OAAO,WAAY;AACf,mBAAK,aAAa;AAClB,mBAAK,QAAQ,QAAQ;AAAA,YACzB;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,KAAK;AACT,YAAI,YAAY,+BAA+B;AAC/C,YAAI,SAAS,GAAG,GAAGD,QAAOC,OAAM;AAChC,YAAI,QAAQ;AAAA,MAChB;AACA,UAAI,gBAAgB;AAChB,YAAI,KAAK;AACT,YAAI,cAAc;AAClB,YAAI,UAAU,SAAS,GAAG,GAAGD,QAAOC,OAAM;AAC1C,YAAI,QAAQ;AAAA,MAChB;AAAA,IACJ;AACA;AACA,QAAI,CAAC,gBAAgB,gBAAgB;AACjC,cAAQ,GAAG,GAAG,OAAO,MAAM;AAAA,IAC/B,WACS,aAAa,QAAQ;AAC1B,MAAK,KAAK,cAAc,SAAU,MAAM;AACpC,gBAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,SAAS,GAAG;AAAA,MAC3E,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAOH;AACX,EAAE,gBAAQ;AACV,IAAO,gBAAQ;;;AChSf,IAAI,qBAAqB;AACzB,IAAI,gBAAgB;AACpB,IAAI,2BAA2B;AAC/B,IAAI,kBAAkB;AACtB,SAAS,aAAa,OAAO;AACzB,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,MAAI,MAAM,aAAa;AACnB,WAAO;AAAA,EACX;AACA,MAAI,OAAQ,MAAM,WAAY,cACvB,OAAQ,MAAM,YAAa,YAAY;AAC1C,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,WAAW,OAAO,QAAQ;AAC/B,MAAI,UAAU,SAAS,cAAc,KAAK;AAC1C,UAAQ,MAAM,UAAU;AAAA,IACpB;AAAA,IACA,WAAW,QAAQ;AAAA,IACnB,YAAY,SAAS;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,KAAK,GAAG,IAAI;AACd,SAAO;AACX;AACA,IAAI,gBAAiB,WAAY;AAC7B,WAASI,eAAc,MAAM,SAAS,MAAM,IAAI;AAC5C,SAAK,OAAO;AACZ,SAAK,cAAc,CAAC;AACpB,SAAK,mBAAmB,CAAC;AACzB,SAAK,UAAU,CAAC;AAChB,SAAK,eAAe,CAAC;AACrB,SAAK,4BAA4B;AACjC,SAAK,OAAO;AACZ,QAAI,eAAe,CAAC,KAAK,YAClB,KAAK,SAAS,YAAY,MAAM;AACvC,SAAK,QAAQ,OAAY,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC9C,SAAK,MAAM,KAAK,oBAAoB;AACpC,SAAK,gBAAgB;AACrB,SAAK,OAAO;AACZ,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACX,MAAK,kBAAkB,IAAI;AAC3B,WAAK,YAAY;AAAA,IACrB;AACA,SAAK,UAAU;AACf,QAAI,aAAa,KAAK;AACtB,SAAK,mBAAmB,CAAC;AACzB,QAAI,SAAS,KAAK;AAClB,QAAI,CAAC,cAAc;AACf,WAAK,SAAS,QAAQ,MAAM,GAAG,IAAI;AACnC,WAAK,UAAU,QAAQ,MAAM,GAAG,IAAI;AACpC,UAAI,UAAU,KAAK,WAAW,WAAW,KAAK,QAAQ,KAAK,OAAO;AAClE,WAAK,YAAY,OAAO;AAAA,IAC5B,OACK;AACD,UAAI,aAAa;AACjB,UAAI,QAAQ,WAAW;AACvB,UAAI,SAAS,WAAW;AACxB,UAAI,KAAK,SAAS,MAAM;AACpB,gBAAQ,KAAK;AAAA,MACjB;AACA,UAAI,KAAK,UAAU,MAAM;AACrB,iBAAS,KAAK;AAAA,MAClB;AACA,WAAK,MAAM,KAAK,oBAAoB;AACpC,iBAAW,QAAQ,QAAQ,KAAK;AAChC,iBAAW,SAAS,SAAS,KAAK;AAClC,WAAK,SAAS;AACd,WAAK,UAAU;AACf,UAAI,YAAY,IAAI,cAAM,YAAY,MAAM,KAAK,GAAG;AACpD,gBAAU,cAAc;AACxB,gBAAU,YAAY;AACtB,aAAO,aAAa,IAAI;AACxB,gBAAU,SAAS;AACnB,iBAAW,KAAK,aAAa;AAC7B,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,UAAU,iBAAiB,WAAY;AACjD,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,eAAc,UAAU,kBAAkB,WAAY;AAClD,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,eAAc,UAAU,wBAAwB,WAAY;AACxD,QAAI,eAAe,KAAK,gBAAgB;AACxC,QAAI,cAAc;AACd,aAAO;AAAA,QACH,YAAY,aAAa,cAAc;AAAA,QACvC,WAAW,aAAa,aAAa;AAAA,MACzC;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,eAAc,UAAU,UAAU,SAAU,UAAU;AAClD,QAAI,OAAO,KAAK,QAAQ,eAAe,IAAI;AAC3C,QAAI,WAAW,KAAK;AACpB,QAAI,aAAa,KAAK;AACtB,SAAK,YAAY,KAAK,OAAO;AAC7B,SAAK,WAAW,MAAM,UAAU,UAAU,KAAK,SAAS;AACxD,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAI,IAAI,WAAW,CAAC;AACpB,UAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,UAAI,CAAC,MAAM,eAAe,MAAM,SAAS;AACrC,YAAI,aAAa,MAAM,IAAI,KAAK,mBAAmB;AACnD,cAAM,QAAQ,UAAU;AAAA,MAC5B;AAAA,IACJ;AACA,QAAI,KAAK,MAAM,cAAc;AACzB,WAAK,mBAAmB,KAAK,MAAM;AAAA,IACvC;AACA,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,UAAU,eAAe,WAAY;AAC/C,SAAK,gBAAgB,KAAK,QAAQ,eAAe,KAAK,CAAC;AAAA,EAC3D;AACA,EAAAA,eAAc,UAAU,kBAAkB,SAAU,MAAM;AACtD,QAAI,MAAM,KAAK;AACf,QAAI,aAAa,KAAK;AACtB,kBAAc,WAAW,MAAM;AAC/B,QAAI,CAAC,KAAK;AACN;AAAA,IACJ;AACA,QAAI,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW,KAAK;AAAA,MAChB,YAAY,KAAK;AAAA,IACrB;AACA,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,UAAI,KAAK,KAAK,CAAC;AACf,UAAI,GAAG,WAAW;AACd,YAAI,CAAC,YAAY;AACb,uBAAa,KAAK,cAAc,KAAK,SAAS,kBAAkB;AAAA,QACpE;AACA,YAAI,CAAC,KAAK;AACN,gBAAM,WAAW;AACjB,cAAI,KAAK;AAAA,QACb;AACA,cAAM,KAAK,IAAI,OAAO,MAAM,MAAM,CAAC;AAAA,MACvC;AAAA,IACJ;AACA,QAAI,KAAK;AACL,UAAI,QAAQ;AAAA,IAChB;AAAA,EACJ;AACA,EAAAA,eAAc,UAAU,gBAAgB,WAAY;AAChD,WAAO,KAAK,SAAS,kBAAkB;AAAA,EAC3C;AACA,EAAAA,eAAc,UAAU,WAAW,SAAU,KAAK,IAAI;AAClD,gBAAY,KAAK,EAAE;AAAA,EACvB;AACA,EAAAA,eAAc,UAAU,aAAa,SAAU,MAAM,UAAU,UAAU,UAAU;AAC/E,QAAI,KAAK,cAAc,UAAU;AAC7B;AAAA,IACJ;AACA,eAAW,YAAY;AACvB,SAAK,mBAAmB,IAAI;AAC5B,QAAI,KAAK,KAAK,aAAa,MAAM,UAAU,QAAQ,GAAG,WAAW,GAAG,UAAU,oBAAoB,GAAG;AACrG,QAAI,KAAK,2BAA2B;AAChC,WAAK,mBAAmB;AAAA,IAC5B;AACA,QAAI,mBAAmB;AACnB,WAAK,gBAAgB,IAAI;AAAA,IAC7B;AACA,QAAI,CAAC,UAAU;AACX,UAAI,SAAS;AACb,oCAAsB,WAAY;AAC9B,eAAO,WAAW,MAAM,UAAU,UAAU,QAAQ;AAAA,MACxD,CAAC;AAAA,IACL,OACK;AACD,WAAK,UAAU,SAAU,OAAO;AAC5B,cAAM,cAAc,MAAM,WAAW;AAAA,MACzC,CAAC;AAAA,IACL;AAAA,EACJ;AACA,EAAAA,eAAc,UAAU,qBAAqB,WAAY;AACrD,QAAI,MAAM,KAAK,SAAS,aAAa,EAAE;AACvC,QAAI,QAAQ,KAAK,SAAS;AAC1B,QAAI,SAAS,KAAK,SAAS;AAC3B,QAAI,UAAU,GAAG,GAAG,OAAO,MAAM;AACjC,SAAK,iBAAiB,SAAU,OAAO;AACnC,UAAI,MAAM,SAAS;AACf,YAAI,UAAU,MAAM,KAAK,GAAG,GAAG,OAAO,MAAM;AAAA,MAChD;AAAA,IACJ,CAAC;AAAA,EACL;AACA,EAAAA,eAAc,UAAU,eAAe,SAAU,MAAM,UAAU,UAAU;AACvE,QAAI,QAAQ;AACZ,QAAI,YAAY,CAAC;AACjB,QAAI,eAAe,KAAK,MAAM;AAC9B,aAAS,KAAK,GAAG,KAAK,KAAK,YAAY,QAAQ,MAAM;AACjD,UAAI,SAAS,KAAK,YAAY,EAAE;AAChC,UAAI,QAAQ,KAAK,QAAQ,MAAM;AAC/B,UAAI,MAAM,eACH,UAAU,KAAK,gBACd,MAAM,WAAW,WAAW;AAChC,kBAAU,KAAK,KAAK;AAAA,MACxB;AAAA,IACJ;AACA,QAAI,WAAW;AACf,QAAI,oBAAoB;AACxB,QAAI,UAAU,SAAUC,IAAG;AACvB,UAAIC,SAAQ,UAAUD,EAAC;AACvB,UAAI,MAAMC,OAAM;AAChB,UAAI,eAAe,gBACZA,OAAM,mBAAmB,MAAM,UAAU,OAAO,QAAQ,OAAO,OAAO;AAC7E,UAAI,QAAQ,WAAWA,OAAM,eAAeA,OAAM;AAClD,UAAI,WAAW,CAAC,YAAYA,OAAM,eAAe,KAAK;AACtD,UAAI,YAAY,YAAY,KAAK,IAAI;AACrC,UAAI,aAAaA,OAAM,WAAW,OAAO,YAAY,CAAC,IAChD,OAAO,mBAAmB;AAChC,UAAIA,OAAM,iBAAiBA,OAAM,YAAY;AACzC,QAAAA,OAAM,MAAM,OAAO,YAAY,YAAY;AAAA,MAC/C,WACS,UAAUA,OAAM,cAAc;AACnC,YAAI,UAAU,KAAK,KAAK;AACxB,YAAI,CAAC,QAAQ,eAAe,CAAC,QAAQ,YAAY,UAAU;AACvD,UAAAA,OAAM,MAAM,OAAO,YAAY,YAAY;AAAA,QAC/C;AAAA,MACJ;AACA,UAAI,UAAU,IAAI;AACd,gBAAQ,MAAM,0CAA0C;AACxD,gBAAQA,OAAM;AAAA,MAClB;AACA,UAAI;AACJ,UAAI,UAAU,SAAU,aAAa;AACjC,YAAI,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,WAAW,MAAM;AAAA,UACjB,YAAY,MAAM;AAAA,QACtB;AACA,aAAK,IAAI,OAAO,IAAIA,OAAM,YAAY,KAAK;AACvC,cAAI,KAAK,KAAK,CAAC;AACf,cAAI,GAAG,WAAW;AACd,gCAAoB;AAAA,UACxB;AACA,gBAAM,WAAW,IAAIA,QAAO,cAAc,aAAa,OAAO,MAAMA,OAAM,aAAa,CAAC;AACxF,cAAI,UAAU;AACV,gBAAI,QAAQ,KAAK,IAAI,IAAI;AACzB,gBAAI,QAAQ,IAAI;AACZ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,MAAM,iBAAiB;AACvB,cAAI,QAAQ;AAAA,QAChB;AAAA,MACJ;AACA,UAAI,cAAc;AACd,YAAI,aAAa,WAAW,GAAG;AAC3B,cAAIA,OAAM;AAAA,QACd,OACK;AACD,cAAI,MAAM,OAAO;AACjB,mBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAC1C,gBAAI,OAAO,aAAa,CAAC;AACzB,gBAAI,KAAK;AACT,gBAAI,UAAU;AACd,gBAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,SAAS,GAAG;AACxE,gBAAI,KAAK;AACT,oBAAQ,IAAI;AACZ,gBAAI,QAAQ;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ,OACK;AACD,YAAI,KAAK;AACT,gBAAQ;AACR,YAAI,QAAQ;AAAA,MAChB;AACA,MAAAA,OAAM,cAAc;AACpB,UAAIA,OAAM,cAAcA,OAAM,YAAY;AACtC,mBAAW;AAAA,MACf;AAAA,IACJ;AACA,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,cAAQ,CAAC;AAAA,IACb;AACA,QAAI,YAAI,KAAK;AACT,MAAK,KAAK,KAAK,SAAS,SAAUA,QAAO;AACrC,YAAIA,UAASA,OAAM,OAAOA,OAAM,IAAI,MAAM;AACtC,UAAAA,OAAM,IAAI,KAAK;AAAA,QACnB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAF,eAAc,UAAU,aAAa,SAAU,IAAI,cAAc,cAAc,aAAa,OAAO,QAAQ;AACvG,QAAI,MAAM,aAAa;AACvB,QAAI,cAAc;AACd,UAAI,YAAY,GAAG,aAAa;AAChC,UAAI,CAAC,eAAe,aAAa,UAAU,UAAU,WAAW,GAAG;AAC/D,cAAM,KAAK,IAAI,OAAO,MAAM;AAC5B,WAAG,iBAAiB,SAAS;AAAA,MACjC;AAAA,IACJ,OACK;AACD,YAAM,KAAK,IAAI,OAAO,MAAM;AAAA,IAChC;AAAA,EACJ;AACA,EAAAA,eAAc,UAAU,WAAW,SAAU,QAAQ,SAAS;AAC1D,QAAI,KAAK,iBAAiB,CAAC,KAAK,2BAA2B;AACvD,eAAS;AAAA,IACb;AACA,QAAI,QAAQ,KAAK,QAAQ,MAAM;AAC/B,QAAI,CAAC,OAAO;AACR,cAAQ,IAAI,cAAM,QAAQ,QAAQ,MAAM,KAAK,GAAG;AAChD,YAAM,SAAS;AACf,YAAM,cAAc;AACpB,UAAI,KAAK,aAAa,MAAM,GAAG;AAC3B,QAAK,MAAM,OAAO,KAAK,aAAa,MAAM,GAAG,IAAI;AAAA,MACrD,WACS,KAAK,aAAa,SAAS,wBAAwB,GAAG;AAC3D,QAAK,MAAM,OAAO,KAAK,aAAa,SAAS,wBAAwB,GAAG,IAAI;AAAA,MAChF;AACA,UAAI,SAAS;AACT,cAAM,UAAU;AAAA,MACpB;AACA,WAAK,YAAY,QAAQ,KAAK;AAC9B,YAAM,YAAY;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,UAAU,cAAc,SAAU,QAAQ,OAAO;AAC3D,QAAI,YAAY,KAAK;AACrB,QAAI,aAAa,KAAK;AACtB,QAAI,MAAM,WAAW;AACrB,QAAI,UAAU,KAAK;AACnB,QAAI,YAAY;AAChB,QAAI,IAAI;AACR,QAAI,UAAU,MAAM,GAAG;AACnB,UAAI,MAAuC;AACvC,QAAK,SAAS,YAAY,SAAS,wBAAwB;AAAA,MAC/D;AACA;AAAA,IACJ;AACA,QAAI,CAAC,aAAa,KAAK,GAAG;AACtB,UAAI,MAAuC;AACvC,QAAK,SAAS,qBAAqB,SAAS,eAAe;AAAA,MAC/D;AACA;AAAA,IACJ;AACA,QAAI,MAAM,KAAK,SAAS,WAAW,CAAC,GAAG;AACnC,WAAK,IAAI,GAAG,IAAI,MAAM,GAAG,KAAK;AAC1B,YAAI,WAAW,CAAC,IAAI,UACb,WAAW,IAAI,CAAC,IAAI,QAAQ;AAC/B;AAAA,QACJ;AAAA,MACJ;AACA,kBAAY,UAAU,WAAW,CAAC,CAAC;AAAA,IACvC;AACA,eAAW,OAAO,IAAI,GAAG,GAAG,MAAM;AAClC,cAAU,MAAM,IAAI;AACpB,QAAI,CAAC,MAAM,SAAS;AAChB,UAAI,WAAW;AACX,YAAI,UAAU,UAAU;AACxB,YAAI,QAAQ,aAAa;AACrB,kBAAQ,aAAa,MAAM,KAAK,QAAQ,WAAW;AAAA,QACvD,OACK;AACD,kBAAQ,YAAY,MAAM,GAAG;AAAA,QACjC;AAAA,MACJ,OACK;AACD,YAAI,QAAQ,YAAY;AACpB,kBAAQ,aAAa,MAAM,KAAK,QAAQ,UAAU;AAAA,QACtD,OACK;AACD,kBAAQ,YAAY,MAAM,GAAG;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,YAAY,MAAM,UAAU;AAAA,EACtC;AACA,EAAAA,eAAc,UAAU,YAAY,SAAU,IAAI,SAAS;AACvD,QAAI,aAAa,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAI,IAAI,WAAW,CAAC;AACpB,SAAG,KAAK,SAAS,KAAK,QAAQ,CAAC,GAAG,CAAC;AAAA,IACvC;AAAA,EACJ;AACA,EAAAA,eAAc,UAAU,mBAAmB,SAAU,IAAI,SAAS;AAC9D,QAAI,aAAa,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAI,IAAI,WAAW,CAAC;AACpB,UAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,UAAI,MAAM,aAAa;AACnB,WAAG,KAAK,SAAS,OAAO,CAAC;AAAA,MAC7B;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,eAAc,UAAU,iBAAiB,SAAU,IAAI,SAAS;AAC5D,QAAI,aAAa,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAI,IAAI,WAAW,CAAC;AACpB,UAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,UAAI,CAAC,MAAM,aAAa;AACpB,WAAG,KAAK,SAAS,OAAO,CAAC;AAAA,MAC7B;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,eAAc,UAAU,YAAY,WAAY;AAC5C,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,eAAc,UAAU,qBAAqB,SAAU,MAAM;AACzD,SAAK,iBAAiB,SAAUE,QAAO,GAAG;AACtC,MAAAA,OAAM,UAAUA,OAAM,SAAS;AAAA,IACnC,CAAC;AACD,aAAS,gBAAgB,KAAK;AAC1B,UAAI,WAAW;AACX,YAAI,UAAU,eAAe,KAAK;AAC9B,oBAAU,UAAU;AAAA,QACxB;AACA,kBAAU,aAAa;AAAA,MAC3B;AAAA,IACJ;AACA,QAAI,KAAK,eAAe;AACpB,eAAS,MAAM,GAAG,MAAM,KAAK,QAAQ,OAAO;AACxC,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,GAAG,WAAW,KAAK,MAAM,CAAC,EAAE,UAAU,GAAG,aAAa;AACtD,eAAK,4BAA4B;AACjC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,YAAY;AAChB,QAAI,wBAAwB;AAC5B,QAAI;AACJ,QAAI;AACJ,SAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAC9B,UAAI,KAAK,KAAK,CAAC;AACf,UAAI,SAAS,GAAG;AAChB,UAAI,QAAQ;AACZ,UAAI,eAAe,QAAQ;AACvB,qBAAa;AACb,gCAAwB;AAAA,MAC5B;AACA,UAAI,GAAG,aAAa;AAChB,gBAAQ,KAAK,SAAS,SAAS,iBAAiB,KAAK,yBAAyB;AAC9E,cAAM,cAAc;AACpB,gCAAwB;AAAA,MAC5B,OACK;AACD,gBAAQ,KAAK,SAAS,UAAU,wBAAwB,IAAI,2BAA2B,IAAI,KAAK,yBAAyB;AAAA,MAC7H;AACA,UAAI,CAAC,MAAM,aAAa;AACpB,QAAK,SAAS,YAAY,SAAS,oCAAoC,MAAM,EAAE;AAAA,MACnF;AACA,UAAI,UAAU,WAAW;AACrB,cAAM,SAAS;AACf,YAAI,MAAM,iBAAiB,GAAG;AAC1B,gBAAM,UAAU;AAAA,QACpB;AACA,cAAM,eAAe;AACrB,YAAI,CAAC,MAAM,aAAa;AACpB,gBAAM,cAAc;AAAA,QACxB,OACK;AACD,gBAAM,cAAc;AAAA,QACxB;AACA,wBAAgB,CAAC;AACjB,oBAAY;AAAA,MAChB;AACA,UAAK,GAAG,UAAU,cAAe,CAAC,GAAG,WAAW;AAC5C,cAAM,UAAU;AAChB,YAAI,MAAM,eAAe,MAAM,cAAc,GAAG;AAC5C,gBAAM,cAAc;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ;AACA,oBAAgB,CAAC;AACjB,SAAK,iBAAiB,SAAUA,QAAO,GAAG;AACtC,UAAI,CAACA,OAAM,UAAUA,OAAM,gBAAgB,IAAI,GAAG;AAC9C,QAAAA,OAAM,UAAU;AAChB,QAAAA,OAAM,eAAeA,OAAM,aAAaA,OAAM,cAAc;AAAA,MAChE;AACA,UAAIA,OAAM,WAAWA,OAAM,cAAc,GAAG;AACxC,QAAAA,OAAM,cAAcA,OAAM;AAAA,MAC9B;AAAA,IACJ,CAAC;AAAA,EACL;AACA,EAAAF,eAAc,UAAU,QAAQ,WAAY;AACxC,SAAK,iBAAiB,KAAK,WAAW;AACtC,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,UAAU,cAAc,SAAU,OAAO;AACnD,UAAM,MAAM;AAAA,EAChB;AACA,EAAAA,eAAc,UAAU,qBAAqB,SAAU,iBAAiB;AACpE,SAAK,mBAAmB;AACxB,IAAK,KAAK,KAAK,SAAS,SAAU,OAAO;AACrC,YAAM,aAAa;AAAA,IACvB,CAAC;AAAA,EACL;AACA,EAAAA,eAAc,UAAU,cAAc,SAAU,QAAQ,QAAQ;AAC5D,QAAI,QAAQ;AACR,UAAI,cAAc,KAAK;AACvB,UAAI,CAAC,YAAY,MAAM,GAAG;AACtB,oBAAY,MAAM,IAAI;AAAA,MAC1B,OACK;AACD,QAAK,MAAM,YAAY,MAAM,GAAG,QAAQ,IAAI;AAAA,MAChD;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC9C,YAAI,UAAU,KAAK,YAAY,CAAC;AAChC,YAAI,YAAY,UAAU,YAAY,SAAS,0BAA0B;AACrE,cAAI,QAAQ,KAAK,QAAQ,OAAO;AAChC,UAAK,MAAM,OAAO,YAAY,MAAM,GAAG,IAAI;AAAA,QAC/C;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,eAAc,UAAU,WAAW,SAAU,QAAQ;AACjD,QAAI,SAAS,KAAK;AAClB,QAAI,aAAa,KAAK;AACtB,QAAI,QAAQ,OAAO,MAAM;AACzB,QAAI,CAAC,OAAO;AACR;AAAA,IACJ;AACA,UAAM,IAAI,WAAW,YAAY,MAAM,GAAG;AAC1C,WAAO,OAAO,MAAM;AACpB,eAAW,OAAY,QAAQ,YAAY,MAAM,GAAG,CAAC;AAAA,EACzD;AACA,EAAAA,eAAc,UAAU,SAAS,SAAU,OAAO,QAAQ;AACtD,QAAI,CAAC,KAAK,SAAS,OAAO;AACtB,UAAI,SAAS,QAAQ,UAAU,MAAM;AACjC;AAAA,MACJ;AACA,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,SAAS,aAAa,EAAE,OAAO,OAAO,MAAM;AAAA,IACrD,OACK;AACD,UAAI,UAAU,KAAK;AACnB,cAAQ,MAAM,UAAU;AACxB,UAAI,OAAO,KAAK;AAChB,UAAI,OAAO,KAAK;AAChB,eAAS,SAAS,KAAK,QAAQ;AAC/B,gBAAU,SAAS,KAAK,SAAS;AACjC,cAAQ,QAAQ,MAAM,GAAG,IAAI;AAC7B,eAAS,QAAQ,MAAM,GAAG,IAAI;AAC9B,cAAQ,MAAM,UAAU;AACxB,UAAI,KAAK,WAAW,SAAS,WAAW,KAAK,SAAS;AAClD,gBAAQ,MAAM,QAAQ,QAAQ;AAC9B,gBAAQ,MAAM,SAAS,SAAS;AAChC,iBAAS,MAAM,KAAK,SAAS;AACzB,cAAI,KAAK,QAAQ,eAAe,EAAE,GAAG;AACjC,iBAAK,QAAQ,EAAE,EAAE,OAAO,OAAO,MAAM;AAAA,UACzC;AAAA,QACJ;AACA,aAAK,QAAQ,IAAI;AAAA,MACrB;AACA,WAAK,SAAS;AACd,WAAK,UAAU;AAAA,IACnB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,UAAU,aAAa,SAAU,QAAQ;AACnD,QAAI,QAAQ,KAAK,QAAQ,MAAM;AAC/B,QAAI,OAAO;AACP,YAAM,MAAM;AAAA,IAChB;AAAA,EACJ;AACA,EAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,SAAK,KAAK,YAAY;AACtB,SAAK,OACD,KAAK,UACD,KAAK,WACD,KAAK,UAAU;AAAA,EAC/B;AACA,EAAAA,eAAc,UAAU,oBAAoB,SAAU,MAAM;AACxD,WAAO,QAAQ,CAAC;AAChB,QAAI,KAAK,iBAAiB,CAAC,KAAK,oBAAoB;AAChD,aAAO,KAAK,QAAQ,aAAa,EAAE;AAAA,IACvC;AACA,QAAI,aAAa,IAAI,cAAM,SAAS,MAAM,KAAK,cAAc,KAAK,GAAG;AACrE,eAAW,YAAY;AACvB,eAAW,MAAM,OAAO,KAAK,mBAAmB,KAAK,gBAAgB;AACrE,QAAI,MAAM,WAAW;AACrB,QAAI,KAAK,cAAc,KAAK,KAAK;AAC7B,WAAK,QAAQ;AACb,UAAI,UAAU,WAAW,IAAI;AAC7B,UAAI,WAAW,WAAW,IAAI;AAC9B,WAAK,UAAU,SAAU,OAAO;AAC5B,YAAI,MAAM,aAAa;AACnB,cAAI,UAAU,MAAM,KAAK,GAAG,GAAG,SAAS,QAAQ;AAAA,QACpD,WACS,MAAM,gBAAgB;AAC3B,cAAI,KAAK;AACT,gBAAM,eAAe,GAAG;AACxB,cAAI,QAAQ;AAAA,QAChB;AAAA,MACJ,CAAC;AAAA,IACL,OACK;AACD,UAAI,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW,KAAK;AAAA,QAChB,YAAY,KAAK;AAAA,MACrB;AACA,UAAI,cAAc,KAAK,QAAQ,eAAe,IAAI;AAClD,eAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACpD,YAAI,KAAK,YAAY,CAAC;AACtB,cAAM,KAAK,IAAI,OAAO,MAAM,MAAM,CAAC;AAAA,MACvC;AAAA,IACJ;AACA,WAAO,WAAW;AAAA,EACtB;AACA,EAAAA,eAAc,UAAU,WAAW,WAAY;AAC3C,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,eAAc,UAAU,YAAY,WAAY;AAC5C,WAAO,KAAK;AAAA,EAChB;AACA,SAAOA;AACX,EAAE;AACF,IAAO,kBAAQ;;;ACnlBR,SAAS,QAAQ,WAAW;AACjC,YAAU,gBAAgB,UAAU,eAAa;AACnD;", "names": ["Layer", "i", "width", "height", "CanvasPainter", "k", "layer"]}