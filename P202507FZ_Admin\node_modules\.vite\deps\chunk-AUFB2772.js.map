{"version": 3, "sources": ["../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/upload/src/ajax.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/utils/typescript.ts", "../../.pnpm/element-plus@2.9.1_vue@3.5.12_typescript@5.3.3_/packages/components/upload/src/upload.ts"], "sourcesContent": ["import { isNil } from 'lodash-unified'\nimport { isArray, throwError } from '@element-plus/utils'\nimport type {\n  UploadProgressEvent,\n  UploadRequestHandler,\n  UploadRequestOptions,\n} from './upload'\n\nconst SCOPE = 'ElUpload'\n\nexport class UploadAjaxError extends Error {\n  name = 'UploadAjaxError'\n  status: number\n  method: string\n  url: string\n\n  constructor(message: string, status: number, method: string, url: string) {\n    super(message)\n    this.status = status\n    this.method = method\n    this.url = url\n  }\n}\n\nfunction getError(\n  action: string,\n  option: UploadRequestOptions,\n  xhr: XMLHttpRequest\n) {\n  let msg: string\n  if (xhr.response) {\n    msg = `${xhr.response.error || xhr.response}`\n  } else if (xhr.responseText) {\n    msg = `${xhr.responseText}`\n  } else {\n    msg = `fail to ${option.method} ${action} ${xhr.status}`\n  }\n\n  return new UploadAjaxError(msg, xhr.status, option.method, action)\n}\n\nfunction getBody(xhr: XMLHttpRequest): XMLHttpRequestResponseType {\n  const text = xhr.responseText || xhr.response\n  if (!text) {\n    return text\n  }\n\n  try {\n    return JSON.parse(text)\n  } catch {\n    return text\n  }\n}\n\nexport const ajaxUpload: UploadRequestHandler = (option) => {\n  if (typeof XMLHttpRequest === 'undefined')\n    throwError(SCOPE, 'XMLHttpRequest is undefined')\n\n  const xhr = new XMLHttpRequest()\n  const action = option.action\n\n  if (xhr.upload) {\n    xhr.upload.addEventListener('progress', (evt) => {\n      const progressEvt = evt as UploadProgressEvent\n      progressEvt.percent = evt.total > 0 ? (evt.loaded / evt.total) * 100 : 0\n      option.onProgress(progressEvt)\n    })\n  }\n\n  const formData = new FormData()\n  if (option.data) {\n    for (const [key, value] of Object.entries(option.data)) {\n      if (isArray(value) && value.length) formData.append(key, ...value)\n      else formData.append(key, value)\n    }\n  }\n  formData.append(option.filename, option.file, option.file.name)\n\n  xhr.addEventListener('error', () => {\n    option.onError(getError(action, option, xhr))\n  })\n\n  xhr.addEventListener('load', () => {\n    if (xhr.status < 200 || xhr.status >= 300) {\n      return option.onError(getError(action, option, xhr))\n    }\n    option.onSuccess(getBody(xhr))\n  })\n\n  xhr.open(option.method, action, true)\n\n  if (option.withCredentials && 'withCredentials' in xhr) {\n    xhr.withCredentials = true\n  }\n\n  const headers = option.headers || {}\n  if (headers instanceof Headers) {\n    headers.forEach((value, key) => xhr.setRequestHeader(key, value))\n  } else {\n    for (const [key, value] of Object.entries(headers)) {\n      if (isNil(value)) continue\n      xhr.setRequestHeader(key, String(value))\n    }\n  }\n\n  xhr.send(formData)\n  return xhr\n}\n", "export const mutable = <T extends readonly any[] | Record<string, unknown>>(\n  val: T\n) => val as Mutable<typeof val>\nexport type Mutable<T> = { -readonly [P in keyof T]: T[P] }\n\nexport type HTMLElementCustomized<T> = HTMLElement & T\n\n/**\n * @deprecated stop to use null\n * @see {@link https://github.com/sindresorhus/meta/discussions/7}\n */\nexport type Nullable<T> = T | null\n\nexport type Arrayable<T> = T | T[]\nexport type Awaitable<T> = Promise<T> | T\n", "import { NOOP, buildProps, definePropType, mutable } from '@element-plus/utils'\nimport { ajaxUpload } from './ajax'\nimport type { Awaitable, Mutable } from '@element-plus/utils'\n\nimport type { UploadAjaxError } from './ajax'\nimport type { ExtractPropTypes } from 'vue'\nimport type Upload from './upload.vue'\n\nexport const uploadListTypes = ['text', 'picture', 'picture-card'] as const\n\nlet fileId = 1\nexport const genFileId = () => Date.now() + fileId++\n\nexport type UploadStatus = 'ready' | 'uploading' | 'success' | 'fail'\nexport interface UploadProgressEvent extends ProgressEvent {\n  percent: number\n}\n\nexport interface UploadRequestOptions {\n  action: string\n  method: string\n  data: Record<string, string | Blob | [Blob, string]>\n  filename: string\n  file: UploadRawFile\n  headers: Headers | Record<string, string | number | null | undefined>\n  onError: (evt: UploadAjaxError) => void\n  onProgress: (evt: UploadProgressEvent) => void\n  onSuccess: (response: any) => void\n  withCredentials: boolean\n}\nexport interface UploadFile {\n  name: string\n  percentage?: number\n  status: UploadStatus\n  size?: number\n  response?: unknown\n  uid: number\n  url?: string\n  raw?: UploadRawFile\n}\nexport type UploadUserFile = Omit<UploadFile, 'status' | 'uid'> &\n  Partial<Pick<UploadFile, 'status' | 'uid'>>\n\nexport type UploadFiles = UploadFile[]\nexport interface UploadRawFile extends File {\n  uid: number\n}\nexport type UploadRequestHandler = (\n  options: UploadRequestOptions\n) => XMLHttpRequest | Promise<unknown>\nexport interface UploadHooks {\n  beforeUpload: (\n    rawFile: UploadRawFile\n  ) => Awaitable<void | undefined | null | boolean | File | Blob>\n  beforeRemove: (\n    uploadFile: UploadFile,\n    uploadFiles: UploadFiles\n  ) => Awaitable<boolean>\n  onRemove: (uploadFile: UploadFile, uploadFiles: UploadFiles) => void\n  onChange: (uploadFile: UploadFile, uploadFiles: UploadFiles) => void\n  onPreview: (uploadFile: UploadFile) => void\n  onSuccess: (\n    response: any,\n    uploadFile: UploadFile,\n    uploadFiles: UploadFiles\n  ) => void\n  onProgress: (\n    evt: UploadProgressEvent,\n    uploadFile: UploadFile,\n    uploadFiles: UploadFiles\n  ) => void\n  onError: (\n    error: Error,\n    uploadFile: UploadFile,\n    uploadFiles: UploadFiles\n  ) => void\n  onExceed: (files: File[], uploadFiles: UploadUserFile[]) => void\n}\n\nexport type UploadData = Mutable<Record<string, any>>\n\nexport const uploadBaseProps = buildProps({\n  /**\n   * @description request URL\n   */\n  action: {\n    type: String,\n    default: '#',\n  },\n  /**\n   * @description request headers\n   */\n  headers: {\n    type: definePropType<Headers | Record<string, any>>(Object),\n  },\n  /**\n   * @description set upload request method\n   */\n  method: {\n    type: String,\n    default: 'post',\n  },\n  /**\n   * @description additions options of request\n   */\n  data: {\n    type: definePropType<\n      | Awaitable<UploadData>\n      | ((rawFile: UploadRawFile) => Awaitable<UploadData>)\n    >([Object, Function, Promise]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description whether uploading multiple files is permitted\n   */\n  multiple: Boolean,\n  /**\n   * @description key name for uploaded file\n   */\n  name: {\n    type: String,\n    default: 'file',\n  },\n  /**\n   * @description whether to activate drag and drop mode\n   */\n  drag: Boolean,\n  /**\n   * @description whether cookies are sent\n   */\n  withCredentials: Boolean,\n  /**\n   * @description whether to show the uploaded file list\n   */\n  showFileList: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description accepted [file types](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#attr-accept), will not work when `thumbnail-mode === true`\n   */\n  accept: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description default uploaded files\n   */\n  fileList: {\n    type: definePropType<UploadUserFile[]>(Array),\n    default: () => mutable([] as const),\n  },\n  /**\n   * @description whether to auto upload file\n   */\n  autoUpload: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description type of file list\n   */\n  listType: {\n    type: String,\n    values: uploadListTypes,\n    default: 'text',\n  },\n  /**\n   * @description override default xhr behavior, allowing you to implement your own upload-file's request\n   */\n  httpRequest: {\n    type: definePropType<UploadRequestHandler>(Function),\n    default: ajaxUpload,\n  },\n  /**\n   * @description whether to disable upload\n   */\n  disabled: Boolean,\n  /**\n   * @description maximum number of uploads allowed\n   */\n  limit: Number,\n} as const)\n\nexport const uploadProps = buildProps({\n  ...uploadBaseProps,\n  /**\n   * @description hook function before uploading with the file to be uploaded as its parameter. If `false` is returned or a `Promise` is returned and then is rejected, uploading will be aborted\n   */\n  beforeUpload: {\n    type: definePropType<UploadHooks['beforeUpload']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function before removing a file with the file and file list as its parameters. If `false` is returned or a `Promise` is returned and then is rejected, removing will be aborted\n   */\n  beforeRemove: {\n    type: definePropType<UploadHooks['beforeRemove']>(Function),\n  },\n  /**\n   * @description hook function when files are removed\n   */\n  onRemove: {\n    type: definePropType<UploadHooks['onRemove']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when select file or upload file success or upload file fail\n   */\n  onChange: {\n    type: definePropType<UploadHooks['onChange']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when clicking the uploaded files\n   */\n  onPreview: {\n    type: definePropType<UploadHooks['onPreview']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when uploaded successfully\n   */\n  onSuccess: {\n    type: definePropType<UploadHooks['onSuccess']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when some progress occurs\n   */\n  onProgress: {\n    type: definePropType<UploadHooks['onProgress']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when some errors occurs\n   */\n  onError: {\n    type: definePropType<UploadHooks['onError']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when limit is exceeded\n   */\n  onExceed: {\n    type: definePropType<UploadHooks['onExceed']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description set HTML attribute: crossorigin.\n   */\n  crossorigin: {\n    type: definePropType<'anonymous' | 'use-credentials' | ''>(String),\n  },\n} as const)\n\nexport type UploadProps = ExtractPropTypes<typeof uploadProps>\n\nexport type UploadInstance = InstanceType<typeof Upload>\n"], "mappings": ";;;;;;;;;;;;;;;;AAEA,IAAM,QAAQ;AACP,IAAM,kBAAN,cAA8B,MAAM;EACzC,YAAY,SAAS,QAAQ,QAAQ,KAAK;AACxC,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,MAAM;EACf;AACA;AACA,SAAS,SAAS,QAAQ,QAAQ,KAAK;AACrC,MAAI;AACJ,MAAI,IAAI,UAAU;AAChB,UAAM,GAAG,IAAI,SAAS,SAAS,IAAI,QAAQ;EAC/C,WAAa,IAAI,cAAc;AAC3B,UAAM,GAAG,IAAI,YAAY;EAC7B,OAAS;AACL,UAAM,WAAW,OAAO,MAAM,IAAI,MAAM,IAAI,IAAI,MAAM;EAC1D;AACE,SAAO,IAAI,gBAAgB,KAAK,IAAI,QAAQ,OAAO,QAAQ,MAAM;AACnE;AACA,SAAS,QAAQ,KAAK;AACpB,QAAM,OAAO,IAAI,gBAAgB,IAAI;AACrC,MAAI,CAAC,MAAM;AACT,WAAO;EACX;AACE,MAAI;AACF,WAAO,KAAK,MAAM,IAAI;EAC1B,SAAW,GAAG;AACV,WAAO;EACX;AACA;AACY,IAAC,aAAa,CAAC,WAAW;AACpC,MAAI,OAAO,mBAAmB;AAC5B,eAAW,OAAO,6BAA6B;AACjD,QAAM,MAAM,IAAI,eAAc;AAC9B,QAAM,SAAS,OAAO;AACtB,MAAI,IAAI,QAAQ;AACd,QAAI,OAAO,iBAAiB,YAAY,CAAC,QAAQ;AAC/C,YAAM,cAAc;AACpB,kBAAY,UAAU,IAAI,QAAQ,IAAI,IAAI,SAAS,IAAI,QAAQ,MAAM;AACrE,aAAO,WAAW,WAAW;IACnC,CAAK;EACL;AACE,QAAM,WAAW,IAAI,SAAQ;AAC7B,MAAI,OAAO,MAAM;AACf,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,OAAO,IAAI,GAAG;AACtD,UAAI,QAAQ,KAAK,KAAK,MAAM;AAC1B,iBAAS,OAAO,KAAK,GAAG,KAAK;;AAE7B,iBAAS,OAAO,KAAK,KAAK;IAClC;EACA;AACE,WAAS,OAAO,OAAO,UAAU,OAAO,MAAM,OAAO,KAAK,IAAI;AAC9D,MAAI,iBAAiB,SAAS,MAAM;AAClC,WAAO,QAAQ,SAAS,QAAQ,QAAQ,GAAG,CAAC;EAChD,CAAG;AACD,MAAI,iBAAiB,QAAQ,MAAM;AACjC,QAAI,IAAI,SAAS,OAAO,IAAI,UAAU,KAAK;AACzC,aAAO,OAAO,QAAQ,SAAS,QAAQ,QAAQ,GAAG,CAAC;IACzD;AACI,WAAO,UAAU,QAAQ,GAAG,CAAC;EACjC,CAAG;AACD,MAAI,KAAK,OAAO,QAAQ,QAAQ,IAAI;AACpC,MAAI,OAAO,mBAAmB,qBAAqB,KAAK;AACtD,QAAI,kBAAkB;EAC1B;AACE,QAAM,UAAU,OAAO,WAAW,CAAA;AAClC,MAAI,mBAAmB,SAAS;AAC9B,YAAQ,QAAQ,CAAC,OAAO,QAAQ,IAAI,iBAAiB,KAAK,KAAK,CAAC;EACpE,OAAS;AACL,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,OAAO,GAAG;AAClD,UAAI,cAAM,KAAK;AACb;AACF,UAAI,iBAAiB,KAAK,OAAO,KAAK,CAAC;IAC7C;EACA;AACE,MAAI,KAAK,QAAQ;AACjB,SAAO;AACT;;;ACjFY,IAAC,UAAU,CAAC,QAAQ;;;;ACEpB,IAAC,kBAAkB,CAAC,QAAQ,WAAW,cAAc;AACjE,IAAI,SAAS;AACD,IAAC,YAAY,MAAM,KAAK,IAAG,IAAK;AAChC,IAAC,kBAAkB,WAAW;EACxC,QAAQ;IACN,MAAM;IACN,SAAS;EACb;EACE,SAAS;IACP,MAAM,eAAe,MAAM;EAC/B;EACE,QAAQ;IACN,MAAM;IACN,SAAS;EACb;EACE,MAAM;IACJ,MAAM,eAAe,CAAC,QAAQ,UAAU,OAAO,CAAC;IAChD,SAAS,MAAM,QAAQ,CAAA,CAAE;EAC7B;EACE,UAAU;EACV,MAAM;IACJ,MAAM;IACN,SAAS;EACb;EACE,MAAM;EACN,iBAAiB;EACjB,cAAc;IACZ,MAAM;IACN,SAAS;EACb;EACE,QAAQ;IACN,MAAM;IACN,SAAS;EACb;EACE,UAAU;IACR,MAAM,eAAe,KAAK;IAC1B,SAAS,MAAM,QAAQ,CAAA,CAAE;EAC7B;EACE,YAAY;IACV,MAAM;IACN,SAAS;EACb;EACE,UAAU;IACR,MAAM;IACN,QAAQ;IACR,SAAS;EACb;EACE,aAAa;IACX,MAAM,eAAe,QAAQ;IAC7B,SAAS;EACb;EACE,UAAU;EACV,OAAO;AACT,CAAC;AACW,IAAC,cAAc,WAAW;EACpC,GAAG;EACH,cAAc;IACZ,MAAM,eAAe,QAAQ;IAC7B,SAAS;EACb;EACE,cAAc;IACZ,MAAM,eAAe,QAAQ;EACjC;EACE,UAAU;IACR,MAAM,eAAe,QAAQ;IAC7B,SAAS;EACb;EACE,UAAU;IACR,MAAM,eAAe,QAAQ;IAC7B,SAAS;EACb;EACE,WAAW;IACT,MAAM,eAAe,QAAQ;IAC7B,SAAS;EACb;EACE,WAAW;IACT,MAAM,eAAe,QAAQ;IAC7B,SAAS;EACb;EACE,YAAY;IACV,MAAM,eAAe,QAAQ;IAC7B,SAAS;EACb;EACE,SAAS;IACP,MAAM,eAAe,QAAQ;IAC7B,SAAS;EACb;EACE,UAAU;IACR,MAAM,eAAe,QAAQ;IAC7B,SAAS;EACb;EACE,aAAa;IACX,MAAM,eAAe,MAAM;EAC/B;AACA,CAAC;", "names": []}