import {
  LANE_INDENTATION,
  collectLanes,
  computeLanesResize,
  getChildLanes,
  getLanesRoot
} from "./chunk-UEIE3ZOK.js";
import "./chunk-T4R4535C.js";
import "./chunk-KKQ6WPIB.js";
import "./chunk-7C6J56BH.js";
import "./chunk-FNF472WR.js";
import "./chunk-YTJ5ESGD.js";
import "./chunk-GFT2G5UO.js";
export {
  LANE_INDENTATION,
  collectLanes,
  computeLanesResize,
  getChildLanes,
  getLanesRoot
};
//# sourceMappingURL=bpmn-js_lib_features_modeling_util_LaneUtil.js.map
