{"version": 3, "sources": ["../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/svg/SVGPathRebuilder.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/svg/mapStyleToAttrs.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/svg/core.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/svg/cssClassId.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/svg/cssAnimation.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/svg/cssEmphasis.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/svg/graphic.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/svg/domapi.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/svg/patch.js", "../../.pnpm/zrender@5.6.0/node_modules/zrender/lib/svg/Painter.js", "../../.pnpm/echarts@5.5.1/node_modules/echarts/lib/renderer/installSVGRenderer.js"], "sourcesContent": ["import { isAroundZero } from './helper.js';\nvar mathSin = Math.sin;\nvar mathCos = Math.cos;\nvar PI = Math.PI;\nvar PI2 = Math.PI * 2;\nvar degree = 180 / PI;\nvar SVGPathRebuilder = (function () {\n    function SVGPathRebuilder() {\n    }\n    SVGPathRebuilder.prototype.reset = function (precision) {\n        this._start = true;\n        this._d = [];\n        this._str = '';\n        this._p = Math.pow(10, precision || 4);\n    };\n    SVGPathRebuilder.prototype.moveTo = function (x, y) {\n        this._add('M', x, y);\n    };\n    SVGPathRebuilder.prototype.lineTo = function (x, y) {\n        this._add('L', x, y);\n    };\n    SVGPathRebuilder.prototype.bezierCurveTo = function (x, y, x2, y2, x3, y3) {\n        this._add('C', x, y, x2, y2, x3, y3);\n    };\n    SVGPathRebuilder.prototype.quadraticCurveTo = function (x, y, x2, y2) {\n        this._add('Q', x, y, x2, y2);\n    };\n    SVGPathRebuilder.prototype.arc = function (cx, cy, r, startAngle, endAngle, anticlockwise) {\n        this.ellipse(cx, cy, r, r, 0, startAngle, endAngle, anticlockwise);\n    };\n    SVGPathRebuilder.prototype.ellipse = function (cx, cy, rx, ry, psi, startAngle, endAngle, anticlockwise) {\n        var dTheta = endAngle - startAngle;\n        var clockwise = !anticlockwise;\n        var dThetaPositive = Math.abs(dTheta);\n        var isCircle = isAroundZero(dThetaPositive - PI2)\n            || (clockwise ? dTheta >= PI2 : -dTheta >= PI2);\n        var unifiedTheta = dTheta > 0 ? dTheta % PI2 : (dTheta % PI2 + PI2);\n        var large = false;\n        if (isCircle) {\n            large = true;\n        }\n        else if (isAroundZero(dThetaPositive)) {\n            large = false;\n        }\n        else {\n            large = (unifiedTheta >= PI) === !!clockwise;\n        }\n        var x0 = cx + rx * mathCos(startAngle);\n        var y0 = cy + ry * mathSin(startAngle);\n        if (this._start) {\n            this._add('M', x0, y0);\n        }\n        var xRot = Math.round(psi * degree);\n        if (isCircle) {\n            var p = 1 / this._p;\n            var dTheta_1 = (clockwise ? 1 : -1) * (PI2 - p);\n            this._add('A', rx, ry, xRot, 1, +clockwise, cx + rx * mathCos(startAngle + dTheta_1), cy + ry * mathSin(startAngle + dTheta_1));\n            if (p > 1e-2) {\n                this._add('A', rx, ry, xRot, 0, +clockwise, x0, y0);\n            }\n        }\n        else {\n            var x = cx + rx * mathCos(endAngle);\n            var y = cy + ry * mathSin(endAngle);\n            this._add('A', rx, ry, xRot, +large, +clockwise, x, y);\n        }\n    };\n    SVGPathRebuilder.prototype.rect = function (x, y, w, h) {\n        this._add('M', x, y);\n        this._add('l', w, 0);\n        this._add('l', 0, h);\n        this._add('l', -w, 0);\n        this._add('Z');\n    };\n    SVGPathRebuilder.prototype.closePath = function () {\n        if (this._d.length > 0) {\n            this._add('Z');\n        }\n    };\n    SVGPathRebuilder.prototype._add = function (cmd, a, b, c, d, e, f, g, h) {\n        var vals = [];\n        var p = this._p;\n        for (var i = 1; i < arguments.length; i++) {\n            var val = arguments[i];\n            if (isNaN(val)) {\n                this._invalid = true;\n                return;\n            }\n            vals.push(Math.round(val * p) / p);\n        }\n        this._d.push(cmd + vals.join(' '));\n        this._start = cmd === 'Z';\n    };\n    SVGPathRebuilder.prototype.generateStr = function () {\n        this._str = this._invalid ? '' : this._d.join('');\n        this._d = [];\n    };\n    SVGPathRebuilder.prototype.getStr = function () {\n        return this._str;\n    };\n    return SVGPathRebuilder;\n}());\nexport default SVGPathRebuilder;\n", "import { DEFAULT_PATH_STYLE } from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineDash } from '../canvas/dashStyle.js';\nimport { map } from '../core/util.js';\nimport { normalizeColor } from './helper.js';\nvar NONE = 'none';\nvar mathRound = Math.round;\nfunction pathHasFill(style) {\n    var fill = style.fill;\n    return fill != null && fill !== NONE;\n}\nfunction pathHasStroke(style) {\n    var stroke = style.stroke;\n    return stroke != null && stroke !== NONE;\n}\nvar strokeProps = ['lineCap', 'miterLimit', 'lineJoin'];\nvar svgStrokeProps = map(strokeProps, function (prop) { return \"stroke-\" + prop.toLowerCase(); });\nexport default function mapStyleToAttrs(updateAttr, style, el, forceUpdate) {\n    var opacity = style.opacity == null ? 1 : style.opacity;\n    if (el instanceof ZRImage) {\n        updateAttr('opacity', opacity);\n        return;\n    }\n    if (pathHasFill(style)) {\n        var fill = normalizeColor(style.fill);\n        updateAttr('fill', fill.color);\n        var fillOpacity = style.fillOpacity != null\n            ? style.fillOpacity * fill.opacity * opacity\n            : fill.opacity * opacity;\n        if (forceUpdate || fillOpacity < 1) {\n            updateAttr('fill-opacity', fillOpacity);\n        }\n    }\n    else {\n        updateAttr('fill', NONE);\n    }\n    if (pathHasStroke(style)) {\n        var stroke = normalizeColor(style.stroke);\n        updateAttr('stroke', stroke.color);\n        var strokeScale = style.strokeNoScale\n            ? el.getLineScale()\n            : 1;\n        var strokeWidth = (strokeScale ? (style.lineWidth || 0) / strokeScale : 0);\n        var strokeOpacity = style.strokeOpacity != null\n            ? style.strokeOpacity * stroke.opacity * opacity\n            : stroke.opacity * opacity;\n        var strokeFirst = style.strokeFirst;\n        if (forceUpdate || strokeWidth !== 1) {\n            updateAttr('stroke-width', strokeWidth);\n        }\n        if (forceUpdate || strokeFirst) {\n            updateAttr('paint-order', strokeFirst ? 'stroke' : 'fill');\n        }\n        if (forceUpdate || strokeOpacity < 1) {\n            updateAttr('stroke-opacity', strokeOpacity);\n        }\n        if (style.lineDash) {\n            var _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n            if (lineDash) {\n                lineDashOffset = mathRound(lineDashOffset || 0);\n                updateAttr('stroke-dasharray', lineDash.join(','));\n                if (lineDashOffset || forceUpdate) {\n                    updateAttr('stroke-dashoffset', lineDashOffset);\n                }\n            }\n        }\n        else if (forceUpdate) {\n            updateAttr('stroke-dasharray', NONE);\n        }\n        for (var i = 0; i < strokeProps.length; i++) {\n            var propName = strokeProps[i];\n            if (forceUpdate || style[propName] !== DEFAULT_PATH_STYLE[propName]) {\n                var val = style[propName] || DEFAULT_PATH_STYLE[propName];\n                val && updateAttr(svgStrokeProps[i], val);\n            }\n        }\n    }\n    else if (forceUpdate) {\n        updateAttr('stroke', NONE);\n    }\n}\n", "import { keys, map } from '../core/util.js';\nimport { encodeHTML } from '../core/dom.js';\nexport var SVGNS = 'http://www.w3.org/2000/svg';\nexport var XLINKNS = 'http://www.w3.org/1999/xlink';\nexport var XMLNS = 'http://www.w3.org/2000/xmlns/';\nexport var XML_NAMESPACE = 'http://www.w3.org/XML/1998/namespace';\nexport var META_DATA_PREFIX = 'ecmeta_';\nexport function createElement(name) {\n    return document.createElementNS(SVGNS, name);\n}\n;\nexport function createVNode(tag, key, attrs, children, text) {\n    return {\n        tag: tag,\n        attrs: attrs || {},\n        children: children,\n        text: text,\n        key: key\n    };\n}\nfunction createElementOpen(name, attrs) {\n    var attrsStr = [];\n    if (attrs) {\n        for (var key in attrs) {\n            var val = attrs[key];\n            var part = key;\n            if (val === false) {\n                continue;\n            }\n            else if (val !== true && val != null) {\n                part += \"=\\\"\" + val + \"\\\"\";\n            }\n            attrsStr.push(part);\n        }\n    }\n    return \"<\" + name + \" \" + attrsStr.join(' ') + \">\";\n}\nfunction createElementClose(name) {\n    return \"</\" + name + \">\";\n}\nexport function vNodeToString(el, opts) {\n    opts = opts || {};\n    var S = opts.newline ? '\\n' : '';\n    function convertElToString(el) {\n        var children = el.children, tag = el.tag, attrs = el.attrs, text = el.text;\n        return createElementOpen(tag, attrs)\n            + (tag !== 'style' ? encodeHTML(text) : text || '')\n            + (children ? \"\" + S + map(children, function (child) { return convertElToString(child); }).join(S) + S : '')\n            + createElementClose(tag);\n    }\n    return convertElToString(el);\n}\nexport function getCssString(selectorNodes, animationNodes, opts) {\n    opts = opts || {};\n    var S = opts.newline ? '\\n' : '';\n    var bracketBegin = \" {\" + S;\n    var bracketEnd = S + \"}\";\n    var selectors = map(keys(selectorNodes), function (className) {\n        return className + bracketBegin + map(keys(selectorNodes[className]), function (attrName) {\n            return attrName + \":\" + selectorNodes[className][attrName] + \";\";\n        }).join(S) + bracketEnd;\n    }).join(S);\n    var animations = map(keys(animationNodes), function (animationName) {\n        return \"@keyframes \" + animationName + bracketBegin + map(keys(animationNodes[animationName]), function (percent) {\n            return percent + bracketBegin + map(keys(animationNodes[animationName][percent]), function (attrName) {\n                var val = animationNodes[animationName][percent][attrName];\n                if (attrName === 'd') {\n                    val = \"path(\\\"\" + val + \"\\\")\";\n                }\n                return attrName + \":\" + val + \";\";\n            }).join(S) + bracketEnd;\n        }).join(S) + bracketEnd;\n    }).join(S);\n    if (!selectors && !animations) {\n        return '';\n    }\n    return ['<![CDATA[', selectors, animations, ']]>'].join(S);\n}\nexport function createBrushScope(zrId) {\n    return {\n        zrId: zrId,\n        shadowCache: {},\n        patternCache: {},\n        gradientCache: {},\n        clipPathCache: {},\n        defs: {},\n        cssNodes: {},\n        cssAnims: {},\n        cssStyleCache: {},\n        cssAnimIdx: 0,\n        shadowIdx: 0,\n        gradientIdx: 0,\n        patternIdx: 0,\n        clipPathIdx: 0\n    };\n}\nexport function createSVGVNode(width, height, children, useViewBox) {\n    return createVNode('svg', 'root', {\n        'width': width,\n        'height': height,\n        'xmlns': SVGNS,\n        'xmlns:xlink': XLINKNS,\n        'version': '1.1',\n        'baseProfile': 'full',\n        'viewBox': useViewBox ? \"0 0 \" + width + \" \" + height : false\n    }, children);\n}\n", "var cssClassIdx = 0;\nexport function getClassId() {\n    return cssClassIdx++;\n}\n", "import { copyTransform } from '../core/Transformable.js';\nimport { createBrushScope } from './core.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport PathProxy from '../core/PathProxy.js';\nimport { getPathPrecision, getSRTTransformString } from './helper.js';\nimport { each, extend, filter, isNumber, isString, keys } from '../core/util.js';\nimport CompoundPath from '../graphic/CompoundPath.js';\nimport { createCubicEasingFunc } from '../animation/cubicEasing.js';\nimport { getClassId } from './cssClassId.js';\nexport var EASING_MAP = {\n    cubicIn: '0.32,0,0.67,0',\n    cubicOut: '0.33,1,0.68,1',\n    cubicInOut: '0.65,0,0.35,1',\n    quadraticIn: '0.11,0,0.5,0',\n    quadraticOut: '0.5,1,0.89,1',\n    quadraticInOut: '0.45,0,0.55,1',\n    quarticIn: '0.5,0,0.75,0',\n    quarticOut: '0.25,1,0.5,1',\n    quarticInOut: '0.76,0,0.24,1',\n    quinticIn: '0.64,0,0.78,0',\n    quinticOut: '0.22,1,0.36,1',\n    quinticInOut: '0.83,0,0.17,1',\n    sinusoidalIn: '0.12,0,0.39,0',\n    sinusoidalOut: '0.61,1,0.88,1',\n    sinusoidalInOut: '0.37,0,0.63,1',\n    exponentialIn: '0.7,0,0.84,0',\n    exponentialOut: '0.16,1,0.3,1',\n    exponentialInOut: '0.87,0,0.13,1',\n    circularIn: '0.55,0,1,0.45',\n    circularOut: '0,0.55,0.45,1',\n    circularInOut: '0.85,0,0.15,1'\n};\nvar transformOriginKey = 'transform-origin';\nfunction buildPathString(el, kfShape, path) {\n    var shape = extend({}, el.shape);\n    extend(shape, kfShape);\n    el.buildPath(path, shape);\n    var svgPathBuilder = new SVGPathRebuilder();\n    svgPathBuilder.reset(getPathPrecision(el));\n    path.rebuildPath(svgPathBuilder, 1);\n    svgPathBuilder.generateStr();\n    return svgPathBuilder.getStr();\n}\nfunction setTransformOrigin(target, transform) {\n    var originX = transform.originX, originY = transform.originY;\n    if (originX || originY) {\n        target[transformOriginKey] = originX + \"px \" + originY + \"px\";\n    }\n}\nexport var ANIMATE_STYLE_MAP = {\n    fill: 'fill',\n    opacity: 'opacity',\n    lineWidth: 'stroke-width',\n    lineDashOffset: 'stroke-dashoffset'\n};\nfunction addAnimation(cssAnim, scope) {\n    var animationName = scope.zrId + '-ani-' + scope.cssAnimIdx++;\n    scope.cssAnims[animationName] = cssAnim;\n    return animationName;\n}\nfunction createCompoundPathCSSAnimation(el, attrs, scope) {\n    var paths = el.shape.paths;\n    var composedAnim = {};\n    var cssAnimationCfg;\n    var cssAnimationName;\n    each(paths, function (path) {\n        var subScope = createBrushScope(scope.zrId);\n        subScope.animation = true;\n        createCSSAnimation(path, {}, subScope, true);\n        var cssAnims = subScope.cssAnims;\n        var cssNodes = subScope.cssNodes;\n        var animNames = keys(cssAnims);\n        var len = animNames.length;\n        if (!len) {\n            return;\n        }\n        cssAnimationName = animNames[len - 1];\n        var lastAnim = cssAnims[cssAnimationName];\n        for (var percent in lastAnim) {\n            var kf = lastAnim[percent];\n            composedAnim[percent] = composedAnim[percent] || { d: '' };\n            composedAnim[percent].d += kf.d || '';\n        }\n        for (var className in cssNodes) {\n            var val = cssNodes[className].animation;\n            if (val.indexOf(cssAnimationName) >= 0) {\n                cssAnimationCfg = val;\n            }\n        }\n    });\n    if (!cssAnimationCfg) {\n        return;\n    }\n    attrs.d = false;\n    var animationName = addAnimation(composedAnim, scope);\n    return cssAnimationCfg.replace(cssAnimationName, animationName);\n}\nfunction getEasingFunc(easing) {\n    return isString(easing)\n        ? EASING_MAP[easing]\n            ? \"cubic-bezier(\" + EASING_MAP[easing] + \")\"\n            : createCubicEasingFunc(easing) ? easing : ''\n        : '';\n}\nexport function createCSSAnimation(el, attrs, scope, onlyShape) {\n    var animators = el.animators;\n    var len = animators.length;\n    var cssAnimations = [];\n    if (el instanceof CompoundPath) {\n        var animationCfg = createCompoundPathCSSAnimation(el, attrs, scope);\n        if (animationCfg) {\n            cssAnimations.push(animationCfg);\n        }\n        else if (!len) {\n            return;\n        }\n    }\n    else if (!len) {\n        return;\n    }\n    var groupAnimators = {};\n    for (var i = 0; i < len; i++) {\n        var animator = animators[i];\n        var cfgArr = [animator.getMaxTime() / 1000 + 's'];\n        var easing = getEasingFunc(animator.getClip().easing);\n        var delay = animator.getDelay();\n        if (easing) {\n            cfgArr.push(easing);\n        }\n        else {\n            cfgArr.push('linear');\n        }\n        if (delay) {\n            cfgArr.push(delay / 1000 + 's');\n        }\n        if (animator.getLoop()) {\n            cfgArr.push('infinite');\n        }\n        var cfg = cfgArr.join(' ');\n        groupAnimators[cfg] = groupAnimators[cfg] || [cfg, []];\n        groupAnimators[cfg][1].push(animator);\n    }\n    function createSingleCSSAnimation(groupAnimator) {\n        var animators = groupAnimator[1];\n        var len = animators.length;\n        var transformKfs = {};\n        var shapeKfs = {};\n        var finalKfs = {};\n        var animationTimingFunctionAttrName = 'animation-timing-function';\n        function saveAnimatorTrackToCssKfs(animator, cssKfs, toCssAttrName) {\n            var tracks = animator.getTracks();\n            var maxTime = animator.getMaxTime();\n            for (var k = 0; k < tracks.length; k++) {\n                var track = tracks[k];\n                if (track.needsAnimate()) {\n                    var kfs = track.keyframes;\n                    var attrName = track.propName;\n                    toCssAttrName && (attrName = toCssAttrName(attrName));\n                    if (attrName) {\n                        for (var i = 0; i < kfs.length; i++) {\n                            var kf = kfs[i];\n                            var percent = Math.round(kf.time / maxTime * 100) + '%';\n                            var kfEasing = getEasingFunc(kf.easing);\n                            var rawValue = kf.rawValue;\n                            if (isString(rawValue) || isNumber(rawValue)) {\n                                cssKfs[percent] = cssKfs[percent] || {};\n                                cssKfs[percent][attrName] = kf.rawValue;\n                                if (kfEasing) {\n                                    cssKfs[percent][animationTimingFunctionAttrName] = kfEasing;\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        for (var i = 0; i < len; i++) {\n            var animator = animators[i];\n            var targetProp = animator.targetName;\n            if (!targetProp) {\n                !onlyShape && saveAnimatorTrackToCssKfs(animator, transformKfs);\n            }\n            else if (targetProp === 'shape') {\n                saveAnimatorTrackToCssKfs(animator, shapeKfs);\n            }\n        }\n        for (var percent in transformKfs) {\n            var transform = {};\n            copyTransform(transform, el);\n            extend(transform, transformKfs[percent]);\n            var str = getSRTTransformString(transform);\n            var timingFunction = transformKfs[percent][animationTimingFunctionAttrName];\n            finalKfs[percent] = str ? {\n                transform: str\n            } : {};\n            setTransformOrigin(finalKfs[percent], transform);\n            if (timingFunction) {\n                finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n            }\n        }\n        ;\n        var path;\n        var canAnimateShape = true;\n        for (var percent in shapeKfs) {\n            finalKfs[percent] = finalKfs[percent] || {};\n            var isFirst = !path;\n            var timingFunction = shapeKfs[percent][animationTimingFunctionAttrName];\n            if (isFirst) {\n                path = new PathProxy();\n            }\n            var len_1 = path.len();\n            path.reset();\n            finalKfs[percent].d = buildPathString(el, shapeKfs[percent], path);\n            var newLen = path.len();\n            if (!isFirst && len_1 !== newLen) {\n                canAnimateShape = false;\n                break;\n            }\n            if (timingFunction) {\n                finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n            }\n        }\n        ;\n        if (!canAnimateShape) {\n            for (var percent in finalKfs) {\n                delete finalKfs[percent].d;\n            }\n        }\n        if (!onlyShape) {\n            for (var i = 0; i < len; i++) {\n                var animator = animators[i];\n                var targetProp = animator.targetName;\n                if (targetProp === 'style') {\n                    saveAnimatorTrackToCssKfs(animator, finalKfs, function (propName) { return ANIMATE_STYLE_MAP[propName]; });\n                }\n            }\n        }\n        var percents = keys(finalKfs);\n        var allTransformOriginSame = true;\n        var transformOrigin;\n        for (var i = 1; i < percents.length; i++) {\n            var p0 = percents[i - 1];\n            var p1 = percents[i];\n            if (finalKfs[p0][transformOriginKey] !== finalKfs[p1][transformOriginKey]) {\n                allTransformOriginSame = false;\n                break;\n            }\n            transformOrigin = finalKfs[p0][transformOriginKey];\n        }\n        if (allTransformOriginSame && transformOrigin) {\n            for (var percent in finalKfs) {\n                if (finalKfs[percent][transformOriginKey]) {\n                    delete finalKfs[percent][transformOriginKey];\n                }\n            }\n            attrs[transformOriginKey] = transformOrigin;\n        }\n        if (filter(percents, function (percent) { return keys(finalKfs[percent]).length > 0; }).length) {\n            var animationName = addAnimation(finalKfs, scope);\n            return animationName + \" \" + groupAnimator[0] + \" both\";\n        }\n    }\n    for (var key in groupAnimators) {\n        var animationCfg = createSingleCSSAnimation(groupAnimators[key]);\n        if (animationCfg) {\n            cssAnimations.push(animationCfg);\n        }\n    }\n    if (cssAnimations.length) {\n        var className = scope.zrId + '-cls-' + getClassId();\n        scope.cssNodes['.' + className] = {\n            animation: cssAnimations.join(',')\n        };\n        attrs[\"class\"] = className;\n    }\n}\n", "import { liftColor } from '../tool/color.js';\nimport { getClassId } from './cssClassId.js';\nexport function createCSSEmphasis(el, attrs, scope) {\n    if (!el.ignore) {\n        if (el.isSilent()) {\n            var style = {\n                'pointer-events': 'none'\n            };\n            setClassAttribute(style, attrs, scope, true);\n        }\n        else {\n            var emphasisStyle = el.states.emphasis && el.states.emphasis.style\n                ? el.states.emphasis.style\n                : {};\n            var fill = emphasisStyle.fill;\n            if (!fill) {\n                var normalFill = el.style && el.style.fill;\n                var selectFill = el.states.select\n                    && el.states.select.style\n                    && el.states.select.style.fill;\n                var fromFill = el.currentStates.indexOf('select') >= 0\n                    ? (selectFill || normalFill)\n                    : normalFill;\n                if (fromFill) {\n                    fill = liftColor(fromFill);\n                }\n            }\n            var lineWidth = emphasisStyle.lineWidth;\n            if (lineWidth) {\n                var scaleX = (!emphasisStyle.strokeNoScale && el.transform)\n                    ? el.transform[0]\n                    : 1;\n                lineWidth = lineWidth / scaleX;\n            }\n            var style = {\n                cursor: 'pointer'\n            };\n            if (fill) {\n                style.fill = fill;\n            }\n            if (emphasisStyle.stroke) {\n                style.stroke = emphasisStyle.stroke;\n            }\n            if (lineWidth) {\n                style['stroke-width'] = lineWidth;\n            }\n            setClassAttribute(style, attrs, scope, true);\n        }\n    }\n}\nfunction setClassAttribute(style, attrs, scope, withHover) {\n    var styleKey = JSON.stringify(style);\n    var className = scope.cssStyleCache[styleKey];\n    if (!className) {\n        className = scope.zrId + '-cls-' + getClassId();\n        scope.cssStyleCache[styleKey] = className;\n        scope.cssNodes['.' + className + (withHover ? ':hover' : '')] = style;\n    }\n    attrs[\"class\"] = attrs[\"class\"] ? (attrs[\"class\"] + ' ' + className) : className;\n}\n", "import { adjustTextY, getIdURL, getMatrixStr, getPathPrecision, getShadow<PERSON>ey, getSRTTransformString, hasShadow, isAroundZero, isGradient, isImagePattern, isLinearGradient, isPattern, isRadialGradient, normalizeColor, round4, TEXT_ALIGN_TO_ANCHOR } from './helper.js';\nimport Path from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineHeight } from '../contain/text.js';\nimport TSpan from '../graphic/TSpan.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport mapStyleToAttrs from './mapStyleToAttrs.js';\nimport { createVNode, vNodeToString, META_DATA_PREFIX } from './core.js';\nimport { assert, clone, isFunction, isString, logError, map, retrieve2 } from '../core/util.js';\nimport { createOrUpdateImage } from '../graphic/helper/image.js';\nimport { createCSSAnimation } from './cssAnimation.js';\nimport { hasSeparateFont, parseFontSize } from '../graphic/Text.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_FAMILY } from '../core/platform.js';\nimport { createCSSEmphasis } from './cssEmphasis.js';\nimport { getElementSSRData } from '../zrender.js';\nvar round = Math.round;\nfunction isImageLike(val) {\n    return val && isString(val.src);\n}\nfunction isCanvasLike(val) {\n    return val && isFunction(val.toDataURL);\n}\nfunction setStyleAttrs(attrs, style, el, scope) {\n    mapStyleToAttrs(function (key, val) {\n        var isFillStroke = key === 'fill' || key === 'stroke';\n        if (isFillStroke && isGradient(val)) {\n            setGradient(style, attrs, key, scope);\n        }\n        else if (isFillStroke && isPattern(val)) {\n            setPattern(el, attrs, key, scope);\n        }\n        else if (isFillStroke && val === 'none') {\n            attrs[key] = 'transparent';\n        }\n        else {\n            attrs[key] = val;\n        }\n    }, style, el, false);\n    setShadow(el, attrs, scope);\n}\nfunction setMetaData(attrs, el) {\n    var metaData = getElementSSRData(el);\n    if (metaData) {\n        metaData.each(function (val, key) {\n            val != null && (attrs[(META_DATA_PREFIX + key).toLowerCase()] = val + '');\n        });\n        if (el.isSilent()) {\n            attrs[META_DATA_PREFIX + 'silent'] = 'true';\n        }\n    }\n}\nfunction noRotateScale(m) {\n    return isAroundZero(m[0] - 1)\n        && isAroundZero(m[1])\n        && isAroundZero(m[2])\n        && isAroundZero(m[3] - 1);\n}\nfunction noTranslate(m) {\n    return isAroundZero(m[4]) && isAroundZero(m[5]);\n}\nfunction setTransform(attrs, m, compress) {\n    if (m && !(noTranslate(m) && noRotateScale(m))) {\n        var mul = compress ? 10 : 1e4;\n        attrs.transform = noRotateScale(m)\n            ? \"translate(\" + round(m[4] * mul) / mul + \" \" + round(m[5] * mul) / mul + \")\" : getMatrixStr(m);\n    }\n}\nfunction convertPolyShape(shape, attrs, mul) {\n    var points = shape.points;\n    var strArr = [];\n    for (var i = 0; i < points.length; i++) {\n        strArr.push(round(points[i][0] * mul) / mul);\n        strArr.push(round(points[i][1] * mul) / mul);\n    }\n    attrs.points = strArr.join(' ');\n}\nfunction validatePolyShape(shape) {\n    return !shape.smooth;\n}\nfunction createAttrsConvert(desc) {\n    var normalizedDesc = map(desc, function (item) {\n        return (typeof item === 'string' ? [item, item] : item);\n    });\n    return function (shape, attrs, mul) {\n        for (var i = 0; i < normalizedDesc.length; i++) {\n            var item = normalizedDesc[i];\n            var val = shape[item[0]];\n            if (val != null) {\n                attrs[item[1]] = round(val * mul) / mul;\n            }\n        }\n    };\n}\nvar builtinShapesDef = {\n    circle: [createAttrsConvert(['cx', 'cy', 'r'])],\n    polyline: [convertPolyShape, validatePolyShape],\n    polygon: [convertPolyShape, validatePolyShape]\n};\nfunction hasShapeAnimation(el) {\n    var animators = el.animators;\n    for (var i = 0; i < animators.length; i++) {\n        if (animators[i].targetName === 'shape') {\n            return true;\n        }\n    }\n    return false;\n}\nexport function brushSVGPath(el, scope) {\n    var style = el.style;\n    var shape = el.shape;\n    var builtinShpDef = builtinShapesDef[el.type];\n    var attrs = {};\n    var needsAnimate = scope.animation;\n    var svgElType = 'path';\n    var strokePercent = el.style.strokePercent;\n    var precision = (scope.compress && getPathPrecision(el)) || 4;\n    if (builtinShpDef\n        && !scope.willUpdate\n        && !(builtinShpDef[1] && !builtinShpDef[1](shape))\n        && !(needsAnimate && hasShapeAnimation(el))\n        && !(strokePercent < 1)) {\n        svgElType = el.type;\n        var mul = Math.pow(10, precision);\n        builtinShpDef[0](shape, attrs, mul);\n    }\n    else {\n        var needBuildPath = !el.path || el.shapeChanged();\n        if (!el.path) {\n            el.createPathProxy();\n        }\n        var path = el.path;\n        if (needBuildPath) {\n            path.beginPath();\n            el.buildPath(path, el.shape);\n            el.pathUpdated();\n        }\n        var pathVersion = path.getVersion();\n        var elExt = el;\n        var svgPathBuilder = elExt.__svgPathBuilder;\n        if (elExt.__svgPathVersion !== pathVersion\n            || !svgPathBuilder\n            || strokePercent !== elExt.__svgPathStrokePercent) {\n            if (!svgPathBuilder) {\n                svgPathBuilder = elExt.__svgPathBuilder = new SVGPathRebuilder();\n            }\n            svgPathBuilder.reset(precision);\n            path.rebuildPath(svgPathBuilder, strokePercent);\n            svgPathBuilder.generateStr();\n            elExt.__svgPathVersion = pathVersion;\n            elExt.__svgPathStrokePercent = strokePercent;\n        }\n        attrs.d = svgPathBuilder.getStr();\n    }\n    setTransform(attrs, el.transform);\n    setStyleAttrs(attrs, style, el, scope);\n    setMetaData(attrs, el);\n    scope.animation && createCSSAnimation(el, attrs, scope);\n    scope.emphasis && createCSSEmphasis(el, attrs, scope);\n    return createVNode(svgElType, el.id + '', attrs);\n}\nexport function brushSVGImage(el, scope) {\n    var style = el.style;\n    var image = style.image;\n    if (image && !isString(image)) {\n        if (isImageLike(image)) {\n            image = image.src;\n        }\n        else if (isCanvasLike(image)) {\n            image = image.toDataURL();\n        }\n    }\n    if (!image) {\n        return;\n    }\n    var x = style.x || 0;\n    var y = style.y || 0;\n    var dw = style.width;\n    var dh = style.height;\n    var attrs = {\n        href: image,\n        width: dw,\n        height: dh\n    };\n    if (x) {\n        attrs.x = x;\n    }\n    if (y) {\n        attrs.y = y;\n    }\n    setTransform(attrs, el.transform);\n    setStyleAttrs(attrs, style, el, scope);\n    setMetaData(attrs, el);\n    scope.animation && createCSSAnimation(el, attrs, scope);\n    return createVNode('image', el.id + '', attrs);\n}\n;\nexport function brushSVGTSpan(el, scope) {\n    var style = el.style;\n    var text = style.text;\n    text != null && (text += '');\n    if (!text || isNaN(style.x) || isNaN(style.y)) {\n        return;\n    }\n    var font = style.font || DEFAULT_FONT;\n    var x = style.x || 0;\n    var y = adjustTextY(style.y || 0, getLineHeight(font), style.textBaseline);\n    var textAlign = TEXT_ALIGN_TO_ANCHOR[style.textAlign]\n        || style.textAlign;\n    var attrs = {\n        'dominant-baseline': 'central',\n        'text-anchor': textAlign\n    };\n    if (hasSeparateFont(style)) {\n        var separatedFontStr = '';\n        var fontStyle = style.fontStyle;\n        var fontSize = parseFontSize(style.fontSize);\n        if (!parseFloat(fontSize)) {\n            return;\n        }\n        var fontFamily = style.fontFamily || DEFAULT_FONT_FAMILY;\n        var fontWeight = style.fontWeight;\n        separatedFontStr += \"font-size:\" + fontSize + \";font-family:\" + fontFamily + \";\";\n        if (fontStyle && fontStyle !== 'normal') {\n            separatedFontStr += \"font-style:\" + fontStyle + \";\";\n        }\n        if (fontWeight && fontWeight !== 'normal') {\n            separatedFontStr += \"font-weight:\" + fontWeight + \";\";\n        }\n        attrs.style = separatedFontStr;\n    }\n    else {\n        attrs.style = \"font: \" + font;\n    }\n    if (text.match(/\\s/)) {\n        attrs['xml:space'] = 'preserve';\n    }\n    if (x) {\n        attrs.x = x;\n    }\n    if (y) {\n        attrs.y = y;\n    }\n    setTransform(attrs, el.transform);\n    setStyleAttrs(attrs, style, el, scope);\n    setMetaData(attrs, el);\n    scope.animation && createCSSAnimation(el, attrs, scope);\n    return createVNode('text', el.id + '', attrs, undefined, text);\n}\nexport function brush(el, scope) {\n    if (el instanceof Path) {\n        return brushSVGPath(el, scope);\n    }\n    else if (el instanceof ZRImage) {\n        return brushSVGImage(el, scope);\n    }\n    else if (el instanceof TSpan) {\n        return brushSVGTSpan(el, scope);\n    }\n}\nfunction setShadow(el, attrs, scope) {\n    var style = el.style;\n    if (hasShadow(style)) {\n        var shadowKey = getShadowKey(el);\n        var shadowCache = scope.shadowCache;\n        var shadowId = shadowCache[shadowKey];\n        if (!shadowId) {\n            var globalScale = el.getGlobalScale();\n            var scaleX = globalScale[0];\n            var scaleY = globalScale[1];\n            if (!scaleX || !scaleY) {\n                return;\n            }\n            var offsetX = style.shadowOffsetX || 0;\n            var offsetY = style.shadowOffsetY || 0;\n            var blur_1 = style.shadowBlur;\n            var _a = normalizeColor(style.shadowColor), opacity = _a.opacity, color = _a.color;\n            var stdDx = blur_1 / 2 / scaleX;\n            var stdDy = blur_1 / 2 / scaleY;\n            var stdDeviation = stdDx + ' ' + stdDy;\n            shadowId = scope.zrId + '-s' + scope.shadowIdx++;\n            scope.defs[shadowId] = createVNode('filter', shadowId, {\n                'id': shadowId,\n                'x': '-100%',\n                'y': '-100%',\n                'width': '300%',\n                'height': '300%'\n            }, [\n                createVNode('feDropShadow', '', {\n                    'dx': offsetX / scaleX,\n                    'dy': offsetY / scaleY,\n                    'stdDeviation': stdDeviation,\n                    'flood-color': color,\n                    'flood-opacity': opacity\n                })\n            ]);\n            shadowCache[shadowKey] = shadowId;\n        }\n        attrs.filter = getIdURL(shadowId);\n    }\n}\nexport function setGradient(style, attrs, target, scope) {\n    var val = style[target];\n    var gradientTag;\n    var gradientAttrs = {\n        'gradientUnits': val.global\n            ? 'userSpaceOnUse'\n            : 'objectBoundingBox'\n    };\n    if (isLinearGradient(val)) {\n        gradientTag = 'linearGradient';\n        gradientAttrs.x1 = val.x;\n        gradientAttrs.y1 = val.y;\n        gradientAttrs.x2 = val.x2;\n        gradientAttrs.y2 = val.y2;\n    }\n    else if (isRadialGradient(val)) {\n        gradientTag = 'radialGradient';\n        gradientAttrs.cx = retrieve2(val.x, 0.5);\n        gradientAttrs.cy = retrieve2(val.y, 0.5);\n        gradientAttrs.r = retrieve2(val.r, 0.5);\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            logError('Illegal gradient type.');\n        }\n        return;\n    }\n    var colors = val.colorStops;\n    var colorStops = [];\n    for (var i = 0, len = colors.length; i < len; ++i) {\n        var offset = round4(colors[i].offset) * 100 + '%';\n        var stopColor = colors[i].color;\n        var _a = normalizeColor(stopColor), color = _a.color, opacity = _a.opacity;\n        var stopsAttrs = {\n            'offset': offset\n        };\n        stopsAttrs['stop-color'] = color;\n        if (opacity < 1) {\n            stopsAttrs['stop-opacity'] = opacity;\n        }\n        colorStops.push(createVNode('stop', i + '', stopsAttrs));\n    }\n    var gradientVNode = createVNode(gradientTag, '', gradientAttrs, colorStops);\n    var gradientKey = vNodeToString(gradientVNode);\n    var gradientCache = scope.gradientCache;\n    var gradientId = gradientCache[gradientKey];\n    if (!gradientId) {\n        gradientId = scope.zrId + '-g' + scope.gradientIdx++;\n        gradientCache[gradientKey] = gradientId;\n        gradientAttrs.id = gradientId;\n        scope.defs[gradientId] = createVNode(gradientTag, gradientId, gradientAttrs, colorStops);\n    }\n    attrs[target] = getIdURL(gradientId);\n}\nexport function setPattern(el, attrs, target, scope) {\n    var val = el.style[target];\n    var boundingRect = el.getBoundingRect();\n    var patternAttrs = {};\n    var repeat = val.repeat;\n    var noRepeat = repeat === 'no-repeat';\n    var repeatX = repeat === 'repeat-x';\n    var repeatY = repeat === 'repeat-y';\n    var child;\n    if (isImagePattern(val)) {\n        var imageWidth_1 = val.imageWidth;\n        var imageHeight_1 = val.imageHeight;\n        var imageSrc = void 0;\n        var patternImage = val.image;\n        if (isString(patternImage)) {\n            imageSrc = patternImage;\n        }\n        else if (isImageLike(patternImage)) {\n            imageSrc = patternImage.src;\n        }\n        else if (isCanvasLike(patternImage)) {\n            imageSrc = patternImage.toDataURL();\n        }\n        if (typeof Image === 'undefined') {\n            var errMsg = 'Image width/height must been given explictly in svg-ssr renderer.';\n            assert(imageWidth_1, errMsg);\n            assert(imageHeight_1, errMsg);\n        }\n        else if (imageWidth_1 == null || imageHeight_1 == null) {\n            var setSizeToVNode_1 = function (vNode, img) {\n                if (vNode) {\n                    var svgEl = vNode.elm;\n                    var width = imageWidth_1 || img.width;\n                    var height = imageHeight_1 || img.height;\n                    if (vNode.tag === 'pattern') {\n                        if (repeatX) {\n                            height = 1;\n                            width /= boundingRect.width;\n                        }\n                        else if (repeatY) {\n                            width = 1;\n                            height /= boundingRect.height;\n                        }\n                    }\n                    vNode.attrs.width = width;\n                    vNode.attrs.height = height;\n                    if (svgEl) {\n                        svgEl.setAttribute('width', width);\n                        svgEl.setAttribute('height', height);\n                    }\n                }\n            };\n            var createdImage = createOrUpdateImage(imageSrc, null, el, function (img) {\n                noRepeat || setSizeToVNode_1(patternVNode, img);\n                setSizeToVNode_1(child, img);\n            });\n            if (createdImage && createdImage.width && createdImage.height) {\n                imageWidth_1 = imageWidth_1 || createdImage.width;\n                imageHeight_1 = imageHeight_1 || createdImage.height;\n            }\n        }\n        child = createVNode('image', 'img', {\n            href: imageSrc,\n            width: imageWidth_1,\n            height: imageHeight_1\n        });\n        patternAttrs.width = imageWidth_1;\n        patternAttrs.height = imageHeight_1;\n    }\n    else if (val.svgElement) {\n        child = clone(val.svgElement);\n        patternAttrs.width = val.svgWidth;\n        patternAttrs.height = val.svgHeight;\n    }\n    if (!child) {\n        return;\n    }\n    var patternWidth;\n    var patternHeight;\n    if (noRepeat) {\n        patternWidth = patternHeight = 1;\n    }\n    else if (repeatX) {\n        patternHeight = 1;\n        patternWidth = patternAttrs.width / boundingRect.width;\n    }\n    else if (repeatY) {\n        patternWidth = 1;\n        patternHeight = patternAttrs.height / boundingRect.height;\n    }\n    else {\n        patternAttrs.patternUnits = 'userSpaceOnUse';\n    }\n    if (patternWidth != null && !isNaN(patternWidth)) {\n        patternAttrs.width = patternWidth;\n    }\n    if (patternHeight != null && !isNaN(patternHeight)) {\n        patternAttrs.height = patternHeight;\n    }\n    var patternTransform = getSRTTransformString(val);\n    patternTransform && (patternAttrs.patternTransform = patternTransform);\n    var patternVNode = createVNode('pattern', '', patternAttrs, [child]);\n    var patternKey = vNodeToString(patternVNode);\n    var patternCache = scope.patternCache;\n    var patternId = patternCache[patternKey];\n    if (!patternId) {\n        patternId = scope.zrId + '-p' + scope.patternIdx++;\n        patternCache[patternKey] = patternId;\n        patternAttrs.id = patternId;\n        patternVNode = scope.defs[patternId] = createVNode('pattern', patternId, patternAttrs, [child]);\n    }\n    attrs[target] = getIdURL(patternId);\n}\nexport function setClipPath(clipPath, attrs, scope) {\n    var clipPathCache = scope.clipPathCache, defs = scope.defs;\n    var clipPathId = clipPathCache[clipPath.id];\n    if (!clipPathId) {\n        clipPathId = scope.zrId + '-c' + scope.clipPathIdx++;\n        var clipPathAttrs = {\n            id: clipPathId\n        };\n        clipPathCache[clipPath.id] = clipPathId;\n        defs[clipPathId] = createVNode('clipPath', clipPathId, clipPathAttrs, [brushSVGPath(clipPath, scope)]);\n    }\n    attrs['clip-path'] = getIdURL(clipPathId);\n}\n", "export function createTextNode(text) {\n    return document.createTextNode(text);\n}\nexport function createComment(text) {\n    return document.createComment(text);\n}\nexport function insertBefore(parentNode, newNode, referenceNode) {\n    parentNode.insertBefore(newNode, referenceNode);\n}\nexport function removeChild(node, child) {\n    node.removeChild(child);\n}\nexport function appendChild(node, child) {\n    node.appendChild(child);\n}\nexport function parentNode(node) {\n    return node.parentNode;\n}\nexport function nextSibling(node) {\n    return node.nextSibling;\n}\nexport function tagName(elm) {\n    return elm.tagName;\n}\nexport function setTextContent(node, text) {\n    node.textContent = text;\n}\nexport function getTextContent(node) {\n    return node.textContent;\n}\nexport function isElement(node) {\n    return node.nodeType === 1;\n}\nexport function isText(node) {\n    return node.nodeType === 3;\n}\nexport function isComment(node) {\n    return node.nodeType === 8;\n}\n", "import { isArray, isObject } from '../core/util.js';\nimport { createElement, createVNode, XMLNS, XML_NAMESPACE, XLINKNS } from './core.js';\nimport * as api from './domapi.js';\nvar colonChar = 58;\nvar xChar = 120;\nvar emptyNode = createVNode('', '');\nfunction isUndef(s) {\n    return s === undefined;\n}\nfunction isDef(s) {\n    return s !== undefined;\n}\nfunction createKeyToOldIdx(children, beginIdx, endIdx) {\n    var map = {};\n    for (var i = beginIdx; i <= endIdx; ++i) {\n        var key = children[i].key;\n        if (key !== undefined) {\n            if (process.env.NODE_ENV !== 'production') {\n                if (map[key] != null) {\n                    console.error(\"Duplicate key \" + key);\n                }\n            }\n            map[key] = i;\n        }\n    }\n    return map;\n}\nfunction sameVnode(vnode1, vnode2) {\n    var isSameKey = vnode1.key === vnode2.key;\n    var isSameTag = vnode1.tag === vnode2.tag;\n    return isSameTag && isSameKey;\n}\nfunction createElm(vnode) {\n    var i;\n    var children = vnode.children;\n    var tag = vnode.tag;\n    if (isDef(tag)) {\n        var elm = (vnode.elm = createElement(tag));\n        updateAttrs(emptyNode, vnode);\n        if (isArray(children)) {\n            for (i = 0; i < children.length; ++i) {\n                var ch = children[i];\n                if (ch != null) {\n                    api.appendChild(elm, createElm(ch));\n                }\n            }\n        }\n        else if (isDef(vnode.text) && !isObject(vnode.text)) {\n            api.appendChild(elm, api.createTextNode(vnode.text));\n        }\n    }\n    else {\n        vnode.elm = api.createTextNode(vnode.text);\n    }\n    return vnode.elm;\n}\nfunction addVnodes(parentElm, before, vnodes, startIdx, endIdx) {\n    for (; startIdx <= endIdx; ++startIdx) {\n        var ch = vnodes[startIdx];\n        if (ch != null) {\n            api.insertBefore(parentElm, createElm(ch), before);\n        }\n    }\n}\nfunction removeVnodes(parentElm, vnodes, startIdx, endIdx) {\n    for (; startIdx <= endIdx; ++startIdx) {\n        var ch = vnodes[startIdx];\n        if (ch != null) {\n            if (isDef(ch.tag)) {\n                var parent_1 = api.parentNode(ch.elm);\n                api.removeChild(parent_1, ch.elm);\n            }\n            else {\n                api.removeChild(parentElm, ch.elm);\n            }\n        }\n    }\n}\nexport function updateAttrs(oldVnode, vnode) {\n    var key;\n    var elm = vnode.elm;\n    var oldAttrs = oldVnode && oldVnode.attrs || {};\n    var attrs = vnode.attrs || {};\n    if (oldAttrs === attrs) {\n        return;\n    }\n    for (key in attrs) {\n        var cur = attrs[key];\n        var old = oldAttrs[key];\n        if (old !== cur) {\n            if (cur === true) {\n                elm.setAttribute(key, '');\n            }\n            else if (cur === false) {\n                elm.removeAttribute(key);\n            }\n            else {\n                if (key === 'style') {\n                    elm.style.cssText = cur;\n                }\n                else if (key.charCodeAt(0) !== xChar) {\n                    elm.setAttribute(key, cur);\n                }\n                else if (key === 'xmlns:xlink' || key === 'xmlns') {\n                    elm.setAttributeNS(XMLNS, key, cur);\n                }\n                else if (key.charCodeAt(3) === colonChar) {\n                    elm.setAttributeNS(XML_NAMESPACE, key, cur);\n                }\n                else if (key.charCodeAt(5) === colonChar) {\n                    elm.setAttributeNS(XLINKNS, key, cur);\n                }\n                else {\n                    elm.setAttribute(key, cur);\n                }\n            }\n        }\n    }\n    for (key in oldAttrs) {\n        if (!(key in attrs)) {\n            elm.removeAttribute(key);\n        }\n    }\n}\nfunction updateChildren(parentElm, oldCh, newCh) {\n    var oldStartIdx = 0;\n    var newStartIdx = 0;\n    var oldEndIdx = oldCh.length - 1;\n    var oldStartVnode = oldCh[0];\n    var oldEndVnode = oldCh[oldEndIdx];\n    var newEndIdx = newCh.length - 1;\n    var newStartVnode = newCh[0];\n    var newEndVnode = newCh[newEndIdx];\n    var oldKeyToIdx;\n    var idxInOld;\n    var elmToMove;\n    var before;\n    while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n        if (oldStartVnode == null) {\n            oldStartVnode = oldCh[++oldStartIdx];\n        }\n        else if (oldEndVnode == null) {\n            oldEndVnode = oldCh[--oldEndIdx];\n        }\n        else if (newStartVnode == null) {\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else if (newEndVnode == null) {\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (sameVnode(oldStartVnode, newStartVnode)) {\n            patchVnode(oldStartVnode, newStartVnode);\n            oldStartVnode = oldCh[++oldStartIdx];\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else if (sameVnode(oldEndVnode, newEndVnode)) {\n            patchVnode(oldEndVnode, newEndVnode);\n            oldEndVnode = oldCh[--oldEndIdx];\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (sameVnode(oldStartVnode, newEndVnode)) {\n            patchVnode(oldStartVnode, newEndVnode);\n            api.insertBefore(parentElm, oldStartVnode.elm, api.nextSibling(oldEndVnode.elm));\n            oldStartVnode = oldCh[++oldStartIdx];\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (sameVnode(oldEndVnode, newStartVnode)) {\n            patchVnode(oldEndVnode, newStartVnode);\n            api.insertBefore(parentElm, oldEndVnode.elm, oldStartVnode.elm);\n            oldEndVnode = oldCh[--oldEndIdx];\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else {\n            if (isUndef(oldKeyToIdx)) {\n                oldKeyToIdx = createKeyToOldIdx(oldCh, oldStartIdx, oldEndIdx);\n            }\n            idxInOld = oldKeyToIdx[newStartVnode.key];\n            if (isUndef(idxInOld)) {\n                api.insertBefore(parentElm, createElm(newStartVnode), oldStartVnode.elm);\n            }\n            else {\n                elmToMove = oldCh[idxInOld];\n                if (elmToMove.tag !== newStartVnode.tag) {\n                    api.insertBefore(parentElm, createElm(newStartVnode), oldStartVnode.elm);\n                }\n                else {\n                    patchVnode(elmToMove, newStartVnode);\n                    oldCh[idxInOld] = undefined;\n                    api.insertBefore(parentElm, elmToMove.elm, oldStartVnode.elm);\n                }\n            }\n            newStartVnode = newCh[++newStartIdx];\n        }\n    }\n    if (oldStartIdx <= oldEndIdx || newStartIdx <= newEndIdx) {\n        if (oldStartIdx > oldEndIdx) {\n            before = newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].elm;\n            addVnodes(parentElm, before, newCh, newStartIdx, newEndIdx);\n        }\n        else {\n            removeVnodes(parentElm, oldCh, oldStartIdx, oldEndIdx);\n        }\n    }\n}\nfunction patchVnode(oldVnode, vnode) {\n    var elm = (vnode.elm = oldVnode.elm);\n    var oldCh = oldVnode.children;\n    var ch = vnode.children;\n    if (oldVnode === vnode) {\n        return;\n    }\n    updateAttrs(oldVnode, vnode);\n    if (isUndef(vnode.text)) {\n        if (isDef(oldCh) && isDef(ch)) {\n            if (oldCh !== ch) {\n                updateChildren(elm, oldCh, ch);\n            }\n        }\n        else if (isDef(ch)) {\n            if (isDef(oldVnode.text)) {\n                api.setTextContent(elm, '');\n            }\n            addVnodes(elm, null, ch, 0, ch.length - 1);\n        }\n        else if (isDef(oldCh)) {\n            removeVnodes(elm, oldCh, 0, oldCh.length - 1);\n        }\n        else if (isDef(oldVnode.text)) {\n            api.setTextContent(elm, '');\n        }\n    }\n    else if (oldVnode.text !== vnode.text) {\n        if (isDef(oldCh)) {\n            removeVnodes(elm, oldCh, 0, oldCh.length - 1);\n        }\n        api.setTextContent(elm, vnode.text);\n    }\n}\nexport default function patch(oldVnode, vnode) {\n    if (sameVnode(oldVnode, vnode)) {\n        patchVnode(oldVnode, vnode);\n    }\n    else {\n        var elm = oldVnode.elm;\n        var parent_2 = api.parentNode(elm);\n        createElm(vnode);\n        if (parent_2 !== null) {\n            api.insertBefore(parent_2, vnode.elm, api.nextSibling(elm));\n            removeVnodes(parent_2, [oldVnode], 0, 0);\n        }\n    }\n    return vnode;\n}\n", "import { brush, setClipPath, setGradient, setPattern } from './graphic.js';\nimport { createElement, createVNode, vNodeToString, getCssString, createBrushScope, createSVGVNode } from './core.js';\nimport { normalizeColor, encodeBase64, isGradient, isPattern } from './helper.js';\nimport { extend, keys, logError, map, noop, retrieve2 } from '../core/util.js';\nimport patch, { updateAttrs } from './patch.js';\nimport { getSize } from '../canvas/helper.js';\nvar svgId = 0;\nvar SVGPainter = (function () {\n    function SVGPainter(root, storage, opts) {\n        this.type = 'svg';\n        this.refreshHover = createMethodNotSupport('refreshHover');\n        this.configLayer = createMethodNotSupport('configLayer');\n        this.storage = storage;\n        this._opts = opts = extend({}, opts);\n        this.root = root;\n        this._id = 'zr' + svgId++;\n        this._oldVNode = createSVGVNode(opts.width, opts.height);\n        if (root && !opts.ssr) {\n            var viewport = this._viewport = document.createElement('div');\n            viewport.style.cssText = 'position:relative;overflow:hidden';\n            var svgDom = this._svgDom = this._oldVNode.elm = createElement('svg');\n            updateAttrs(null, this._oldVNode);\n            viewport.appendChild(svgDom);\n            root.appendChild(viewport);\n        }\n        this.resize(opts.width, opts.height);\n    }\n    SVGPainter.prototype.getType = function () {\n        return this.type;\n    };\n    SVGPainter.prototype.getViewportRoot = function () {\n        return this._viewport;\n    };\n    SVGPainter.prototype.getViewportRootOffset = function () {\n        var viewportRoot = this.getViewportRoot();\n        if (viewportRoot) {\n            return {\n                offsetLeft: viewportRoot.offsetLeft || 0,\n                offsetTop: viewportRoot.offsetTop || 0\n            };\n        }\n    };\n    SVGPainter.prototype.getSvgDom = function () {\n        return this._svgDom;\n    };\n    SVGPainter.prototype.refresh = function () {\n        if (this.root) {\n            var vnode = this.renderToVNode({\n                willUpdate: true\n            });\n            vnode.attrs.style = 'position:absolute;left:0;top:0;user-select:none';\n            patch(this._oldVNode, vnode);\n            this._oldVNode = vnode;\n        }\n    };\n    SVGPainter.prototype.renderOneToVNode = function (el) {\n        return brush(el, createBrushScope(this._id));\n    };\n    SVGPainter.prototype.renderToVNode = function (opts) {\n        opts = opts || {};\n        var list = this.storage.getDisplayList(true);\n        var width = this._width;\n        var height = this._height;\n        var scope = createBrushScope(this._id);\n        scope.animation = opts.animation;\n        scope.willUpdate = opts.willUpdate;\n        scope.compress = opts.compress;\n        scope.emphasis = opts.emphasis;\n        var children = [];\n        var bgVNode = this._bgVNode = createBackgroundVNode(width, height, this._backgroundColor, scope);\n        bgVNode && children.push(bgVNode);\n        var mainVNode = !opts.compress\n            ? (this._mainVNode = createVNode('g', 'main', {}, [])) : null;\n        this._paintList(list, scope, mainVNode ? mainVNode.children : children);\n        mainVNode && children.push(mainVNode);\n        var defs = map(keys(scope.defs), function (id) { return scope.defs[id]; });\n        if (defs.length) {\n            children.push(createVNode('defs', 'defs', {}, defs));\n        }\n        if (opts.animation) {\n            var animationCssStr = getCssString(scope.cssNodes, scope.cssAnims, { newline: true });\n            if (animationCssStr) {\n                var styleNode = createVNode('style', 'stl', {}, [], animationCssStr);\n                children.push(styleNode);\n            }\n        }\n        return createSVGVNode(width, height, children, opts.useViewBox);\n    };\n    SVGPainter.prototype.renderToString = function (opts) {\n        opts = opts || {};\n        return vNodeToString(this.renderToVNode({\n            animation: retrieve2(opts.cssAnimation, true),\n            emphasis: retrieve2(opts.cssEmphasis, true),\n            willUpdate: false,\n            compress: true,\n            useViewBox: retrieve2(opts.useViewBox, true)\n        }), { newline: true });\n    };\n    SVGPainter.prototype.setBackgroundColor = function (backgroundColor) {\n        this._backgroundColor = backgroundColor;\n    };\n    SVGPainter.prototype.getSvgRoot = function () {\n        return this._mainVNode && this._mainVNode.elm;\n    };\n    SVGPainter.prototype._paintList = function (list, scope, out) {\n        var listLen = list.length;\n        var clipPathsGroupsStack = [];\n        var clipPathsGroupsStackDepth = 0;\n        var currentClipPathGroup;\n        var prevClipPaths;\n        var clipGroupNodeIdx = 0;\n        for (var i = 0; i < listLen; i++) {\n            var displayable = list[i];\n            if (!displayable.invisible) {\n                var clipPaths = displayable.__clipPaths;\n                var len = clipPaths && clipPaths.length || 0;\n                var prevLen = prevClipPaths && prevClipPaths.length || 0;\n                var lca = void 0;\n                for (lca = Math.max(len - 1, prevLen - 1); lca >= 0; lca--) {\n                    if (clipPaths && prevClipPaths\n                        && clipPaths[lca] === prevClipPaths[lca]) {\n                        break;\n                    }\n                }\n                for (var i_1 = prevLen - 1; i_1 > lca; i_1--) {\n                    clipPathsGroupsStackDepth--;\n                    currentClipPathGroup = clipPathsGroupsStack[clipPathsGroupsStackDepth - 1];\n                }\n                for (var i_2 = lca + 1; i_2 < len; i_2++) {\n                    var groupAttrs = {};\n                    setClipPath(clipPaths[i_2], groupAttrs, scope);\n                    var g = createVNode('g', 'clip-g-' + clipGroupNodeIdx++, groupAttrs, []);\n                    (currentClipPathGroup ? currentClipPathGroup.children : out).push(g);\n                    clipPathsGroupsStack[clipPathsGroupsStackDepth++] = g;\n                    currentClipPathGroup = g;\n                }\n                prevClipPaths = clipPaths;\n                var ret = brush(displayable, scope);\n                if (ret) {\n                    (currentClipPathGroup ? currentClipPathGroup.children : out).push(ret);\n                }\n            }\n        }\n    };\n    SVGPainter.prototype.resize = function (width, height) {\n        var opts = this._opts;\n        var root = this.root;\n        var viewport = this._viewport;\n        width != null && (opts.width = width);\n        height != null && (opts.height = height);\n        if (root && viewport) {\n            viewport.style.display = 'none';\n            width = getSize(root, 0, opts);\n            height = getSize(root, 1, opts);\n            viewport.style.display = '';\n        }\n        if (this._width !== width || this._height !== height) {\n            this._width = width;\n            this._height = height;\n            if (viewport) {\n                var viewportStyle = viewport.style;\n                viewportStyle.width = width + 'px';\n                viewportStyle.height = height + 'px';\n            }\n            if (!isPattern(this._backgroundColor)) {\n                var svgDom = this._svgDom;\n                if (svgDom) {\n                    svgDom.setAttribute('width', width);\n                    svgDom.setAttribute('height', height);\n                }\n                var bgEl = this._bgVNode && this._bgVNode.elm;\n                if (bgEl) {\n                    bgEl.setAttribute('width', width);\n                    bgEl.setAttribute('height', height);\n                }\n            }\n            else {\n                this.refresh();\n            }\n        }\n    };\n    SVGPainter.prototype.getWidth = function () {\n        return this._width;\n    };\n    SVGPainter.prototype.getHeight = function () {\n        return this._height;\n    };\n    SVGPainter.prototype.dispose = function () {\n        if (this.root) {\n            this.root.innerHTML = '';\n        }\n        this._svgDom =\n            this._viewport =\n                this.storage =\n                    this._oldVNode =\n                        this._bgVNode =\n                            this._mainVNode = null;\n    };\n    SVGPainter.prototype.clear = function () {\n        if (this._svgDom) {\n            this._svgDom.innerHTML = null;\n        }\n        this._oldVNode = null;\n    };\n    SVGPainter.prototype.toDataURL = function (base64) {\n        var str = this.renderToString();\n        var prefix = 'data:image/svg+xml;';\n        if (base64) {\n            str = encodeBase64(str);\n            return str && prefix + 'base64,' + str;\n        }\n        return prefix + 'charset=UTF-8,' + encodeURIComponent(str);\n    };\n    return SVGPainter;\n}());\nfunction createMethodNotSupport(method) {\n    return function () {\n        if (process.env.NODE_ENV !== 'production') {\n            logError('In SVG mode painter not support method \"' + method + '\"');\n        }\n    };\n}\nfunction createBackgroundVNode(width, height, backgroundColor, scope) {\n    var bgVNode;\n    if (backgroundColor && backgroundColor !== 'none') {\n        bgVNode = createVNode('rect', 'bg', {\n            width: width,\n            height: height,\n            x: '0',\n            y: '0'\n        });\n        if (isGradient(backgroundColor)) {\n            setGradient({ fill: backgroundColor }, bgVNode.attrs, 'fill', scope);\n        }\n        else if (isPattern(backgroundColor)) {\n            setPattern({\n                style: {\n                    fill: backgroundColor\n                },\n                dirty: noop,\n                getBoundingRect: function () { return ({ width: width, height: height }); }\n            }, bgVNode.attrs, 'fill', scope);\n        }\n        else {\n            var _a = normalizeColor(backgroundColor), color = _a.color, opacity = _a.opacity;\n            bgVNode.attrs.fill = color;\n            opacity < 1 && (bgVNode.attrs['fill-opacity'] = opacity);\n        }\n    }\n    return bgVNode;\n}\nexport default SVGPainter;\n", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport SVGPainter from 'zrender/lib/svg/Painter.js';\nexport function install(registers) {\n  registers.registerPainter('svg', SVGPainter);\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAI,UAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,KAAK,KAAK;AACd,IAAI,MAAM,KAAK,KAAK;AACpB,IAAI,SAAS,MAAM;AACnB,IAAI,mBAAoB,WAAY;AAChC,WAASA,oBAAmB;AAAA,EAC5B;AACA,EAAAA,kBAAiB,UAAU,QAAQ,SAAU,WAAW;AACpD,SAAK,SAAS;AACd,SAAK,KAAK,CAAC;AACX,SAAK,OAAO;AACZ,SAAK,KAAK,KAAK,IAAI,IAAI,aAAa,CAAC;AAAA,EACzC;AACA,EAAAA,kBAAiB,UAAU,SAAS,SAAU,GAAG,GAAG;AAChD,SAAK,KAAK,KAAK,GAAG,CAAC;AAAA,EACvB;AACA,EAAAA,kBAAiB,UAAU,SAAS,SAAU,GAAG,GAAG;AAChD,SAAK,KAAK,KAAK,GAAG,CAAC;AAAA,EACvB;AACA,EAAAA,kBAAiB,UAAU,gBAAgB,SAAU,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI;AACvE,SAAK,KAAK,KAAK,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,EACvC;AACA,EAAAA,kBAAiB,UAAU,mBAAmB,SAAU,GAAG,GAAG,IAAI,IAAI;AAClE,SAAK,KAAK,KAAK,GAAG,GAAG,IAAI,EAAE;AAAA,EAC/B;AACA,EAAAA,kBAAiB,UAAU,MAAM,SAAU,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe;AACvF,SAAK,QAAQ,IAAI,IAAI,GAAG,GAAG,GAAG,YAAY,UAAU,aAAa;AAAA,EACrE;AACA,EAAAA,kBAAiB,UAAU,UAAU,SAAU,IAAI,IAAI,IAAI,IAAI,KAAK,YAAY,UAAU,eAAe;AACrG,QAAI,SAAS,WAAW;AACxB,QAAI,YAAY,CAAC;AACjB,QAAI,iBAAiB,KAAK,IAAI,MAAM;AACpC,QAAI,WAAW,aAAa,iBAAiB,GAAG,MACxC,YAAY,UAAU,MAAM,CAAC,UAAU;AAC/C,QAAI,eAAe,SAAS,IAAI,SAAS,MAAO,SAAS,MAAM;AAC/D,QAAI,QAAQ;AACZ,QAAI,UAAU;AACV,cAAQ;AAAA,IACZ,WACS,aAAa,cAAc,GAAG;AACnC,cAAQ;AAAA,IACZ,OACK;AACD,cAAS,gBAAgB,OAAQ,CAAC,CAAC;AAAA,IACvC;AACA,QAAI,KAAK,KAAK,KAAK,QAAQ,UAAU;AACrC,QAAI,KAAK,KAAK,KAAK,QAAQ,UAAU;AACrC,QAAI,KAAK,QAAQ;AACb,WAAK,KAAK,KAAK,IAAI,EAAE;AAAA,IACzB;AACA,QAAI,OAAO,KAAK,MAAM,MAAM,MAAM;AAClC,QAAI,UAAU;AACV,UAAI,IAAI,IAAI,KAAK;AACjB,UAAI,YAAY,YAAY,IAAI,OAAO,MAAM;AAC7C,WAAK,KAAK,KAAK,IAAI,IAAI,MAAM,GAAG,CAAC,WAAW,KAAK,KAAK,QAAQ,aAAa,QAAQ,GAAG,KAAK,KAAK,QAAQ,aAAa,QAAQ,CAAC;AAC9H,UAAI,IAAI,MAAM;AACV,aAAK,KAAK,KAAK,IAAI,IAAI,MAAM,GAAG,CAAC,WAAW,IAAI,EAAE;AAAA,MACtD;AAAA,IACJ,OACK;AACD,UAAI,IAAI,KAAK,KAAK,QAAQ,QAAQ;AAClC,UAAI,IAAI,KAAK,KAAK,QAAQ,QAAQ;AAClC,WAAK,KAAK,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC;AAAA,IACzD;AAAA,EACJ;AACA,EAAAA,kBAAiB,UAAU,OAAO,SAAU,GAAG,GAAG,GAAG,GAAG;AACpD,SAAK,KAAK,KAAK,GAAG,CAAC;AACnB,SAAK,KAAK,KAAK,GAAG,CAAC;AACnB,SAAK,KAAK,KAAK,GAAG,CAAC;AACnB,SAAK,KAAK,KAAK,CAAC,GAAG,CAAC;AACpB,SAAK,KAAK,GAAG;AAAA,EACjB;AACA,EAAAA,kBAAiB,UAAU,YAAY,WAAY;AAC/C,QAAI,KAAK,GAAG,SAAS,GAAG;AACpB,WAAK,KAAK,GAAG;AAAA,IACjB;AAAA,EACJ;AACA,EAAAA,kBAAiB,UAAU,OAAO,SAAU,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACrE,QAAI,OAAO,CAAC;AACZ,QAAI,IAAI,KAAK;AACb,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,UAAI,MAAM,UAAU,CAAC;AACrB,UAAI,MAAM,GAAG,GAAG;AACZ,aAAK,WAAW;AAChB;AAAA,MACJ;AACA,WAAK,KAAK,KAAK,MAAM,MAAM,CAAC,IAAI,CAAC;AAAA,IACrC;AACA,SAAK,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,CAAC;AACjC,SAAK,SAAS,QAAQ;AAAA,EAC1B;AACA,EAAAA,kBAAiB,UAAU,cAAc,WAAY;AACjD,SAAK,OAAO,KAAK,WAAW,KAAK,KAAK,GAAG,KAAK,EAAE;AAChD,SAAK,KAAK,CAAC;AAAA,EACf;AACA,EAAAA,kBAAiB,UAAU,SAAS,WAAY;AAC5C,WAAO,KAAK;AAAA,EAChB;AACA,SAAOA;AACX,EAAE;AACF,IAAO,2BAAQ;;;ACjGf,IAAI,OAAO;AACX,IAAI,YAAY,KAAK;AACrB,SAAS,YAAY,OAAO;AACxB,MAAI,OAAO,MAAM;AACjB,SAAO,QAAQ,QAAQ,SAAS;AACpC;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,SAAS,MAAM;AACnB,SAAO,UAAU,QAAQ,WAAW;AACxC;AACA,IAAI,cAAc,CAAC,WAAW,cAAc,UAAU;AACtD,IAAI,iBAAiB,IAAI,aAAa,SAAU,MAAM;AAAE,SAAO,YAAY,KAAK,YAAY;AAAG,CAAC;AACjF,SAAR,gBAAiC,YAAY,OAAO,IAAI,aAAa;AACxE,MAAI,UAAU,MAAM,WAAW,OAAO,IAAI,MAAM;AAChD,MAAI,cAAc,eAAS;AACvB,eAAW,WAAW,OAAO;AAC7B;AAAA,EACJ;AACA,MAAI,YAAY,KAAK,GAAG;AACpB,QAAI,OAAO,eAAe,MAAM,IAAI;AACpC,eAAW,QAAQ,KAAK,KAAK;AAC7B,QAAI,cAAc,MAAM,eAAe,OACjC,MAAM,cAAc,KAAK,UAAU,UACnC,KAAK,UAAU;AACrB,QAAI,eAAe,cAAc,GAAG;AAChC,iBAAW,gBAAgB,WAAW;AAAA,IAC1C;AAAA,EACJ,OACK;AACD,eAAW,QAAQ,IAAI;AAAA,EAC3B;AACA,MAAI,cAAc,KAAK,GAAG;AACtB,QAAI,SAAS,eAAe,MAAM,MAAM;AACxC,eAAW,UAAU,OAAO,KAAK;AACjC,QAAI,cAAc,MAAM,gBAClB,GAAG,aAAa,IAChB;AACN,QAAI,cAAe,eAAe,MAAM,aAAa,KAAK,cAAc;AACxE,QAAI,gBAAgB,MAAM,iBAAiB,OACrC,MAAM,gBAAgB,OAAO,UAAU,UACvC,OAAO,UAAU;AACvB,QAAI,cAAc,MAAM;AACxB,QAAI,eAAe,gBAAgB,GAAG;AAClC,iBAAW,gBAAgB,WAAW;AAAA,IAC1C;AACA,QAAI,eAAe,aAAa;AAC5B,iBAAW,eAAe,cAAc,WAAW,MAAM;AAAA,IAC7D;AACA,QAAI,eAAe,gBAAgB,GAAG;AAClC,iBAAW,kBAAkB,aAAa;AAAA,IAC9C;AACA,QAAI,MAAM,UAAU;AAChB,UAAI,KAAK,YAAY,EAAE,GAAG,WAAW,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AACjE,UAAI,UAAU;AACV,yBAAiB,UAAU,kBAAkB,CAAC;AAC9C,mBAAW,oBAAoB,SAAS,KAAK,GAAG,CAAC;AACjD,YAAI,kBAAkB,aAAa;AAC/B,qBAAW,qBAAqB,cAAc;AAAA,QAClD;AAAA,MACJ;AAAA,IACJ,WACS,aAAa;AAClB,iBAAW,oBAAoB,IAAI;AAAA,IACvC;AACA,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,UAAI,WAAW,YAAY,CAAC;AAC5B,UAAI,eAAe,MAAM,QAAQ,MAAM,mBAAmB,QAAQ,GAAG;AACjE,YAAI,MAAM,MAAM,QAAQ,KAAK,mBAAmB,QAAQ;AACxD,eAAO,WAAW,eAAe,CAAC,GAAG,GAAG;AAAA,MAC5C;AAAA,IACJ;AAAA,EACJ,WACS,aAAa;AAClB,eAAW,UAAU,IAAI;AAAA,EAC7B;AACJ;;;AC9EO,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,IAAI,mBAAmB;AACvB,SAAS,cAAc,MAAM;AAChC,SAAO,SAAS,gBAAgB,OAAO,IAAI;AAC/C;AAEO,SAAS,YAAY,KAAK,KAAK,OAAO,UAAU,MAAM;AACzD,SAAO;AAAA,IACH;AAAA,IACA,OAAO,SAAS,CAAC;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,kBAAkB,MAAM,OAAO;AACpC,MAAI,WAAW,CAAC;AAChB,MAAI,OAAO;AACP,aAAS,OAAO,OAAO;AACnB,UAAI,MAAM,MAAM,GAAG;AACnB,UAAI,OAAO;AACX,UAAI,QAAQ,OAAO;AACf;AAAA,MACJ,WACS,QAAQ,QAAQ,OAAO,MAAM;AAClC,gBAAQ,OAAQ,MAAM;AAAA,MAC1B;AACA,eAAS,KAAK,IAAI;AAAA,IACtB;AAAA,EACJ;AACA,SAAO,MAAM,OAAO,MAAM,SAAS,KAAK,GAAG,IAAI;AACnD;AACA,SAAS,mBAAmB,MAAM;AAC9B,SAAO,OAAO,OAAO;AACzB;AACO,SAAS,cAAc,IAAI,MAAM;AACpC,SAAO,QAAQ,CAAC;AAChB,MAAI,IAAI,KAAK,UAAU,OAAO;AAC9B,WAAS,kBAAkBC,KAAI;AAC3B,QAAI,WAAWA,IAAG,UAAU,MAAMA,IAAG,KAAK,QAAQA,IAAG,OAAO,OAAOA,IAAG;AACtE,WAAO,kBAAkB,KAAK,KAAK,KAC5B,QAAQ,UAAU,WAAW,IAAI,IAAI,QAAQ,OAC7C,WAAW,KAAK,IAAI,IAAI,UAAU,SAAU,OAAO;AAAE,aAAO,kBAAkB,KAAK;AAAA,IAAG,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,MACxG,mBAAmB,GAAG;AAAA,EAChC;AACA,SAAO,kBAAkB,EAAE;AAC/B;AACO,SAAS,aAAa,eAAe,gBAAgB,MAAM;AAC9D,SAAO,QAAQ,CAAC;AAChB,MAAI,IAAI,KAAK,UAAU,OAAO;AAC9B,MAAI,eAAe,OAAO;AAC1B,MAAI,aAAa,IAAI;AACrB,MAAI,YAAY,IAAI,KAAK,aAAa,GAAG,SAAU,WAAW;AAC1D,WAAO,YAAY,eAAe,IAAI,KAAK,cAAc,SAAS,CAAC,GAAG,SAAU,UAAU;AACtF,aAAO,WAAW,MAAM,cAAc,SAAS,EAAE,QAAQ,IAAI;AAAA,IACjE,CAAC,EAAE,KAAK,CAAC,IAAI;AAAA,EACjB,CAAC,EAAE,KAAK,CAAC;AACT,MAAI,aAAa,IAAI,KAAK,cAAc,GAAG,SAAU,eAAe;AAChE,WAAO,gBAAgB,gBAAgB,eAAe,IAAI,KAAK,eAAe,aAAa,CAAC,GAAG,SAAU,SAAS;AAC9G,aAAO,UAAU,eAAe,IAAI,KAAK,eAAe,aAAa,EAAE,OAAO,CAAC,GAAG,SAAU,UAAU;AAClG,YAAI,MAAM,eAAe,aAAa,EAAE,OAAO,EAAE,QAAQ;AACzD,YAAI,aAAa,KAAK;AAClB,gBAAM,WAAY,MAAM;AAAA,QAC5B;AACA,eAAO,WAAW,MAAM,MAAM;AAAA,MAClC,CAAC,EAAE,KAAK,CAAC,IAAI;AAAA,IACjB,CAAC,EAAE,KAAK,CAAC,IAAI;AAAA,EACjB,CAAC,EAAE,KAAK,CAAC;AACT,MAAI,CAAC,aAAa,CAAC,YAAY;AAC3B,WAAO;AAAA,EACX;AACA,SAAO,CAAC,aAAa,WAAW,YAAY,KAAK,EAAE,KAAK,CAAC;AAC7D;AACO,SAAS,iBAAiB,MAAM;AACnC,SAAO;AAAA,IACH;AAAA,IACA,aAAa,CAAC;AAAA,IACd,cAAc,CAAC;AAAA,IACf,eAAe,CAAC;AAAA,IAChB,eAAe,CAAC;AAAA,IAChB,MAAM,CAAC;AAAA,IACP,UAAU,CAAC;AAAA,IACX,UAAU,CAAC;AAAA,IACX,eAAe,CAAC;AAAA,IAChB,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,EACjB;AACJ;AACO,SAAS,eAAe,OAAO,QAAQ,UAAU,YAAY;AAChE,SAAO,YAAY,OAAO,QAAQ;AAAA,IAC9B,SAAS;AAAA,IACT,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,WAAW,aAAa,SAAS,QAAQ,MAAM,SAAS;AAAA,EAC5D,GAAG,QAAQ;AACf;;;AC1GA,IAAI,cAAc;AACX,SAAS,aAAa;AACzB,SAAO;AACX;;;ACMO,IAAI,aAAa;AAAA,EACpB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,eAAe;AACnB;AACA,IAAI,qBAAqB;AACzB,SAAS,gBAAgB,IAAI,SAAS,MAAM;AACxC,MAAI,QAAQ,OAAO,CAAC,GAAG,GAAG,KAAK;AAC/B,SAAO,OAAO,OAAO;AACrB,KAAG,UAAU,MAAM,KAAK;AACxB,MAAI,iBAAiB,IAAI,yBAAiB;AAC1C,iBAAe,MAAM,iBAAiB,EAAE,CAAC;AACzC,OAAK,YAAY,gBAAgB,CAAC;AAClC,iBAAe,YAAY;AAC3B,SAAO,eAAe,OAAO;AACjC;AACA,SAAS,mBAAmB,QAAQ,WAAW;AAC3C,MAAI,UAAU,UAAU,SAAS,UAAU,UAAU;AACrD,MAAI,WAAW,SAAS;AACpB,WAAO,kBAAkB,IAAI,UAAU,QAAQ,UAAU;AAAA,EAC7D;AACJ;AACO,IAAI,oBAAoB;AAAA,EAC3B,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,gBAAgB;AACpB;AACA,SAAS,aAAa,SAAS,OAAO;AAClC,MAAI,gBAAgB,MAAM,OAAO,UAAU,MAAM;AACjD,QAAM,SAAS,aAAa,IAAI;AAChC,SAAO;AACX;AACA,SAAS,+BAA+B,IAAI,OAAO,OAAO;AACtD,MAAI,QAAQ,GAAG,MAAM;AACrB,MAAI,eAAe,CAAC;AACpB,MAAI;AACJ,MAAI;AACJ,OAAK,OAAO,SAAU,MAAM;AACxB,QAAI,WAAW,iBAAiB,MAAM,IAAI;AAC1C,aAAS,YAAY;AACrB,uBAAmB,MAAM,CAAC,GAAG,UAAU,IAAI;AAC3C,QAAI,WAAW,SAAS;AACxB,QAAI,WAAW,SAAS;AACxB,QAAI,YAAY,KAAK,QAAQ;AAC7B,QAAI,MAAM,UAAU;AACpB,QAAI,CAAC,KAAK;AACN;AAAA,IACJ;AACA,uBAAmB,UAAU,MAAM,CAAC;AACpC,QAAI,WAAW,SAAS,gBAAgB;AACxC,aAAS,WAAW,UAAU;AAC1B,UAAI,KAAK,SAAS,OAAO;AACzB,mBAAa,OAAO,IAAI,aAAa,OAAO,KAAK,EAAE,GAAG,GAAG;AACzD,mBAAa,OAAO,EAAE,KAAK,GAAG,KAAK;AAAA,IACvC;AACA,aAAS,aAAa,UAAU;AAC5B,UAAI,MAAM,SAAS,SAAS,EAAE;AAC9B,UAAI,IAAI,QAAQ,gBAAgB,KAAK,GAAG;AACpC,0BAAkB;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,MAAI,CAAC,iBAAiB;AAClB;AAAA,EACJ;AACA,QAAM,IAAI;AACV,MAAI,gBAAgB,aAAa,cAAc,KAAK;AACpD,SAAO,gBAAgB,QAAQ,kBAAkB,aAAa;AAClE;AACA,SAAS,cAAc,QAAQ;AAC3B,SAAO,SAAS,MAAM,IAChB,WAAW,MAAM,IACb,kBAAkB,WAAW,MAAM,IAAI,MACvC,sBAAsB,MAAM,IAAI,SAAS,KAC7C;AACV;AACO,SAAS,mBAAmB,IAAI,OAAO,OAAO,WAAW;AAC5D,MAAI,YAAY,GAAG;AACnB,MAAI,MAAM,UAAU;AACpB,MAAI,gBAAgB,CAAC;AACrB,MAAI,cAAc,sBAAc;AAC5B,QAAI,eAAe,+BAA+B,IAAI,OAAO,KAAK;AAClE,QAAI,cAAc;AACd,oBAAc,KAAK,YAAY;AAAA,IACnC,WACS,CAAC,KAAK;AACX;AAAA,IACJ;AAAA,EACJ,WACS,CAAC,KAAK;AACX;AAAA,EACJ;AACA,MAAI,iBAAiB,CAAC;AACtB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,QAAI,WAAW,UAAU,CAAC;AAC1B,QAAI,SAAS,CAAC,SAAS,WAAW,IAAI,MAAO,GAAG;AAChD,QAAI,SAAS,cAAc,SAAS,QAAQ,EAAE,MAAM;AACpD,QAAI,QAAQ,SAAS,SAAS;AAC9B,QAAI,QAAQ;AACR,aAAO,KAAK,MAAM;AAAA,IACtB,OACK;AACD,aAAO,KAAK,QAAQ;AAAA,IACxB;AACA,QAAI,OAAO;AACP,aAAO,KAAK,QAAQ,MAAO,GAAG;AAAA,IAClC;AACA,QAAI,SAAS,QAAQ,GAAG;AACpB,aAAO,KAAK,UAAU;AAAA,IAC1B;AACA,QAAI,MAAM,OAAO,KAAK,GAAG;AACzB,mBAAe,GAAG,IAAI,eAAe,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AACrD,mBAAe,GAAG,EAAE,CAAC,EAAE,KAAK,QAAQ;AAAA,EACxC;AACA,WAAS,yBAAyB,eAAe;AAC7C,QAAIC,aAAY,cAAc,CAAC;AAC/B,QAAIC,OAAMD,WAAU;AACpB,QAAI,eAAe,CAAC;AACpB,QAAI,WAAW,CAAC;AAChB,QAAI,WAAW,CAAC;AAChB,QAAI,kCAAkC;AACtC,aAAS,0BAA0BE,WAAU,QAAQ,eAAe;AAChE,UAAI,SAASA,UAAS,UAAU;AAChC,UAAI,UAAUA,UAAS,WAAW;AAClC,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAI,QAAQ,OAAO,CAAC;AACpB,YAAI,MAAM,aAAa,GAAG;AACtB,cAAI,MAAM,MAAM;AAChB,cAAI,WAAW,MAAM;AACrB,4BAAkB,WAAW,cAAc,QAAQ;AACnD,cAAI,UAAU;AACV,qBAASC,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACjC,kBAAI,KAAK,IAAIA,EAAC;AACd,kBAAIC,WAAU,KAAK,MAAM,GAAG,OAAO,UAAU,GAAG,IAAI;AACpD,kBAAI,WAAW,cAAc,GAAG,MAAM;AACtC,kBAAI,WAAW,GAAG;AAClB,kBAAI,SAAS,QAAQ,KAAK,SAAS,QAAQ,GAAG;AAC1C,uBAAOA,QAAO,IAAI,OAAOA,QAAO,KAAK,CAAC;AACtC,uBAAOA,QAAO,EAAE,QAAQ,IAAI,GAAG;AAC/B,oBAAI,UAAU;AACV,yBAAOA,QAAO,EAAE,+BAA+B,IAAI;AAAA,gBACvD;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,aAASD,KAAI,GAAGA,KAAIF,MAAKE,MAAK;AAC1B,UAAID,YAAWF,WAAUG,EAAC;AAC1B,UAAI,aAAaD,UAAS;AAC1B,UAAI,CAAC,YAAY;AACb,SAAC,aAAa,0BAA0BA,WAAU,YAAY;AAAA,MAClE,WACS,eAAe,SAAS;AAC7B,kCAA0BA,WAAU,QAAQ;AAAA,MAChD;AAAA,IACJ;AACA,aAAS,WAAW,cAAc;AAC9B,UAAI,YAAY,CAAC;AACjB,oBAAc,WAAW,EAAE;AAC3B,aAAO,WAAW,aAAa,OAAO,CAAC;AACvC,UAAI,MAAM,sBAAsB,SAAS;AACzC,UAAI,iBAAiB,aAAa,OAAO,EAAE,+BAA+B;AAC1E,eAAS,OAAO,IAAI,MAAM;AAAA,QACtB,WAAW;AAAA,MACf,IAAI,CAAC;AACL,yBAAmB,SAAS,OAAO,GAAG,SAAS;AAC/C,UAAI,gBAAgB;AAChB,iBAAS,OAAO,EAAE,+BAA+B,IAAI;AAAA,MACzD;AAAA,IACJ;AACA;AACA,QAAI;AACJ,QAAI,kBAAkB;AACtB,aAAS,WAAW,UAAU;AAC1B,eAAS,OAAO,IAAI,SAAS,OAAO,KAAK,CAAC;AAC1C,UAAI,UAAU,CAAC;AACf,UAAI,iBAAiB,SAAS,OAAO,EAAE,+BAA+B;AACtE,UAAI,SAAS;AACT,eAAO,IAAI,kBAAU;AAAA,MACzB;AACA,UAAI,QAAQ,KAAK,IAAI;AACrB,WAAK,MAAM;AACX,eAAS,OAAO,EAAE,IAAI,gBAAgB,IAAI,SAAS,OAAO,GAAG,IAAI;AACjE,UAAI,SAAS,KAAK,IAAI;AACtB,UAAI,CAAC,WAAW,UAAU,QAAQ;AAC9B,0BAAkB;AAClB;AAAA,MACJ;AACA,UAAI,gBAAgB;AAChB,iBAAS,OAAO,EAAE,+BAA+B,IAAI;AAAA,MACzD;AAAA,IACJ;AACA;AACA,QAAI,CAAC,iBAAiB;AAClB,eAAS,WAAW,UAAU;AAC1B,eAAO,SAAS,OAAO,EAAE;AAAA,MAC7B;AAAA,IACJ;AACA,QAAI,CAAC,WAAW;AACZ,eAASC,KAAI,GAAGA,KAAIF,MAAKE,MAAK;AAC1B,YAAID,YAAWF,WAAUG,EAAC;AAC1B,YAAI,aAAaD,UAAS;AAC1B,YAAI,eAAe,SAAS;AACxB,oCAA0BA,WAAU,UAAU,SAAU,UAAU;AAAE,mBAAO,kBAAkB,QAAQ;AAAA,UAAG,CAAC;AAAA,QAC7G;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,WAAW,KAAK,QAAQ;AAC5B,QAAI,yBAAyB;AAC7B,QAAI;AACJ,aAASC,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACtC,UAAI,KAAK,SAASA,KAAI,CAAC;AACvB,UAAI,KAAK,SAASA,EAAC;AACnB,UAAI,SAAS,EAAE,EAAE,kBAAkB,MAAM,SAAS,EAAE,EAAE,kBAAkB,GAAG;AACvE,iCAAyB;AACzB;AAAA,MACJ;AACA,wBAAkB,SAAS,EAAE,EAAE,kBAAkB;AAAA,IACrD;AACA,QAAI,0BAA0B,iBAAiB;AAC3C,eAAS,WAAW,UAAU;AAC1B,YAAI,SAAS,OAAO,EAAE,kBAAkB,GAAG;AACvC,iBAAO,SAAS,OAAO,EAAE,kBAAkB;AAAA,QAC/C;AAAA,MACJ;AACA,YAAM,kBAAkB,IAAI;AAAA,IAChC;AACA,QAAI,OAAO,UAAU,SAAUC,UAAS;AAAE,aAAO,KAAK,SAASA,QAAO,CAAC,EAAE,SAAS;AAAA,IAAG,CAAC,EAAE,QAAQ;AAC5F,UAAI,gBAAgB,aAAa,UAAU,KAAK;AAChD,aAAO,gBAAgB,MAAM,cAAc,CAAC,IAAI;AAAA,IACpD;AAAA,EACJ;AACA,WAAS,OAAO,gBAAgB;AAC5B,QAAI,eAAe,yBAAyB,eAAe,GAAG,CAAC;AAC/D,QAAI,cAAc;AACd,oBAAc,KAAK,YAAY;AAAA,IACnC;AAAA,EACJ;AACA,MAAI,cAAc,QAAQ;AACtB,QAAI,YAAY,MAAM,OAAO,UAAU,WAAW;AAClD,UAAM,SAAS,MAAM,SAAS,IAAI;AAAA,MAC9B,WAAW,cAAc,KAAK,GAAG;AAAA,IACrC;AACA,UAAM,OAAO,IAAI;AAAA,EACrB;AACJ;;;ACjRO,SAAS,kBAAkB,IAAI,OAAO,OAAO;AAChD,MAAI,CAAC,GAAG,QAAQ;AACZ,QAAI,GAAG,SAAS,GAAG;AACf,UAAI,QAAQ;AAAA,QACR,kBAAkB;AAAA,MACtB;AACA,wBAAkB,OAAO,OAAO,OAAO,IAAI;AAAA,IAC/C,OACK;AACD,UAAI,gBAAgB,GAAG,OAAO,YAAY,GAAG,OAAO,SAAS,QACvD,GAAG,OAAO,SAAS,QACnB,CAAC;AACP,UAAI,OAAO,cAAc;AACzB,UAAI,CAAC,MAAM;AACP,YAAI,aAAa,GAAG,SAAS,GAAG,MAAM;AACtC,YAAI,aAAa,GAAG,OAAO,UACpB,GAAG,OAAO,OAAO,SACjB,GAAG,OAAO,OAAO,MAAM;AAC9B,YAAI,WAAW,GAAG,cAAc,QAAQ,QAAQ,KAAK,IAC9C,cAAc,aACf;AACN,YAAI,UAAU;AACV,iBAAO,UAAU,QAAQ;AAAA,QAC7B;AAAA,MACJ;AACA,UAAI,YAAY,cAAc;AAC9B,UAAI,WAAW;AACX,YAAI,SAAU,CAAC,cAAc,iBAAiB,GAAG,YAC3C,GAAG,UAAU,CAAC,IACd;AACN,oBAAY,YAAY;AAAA,MAC5B;AACA,UAAI,QAAQ;AAAA,QACR,QAAQ;AAAA,MACZ;AACA,UAAI,MAAM;AACN,cAAM,OAAO;AAAA,MACjB;AACA,UAAI,cAAc,QAAQ;AACtB,cAAM,SAAS,cAAc;AAAA,MACjC;AACA,UAAI,WAAW;AACX,cAAM,cAAc,IAAI;AAAA,MAC5B;AACA,wBAAkB,OAAO,OAAO,OAAO,IAAI;AAAA,IAC/C;AAAA,EACJ;AACJ;AACA,SAAS,kBAAkB,OAAO,OAAO,OAAO,WAAW;AACvD,MAAI,WAAW,KAAK,UAAU,KAAK;AACnC,MAAI,YAAY,MAAM,cAAc,QAAQ;AAC5C,MAAI,CAAC,WAAW;AACZ,gBAAY,MAAM,OAAO,UAAU,WAAW;AAC9C,UAAM,cAAc,QAAQ,IAAI;AAChC,UAAM,SAAS,MAAM,aAAa,YAAY,WAAW,GAAG,IAAI;AAAA,EACpE;AACA,QAAM,OAAO,IAAI,MAAM,OAAO,IAAK,MAAM,OAAO,IAAI,MAAM,YAAa;AAC3E;;;AC5CA,IAAI,QAAQ,KAAK;AACjB,SAAS,YAAY,KAAK;AACtB,SAAO,OAAO,SAAS,IAAI,GAAG;AAClC;AACA,SAAS,aAAa,KAAK;AACvB,SAAO,OAAO,WAAW,IAAI,SAAS;AAC1C;AACA,SAAS,cAAc,OAAO,OAAO,IAAI,OAAO;AAC5C,kBAAgB,SAAU,KAAK,KAAK;AAChC,QAAI,eAAe,QAAQ,UAAU,QAAQ;AAC7C,QAAI,gBAAgB,WAAW,GAAG,GAAG;AACjC,kBAAY,OAAO,OAAO,KAAK,KAAK;AAAA,IACxC,WACS,gBAAgB,UAAU,GAAG,GAAG;AACrC,iBAAW,IAAI,OAAO,KAAK,KAAK;AAAA,IACpC,WACS,gBAAgB,QAAQ,QAAQ;AACrC,YAAM,GAAG,IAAI;AAAA,IACjB,OACK;AACD,YAAM,GAAG,IAAI;AAAA,IACjB;AAAA,EACJ,GAAG,OAAO,IAAI,KAAK;AACnB,YAAU,IAAI,OAAO,KAAK;AAC9B;AACA,SAAS,YAAY,OAAO,IAAI;AAC5B,MAAI,WAAW,kBAAkB,EAAE;AACnC,MAAI,UAAU;AACV,aAAS,KAAK,SAAU,KAAK,KAAK;AAC9B,aAAO,SAAS,OAAO,mBAAmB,KAAK,YAAY,CAAC,IAAI,MAAM;AAAA,IAC1E,CAAC;AACD,QAAI,GAAG,SAAS,GAAG;AACf,YAAM,mBAAmB,QAAQ,IAAI;AAAA,IACzC;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,GAAG;AACtB,SAAO,aAAa,EAAE,CAAC,IAAI,CAAC,KACrB,aAAa,EAAE,CAAC,CAAC,KACjB,aAAa,EAAE,CAAC,CAAC,KACjB,aAAa,EAAE,CAAC,IAAI,CAAC;AAChC;AACA,SAAS,YAAY,GAAG;AACpB,SAAO,aAAa,EAAE,CAAC,CAAC,KAAK,aAAa,EAAE,CAAC,CAAC;AAClD;AACA,SAAS,aAAa,OAAO,GAAG,UAAU;AACtC,MAAI,KAAK,EAAE,YAAY,CAAC,KAAK,cAAc,CAAC,IAAI;AAC5C,QAAI,MAAM,WAAW,KAAK;AAC1B,UAAM,YAAY,cAAc,CAAC,IAC3B,eAAe,MAAM,EAAE,CAAC,IAAI,GAAG,IAAI,MAAM,MAAM,MAAM,EAAE,CAAC,IAAI,GAAG,IAAI,MAAM,MAAM,aAAa,CAAC;AAAA,EACvG;AACJ;AACA,SAAS,iBAAiB,OAAO,OAAO,KAAK;AACzC,MAAI,SAAS,MAAM;AACnB,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,WAAO,KAAK,MAAM,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG;AAC3C,WAAO,KAAK,MAAM,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG;AAAA,EAC/C;AACA,QAAM,SAAS,OAAO,KAAK,GAAG;AAClC;AACA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,CAAC,MAAM;AAClB;AACA,SAAS,mBAAmB,MAAM;AAC9B,MAAI,iBAAiB,IAAI,MAAM,SAAU,MAAM;AAC3C,WAAQ,OAAO,SAAS,WAAW,CAAC,MAAM,IAAI,IAAI;AAAA,EACtD,CAAC;AACD,SAAO,SAAU,OAAO,OAAO,KAAK;AAChC,aAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC5C,UAAI,OAAO,eAAe,CAAC;AAC3B,UAAI,MAAM,MAAM,KAAK,CAAC,CAAC;AACvB,UAAI,OAAO,MAAM;AACb,cAAM,KAAK,CAAC,CAAC,IAAI,MAAM,MAAM,GAAG,IAAI;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAI,mBAAmB;AAAA,EACnB,QAAQ,CAAC,mBAAmB,CAAC,MAAM,MAAM,GAAG,CAAC,CAAC;AAAA,EAC9C,UAAU,CAAC,kBAAkB,iBAAiB;AAAA,EAC9C,SAAS,CAAC,kBAAkB,iBAAiB;AACjD;AACA,SAAS,kBAAkB,IAAI;AAC3B,MAAI,YAAY,GAAG;AACnB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,UAAU,CAAC,EAAE,eAAe,SAAS;AACrC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,aAAa,IAAI,OAAO;AACpC,MAAI,QAAQ,GAAG;AACf,MAAI,QAAQ,GAAG;AACf,MAAI,gBAAgB,iBAAiB,GAAG,IAAI;AAC5C,MAAI,QAAQ,CAAC;AACb,MAAI,eAAe,MAAM;AACzB,MAAI,YAAY;AAChB,MAAI,gBAAgB,GAAG,MAAM;AAC7B,MAAI,YAAa,MAAM,YAAY,iBAAiB,EAAE,KAAM;AAC5D,MAAI,iBACG,CAAC,MAAM,cACP,EAAE,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,KAAK,MAC7C,EAAE,gBAAgB,kBAAkB,EAAE,MACtC,EAAE,gBAAgB,IAAI;AACzB,gBAAY,GAAG;AACf,QAAI,MAAM,KAAK,IAAI,IAAI,SAAS;AAChC,kBAAc,CAAC,EAAE,OAAO,OAAO,GAAG;AAAA,EACtC,OACK;AACD,QAAI,gBAAgB,CAAC,GAAG,QAAQ,GAAG,aAAa;AAChD,QAAI,CAAC,GAAG,MAAM;AACV,SAAG,gBAAgB;AAAA,IACvB;AACA,QAAI,OAAO,GAAG;AACd,QAAI,eAAe;AACf,WAAK,UAAU;AACf,SAAG,UAAU,MAAM,GAAG,KAAK;AAC3B,SAAG,YAAY;AAAA,IACnB;AACA,QAAI,cAAc,KAAK,WAAW;AAClC,QAAI,QAAQ;AACZ,QAAI,iBAAiB,MAAM;AAC3B,QAAI,MAAM,qBAAqB,eACxB,CAAC,kBACD,kBAAkB,MAAM,wBAAwB;AACnD,UAAI,CAAC,gBAAgB;AACjB,yBAAiB,MAAM,mBAAmB,IAAI,yBAAiB;AAAA,MACnE;AACA,qBAAe,MAAM,SAAS;AAC9B,WAAK,YAAY,gBAAgB,aAAa;AAC9C,qBAAe,YAAY;AAC3B,YAAM,mBAAmB;AACzB,YAAM,yBAAyB;AAAA,IACnC;AACA,UAAM,IAAI,eAAe,OAAO;AAAA,EACpC;AACA,eAAa,OAAO,GAAG,SAAS;AAChC,gBAAc,OAAO,OAAO,IAAI,KAAK;AACrC,cAAY,OAAO,EAAE;AACrB,QAAM,aAAa,mBAAmB,IAAI,OAAO,KAAK;AACtD,QAAM,YAAY,kBAAkB,IAAI,OAAO,KAAK;AACpD,SAAO,YAAY,WAAW,GAAG,KAAK,IAAI,KAAK;AACnD;AACO,SAAS,cAAc,IAAI,OAAO;AACrC,MAAI,QAAQ,GAAG;AACf,MAAI,QAAQ,MAAM;AAClB,MAAI,SAAS,CAAC,SAAS,KAAK,GAAG;AAC3B,QAAI,YAAY,KAAK,GAAG;AACpB,cAAQ,MAAM;AAAA,IAClB,WACS,aAAa,KAAK,GAAG;AAC1B,cAAQ,MAAM,UAAU;AAAA,IAC5B;AAAA,EACJ;AACA,MAAI,CAAC,OAAO;AACR;AAAA,EACJ;AACA,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,KAAK,MAAM;AACf,MAAI,KAAK,MAAM;AACf,MAAI,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AACA,MAAI,GAAG;AACH,UAAM,IAAI;AAAA,EACd;AACA,MAAI,GAAG;AACH,UAAM,IAAI;AAAA,EACd;AACA,eAAa,OAAO,GAAG,SAAS;AAChC,gBAAc,OAAO,OAAO,IAAI,KAAK;AACrC,cAAY,OAAO,EAAE;AACrB,QAAM,aAAa,mBAAmB,IAAI,OAAO,KAAK;AACtD,SAAO,YAAY,SAAS,GAAG,KAAK,IAAI,KAAK;AACjD;AAEO,SAAS,cAAc,IAAI,OAAO;AACrC,MAAI,QAAQ,GAAG;AACf,MAAI,OAAO,MAAM;AACjB,UAAQ,SAAS,QAAQ;AACzB,MAAI,CAAC,QAAQ,MAAM,MAAM,CAAC,KAAK,MAAM,MAAM,CAAC,GAAG;AAC3C;AAAA,EACJ;AACA,MAAI,OAAO,MAAM,QAAQ;AACzB,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,IAAI,YAAY,MAAM,KAAK,GAAG,cAAc,IAAI,GAAG,MAAM,YAAY;AACzE,MAAI,YAAY,qBAAqB,MAAM,SAAS,KAC7C,MAAM;AACb,MAAI,QAAQ;AAAA,IACR,qBAAqB;AAAA,IACrB,eAAe;AAAA,EACnB;AACA,MAAI,gBAAgB,KAAK,GAAG;AACxB,QAAI,mBAAmB;AACvB,QAAI,YAAY,MAAM;AACtB,QAAI,WAAW,cAAc,MAAM,QAAQ;AAC3C,QAAI,CAAC,WAAW,QAAQ,GAAG;AACvB;AAAA,IACJ;AACA,QAAI,aAAa,MAAM,cAAc;AACrC,QAAI,aAAa,MAAM;AACvB,wBAAoB,eAAe,WAAW,kBAAkB,aAAa;AAC7E,QAAI,aAAa,cAAc,UAAU;AACrC,0BAAoB,gBAAgB,YAAY;AAAA,IACpD;AACA,QAAI,cAAc,eAAe,UAAU;AACvC,0BAAoB,iBAAiB,aAAa;AAAA,IACtD;AACA,UAAM,QAAQ;AAAA,EAClB,OACK;AACD,UAAM,QAAQ,WAAW;AAAA,EAC7B;AACA,MAAI,KAAK,MAAM,IAAI,GAAG;AAClB,UAAM,WAAW,IAAI;AAAA,EACzB;AACA,MAAI,GAAG;AACH,UAAM,IAAI;AAAA,EACd;AACA,MAAI,GAAG;AACH,UAAM,IAAI;AAAA,EACd;AACA,eAAa,OAAO,GAAG,SAAS;AAChC,gBAAc,OAAO,OAAO,IAAI,KAAK;AACrC,cAAY,OAAO,EAAE;AACrB,QAAM,aAAa,mBAAmB,IAAI,OAAO,KAAK;AACtD,SAAO,YAAY,QAAQ,GAAG,KAAK,IAAI,OAAO,QAAW,IAAI;AACjE;AACO,SAAS,MAAM,IAAI,OAAO;AAC7B,MAAI,cAAc,cAAM;AACpB,WAAO,aAAa,IAAI,KAAK;AAAA,EACjC,WACS,cAAc,eAAS;AAC5B,WAAO,cAAc,IAAI,KAAK;AAAA,EAClC,WACS,cAAc,eAAO;AAC1B,WAAO,cAAc,IAAI,KAAK;AAAA,EAClC;AACJ;AACA,SAAS,UAAU,IAAI,OAAO,OAAO;AACjC,MAAI,QAAQ,GAAG;AACf,MAAI,UAAU,KAAK,GAAG;AAClB,QAAI,YAAY,aAAa,EAAE;AAC/B,QAAI,cAAc,MAAM;AACxB,QAAI,WAAW,YAAY,SAAS;AACpC,QAAI,CAAC,UAAU;AACX,UAAI,cAAc,GAAG,eAAe;AACpC,UAAI,SAAS,YAAY,CAAC;AAC1B,UAAI,SAAS,YAAY,CAAC;AAC1B,UAAI,CAAC,UAAU,CAAC,QAAQ;AACpB;AAAA,MACJ;AACA,UAAI,UAAU,MAAM,iBAAiB;AACrC,UAAI,UAAU,MAAM,iBAAiB;AACrC,UAAI,SAAS,MAAM;AACnB,UAAI,KAAK,eAAe,MAAM,WAAW,GAAG,UAAU,GAAG,SAAS,QAAQ,GAAG;AAC7E,UAAI,QAAQ,SAAS,IAAI;AACzB,UAAI,QAAQ,SAAS,IAAI;AACzB,UAAI,eAAe,QAAQ,MAAM;AACjC,iBAAW,MAAM,OAAO,OAAO,MAAM;AACrC,YAAM,KAAK,QAAQ,IAAI,YAAY,UAAU,UAAU;AAAA,QACnD,MAAM;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,MACd,GAAG;AAAA,QACC,YAAY,gBAAgB,IAAI;AAAA,UAC5B,MAAM,UAAU;AAAA,UAChB,MAAM,UAAU;AAAA,UAChB,gBAAgB;AAAA,UAChB,eAAe;AAAA,UACf,iBAAiB;AAAA,QACrB,CAAC;AAAA,MACL,CAAC;AACD,kBAAY,SAAS,IAAI;AAAA,IAC7B;AACA,UAAM,SAAS,SAAS,QAAQ;AAAA,EACpC;AACJ;AACO,SAAS,YAAY,OAAO,OAAO,QAAQ,OAAO;AACrD,MAAI,MAAM,MAAM,MAAM;AACtB,MAAI;AACJ,MAAI,gBAAgB;AAAA,IAChB,iBAAiB,IAAI,SACf,mBACA;AAAA,EACV;AACA,MAAI,iBAAiB,GAAG,GAAG;AACvB,kBAAc;AACd,kBAAc,KAAK,IAAI;AACvB,kBAAc,KAAK,IAAI;AACvB,kBAAc,KAAK,IAAI;AACvB,kBAAc,KAAK,IAAI;AAAA,EAC3B,WACS,iBAAiB,GAAG,GAAG;AAC5B,kBAAc;AACd,kBAAc,KAAK,UAAU,IAAI,GAAG,GAAG;AACvC,kBAAc,KAAK,UAAU,IAAI,GAAG,GAAG;AACvC,kBAAc,IAAI,UAAU,IAAI,GAAG,GAAG;AAAA,EAC1C,OACK;AACD,QAAI,MAAuC;AACvC,eAAS,wBAAwB;AAAA,IACrC;AACA;AAAA,EACJ;AACA,MAAI,SAAS,IAAI;AACjB,MAAI,aAAa,CAAC;AAClB,WAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC/C,QAAI,SAAS,OAAO,OAAO,CAAC,EAAE,MAAM,IAAI,MAAM;AAC9C,QAAI,YAAY,OAAO,CAAC,EAAE;AAC1B,QAAI,KAAK,eAAe,SAAS,GAAG,QAAQ,GAAG,OAAO,UAAU,GAAG;AACnE,QAAI,aAAa;AAAA,MACb,UAAU;AAAA,IACd;AACA,eAAW,YAAY,IAAI;AAC3B,QAAI,UAAU,GAAG;AACb,iBAAW,cAAc,IAAI;AAAA,IACjC;AACA,eAAW,KAAK,YAAY,QAAQ,IAAI,IAAI,UAAU,CAAC;AAAA,EAC3D;AACA,MAAI,gBAAgB,YAAY,aAAa,IAAI,eAAe,UAAU;AAC1E,MAAI,cAAc,cAAc,aAAa;AAC7C,MAAI,gBAAgB,MAAM;AAC1B,MAAI,aAAa,cAAc,WAAW;AAC1C,MAAI,CAAC,YAAY;AACb,iBAAa,MAAM,OAAO,OAAO,MAAM;AACvC,kBAAc,WAAW,IAAI;AAC7B,kBAAc,KAAK;AACnB,UAAM,KAAK,UAAU,IAAI,YAAY,aAAa,YAAY,eAAe,UAAU;AAAA,EAC3F;AACA,QAAM,MAAM,IAAI,SAAS,UAAU;AACvC;AACO,SAAS,WAAW,IAAI,OAAO,QAAQ,OAAO;AACjD,MAAI,MAAM,GAAG,MAAM,MAAM;AACzB,MAAI,eAAe,GAAG,gBAAgB;AACtC,MAAI,eAAe,CAAC;AACpB,MAAI,SAAS,IAAI;AACjB,MAAI,WAAW,WAAW;AAC1B,MAAI,UAAU,WAAW;AACzB,MAAI,UAAU,WAAW;AACzB,MAAI;AACJ,MAAI,eAAe,GAAG,GAAG;AACrB,QAAI,eAAe,IAAI;AACvB,QAAI,gBAAgB,IAAI;AACxB,QAAI,WAAW;AACf,QAAI,eAAe,IAAI;AACvB,QAAI,SAAS,YAAY,GAAG;AACxB,iBAAW;AAAA,IACf,WACS,YAAY,YAAY,GAAG;AAChC,iBAAW,aAAa;AAAA,IAC5B,WACS,aAAa,YAAY,GAAG;AACjC,iBAAW,aAAa,UAAU;AAAA,IACtC;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,UAAI,SAAS;AACb,aAAO,cAAc,MAAM;AAC3B,aAAO,eAAe,MAAM;AAAA,IAChC,WACS,gBAAgB,QAAQ,iBAAiB,MAAM;AACpD,UAAI,mBAAmB,SAAU,OAAO,KAAK;AACzC,YAAI,OAAO;AACP,cAAI,QAAQ,MAAM;AAClB,cAAI,QAAQ,gBAAgB,IAAI;AAChC,cAAI,SAAS,iBAAiB,IAAI;AAClC,cAAI,MAAM,QAAQ,WAAW;AACzB,gBAAI,SAAS;AACT,uBAAS;AACT,uBAAS,aAAa;AAAA,YAC1B,WACS,SAAS;AACd,sBAAQ;AACR,wBAAU,aAAa;AAAA,YAC3B;AAAA,UACJ;AACA,gBAAM,MAAM,QAAQ;AACpB,gBAAM,MAAM,SAAS;AACrB,cAAI,OAAO;AACP,kBAAM,aAAa,SAAS,KAAK;AACjC,kBAAM,aAAa,UAAU,MAAM;AAAA,UACvC;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,eAAe,oBAAoB,UAAU,MAAM,IAAI,SAAU,KAAK;AACtE,oBAAY,iBAAiB,cAAc,GAAG;AAC9C,yBAAiB,OAAO,GAAG;AAAA,MAC/B,CAAC;AACD,UAAI,gBAAgB,aAAa,SAAS,aAAa,QAAQ;AAC3D,uBAAe,gBAAgB,aAAa;AAC5C,wBAAgB,iBAAiB,aAAa;AAAA,MAClD;AAAA,IACJ;AACA,YAAQ,YAAY,SAAS,OAAO;AAAA,MAChC,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ,CAAC;AACD,iBAAa,QAAQ;AACrB,iBAAa,SAAS;AAAA,EAC1B,WACS,IAAI,YAAY;AACrB,YAAQ,MAAM,IAAI,UAAU;AAC5B,iBAAa,QAAQ,IAAI;AACzB,iBAAa,SAAS,IAAI;AAAA,EAC9B;AACA,MAAI,CAAC,OAAO;AACR;AAAA,EACJ;AACA,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU;AACV,mBAAe,gBAAgB;AAAA,EACnC,WACS,SAAS;AACd,oBAAgB;AAChB,mBAAe,aAAa,QAAQ,aAAa;AAAA,EACrD,WACS,SAAS;AACd,mBAAe;AACf,oBAAgB,aAAa,SAAS,aAAa;AAAA,EACvD,OACK;AACD,iBAAa,eAAe;AAAA,EAChC;AACA,MAAI,gBAAgB,QAAQ,CAAC,MAAM,YAAY,GAAG;AAC9C,iBAAa,QAAQ;AAAA,EACzB;AACA,MAAI,iBAAiB,QAAQ,CAAC,MAAM,aAAa,GAAG;AAChD,iBAAa,SAAS;AAAA,EAC1B;AACA,MAAI,mBAAmB,sBAAsB,GAAG;AAChD,uBAAqB,aAAa,mBAAmB;AACrD,MAAI,eAAe,YAAY,WAAW,IAAI,cAAc,CAAC,KAAK,CAAC;AACnE,MAAI,aAAa,cAAc,YAAY;AAC3C,MAAI,eAAe,MAAM;AACzB,MAAI,YAAY,aAAa,UAAU;AACvC,MAAI,CAAC,WAAW;AACZ,gBAAY,MAAM,OAAO,OAAO,MAAM;AACtC,iBAAa,UAAU,IAAI;AAC3B,iBAAa,KAAK;AAClB,mBAAe,MAAM,KAAK,SAAS,IAAI,YAAY,WAAW,WAAW,cAAc,CAAC,KAAK,CAAC;AAAA,EAClG;AACA,QAAM,MAAM,IAAI,SAAS,SAAS;AACtC;AACO,SAAS,YAAY,UAAU,OAAO,OAAO;AAChD,MAAI,gBAAgB,MAAM,eAAe,OAAO,MAAM;AACtD,MAAI,aAAa,cAAc,SAAS,EAAE;AAC1C,MAAI,CAAC,YAAY;AACb,iBAAa,MAAM,OAAO,OAAO,MAAM;AACvC,QAAI,gBAAgB;AAAA,MAChB,IAAI;AAAA,IACR;AACA,kBAAc,SAAS,EAAE,IAAI;AAC7B,SAAK,UAAU,IAAI,YAAY,YAAY,YAAY,eAAe,CAAC,aAAa,UAAU,KAAK,CAAC,CAAC;AAAA,EACzG;AACA,QAAM,WAAW,IAAI,SAAS,UAAU;AAC5C;;;AC/dO,SAAS,eAAe,MAAM;AACjC,SAAO,SAAS,eAAe,IAAI;AACvC;AAIO,SAAS,aAAaC,aAAY,SAAS,eAAe;AAC7D,EAAAA,YAAW,aAAa,SAAS,aAAa;AAClD;AACO,SAAS,YAAY,MAAM,OAAO;AACrC,OAAK,YAAY,KAAK;AAC1B;AACO,SAAS,YAAY,MAAM,OAAO;AACrC,OAAK,YAAY,KAAK;AAC1B;AACO,SAAS,WAAW,MAAM;AAC7B,SAAO,KAAK;AAChB;AACO,SAAS,YAAY,MAAM;AAC9B,SAAO,KAAK;AAChB;AAIO,SAAS,eAAe,MAAM,MAAM;AACvC,OAAK,cAAc;AACvB;;;ACvBA,IAAI,YAAY;AAChB,IAAI,QAAQ;AACZ,IAAI,YAAY,YAAY,IAAI,EAAE;AAClC,SAAS,QAAQ,GAAG;AAChB,SAAO,MAAM;AACjB;AACA,SAAS,MAAM,GAAG;AACd,SAAO,MAAM;AACjB;AACA,SAAS,kBAAkB,UAAU,UAAU,QAAQ;AACnD,MAAIC,OAAM,CAAC;AACX,WAAS,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAG;AACrC,QAAI,MAAM,SAAS,CAAC,EAAE;AACtB,QAAI,QAAQ,QAAW;AACnB,UAAI,MAAuC;AACvC,YAAIA,KAAI,GAAG,KAAK,MAAM;AAClB,kBAAQ,MAAM,mBAAmB,GAAG;AAAA,QACxC;AAAA,MACJ;AACA,MAAAA,KAAI,GAAG,IAAI;AAAA,IACf;AAAA,EACJ;AACA,SAAOA;AACX;AACA,SAAS,UAAU,QAAQ,QAAQ;AAC/B,MAAI,YAAY,OAAO,QAAQ,OAAO;AACtC,MAAI,YAAY,OAAO,QAAQ,OAAO;AACtC,SAAO,aAAa;AACxB;AACA,SAAS,UAAU,OAAO;AACtB,MAAI;AACJ,MAAI,WAAW,MAAM;AACrB,MAAI,MAAM,MAAM;AAChB,MAAI,MAAM,GAAG,GAAG;AACZ,QAAI,MAAO,MAAM,MAAM,cAAc,GAAG;AACxC,gBAAY,WAAW,KAAK;AAC5B,QAAI,QAAQ,QAAQ,GAAG;AACnB,WAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AAClC,YAAI,KAAK,SAAS,CAAC;AACnB,YAAI,MAAM,MAAM;AACZ,UAAI,YAAY,KAAK,UAAU,EAAE,CAAC;AAAA,QACtC;AAAA,MACJ;AAAA,IACJ,WACS,MAAM,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,IAAI,GAAG;AACjD,MAAI,YAAY,KAAS,eAAe,MAAM,IAAI,CAAC;AAAA,IACvD;AAAA,EACJ,OACK;AACD,UAAM,MAAU,eAAe,MAAM,IAAI;AAAA,EAC7C;AACA,SAAO,MAAM;AACjB;AACA,SAAS,UAAU,WAAW,QAAQ,QAAQ,UAAU,QAAQ;AAC5D,SAAO,YAAY,QAAQ,EAAE,UAAU;AACnC,QAAI,KAAK,OAAO,QAAQ;AACxB,QAAI,MAAM,MAAM;AACZ,MAAI,aAAa,WAAW,UAAU,EAAE,GAAG,MAAM;AAAA,IACrD;AAAA,EACJ;AACJ;AACA,SAAS,aAAa,WAAW,QAAQ,UAAU,QAAQ;AACvD,SAAO,YAAY,QAAQ,EAAE,UAAU;AACnC,QAAI,KAAK,OAAO,QAAQ;AACxB,QAAI,MAAM,MAAM;AACZ,UAAI,MAAM,GAAG,GAAG,GAAG;AACf,YAAI,WAAe,WAAW,GAAG,GAAG;AACpC,QAAI,YAAY,UAAU,GAAG,GAAG;AAAA,MACpC,OACK;AACD,QAAI,YAAY,WAAW,GAAG,GAAG;AAAA,MACrC;AAAA,IACJ;AAAA,EACJ;AACJ;AACO,SAAS,YAAY,UAAU,OAAO;AACzC,MAAI;AACJ,MAAI,MAAM,MAAM;AAChB,MAAI,WAAW,YAAY,SAAS,SAAS,CAAC;AAC9C,MAAI,QAAQ,MAAM,SAAS,CAAC;AAC5B,MAAI,aAAa,OAAO;AACpB;AAAA,EACJ;AACA,OAAK,OAAO,OAAO;AACf,QAAI,MAAM,MAAM,GAAG;AACnB,QAAI,MAAM,SAAS,GAAG;AACtB,QAAI,QAAQ,KAAK;AACb,UAAI,QAAQ,MAAM;AACd,YAAI,aAAa,KAAK,EAAE;AAAA,MAC5B,WACS,QAAQ,OAAO;AACpB,YAAI,gBAAgB,GAAG;AAAA,MAC3B,OACK;AACD,YAAI,QAAQ,SAAS;AACjB,cAAI,MAAM,UAAU;AAAA,QACxB,WACS,IAAI,WAAW,CAAC,MAAM,OAAO;AAClC,cAAI,aAAa,KAAK,GAAG;AAAA,QAC7B,WACS,QAAQ,iBAAiB,QAAQ,SAAS;AAC/C,cAAI,eAAe,OAAO,KAAK,GAAG;AAAA,QACtC,WACS,IAAI,WAAW,CAAC,MAAM,WAAW;AACtC,cAAI,eAAe,eAAe,KAAK,GAAG;AAAA,QAC9C,WACS,IAAI,WAAW,CAAC,MAAM,WAAW;AACtC,cAAI,eAAe,SAAS,KAAK,GAAG;AAAA,QACxC,OACK;AACD,cAAI,aAAa,KAAK,GAAG;AAAA,QAC7B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,OAAK,OAAO,UAAU;AAClB,QAAI,EAAE,OAAO,QAAQ;AACjB,UAAI,gBAAgB,GAAG;AAAA,IAC3B;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,WAAW,OAAO,OAAO;AAC7C,MAAI,cAAc;AAClB,MAAI,cAAc;AAClB,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,gBAAgB,MAAM,CAAC;AAC3B,MAAI,cAAc,MAAM,SAAS;AACjC,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,gBAAgB,MAAM,CAAC;AAC3B,MAAI,cAAc,MAAM,SAAS;AACjC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAO,eAAe,aAAa,eAAe,WAAW;AACzD,QAAI,iBAAiB,MAAM;AACvB,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACvC,WACS,eAAe,MAAM;AAC1B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACnC,WACS,iBAAiB,MAAM;AAC5B,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACvC,WACS,eAAe,MAAM;AAC1B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACnC,WACS,UAAU,eAAe,aAAa,GAAG;AAC9C,iBAAW,eAAe,aAAa;AACvC,sBAAgB,MAAM,EAAE,WAAW;AACnC,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACvC,WACS,UAAU,aAAa,WAAW,GAAG;AAC1C,iBAAW,aAAa,WAAW;AACnC,oBAAc,MAAM,EAAE,SAAS;AAC/B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACnC,WACS,UAAU,eAAe,WAAW,GAAG;AAC5C,iBAAW,eAAe,WAAW;AACrC,MAAI,aAAa,WAAW,cAAc,KAAS,YAAY,YAAY,GAAG,CAAC;AAC/E,sBAAgB,MAAM,EAAE,WAAW;AACnC,oBAAc,MAAM,EAAE,SAAS;AAAA,IACnC,WACS,UAAU,aAAa,aAAa,GAAG;AAC5C,iBAAW,aAAa,aAAa;AACrC,MAAI,aAAa,WAAW,YAAY,KAAK,cAAc,GAAG;AAC9D,oBAAc,MAAM,EAAE,SAAS;AAC/B,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACvC,OACK;AACD,UAAI,QAAQ,WAAW,GAAG;AACtB,sBAAc,kBAAkB,OAAO,aAAa,SAAS;AAAA,MACjE;AACA,iBAAW,YAAY,cAAc,GAAG;AACxC,UAAI,QAAQ,QAAQ,GAAG;AACnB,QAAI,aAAa,WAAW,UAAU,aAAa,GAAG,cAAc,GAAG;AAAA,MAC3E,OACK;AACD,oBAAY,MAAM,QAAQ;AAC1B,YAAI,UAAU,QAAQ,cAAc,KAAK;AACrC,UAAI,aAAa,WAAW,UAAU,aAAa,GAAG,cAAc,GAAG;AAAA,QAC3E,OACK;AACD,qBAAW,WAAW,aAAa;AACnC,gBAAM,QAAQ,IAAI;AAClB,UAAI,aAAa,WAAW,UAAU,KAAK,cAAc,GAAG;AAAA,QAChE;AAAA,MACJ;AACA,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACvC;AAAA,EACJ;AACA,MAAI,eAAe,aAAa,eAAe,WAAW;AACtD,QAAI,cAAc,WAAW;AACzB,eAAS,MAAM,YAAY,CAAC,KAAK,OAAO,OAAO,MAAM,YAAY,CAAC,EAAE;AACpE,gBAAU,WAAW,QAAQ,OAAO,aAAa,SAAS;AAAA,IAC9D,OACK;AACD,mBAAa,WAAW,OAAO,aAAa,SAAS;AAAA,IACzD;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,UAAU,OAAO;AACjC,MAAI,MAAO,MAAM,MAAM,SAAS;AAChC,MAAI,QAAQ,SAAS;AACrB,MAAI,KAAK,MAAM;AACf,MAAI,aAAa,OAAO;AACpB;AAAA,EACJ;AACA,cAAY,UAAU,KAAK;AAC3B,MAAI,QAAQ,MAAM,IAAI,GAAG;AACrB,QAAI,MAAM,KAAK,KAAK,MAAM,EAAE,GAAG;AAC3B,UAAI,UAAU,IAAI;AACd,uBAAe,KAAK,OAAO,EAAE;AAAA,MACjC;AAAA,IACJ,WACS,MAAM,EAAE,GAAG;AAChB,UAAI,MAAM,SAAS,IAAI,GAAG;AACtB,QAAI,eAAe,KAAK,EAAE;AAAA,MAC9B;AACA,gBAAU,KAAK,MAAM,IAAI,GAAG,GAAG,SAAS,CAAC;AAAA,IAC7C,WACS,MAAM,KAAK,GAAG;AACnB,mBAAa,KAAK,OAAO,GAAG,MAAM,SAAS,CAAC;AAAA,IAChD,WACS,MAAM,SAAS,IAAI,GAAG;AAC3B,MAAI,eAAe,KAAK,EAAE;AAAA,IAC9B;AAAA,EACJ,WACS,SAAS,SAAS,MAAM,MAAM;AACnC,QAAI,MAAM,KAAK,GAAG;AACd,mBAAa,KAAK,OAAO,GAAG,MAAM,SAAS,CAAC;AAAA,IAChD;AACA,IAAI,eAAe,KAAK,MAAM,IAAI;AAAA,EACtC;AACJ;AACe,SAAR,MAAuB,UAAU,OAAO;AAC3C,MAAI,UAAU,UAAU,KAAK,GAAG;AAC5B,eAAW,UAAU,KAAK;AAAA,EAC9B,OACK;AACD,QAAI,MAAM,SAAS;AACnB,QAAI,WAAe,WAAW,GAAG;AACjC,cAAU,KAAK;AACf,QAAI,aAAa,MAAM;AACnB,MAAI,aAAa,UAAU,MAAM,KAAS,YAAY,GAAG,CAAC;AAC1D,mBAAa,UAAU,CAAC,QAAQ,GAAG,GAAG,CAAC;AAAA,IAC3C;AAAA,EACJ;AACA,SAAO;AACX;;;ACtPA,IAAI,QAAQ;AACZ,IAAI,aAAc,WAAY;AAC1B,WAASC,YAAW,MAAM,SAAS,MAAM;AACrC,SAAK,OAAO;AACZ,SAAK,eAAe,uBAAuB,cAAc;AACzD,SAAK,cAAc,uBAAuB,aAAa;AACvD,SAAK,UAAU;AACf,SAAK,QAAQ,OAAO,OAAO,CAAC,GAAG,IAAI;AACnC,SAAK,OAAO;AACZ,SAAK,MAAM,OAAO;AAClB,SAAK,YAAY,eAAe,KAAK,OAAO,KAAK,MAAM;AACvD,QAAI,QAAQ,CAAC,KAAK,KAAK;AACnB,UAAI,WAAW,KAAK,YAAY,SAAS,cAAc,KAAK;AAC5D,eAAS,MAAM,UAAU;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,UAAU,MAAM,cAAc,KAAK;AACpE,kBAAY,MAAM,KAAK,SAAS;AAChC,eAAS,YAAY,MAAM;AAC3B,WAAK,YAAY,QAAQ;AAAA,IAC7B;AACA,SAAK,OAAO,KAAK,OAAO,KAAK,MAAM;AAAA,EACvC;AACA,EAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,YAAW,UAAU,kBAAkB,WAAY;AAC/C,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,YAAW,UAAU,wBAAwB,WAAY;AACrD,QAAI,eAAe,KAAK,gBAAgB;AACxC,QAAI,cAAc;AACd,aAAO;AAAA,QACH,YAAY,aAAa,cAAc;AAAA,QACvC,WAAW,aAAa,aAAa;AAAA,MACzC;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,YAAW,UAAU,YAAY,WAAY;AACzC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,QAAI,KAAK,MAAM;AACX,UAAI,QAAQ,KAAK,cAAc;AAAA,QAC3B,YAAY;AAAA,MAChB,CAAC;AACD,YAAM,MAAM,QAAQ;AACpB,YAAM,KAAK,WAAW,KAAK;AAC3B,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AACA,EAAAA,YAAW,UAAU,mBAAmB,SAAU,IAAI;AAClD,WAAO,MAAM,IAAI,iBAAiB,KAAK,GAAG,CAAC;AAAA,EAC/C;AACA,EAAAA,YAAW,UAAU,gBAAgB,SAAU,MAAM;AACjD,WAAO,QAAQ,CAAC;AAChB,QAAI,OAAO,KAAK,QAAQ,eAAe,IAAI;AAC3C,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,KAAK;AAClB,QAAI,QAAQ,iBAAiB,KAAK,GAAG;AACrC,UAAM,YAAY,KAAK;AACvB,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,KAAK;AACtB,UAAM,WAAW,KAAK;AACtB,QAAI,WAAW,CAAC;AAChB,QAAI,UAAU,KAAK,WAAW,sBAAsB,OAAO,QAAQ,KAAK,kBAAkB,KAAK;AAC/F,eAAW,SAAS,KAAK,OAAO;AAChC,QAAI,YAAY,CAAC,KAAK,WACf,KAAK,aAAa,YAAY,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAK;AAC7D,SAAK,WAAW,MAAM,OAAO,YAAY,UAAU,WAAW,QAAQ;AACtE,iBAAa,SAAS,KAAK,SAAS;AACpC,QAAI,OAAO,IAAI,KAAK,MAAM,IAAI,GAAG,SAAU,IAAI;AAAE,aAAO,MAAM,KAAK,EAAE;AAAA,IAAG,CAAC;AACzE,QAAI,KAAK,QAAQ;AACb,eAAS,KAAK,YAAY,QAAQ,QAAQ,CAAC,GAAG,IAAI,CAAC;AAAA,IACvD;AACA,QAAI,KAAK,WAAW;AAChB,UAAI,kBAAkB,aAAa,MAAM,UAAU,MAAM,UAAU,EAAE,SAAS,KAAK,CAAC;AACpF,UAAI,iBAAiB;AACjB,YAAI,YAAY,YAAY,SAAS,OAAO,CAAC,GAAG,CAAC,GAAG,eAAe;AACnE,iBAAS,KAAK,SAAS;AAAA,MAC3B;AAAA,IACJ;AACA,WAAO,eAAe,OAAO,QAAQ,UAAU,KAAK,UAAU;AAAA,EAClE;AACA,EAAAA,YAAW,UAAU,iBAAiB,SAAU,MAAM;AAClD,WAAO,QAAQ,CAAC;AAChB,WAAO,cAAc,KAAK,cAAc;AAAA,MACpC,WAAW,UAAU,KAAK,cAAc,IAAI;AAAA,MAC5C,UAAU,UAAU,KAAK,aAAa,IAAI;AAAA,MAC1C,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY,UAAU,KAAK,YAAY,IAAI;AAAA,IAC/C,CAAC,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,EACzB;AACA,EAAAA,YAAW,UAAU,qBAAqB,SAAU,iBAAiB;AACjE,SAAK,mBAAmB;AAAA,EAC5B;AACA,EAAAA,YAAW,UAAU,aAAa,WAAY;AAC1C,WAAO,KAAK,cAAc,KAAK,WAAW;AAAA,EAC9C;AACA,EAAAA,YAAW,UAAU,aAAa,SAAU,MAAM,OAAO,KAAK;AAC1D,QAAI,UAAU,KAAK;AACnB,QAAI,uBAAuB,CAAC;AAC5B,QAAI,4BAA4B;AAChC,QAAI;AACJ,QAAI;AACJ,QAAI,mBAAmB;AACvB,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAC9B,UAAI,cAAc,KAAK,CAAC;AACxB,UAAI,CAAC,YAAY,WAAW;AACxB,YAAI,YAAY,YAAY;AAC5B,YAAI,MAAM,aAAa,UAAU,UAAU;AAC3C,YAAI,UAAU,iBAAiB,cAAc,UAAU;AACvD,YAAI,MAAM;AACV,aAAK,MAAM,KAAK,IAAI,MAAM,GAAG,UAAU,CAAC,GAAG,OAAO,GAAG,OAAO;AACxD,cAAI,aAAa,iBACV,UAAU,GAAG,MAAM,cAAc,GAAG,GAAG;AAC1C;AAAA,UACJ;AAAA,QACJ;AACA,iBAAS,MAAM,UAAU,GAAG,MAAM,KAAK,OAAO;AAC1C;AACA,iCAAuB,qBAAqB,4BAA4B,CAAC;AAAA,QAC7E;AACA,iBAAS,MAAM,MAAM,GAAG,MAAM,KAAK,OAAO;AACtC,cAAI,aAAa,CAAC;AAClB,sBAAY,UAAU,GAAG,GAAG,YAAY,KAAK;AAC7C,cAAI,IAAI,YAAY,KAAK,YAAY,oBAAoB,YAAY,CAAC,CAAC;AACvE,WAAC,uBAAuB,qBAAqB,WAAW,KAAK,KAAK,CAAC;AACnE,+BAAqB,2BAA2B,IAAI;AACpD,iCAAuB;AAAA,QAC3B;AACA,wBAAgB;AAChB,YAAI,MAAM,MAAM,aAAa,KAAK;AAClC,YAAI,KAAK;AACL,WAAC,uBAAuB,qBAAqB,WAAW,KAAK,KAAK,GAAG;AAAA,QACzE;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,YAAW,UAAU,SAAS,SAAU,OAAO,QAAQ;AACnD,QAAI,OAAO,KAAK;AAChB,QAAI,OAAO,KAAK;AAChB,QAAI,WAAW,KAAK;AACpB,aAAS,SAAS,KAAK,QAAQ;AAC/B,cAAU,SAAS,KAAK,SAAS;AACjC,QAAI,QAAQ,UAAU;AAClB,eAAS,MAAM,UAAU;AACzB,cAAQ,QAAQ,MAAM,GAAG,IAAI;AAC7B,eAAS,QAAQ,MAAM,GAAG,IAAI;AAC9B,eAAS,MAAM,UAAU;AAAA,IAC7B;AACA,QAAI,KAAK,WAAW,SAAS,KAAK,YAAY,QAAQ;AAClD,WAAK,SAAS;AACd,WAAK,UAAU;AACf,UAAI,UAAU;AACV,YAAI,gBAAgB,SAAS;AAC7B,sBAAc,QAAQ,QAAQ;AAC9B,sBAAc,SAAS,SAAS;AAAA,MACpC;AACA,UAAI,CAAC,UAAU,KAAK,gBAAgB,GAAG;AACnC,YAAI,SAAS,KAAK;AAClB,YAAI,QAAQ;AACR,iBAAO,aAAa,SAAS,KAAK;AAClC,iBAAO,aAAa,UAAU,MAAM;AAAA,QACxC;AACA,YAAI,OAAO,KAAK,YAAY,KAAK,SAAS;AAC1C,YAAI,MAAM;AACN,eAAK,aAAa,SAAS,KAAK;AAChC,eAAK,aAAa,UAAU,MAAM;AAAA,QACtC;AAAA,MACJ,OACK;AACD,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,YAAW,UAAU,WAAW,WAAY;AACxC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,YAAW,UAAU,YAAY,WAAY;AACzC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,QAAI,KAAK,MAAM;AACX,WAAK,KAAK,YAAY;AAAA,IAC1B;AACA,SAAK,UACD,KAAK,YACD,KAAK,UACD,KAAK,YACD,KAAK,WACD,KAAK,aAAa;AAAA,EAC1C;AACA,EAAAA,YAAW,UAAU,QAAQ,WAAY;AACrC,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,YAAY;AAAA,IAC7B;AACA,SAAK,YAAY;AAAA,EACrB;AACA,EAAAA,YAAW,UAAU,YAAY,SAAU,QAAQ;AAC/C,QAAI,MAAM,KAAK,eAAe;AAC9B,QAAI,SAAS;AACb,QAAI,QAAQ;AACR,YAAM,aAAa,GAAG;AACtB,aAAO,OAAO,SAAS,YAAY;AAAA,IACvC;AACA,WAAO,SAAS,mBAAmB,mBAAmB,GAAG;AAAA,EAC7D;AACA,SAAOA;AACX,EAAE;AACF,SAAS,uBAAuB,QAAQ;AACpC,SAAO,WAAY;AACf,QAAI,MAAuC;AACvC,eAAS,6CAA6C,SAAS,GAAG;AAAA,IACtE;AAAA,EACJ;AACJ;AACA,SAAS,sBAAsB,OAAO,QAAQ,iBAAiB,OAAO;AAClE,MAAI;AACJ,MAAI,mBAAmB,oBAAoB,QAAQ;AAC/C,cAAU,YAAY,QAAQ,MAAM;AAAA,MAChC;AAAA,MACA;AAAA,MACA,GAAG;AAAA,MACH,GAAG;AAAA,IACP,CAAC;AACD,QAAI,WAAW,eAAe,GAAG;AAC7B,kBAAY,EAAE,MAAM,gBAAgB,GAAG,QAAQ,OAAO,QAAQ,KAAK;AAAA,IACvE,WACS,UAAU,eAAe,GAAG;AACjC,iBAAW;AAAA,QACP,OAAO;AAAA,UACH,MAAM;AAAA,QACV;AAAA,QACA,OAAO;AAAA,QACP,iBAAiB,WAAY;AAAE,iBAAQ,EAAE,OAAc,OAAe;AAAA,QAAI;AAAA,MAC9E,GAAG,QAAQ,OAAO,QAAQ,KAAK;AAAA,IACnC,OACK;AACD,UAAI,KAAK,eAAe,eAAe,GAAG,QAAQ,GAAG,OAAO,UAAU,GAAG;AACzE,cAAQ,MAAM,OAAO;AACrB,gBAAU,MAAM,QAAQ,MAAM,cAAc,IAAI;AAAA,IACpD;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAO,kBAAQ;;;AC/MR,SAAS,QAAQ,WAAW;AACjC,YAAU,gBAAgB,OAAO,eAAU;AAC7C;", "names": ["SVGPathRebuilder", "el", "animators", "len", "animator", "i", "percent", "parentNode", "map", "SVGPainter"]}