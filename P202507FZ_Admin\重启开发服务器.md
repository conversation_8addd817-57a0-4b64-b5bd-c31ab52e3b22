# 重启开发服务器说明

## 🔄 需要重启的原因
由于修改了 `vite.config.ts` 中的代理配置，需要重启开发服务器才能生效。

## 📋 操作步骤

### 1. 停止当前服务器
在运行开发服务器的终端中按 `Ctrl + C` 停止服务

### 2. 重新启动服务器
```bash
# 进入前端项目目录
cd P202507FZ_Admin

# 启动开发服务器
npm run dev
# 或者
pnpm dev
# 或者
yarn dev
```

### 3. 验证代理是否生效
1. 打开浏览器访问管理后台
2. 进入"工具管理" -> "标注工具"页面
3. 查看iframe是否正常加载
4. 检查浏览器开发者工具的Network标签，应该看到请求路径为 `/annotation-tool/...`

## 🔧 代理配置说明

新增的代理配置会：
1. **路径重写**: `/annotation-tool` -> `http://115.190.131.171:19003`
2. **CSRF绕过**: 设置正确的Referer和Origin头
3. **Cookie转发**: 自动转发所有cookie
4. **安全头修改**: 移除X-Frame-Options限制
5. **SameSite修复**: 修改cookie的SameSite属性

## ✅ 预期效果

重启后，标注工具应该能够：
- ✅ 在iframe中正常加载
- ✅ 绕过CSRF验证
- ✅ 正常登录和使用
- ✅ 显示连接状态指示器

## 🚨 如果仍有问题

如果重启后仍然有CSRF问题，请检查：
1. 代理配置是否正确应用
2. 浏览器控制台是否有新的错误信息
3. Network标签中的请求路径是否正确

## 📞 技术支持

如需帮助，请提供：
1. 浏览器控制台错误信息
2. Network标签的请求详情
3. 服务器启动日志
