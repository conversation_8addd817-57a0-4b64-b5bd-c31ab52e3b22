import "./chunk-RY4GFZAC.js";
import "./chunk-T6X6BW3Q.js";
import "./chunk-JNVPMB65.js";
import "./chunk-EG4GUIGL.js";
import "./chunk-QW7RFS2V.js";
import "./chunk-DT27FE2Z.js";
import "./chunk-IGTSGAF6.js";
import "./chunk-ZEDOIMXV.js";
import "./chunk-32IZWDU5.js";
import "./chunk-4DWWJNPT.js";
import "./chunk-P3D55END.js";
import "./chunk-DYSQVGL7.js";
import "./chunk-T5QLA2UI.js";
import "./chunk-3OT37MJW.js";
import "./chunk-DGUVPEUE.js";
import "./chunk-PM2XH6SQ.js";
import "./chunk-D7ZY375I.js";
import "./chunk-32KF5WYD.js";
import "./chunk-L57FFBZP.js";
import "./chunk-D3GOU4SV.js";
import "./chunk-CV5T7OKF.js";
import "./chunk-AJDG7GJR.js";
import "./chunk-UNJYNBID.js";
import "./chunk-TXZS53GN.js";
import "./chunk-3VV5MCL4.js";
import "./chunk-OGP444AS.js";
import "./chunk-QOPKUPCY.js";
import "./chunk-IRIWGUOS.js";
import "./chunk-KCGT3WTW.js";
import "./chunk-CAQCCKIB.js";
import "./chunk-ZRW7SXYD.js";
import "./chunk-FREIEH5N.js";
import {
  ElAutocomplete,
  ElButton,
  ElCascader,
  ElCheckbox,
  ElCheckboxButton,
  ElCheckboxGroup,
  ElCol,
  ElColorPicker,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElIcon,
  ElInput,
  ElInputNumber,
  ElPopover,
  ElProgress,
  ElRadio,
  ElRadioButton,
  ElRadioGroup,
  ElRate,
  ElRow,
  ElSelect,
  ElSlider,
  ElSwitch,
  ElTimePicker,
  ElTooltip,
  ElTree,
  ElUpload
} from "./chunk-SUFY5BT2.js";
import "./chunk-E2UCDU7D.js";
import "./chunk-AUFB2772.js";
import "./chunk-XYFNPXUZ.js";
import "./chunk-LROEKXT5.js";
import "./chunk-O7KFMITO.js";
import "./chunk-QGNZAP2K.js";
import "./chunk-YNRHTVZR.js";
import "./chunk-54OEU24D.js";
import "./chunk-67TUTJCN.js";
import "./chunk-ULX5FOVL.js";
import "./chunk-GTWINWNV.js";
import "./chunk-7BPWZNUD.js";
import "./chunk-GFT2G5UO.js";

// node_modules/.pnpm/@form-create+element-ui@3.2.14_vue@3.5.12_typescript@5.3.3_/node_modules/@form-create/element-ui/auto-import.js
function install(formCreate) {
  formCreate.useApp((_, app) => {
    app.component(ElForm.name) || app.use(ElForm);
    app.component(ElButton.name) || app.use(ElButton);
    app.component(ElRow.name) || app.use(ElRow);
    app.component(ElCol.name) || app.use(ElCol);
    app.component(ElInput.name) || app.use(ElInput);
    app.component(ElInputNumber.name) || app.use(ElInputNumber);
    app.component(ElCascader.name) || app.use(ElCascader);
    app.component(ElPopover.name) || app.use(ElPopover);
    app.component(ElTooltip.name) || app.use(ElTooltip);
    app.component(ElAutocomplete.name) || app.use(ElAutocomplete);
    app.component(ElCheckboxGroup.name) || app.use(ElCheckboxGroup);
    app.component(ElCheckboxButton.name) || app.use(ElCheckboxButton);
    app.component(ElRadioGroup.name) || app.use(ElRadioGroup);
    app.component(ElRadioButton.name) || app.use(ElRadioButton);
    app.component(ElRadio.name) || app.use(ElRadio);
    app.component(ElDialog.name) || app.use(ElDialog);
    app.component(ElCheckbox.name) || app.use(ElCheckbox);
    app.component(ElSelect.name) || app.use(ElSelect);
    app.component(ElTree.name) || app.use(ElTree);
    app.component(ElUpload.name) || app.use(ElUpload);
    app.component(ElSlider.name) || app.use(ElSlider);
    app.component(ElRate.name) || app.use(ElRate);
    app.component(ElColorPicker.name) || app.use(ElColorPicker);
    app.component(ElSwitch.name) || app.use(ElSwitch);
    app.component(ElDatePicker.name) || app.use(ElDatePicker);
    app.component(ElIcon.name) || app.use(ElIcon);
    app.component(ElTimePicker.name) || app.use(ElTimePicker);
    app.component(ElProgress.name) || app.use(ElProgress);
  });
}
export {
  install as default
};
//# sourceMappingURL=@form-create_element-ui_auto-import.js.map
