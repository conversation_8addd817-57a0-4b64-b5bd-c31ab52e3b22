{"version": 3, "sources": ["../../.pnpm/randomcolor@0.6.2/node_modules/randomcolor/randomColor.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/ScopeTraits.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/ScopeStates.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/Scope.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/util/SetUtil.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/util/EventsUtil.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/util/ModelUtil.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/Simulator.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/StartEventBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/EndEventBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/BoundaryEventBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/IntermediateCatchEventBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/IntermediateThrowEventBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/ExclusiveGatewayBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/ParallelGatewayBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/EventBasedGatewayBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/InclusiveGatewayBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/ActivityBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/SubProcessBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/TransactionBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/SequenceFlowBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/MessageFlowBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/util/ElementHelper.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/EventBehaviors.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/ScopeBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/ProcessBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/behaviors/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/simulator/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/animation/behaviors/AnimatedMessageFlowBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/animation/behaviors/AnimatedSequenceFlowBehavior.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/animation/behaviors/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/util/EventHelper.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/scope-filter/ScopeFilter.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/scope-filter/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/animation/Animation.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/animation/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/colored-scopes/ColoredScopes.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/colored-scopes/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/icons/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/ExclusiveGatewayHandler.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/InclusiveGatewayHandler.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/PauseHandler.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/TriggerHandler.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/context-pads/ContextPads.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/context-pads/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/simulation-state/SimulationState.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/element-notifications/ElementNotifications.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/element-notifications/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/notifications/Notifications.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/notifications/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/simulation-state/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/show-scopes/ShowScopes.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/simulation-styles/SimulationStyles.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/simulation-styles/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/show-scopes/index.js", "../../.pnpm/diagram-js@12.8.1/node_modules/diagram-js/lib/util/EscapeUtil.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/log/Log.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/log/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/element-support/ElementSupport.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/element-support/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/pause-simulation/PauseSimulation.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/pause-simulation/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/reset-simulation/ResetSimulation.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/reset-simulation/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/token-count/TokenCount.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/token-count/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/set-animation-speed/SetAnimationSpeed.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/set-animation-speed/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/exclusive-gateway-settings/ExclusiveGatewaySettings.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/element-colors/ElementColors.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/element-colors/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/exclusive-gateway-settings/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/neutral-element-colors/NeutralElementColors.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/neutral-element-colors/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/inclusive-gateway-settings/InclusiveGatewaySettings.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/inclusive-gateway-settings/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/palette/Palette.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/palette/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/base.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/disable-modeling/DisableModeling.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/disable-modeling/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/toggle-mode/modeler/ToggleMode.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/toggle-mode/modeler/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/editor-actions/EditorActions.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/editor-actions/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/keyboard-bindings/KeyboardBindings.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/features/keyboard-bindings/index.js", "../../.pnpm/bpmn-js-token-simulation@0.36.2/node_modules/bpmn-js-token-simulation/lib/modeler.js"], "sourcesContent": ["// randomColor by <PERSON> under the CC0 license\n// https://github.com/davidmerfield/randomColor/\n\n;(function(root, factory) {\n\n  // Support CommonJS\n  if (typeof exports === 'object') {\n    var randomColor = factory();\n\n    // Support NodeJS & Component, which allow module.exports to be a function\n    if (typeof module === 'object' && module && module.exports) {\n      exports = module.exports = randomColor;\n    }\n\n    // Support CommonJS 1.1.1 spec\n    exports.randomColor = randomColor;\n\n  // Support AMD\n  } else if (typeof define === 'function' && define.amd) {\n    define([], factory);\n\n  // Support vanilla script loading\n  } else {\n    root.randomColor = factory();\n  }\n\n}(this, function() {\n\n  // Seed to get repeatable colors\n  var seed = null;\n\n  // Shared color dictionary\n  var colorDictionary = {};\n\n  // Populate the color dictionary\n  loadColorBounds();\n\n  // check if a range is taken\n  var colorRanges = [];\n\n  var randomColor = function (options) {\n\n    options = options || {};\n\n    // Check if there is a seed and ensure it's an\n    // integer. Otherwise, reset the seed value.\n    if (options.seed !== undefined && options.seed !== null && options.seed === parseInt(options.seed, 10)) {\n      seed = options.seed;\n\n    // A string was passed as a seed\n    } else if (typeof options.seed === 'string') {\n      seed = stringToInteger(options.seed);\n\n    // Something was passed as a seed but it wasn't an integer or string\n    } else if (options.seed !== undefined && options.seed !== null) {\n      throw new TypeError('The seed value must be an integer or string');\n\n    // No seed, reset the value outside.\n    } else {\n      seed = null;\n    }\n\n    var H,S,B;\n\n    // Check if we need to generate multiple colors\n    if (options.count !== null && options.count !== undefined) {\n\n      var totalColors = options.count,\n          colors = [];\n      // Value false at index i means the range i is not taken yet.\n      for (var i = 0; i < options.count; i++) {\n        colorRanges.push(false)\n        }\n      options.count = null;\n\n      while (totalColors > colors.length) {\n\n        var color = randomColor(options);\n\n        if (seed !== null) {\n          options.seed = seed;\n        }\n\n        colors.push(color);\n      }\n\n      options.count = totalColors;\n\n      return colors;\n    }\n\n    // First we pick a hue (H)\n    H = pickHue(options);\n\n    // Then use H to determine saturation (S)\n    S = pickSaturation(H, options);\n\n    // Then use S and H to determine brightness (B).\n    B = pickBrightness(H, S, options);\n\n    // Then we return the HSB color in the desired format\n    return setFormat([H,S,B], options);\n  };\n\n  function pickHue(options) {\n    if (colorRanges.length > 0) {\n      var hueRange = getRealHueRange(options.hue)\n\n      var hue = randomWithin(hueRange)\n\n      //Each of colorRanges.length ranges has a length equal approximatelly one step\n      var step = (hueRange[1] - hueRange[0]) / colorRanges.length\n\n      var j = parseInt((hue - hueRange[0]) / step)\n\n      //Check if the range j is taken\n      if (colorRanges[j] === true) {\n        j = (j + 2) % colorRanges.length\n      }\n      else {\n        colorRanges[j] = true\n           }\n\n      var min = (hueRange[0] + j * step) % 359,\n          max = (hueRange[0] + (j + 1) * step) % 359;\n\n      hueRange = [min, max]\n\n      hue = randomWithin(hueRange)\n\n      if (hue < 0) {hue = 360 + hue;}\n      return hue\n    }\n    else {\n      var hueRange = getHueRange(options.hue)\n\n      hue = randomWithin(hueRange);\n      // Instead of storing red as two seperate ranges,\n      // we group them, using negative numbers\n      if (hue < 0) {\n        hue = 360 + hue;\n      }\n\n      return hue;\n    }\n  }\n\n  function pickSaturation (hue, options) {\n\n    if (options.hue === 'monochrome') {\n      return 0;\n    }\n\n    if (options.luminosity === 'random') {\n      return randomWithin([0,100]);\n    }\n\n    var saturationRange = getSaturationRange(hue);\n\n    var sMin = saturationRange[0],\n        sMax = saturationRange[1];\n\n    switch (options.luminosity) {\n\n      case 'bright':\n        sMin = 55;\n        break;\n\n      case 'dark':\n        sMin = sMax - 10;\n        break;\n\n      case 'light':\n        sMax = 55;\n        break;\n   }\n\n    return randomWithin([sMin, sMax]);\n\n  }\n\n  function pickBrightness (H, S, options) {\n\n    var bMin = getMinimumBrightness(H, S),\n        bMax = 100;\n\n    switch (options.luminosity) {\n\n      case 'dark':\n        bMax = bMin + 20;\n        break;\n\n      case 'light':\n        bMin = (bMax + bMin)/2;\n        break;\n\n      case 'random':\n        bMin = 0;\n        bMax = 100;\n        break;\n    }\n\n    return randomWithin([bMin, bMax]);\n  }\n\n  function setFormat (hsv, options) {\n\n    switch (options.format) {\n\n      case 'hsvArray':\n        return hsv;\n\n      case 'hslArray':\n        return HSVtoHSL(hsv);\n\n      case 'hsl':\n        var hsl = HSVtoHSL(hsv);\n        return 'hsl('+hsl[0]+', '+hsl[1]+'%, '+hsl[2]+'%)';\n\n      case 'hsla':\n        var hslColor = HSVtoHSL(hsv);\n        var alpha = options.alpha || Math.random();\n        return 'hsla('+hslColor[0]+', '+hslColor[1]+'%, '+hslColor[2]+'%, ' + alpha + ')';\n\n      case 'rgbArray':\n        return HSVtoRGB(hsv);\n\n      case 'rgb':\n        var rgb = HSVtoRGB(hsv);\n        return 'rgb(' + rgb.join(', ') + ')';\n\n      case 'rgba':\n        var rgbColor = HSVtoRGB(hsv);\n        var alpha = options.alpha || Math.random();\n        return 'rgba(' + rgbColor.join(', ') + ', ' + alpha + ')';\n\n      default:\n        return HSVtoHex(hsv);\n    }\n\n  }\n\n  function getMinimumBrightness(H, S) {\n\n    var lowerBounds = getColorInfo(H).lowerBounds;\n\n    for (var i = 0; i < lowerBounds.length - 1; i++) {\n\n      var s1 = lowerBounds[i][0],\n          v1 = lowerBounds[i][1];\n\n      var s2 = lowerBounds[i+1][0],\n          v2 = lowerBounds[i+1][1];\n\n      if (S >= s1 && S <= s2) {\n\n         var m = (v2 - v1)/(s2 - s1),\n             b = v1 - m*s1;\n\n         return m*S + b;\n      }\n\n    }\n\n    return 0;\n  }\n\n  function getHueRange (colorInput) {\n\n    if (typeof parseInt(colorInput) === 'number') {\n\n      var number = parseInt(colorInput);\n\n      if (number < 360 && number > 0) {\n        return [number, number];\n      }\n\n    }\n\n    if (typeof colorInput === 'string') {\n\n      if (colorDictionary[colorInput]) {\n        var color = colorDictionary[colorInput];\n        if (color.hueRange) {return color.hueRange;}\n      } else if (colorInput.match(/^#?([0-9A-F]{3}|[0-9A-F]{6})$/i)) {\n        var hue = HexToHSB(colorInput)[0];\n        return [ hue, hue ];\n      }\n    }\n\n    return [0,360];\n\n  }\n\n  function getSaturationRange (hue) {\n    return getColorInfo(hue).saturationRange;\n  }\n\n  function getColorInfo (hue) {\n\n    // Maps red colors to make picking hue easier\n    if (hue >= 334 && hue <= 360) {\n      hue-= 360;\n    }\n\n    for (var colorName in colorDictionary) {\n       var color = colorDictionary[colorName];\n       if (color.hueRange &&\n           hue >= color.hueRange[0] &&\n           hue <= color.hueRange[1]) {\n          return colorDictionary[colorName];\n       }\n    } return 'Color not found';\n  }\n\n  function randomWithin (range) {\n    if (seed === null) {\n      //generate random evenly destinct number from : https://martin.ankerl.com/2009/12/09/how-to-create-random-colors-programmatically/\n      var golden_ratio = 0.618033988749895\n      var r=Math.random()\n      r += golden_ratio\n      r %= 1\n      return Math.floor(range[0] + r*(range[1] + 1 - range[0]));\n    } else {\n      //Seeded random algorithm from http://indiegamr.com/generate-repeatable-random-numbers-in-js/\n      var max = range[1] || 1;\n      var min = range[0] || 0;\n      seed = (seed * 9301 + 49297) % 233280;\n      var rnd = seed / 233280.0;\n      return Math.floor(min + rnd * (max - min));\n}\n  }\n\n  function HSVtoHex (hsv){\n\n    var rgb = HSVtoRGB(hsv);\n\n    function componentToHex(c) {\n        var hex = c.toString(16);\n        return hex.length == 1 ? '0' + hex : hex;\n    }\n\n    var hex = '#' + componentToHex(rgb[0]) + componentToHex(rgb[1]) + componentToHex(rgb[2]);\n\n    return hex;\n\n  }\n\n  function defineColor (name, hueRange, lowerBounds) {\n\n    var sMin = lowerBounds[0][0],\n        sMax = lowerBounds[lowerBounds.length - 1][0],\n\n        bMin = lowerBounds[lowerBounds.length - 1][1],\n        bMax = lowerBounds[0][1];\n\n    colorDictionary[name] = {\n      hueRange: hueRange,\n      lowerBounds: lowerBounds,\n      saturationRange: [sMin, sMax],\n      brightnessRange: [bMin, bMax]\n    };\n\n  }\n\n  function loadColorBounds () {\n\n    defineColor(\n      'monochrome',\n      null,\n      [[0,0],[100,0]]\n    );\n\n    defineColor(\n      'red',\n      [-26,18],\n      [[20,100],[30,92],[40,89],[50,85],[60,78],[70,70],[80,60],[90,55],[100,50]]\n    );\n\n    defineColor(\n      'orange',\n      [18,46],\n      [[20,100],[30,93],[40,88],[50,86],[60,85],[70,70],[100,70]]\n    );\n\n    defineColor(\n      'yellow',\n      [46,62],\n      [[25,100],[40,94],[50,89],[60,86],[70,84],[80,82],[90,80],[100,75]]\n    );\n\n    defineColor(\n      'green',\n      [62,178],\n      [[30,100],[40,90],[50,85],[60,81],[70,74],[80,64],[90,50],[100,40]]\n    );\n\n    defineColor(\n      'blue',\n      [178, 257],\n      [[20,100],[30,86],[40,80],[50,74],[60,60],[70,52],[80,44],[90,39],[100,35]]\n    );\n\n    defineColor(\n      'purple',\n      [257, 282],\n      [[20,100],[30,87],[40,79],[50,70],[60,65],[70,59],[80,52],[90,45],[100,42]]\n    );\n\n    defineColor(\n      'pink',\n      [282, 334],\n      [[20,100],[30,90],[40,86],[60,84],[80,80],[90,75],[100,73]]\n    );\n\n  }\n\n  function HSVtoRGB (hsv) {\n\n    // this doesn't work for the values of 0 and 360\n    // here's the hacky fix\n    var h = hsv[0];\n    if (h === 0) {h = 1;}\n    if (h === 360) {h = 359;}\n\n    // Rebase the h,s,v values\n    h = h/360;\n    var s = hsv[1]/100,\n        v = hsv[2]/100;\n\n    var h_i = Math.floor(h*6),\n      f = h * 6 - h_i,\n      p = v * (1 - s),\n      q = v * (1 - f*s),\n      t = v * (1 - (1 - f)*s),\n      r = 256,\n      g = 256,\n      b = 256;\n\n    switch(h_i) {\n      case 0: r = v; g = t; b = p;  break;\n      case 1: r = q; g = v; b = p;  break;\n      case 2: r = p; g = v; b = t;  break;\n      case 3: r = p; g = q; b = v;  break;\n      case 4: r = t; g = p; b = v;  break;\n      case 5: r = v; g = p; b = q;  break;\n    }\n\n    var result = [Math.floor(r*255), Math.floor(g*255), Math.floor(b*255)];\n    return result;\n  }\n\n  function HexToHSB (hex) {\n    hex = hex.replace(/^#/, '');\n    hex = hex.length === 3 ? hex.replace(/(.)/g, '$1$1') : hex;\n\n    var red = parseInt(hex.substr(0, 2), 16) / 255,\n          green = parseInt(hex.substr(2, 2), 16) / 255,\n          blue = parseInt(hex.substr(4, 2), 16) / 255;\n\n    var cMax = Math.max(red, green, blue),\n          delta = cMax - Math.min(red, green, blue),\n          saturation = cMax ? (delta / cMax) : 0;\n\n    switch (cMax) {\n      case red: return [ 60 * (((green - blue) / delta) % 6) || 0, saturation, cMax ];\n      case green: return [ 60 * (((blue - red) / delta) + 2) || 0, saturation, cMax ];\n      case blue: return [ 60 * (((red - green) / delta) + 4) || 0, saturation, cMax ];\n    }\n  }\n\n  function HSVtoHSL (hsv) {\n    var h = hsv[0],\n      s = hsv[1]/100,\n      v = hsv[2]/100,\n      k = (2-s)*v;\n\n    return [\n      h,\n      Math.round(s*v / (k<1 ? k : 2-k) * 10000) / 100,\n      k/2 * 100\n    ];\n  }\n\n  function stringToInteger (string) {\n    var total = 0\n    for (var i = 0; i !== string.length; i++) {\n      if (total >= Number.MAX_SAFE_INTEGER) break;\n      total += string.charCodeAt(i)\n    }\n    return total\n  }\n\n  // get The range of given hue when options.count!=0\n  function getRealHueRange(colorHue)\n  { if (!isNaN(colorHue)) {\n    var number = parseInt(colorHue);\n\n    if (number < 360 && number > 0) {\n      return getColorInfo(colorHue).hueRange\n    }\n  }\n    else if (typeof colorHue === 'string') {\n\n      if (colorDictionary[colorHue]) {\n        var color = colorDictionary[colorHue];\n\n        if (color.hueRange) {\n          return color.hueRange\n       }\n    } else if (colorHue.match(/^#?([0-9A-F]{3}|[0-9A-F]{6})$/i)) {\n        var hue = HexToHSB(colorHue)[0]\n        return getColorInfo(hue).hueRange\n    }\n  }\n\n    return [0,360]\n}\n  return randomColor;\n}));\n", "/* eslint no-bitwise: off */\n\nconst ACTIVATED = 1;\nconst RUNNING = 1 << 1;\nconst ENDING = 1 << 2;\nconst ENDED = 1 << 3;\nconst DESTROYED = 1 << 4;\nconst FAILED = 1 << 5;\nconst TERMINATED = 1 << 6;\nconst CANCELED = 1 << 7;\nconst COMPLETED = 1 << 8;\nconst COMPENSABLE = 1 << 9;\n\nconst ACTIVE = ACTIVATED | RUNNING | ENDING;\nconst NOT_DEAD = ACTIVATED | ENDED;\n\nexport const ScopeTraits = Object.freeze({\n  ACTIVATED,\n  RUNNING,\n  ENDING,\n  ENDED,\n  DESTROYED,\n  FAILED,\n  TERMINATED,\n  CANCELED,\n  COMPLETED,\n  COMPENSABLE,\n  ACTIVE,\n  NOT_DEAD\n});", "/* eslint no-bitwise: off */\n\nimport {\n  ScopeTraits\n} from './ScopeTraits';\n\nconst SELF = {};\n\nfunction illegalTransition(state, target) {\n  throw new Error(`illegal transition: ${state.name} -> ${target}`);\n}\n\nfunction orSelf(state, self) {\n  if (state === SELF) {\n    return self;\n  }\n\n  return state;\n}\n\n/**\n * A representation of a scopes state with name, traits, and supported\n * transitions to other states.\n */\nexport class ScopeState {\n\n  /**\n   * @param {string} name\n   * @param {number} traits\n   * @param {object} [transitions]\n   * @param {ScopeState} [transitions.start]\n   * @param {ScopeState} [transitions.cancel]\n   * @param {ScopeState} [transitions.complete]\n   * @param {ScopeState} [transitions.destroy]\n   * @param {ScopeState} [transitions.fail]\n   * @param {ScopeState} [transitions.terminate]\n   * @param {ScopeState} [transitions.compensable]\n   */\n  constructor(name, traits, {\n    start,\n    cancel,\n    complete,\n    destroy,\n    fail,\n    terminate,\n    compensable\n  } = {}) {\n    this.name = name;\n\n    /**\n     * A bit-wise encoded set of traits\n     * characterizing the scope.\n     *\n     * @type {number}\n     */\n    this.traits = traits;\n\n    this._start = orSelf(start, this);\n    this._compensable = orSelf(compensable, this);\n    this._cancel = orSelf(cancel, this);\n    this._complete = orSelf(complete, this);\n    this._destroy = orSelf(destroy, this);\n    this._fail = orSelf(fail, this);\n    this._terminate = orSelf(terminate, this);\n  }\n\n  /**\n   * @param {number} trait\n   * @return {boolean}\n   */\n  hasTrait(trait) {\n    return (this.traits & trait) !== 0;\n  }\n\n  /**\n   * @return {ScopeState}\n   */\n  complete() {\n    return this._complete || illegalTransition(this, 'complete');\n  }\n\n  /**\n   * @return {ScopeState}\n   */\n  destroy() {\n    return this._destroy || illegalTransition(this, 'destroy');\n  }\n\n  /**\n   * @return {ScopeState}\n   */\n  cancel() {\n    return this._cancel || illegalTransition(this, 'cancel');\n  }\n\n  /**\n   * @return {ScopeState}\n   */\n  fail() {\n    return this._fail || illegalTransition(this, 'fail');\n  }\n\n  /**\n   * @return {ScopeState}\n   */\n  terminate() {\n    return this._terminate || illegalTransition(this, 'terminate');\n  }\n\n  /**\n   * @return {ScopeState}\n   */\n  compensable() {\n    return this._compensable || illegalTransition(this, 'compensable');\n  }\n\n  /**\n   * @return {ScopeState}\n   */\n  start() {\n    return this._start || illegalTransition(this, 'start');\n  }\n}\n\nconst FAILED = new ScopeState('failed', ScopeTraits.DESTROYED | ScopeTraits.FAILED);\n\nconst TERMINATED = new ScopeState('terminated', ScopeTraits.DESTROYED | ScopeTraits.TERMINATED);\n\nconst COMPLETED = new ScopeState('completed', ScopeTraits.DESTROYED | ScopeTraits.COMPLETED);\n\nconst TERMINATING = new ScopeState('terminating', ScopeTraits.ENDING | ScopeTraits.TERMINATED, {\n  destroy: TERMINATED\n});\n\nconst CANCELING = new ScopeState('canceling', ScopeTraits.ENDING | ScopeTraits.FAILED | ScopeTraits.CANCELED, {\n  destroy: FAILED,\n  complete: SELF,\n  terminate: TERMINATING\n});\n\nconst COMPLETING = new ScopeState('completing', ScopeTraits.ENDING | ScopeTraits.COMPLETED, {\n  destroy: COMPLETED,\n  cancel: CANCELING,\n  terminate: TERMINATING\n});\n\nconst FAILING = new ScopeState('failing', ScopeTraits.ENDING | ScopeTraits.FAILED, {\n  cancel: CANCELING,\n  complete: COMPLETING,\n  destroy: FAILED,\n  terminate: TERMINATING\n});\n\nconst COMPENSABLE_FAILING = new ScopeState('compensable:failing', ScopeTraits.ENDING | ScopeTraits.FAILED, {\n  complete: SELF,\n  terminate: TERMINATING,\n  destroy: FAILED\n});\n\nconst COMPENSABLE_COMPLETED = new ScopeState('compensable:completed', ScopeTraits.ENDED | ScopeTraits.COMPLETED, {\n  cancel: CANCELING,\n  fail: COMPENSABLE_FAILING,\n  destroy: COMPLETED,\n  compensable: SELF\n});\n\nconst COMPENSABLE_COMPLETING = new ScopeState('compensable:completing', ScopeTraits.ENDING | ScopeTraits.COMPLETED, {\n  destroy: COMPENSABLE_COMPLETED,\n  terminate: TERMINATING,\n  compensable: SELF\n});\n\nconst COMPENSABLE_RUNNING = new ScopeState('compensable:running', ScopeTraits.RUNNING | ScopeTraits.COMPENSABLE, {\n  cancel: CANCELING,\n  complete: COMPENSABLE_COMPLETING,\n  compensable: SELF,\n  destroy: COMPENSABLE_COMPLETED,\n  fail: FAILING,\n  terminate: TERMINATING\n});\n\nconst RUNNING = new ScopeState('running', ScopeTraits.RUNNING, {\n  cancel: CANCELING,\n  complete: COMPLETING,\n  compensable: COMPENSABLE_RUNNING,\n  destroy: TERMINATED,\n  fail: FAILING,\n  terminate: TERMINATING\n});\n\nconst ACTIVATED = new ScopeState('activated', ScopeTraits.ACTIVATED, {\n  start: RUNNING,\n  destroy: TERMINATED\n});\n\nexport const ScopeStates = Object.freeze({\n  ACTIVATED,\n  RUNNING,\n  CANCELING,\n  COMPLETING,\n  COMPLETED,\n  FAILING,\n  FAILED,\n  TERMINATING,\n  TERMINATED,\n});", "import { ScopeTraits } from './ScopeTraits';\nimport { ScopeStates } from './ScopeStates';\n\n\n/**\n * A representation of anything runnable in token simulation land.\n */\nexport default class Scope {\n\n  /**\n   * @param {string} id\n   * @param {Element} element\n   * @param {Scope} parent\n   * @param {Scope} initiator\n   *\n   * @constructor\n   */\n  constructor(id, element, parent = null, initiator = null) {\n    this.id = id;\n    this.element = element;\n    this.parent = parent;\n    this.initiator = initiator;\n\n    this.subscriptions = new Set();\n\n    this.children = [];\n    this.state = ScopeStates.ACTIVATED;\n  }\n\n  /**\n   * @return {boolean}\n   */\n  get running() {\n    return this.hasTrait(ScopeTraits.RUNNING);\n  }\n\n  /**\n   * @return {boolean}\n   */\n  get destroyed() {\n    return this.hasTrait(ScopeTraits.DESTROYED);\n  }\n\n  /**\n   * @return {boolean}\n   */\n  get completed() {\n    return this.hasTrait(ScopeTraits.COMPLETED);\n  }\n\n  /**\n   * @return {boolean}\n   */\n  get canceled() {\n    return this.hasTrait(ScopeTraits.CANCELED);\n  }\n\n  /**\n   * @return {boolean}\n   */\n  get failed() {\n    return this.hasTrait(ScopeTraits.FAILED);\n  }\n\n  /**\n   * @param {number} phase\n   * @return {boolean}\n   */\n  hasTrait(trait) {\n    return this.state.hasTrait(trait);\n  }\n\n  /**\n   * Start the scope\n   *\n   * @return {Scope}\n   */\n  start() {\n    this.state = this.state.start();\n\n    return this;\n  }\n\n  /**\n   * Make this scope compensable.\n   *\n   * @return {Scope}\n   */\n  compensable() {\n    this.state = this.state.compensable();\n\n    return this;\n  }\n\n  /**\n   * @param {Scope} initiator\n   *\n   * @return {Scope}\n   */\n  fail(initiator) {\n    if (!this.failed) {\n      this.state = this.state.fail();\n\n      this.failInitiator = initiator;\n    }\n\n    return this;\n  }\n\n  cancel(initiator) {\n\n    if (!this.canceled) {\n      this.state = this.state.cancel();\n\n      this.cancelInitiator = initiator;\n    }\n\n    return this;\n  }\n\n  /**\n   * @param {Scope} initiator\n   *\n   * @return {Scope}\n   */\n  terminate(initiator) {\n    this.state = this.state.terminate();\n\n    this.terminateInitiator = initiator;\n\n    return this;\n  }\n\n  /**\n   * @return {Scope}\n   */\n  complete() {\n    this.state = this.state.complete();\n\n    return this;\n  }\n\n  /**\n   * Destroy the scope\n   *\n   * @param {Scope} initiator\n   *\n   * @return {Scope}\n   */\n  destroy(initiator) {\n    this.state = this.state.destroy();\n\n    this.destroyInitiator = initiator;\n\n    return this;\n  }\n\n  /**\n   * @return {number}\n   */\n  getTokens() {\n    return this.children.filter(c => !c.destroyed).length;\n  }\n\n  /**\n   * @param {Element} element\n   *\n   * @return {number}\n   */\n  getTokensByElement(element) {\n    return this.children.filter(c => !c.destroyed && c.element === element).length;\n  }\n\n}", "export function filterSet(set, matchFn) {\n\n  const matched = [];\n\n  for (const el of set) {\n    if (matchFn(el)) {\n      matched.push(el);\n    }\n  }\n\n  return matched;\n}\n\nexport function findSet(set, matchFn) {\n\n  for (const el of set) {\n    if (matchFn(el)) {\n      return el;\n    }\n  }\n\n  return null;\n}", "export function eventsMatch(a, b) {\n  const attrMatch = [ 'type', 'name', 'iref' ].every(attr => !(attr in a) || a[attr] === b[attr]);\n  const catchAllMatch = !b.ref && (b.type === 'error' || b.type === 'escalation');\n\n  return attrMatch && (catchAllMatch || refsMatch(a, b));\n}\n\nexport function refsMatch(a, b) {\n  const attr = 'ref';\n  return !(attr in a) || a[attr] === b[attr];\n}", "import {\n  getPlaneIdFromShape\n} from 'bpmn-js/lib/util/DrilldownUtil';\n\nimport {\n  is,\n  getBusinessObject\n} from 'bpmn-js/lib/util/ModelUtil';\n\nimport {\n  some\n} from 'min-dash';\n\n\nexport { is, getBusinessObject };\n\nexport function filterSequenceFlows(flows) {\n  return flows.filter(f => is(f, 'bpmn:SequenceFlow'));\n}\n\nexport function isMessageFlow(element) {\n  return is(element, 'bpmn:MessageFlow');\n}\n\nexport function isSequenceFlow(element) {\n  return is(element, 'bpmn:SequenceFlow');\n}\n\nexport function isMessageCatch(element) {\n  return isCatchEvent(element) && isTypedEvent(element, 'bpmn:MessageEventDefinition');\n}\n\nexport function isLinkCatch(element) {\n  return isCatchEvent(element) && isTypedEvent(element, 'bpmn:LinkEventDefinition');\n}\n\nexport function isCompensationEvent(element) {\n  return isCatchEvent(element) && isTypedEvent(element, 'bpmn:CompensateEventDefinition');\n}\n\nexport function isCompensationActivity(element) {\n  return is(element, 'bpmn:Activity') && element.businessObject.isForCompensation;\n}\n\nexport function isCatchEvent(element) {\n  return (\n    is(element, 'bpmn:CatchEvent') ||\n    is(element, 'bpmn:ReceiveTask')\n  ) && !isLabel(element);\n}\n\nexport function isBoundaryEvent(element) {\n  return is(element, 'bpmn:BoundaryEvent') && !isLabel(element);\n}\n\nexport function isNoneStartEvent(element) {\n  return isStartEvent(element) && !isTypedEvent(element);\n}\n\nexport function isImplicitStartEvent(element) {\n  if (isLabel(element)) {\n    return false;\n  }\n\n  if (!isAny(element, [\n    'bpmn:Activity',\n    'bpmn:IntermediateCatchEvent',\n    'bpmn:IntermediateThrowEvent',\n    'bpmn:Gateway',\n    'bpmn:EndEvent'\n  ])) {\n    return false;\n  }\n\n  if (isLinkCatch(element)) {\n    return false;\n  }\n\n  const incoming = element.incoming.find(isSequenceFlow);\n\n  if (incoming) {\n    return false;\n  }\n\n  if (isCompensationActivity(element)) {\n    return false;\n  }\n\n  if (isEventSubProcess(element)) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function isStartEvent(element) {\n  return is(element, 'bpmn:StartEvent') && !isLabel(element);\n}\n\nexport function isLabel(element) {\n  return !!element.labelTarget;\n}\n\nexport function isEventSubProcess(element) {\n  return getBusinessObject(element).triggeredByEvent;\n}\n\nexport function isInterrupting(element) {\n  return (\n    is(element, 'bpmn:StartEvent') && getBusinessObject(element).isInterrupting\n  ) || (\n    is(element, 'bpmn:BoundaryEvent') && getBusinessObject(element).cancelActivity\n  );\n}\n\nexport function isAny(element, types) {\n  return types.some(type => is(element, type));\n}\n\n/**\n * @param { DiagramElement} event\n * @param {string|undefined} [eventDefinitionType]\n *\n * @return {boolean}\n */\nexport function isTypedEvent(event, eventDefinitionType) {\n  return some(getBusinessObject(event).eventDefinitions, definition => {\n    return eventDefinitionType ? is(definition, eventDefinitionType) : true;\n  });\n}\n\nexport function getChildren(element, elementRegistry) {\n  if (element.children && element.children.length !== 0) {\n    return element.children;\n  }\n\n  if (is(element, 'bpmn:SubProcess') && !element.di.isExpanded) {\n\n    // ensure bpmn-js@9 compatibility\n    //\n    // sub-process may be collapsed, in this case operate on the plane\n    return elementRegistry.get(getPlaneIdFromShape(element)).children;\n  }\n\n  return [];\n}\n", "import Ids from 'ids';\n\nimport Scope from './Scope';\nimport { ScopeTraits } from './ScopeTraits';\n\nimport {\n  filterSet,\n  findSet\n} from './util/SetUtil';\n\nimport {\n  eventsMatch,\n  refsMatch\n} from './util/EventsUtil';\n\nimport {\n  getBusinessObject,\n  getChildren,\n  is,\n  isAny,\n  isBoundaryEvent,\n  isCompensationEvent,\n  isEventSubProcess,\n  isImplicitStartEvent,\n  isInterrupting,\n  isStartEvent\n} from './util/ModelUtil';\n\n/**\n * @typedef { any } DiagramElement\n *\n * @typedef { {\n *   element: DiagramElement,\n *   interrupting: boolean,\n *   boundary: boolean,\n *   iref?: string,\n *   ref: DiagramElement,\n *   persistent?: boolean,\n *   type: string\n * } } SimulatorEvent\n */\n\nexport default function Simulator(injector, eventBus, elementRegistry) {\n\n  const ids = injector.get('scopeIds', false) || new Ids([ 32, 36 ]);\n\n  // element configuration\n  const configuration = {};\n\n  const behaviors = {};\n\n  const noopBehavior = new NoopBehavior();\n\n  const changedElements = new Set();\n\n  const jobs = [];\n\n  const scopes = new Set();\n  const subscriptions = new Set();\n\n  on('tick', function() {\n    for (const element of changedElements) {\n      emit('elementChanged', {\n        element\n      });\n    }\n\n    changedElements.clear();\n  });\n\n  function queue(scope, task) {\n\n    // add this task\n    jobs.push([ task, scope ]);\n\n    if (jobs.length !== 1) {\n      return;\n    }\n\n    let next;\n\n    while ((next = jobs[0])) {\n\n      const [ task, scope ] = next;\n\n      if (!scope.destroyed) {\n        task();\n      }\n\n      // remove first task\n      jobs.shift();\n    }\n\n    emit('tick');\n  }\n\n  function getBehavior(element) {\n    return behaviors[element.type] || noopBehavior;\n  }\n\n  function signal(context) {\n\n    const {\n      element,\n      parentScope,\n      initiator = null,\n      scope = initializeScope({\n        element,\n        parent: parentScope,\n        initiator\n      })\n    } = context;\n\n    queue(scope, function() {\n\n      if (!scope.running) {\n        scope.start();\n      }\n\n      trace('signal', {\n        ...context,\n        scope\n      });\n\n      getBehavior(element).signal({\n        ...context,\n        scope\n      });\n\n      if (scope.parent) {\n        scopeChanged(scope.parent);\n      }\n    });\n\n    return scope;\n  }\n\n  function enter(context) {\n\n    const {\n      element,\n      scope: parentScope,\n      initiator = parentScope\n    } = context;\n\n    const scope = initializeScope({\n      element,\n      parent: parentScope,\n      initiator\n    });\n\n    queue(scope, function() {\n\n      if (!scope.running) {\n        scope.start();\n      }\n\n      trace('enter', context);\n\n      getBehavior(element).enter({\n        ...context,\n        initiator,\n        scope\n      });\n\n      if (scope.parent) {\n        scopeChanged(scope.parent);\n      }\n    });\n\n    return scope;\n  }\n\n  function exit(context) {\n\n    const {\n      element,\n      scope,\n      initiator = scope\n    } = context;\n\n    queue(scope, function() {\n\n      trace('exit', context);\n\n      getBehavior(element).exit({\n        ...context,\n        initiator\n      });\n\n      if (scope.running) {\n        scope.complete();\n      }\n\n      destroyScope(scope, initiator);\n\n      scope.parent && scopeChanged(scope.parent);\n    });\n  }\n\n  function trigger(context) {\n    const {\n      event: _event,\n      initiator,\n      scope\n    } = context;\n\n    // behavior depends on available event subscriptions\n    //\n    // interrupt (one-off, clear all events)\n    //   => keep interrupting boundary event sub-scriptions of same type, if available\n    //\n    // continue (one-off signal)\n    //\n    // non-interrupting (as many as needed)\n\n    const event = getEvent(_event);\n\n    const subscriptions = scope.subscriptions;\n\n    let matchingSubscriptions = filterSet(\n      subscriptions, subscription => eventsMatch(event, subscription.event)\n    );\n\n    if (event.type === 'error' || event.type === 'escalation') {\n      const referenceSubscriptions = filterSet(\n        matchingSubscriptions, subscription => refsMatch(event, subscription.event)\n      );\n\n      if (matchingSubscriptions.every(subscription => subscription.event.boundary)\n          && referenceSubscriptions.some(subscription => subscription.event.boundary)\n          || referenceSubscriptions.some(subscription => !subscription.event.boundary)) {\n        matchingSubscriptions = referenceSubscriptions;\n      }\n    }\n\n    const nonInterrupting = matchingSubscriptions.filter(\n      subscription => !subscription.event.interrupting\n    );\n\n    const interrupting = matchingSubscriptions.filter(\n      subscription => subscription.event.interrupting\n    );\n\n    if (!interrupting.length) {\n      return nonInterrupting.map(\n        subscription => subscription.triggerFn(initiator)\n      ).flat();\n    }\n\n    const interrupt = interrupting.find(subscription => !subscription.event.boundary) || interrupting[0];\n\n    const remainingSubscriptions = filterSet(\n      subscriptions,\n      subscription => subscription.event.persistent || isRethrow(subscription.event, interrupt.event)\n    );\n\n    subscriptions.forEach(subscription => {\n      if (!remainingSubscriptions.includes(subscription)) {\n        subscription.remove();\n      }\n    });\n\n    return [ interrupt.triggerFn(initiator) ].flat().filter(s => s);\n  }\n\n  function subscribe(scope, event, triggerFn) {\n\n    event = getEvent(event);\n\n    const element = event.element;\n\n    const subscription = {\n      scope,\n      event,\n      element,\n      triggerFn,\n      remove() {\n        unsubscribe(subscription);\n      }\n    };\n\n    subscriptions.add(subscription);\n\n    scope.subscriptions.add(subscription);\n\n    if (element) {\n      elementChanged(element);\n    }\n\n    return subscription;\n  }\n\n  function unsubscribe(subscription) {\n    const {\n      scope,\n      event\n    } = subscription;\n\n    subscriptions.delete(subscription);\n\n    scope.subscriptions.delete(subscription);\n\n    if (event.element) {\n      elementChanged(event.element);\n    }\n  }\n\n  function createInternalRef(element) {\n    if (\n      is(element, 'bpmn:StartEvent') ||\n      is(element, 'bpmn:IntermediateCatchEvent') ||\n      is(element, 'bpmn:ReceiveTask') ||\n      isSpecialBoundaryEvent(element)\n    ) {\n      return getBusinessObject(element).name || element.id;\n    }\n\n    return null;\n  }\n\n  /**\n   * @param { any } element\n   *\n   * @return {SimulatorEvent}\n   */\n  function getNoneEvent(element) {\n    return {\n      element,\n      interrupting: false,\n      boundary: false,\n      iref: element.id,\n      type: 'none'\n    };\n  }\n\n  /**\n   * @param { any } element\n   *\n   * @return {SimulatorEvent}\n   */\n  function getEvent(element) {\n\n    // do not double-return element\n    if (!element.businessObject) {\n      return element;\n    }\n\n    const interrupting = isInterrupting(element);\n    const boundary = isBoundaryEvent(element);\n\n    // we do create an internal reference for\n    // catch-like events to ensure these can\n    // be triggered via the UI exclusively\n    const iref = createInternalRef(element);\n\n    const baseEvent = {\n      element,\n      interrupting,\n      boundary,\n      ...(iref ? { iref } : {})\n    };\n\n    const eventDefinition = getEventDefinitions(element)[0];\n\n    if (!eventDefinition) {\n\n      return {\n        ...baseEvent,\n        type: isImplicitMessageCatch(element) ? 'message' : 'none'\n      };\n    }\n\n    if (is(eventDefinition, 'bpmn:LinkEventDefinition')) {\n      return {\n        ...baseEvent,\n        type: 'link',\n        name: eventDefinition.name\n      };\n    }\n\n    if (is(eventDefinition, 'bpmn:SignalEventDefinition')) {\n      return {\n        ...baseEvent,\n        type: 'signal',\n        ref: eventDefinition.signalRef\n      };\n    }\n\n    if (is(eventDefinition, 'bpmn:TimerEventDefinition')) {\n      return {\n        ...baseEvent,\n        type: 'timer'\n      };\n    }\n\n    if (is(eventDefinition, 'bpmn:ConditionalEventDefinition')) {\n      return {\n        ...baseEvent,\n        type: 'condition',\n      };\n    }\n\n    if (is(eventDefinition, 'bpmn:EscalationEventDefinition')) {\n      return {\n        ...baseEvent,\n        type: 'escalation',\n        ref: eventDefinition.escalationRef\n      };\n    }\n\n    if (is(eventDefinition, 'bpmn:CancelEventDefinition')) {\n      return {\n        ...baseEvent,\n        type: 'cancel'\n      };\n    }\n\n    if (is(eventDefinition, 'bpmn:ErrorEventDefinition')) {\n      return {\n        ...baseEvent,\n        type: 'error',\n        ref: eventDefinition.errorRef\n      };\n    }\n\n    if (is(eventDefinition, 'bpmn:MessageEventDefinition')) {\n      return {\n        ...baseEvent,\n        type: 'message',\n        ref: eventDefinition.messageRef\n      };\n    }\n\n    if (is(eventDefinition, 'bpmn:CompensateEventDefinition')) {\n\n      let ref = eventDefinition.activityRef && elementRegistry.get(eventDefinition.activityRef.id);\n\n      if (!ref) {\n\n        if (isStartEvent(element) && isEventSubProcess(element.parent)) {\n\n          // start event in event sub-process compensates\n          // parent process (or participant)\n          ref = element.parent.parent;\n        } else if (isBoundaryEvent(element)) {\n\n          // boundary event compensates activity it is attached to\n          ref = element.host;\n        } else {\n\n          // parent is cancel scope\n          ref = element.parent;\n        }\n      }\n\n      return {\n        ...baseEvent,\n        type: 'compensate',\n        ref,\n        persistent: true\n      };\n    }\n\n    throw new Error('unknown event definition', eventDefinition);\n  }\n\n  function createScope(context, emitEvent = true) {\n\n    const {\n      element,\n      parent: parentScope,\n      initiator\n    } = context;\n\n    emitEvent && trace('createScope', {\n      element,\n      scope: parentScope\n    });\n\n    const scope = new Scope(ids.next(), element, parentScope, initiator);\n\n    if (parentScope) {\n      parentScope.children.push(scope);\n    }\n\n    scopes.add(scope);\n\n    emitEvent && emit('createScope', {\n      scope\n    });\n\n    elementChanged(element);\n\n    if (parentScope) {\n      elementChanged(parentScope.element);\n    }\n\n    return scope;\n  }\n\n  function subscriptionFilter(filter) {\n\n    if (typeof filter === 'function') {\n      return filter;\n    }\n\n    const {\n      event: _event,\n      element,\n      scope\n    } = filter;\n\n    const elements = filter.elements || (element && [ element ]);\n    const event = _event && getEvent(_event);\n\n    return (\n      (subscription) =>\n        (!event || eventsMatch(event, subscription.event)) &&\n        (!elements || elements.includes(subscription.element)) &&\n        (!scope || scope === subscription.scope)\n    );\n  }\n\n  function scopeSubscriptionFilter(event) {\n    const matchesSubscription = event === 'function' ? event : subscriptionFilter(event);\n\n    return (\n      scope => Array.from(scope.subscriptions).some(matchesSubscription)\n    );\n  }\n\n  function scopeFilter(filter) {\n\n    if (typeof filter === 'function') {\n      return filter;\n    }\n\n    const {\n      element,\n      waitsOnElement,\n      parent,\n      trait = ScopeTraits.RUNNING,\n      subscribedTo\n    } = filter;\n\n    const isSubscribed = subscribedTo ? scopeSubscriptionFilter(subscribedTo) : () => true;\n\n    return (\n      scope =>\n        (!element || scope.element === element) &&\n        (!parent || scope.parent === parent) &&\n        (!waitsOnElement || scope.getTokensByElement(waitsOnElement) > 0) &&\n        scope.hasTrait(trait) &&\n        isSubscribed(scope)\n    );\n  }\n\n  function findSubscriptions(filter) {\n    return filterSet(subscriptions, subscriptionFilter(filter));\n  }\n\n  function findSubscription(filter) {\n    return findSet(subscriptions, subscriptionFilter(filter));\n  }\n\n  function findScopes(filter) {\n    return filterSet(scopes, scopeFilter(filter));\n  }\n\n  function findScope(filter) {\n    return findSet(scopes, scopeFilter(filter));\n  }\n\n  function destroyScope(scope, initiator = null) {\n\n    if (scope.destroyed) {\n      return;\n    }\n\n    scope.destroy(initiator);\n\n    // remove outdated subscriptions\n    for (const subscription of scope.subscriptions) {\n      const trait = subscription.event.traits || ScopeTraits.ACTIVE;\n\n      if (!scope.hasTrait(trait)) {\n        unsubscribe(subscription);\n      }\n    }\n\n    // depending on taken transition scope many not actually\n    // be destroyed but in an inactive / completed state\n    //\n    // only perform additional destructive operations in case we're\n    // actually DEAD.\n    if (scope.destroyed) {\n\n      // destroy child scopes\n      for (const childScope of scope.children) {\n        if (!childScope.destroyed) {\n          destroyScope(childScope, initiator);\n        }\n      }\n\n      trace('destroyScope', {\n        element: scope.element,\n        scope\n      });\n\n      // remove dead scope\n      scopes.delete(scope);\n\n      emit('destroyScope', {\n        scope\n      });\n    }\n\n    elementChanged(scope.element);\n\n    if (scope.parent) {\n      elementChanged(scope.parent.element);\n    }\n  }\n\n  function trace(action, context) {\n\n    emit('trace', {\n      ...context,\n      action\n    });\n  }\n\n  function elementChanged(element) {\n    changedElements.add(element);\n\n    // tick, unless jobs are queued\n    // (and tick is going to happen naturally)\n    if (!jobs.length) {\n      emit('tick');\n    }\n  }\n\n  function scopeChanged(scope) {\n    emit('scopeChanged', {\n      scope\n    });\n  }\n\n  function emit(event, payload = {}) {\n    return eventBus.fire(`tokenSimulation.simulator.${event}`, payload);\n  }\n\n  function on(event, callback) {\n    eventBus.on('tokenSimulation.simulator.' + event, callback);\n  }\n\n  function off(event, callback) {\n    eventBus.off('tokenSimulation.simulator.' + event, callback);\n  }\n\n  function setConfig(element, updatedConfig) {\n\n    const existingConfig = getConfig(element);\n\n    configuration[element.id || element] = {\n      ...existingConfig,\n      ...updatedConfig\n    };\n\n    elementChanged(element);\n  }\n\n  function initializeRootScopes() {\n\n    const rootScopes = [];\n\n    elementRegistry.forEach(element => {\n\n      if (!isAny(element, [ 'bpmn:Process', 'bpmn:Participant' ])) {\n        return;\n      }\n\n      const scope = createScope({\n        element\n      }, false);\n\n      rootScopes.push(scope);\n\n      const startEvents = element.children.filter(isStartEvent);\n\n      const implicitStartEvents = element.children.filter(isImplicitStartEvent);\n\n      for (const startEvent of startEvents) {\n\n        const event = {\n          ...getEvent(startEvent),\n          interrupting: false\n        };\n\n        // start events can always be triggered\n        subscribe(scope, event, initiator => signal({\n          element,\n          startEvent: startEvent,\n          initiator\n        }));\n      }\n\n      if (!startEvents.length) {\n\n        for (const implicitStartEvent of implicitStartEvents) {\n\n          const event = getNoneEvent(implicitStartEvent);\n\n          // start events can always be triggered\n          subscribe(scope, event, initiator => signal({\n            element,\n            initiator\n          }));\n        }\n      }\n    });\n\n    return rootScopes;\n  }\n\n  function initializeScope(context) {\n\n    const {\n      element\n    } = context;\n\n    const scope = createScope(context);\n\n    const {\n      attachers = []\n    } = element;\n\n    const children = getChildren(element, elementRegistry);\n\n    for (const childElement of children) {\n\n      // event sub-process start events\n      if (isEventSubProcess(childElement)) {\n        const startEvents = getChildren(childElement, elementRegistry).filter(\n          element => isStartEvent(element) && !isCompensationEvent(element)\n        );\n\n        for (const startEvent of startEvents) {\n          subscribe(scope, startEvent, initiator => {\n\n            return signal({\n              element: childElement,\n              parentScope: scope,\n              startEvent,\n              initiator\n            });\n          });\n        }\n      }\n    }\n\n    for (const attacher of attachers) {\n\n      // boundary events\n      if (isBoundaryEvent(attacher) && !isCompensationEvent(attacher)) {\n\n        subscribe(scope, attacher, initiator => {\n          return signal({\n            element: attacher,\n            parentScope: scope.parent,\n            hostScope: scope,\n            initiator\n          });\n        });\n      }\n    }\n\n    return scope;\n  }\n\n  function getConfig(element) {\n    return configuration[element.id || element] || {};\n  }\n\n  function waitForScopes(scope, scopes) {\n\n    if (!scopes.length) {\n      return;\n    }\n\n    const event = {\n      type: 'all-completed',\n      persistent: false\n    };\n\n    const remainingScopes = new Set(scopes);\n\n    const destroyListener = (destroyEvent) => {\n      remainingScopes.delete(destroyEvent.scope);\n\n      if (remainingScopes.size === 0) {\n        off('destroyScope', destroyListener);\n\n        trigger({\n          scope,\n          event\n        });\n      }\n    };\n\n    on('destroyScope', destroyListener);\n\n    return event;\n  }\n\n  function waitAtElement(element, wait = true) {\n    setConfig(element, {\n      wait\n    });\n  }\n\n  function reset() {\n    for (const scope of scopes) {\n      destroyScope(scope);\n    }\n\n    for (const rootScope of initializeRootScopes()) {\n      scopes.add(rootScope);\n    }\n\n    // TODO(nikku): clear configuration?\n\n    emit('tick');\n    emit('reset');\n  }\n\n  // utilties\n  this.createScope = createScope;\n  this.destroyScope = destroyScope;\n\n  // inspection\n  this.findScope = findScope;\n  this.findScopes = findScopes;\n\n  this.findSubscription = findSubscription;\n  this.findSubscriptions = findSubscriptions;\n\n  // configuration\n  this.waitAtElement = waitAtElement;\n\n  this.waitForScopes = waitForScopes;\n\n  this.setConfig = setConfig;\n  this.getConfig = getConfig;\n\n  // driving simulation forward\n  this.signal = signal;\n  this.enter = enter;\n  this.exit = exit;\n\n  // BPMN event subscriptions and triggers\n  this.subscribe = subscribe;\n  this.trigger = trigger;\n\n  // life-cycle\n  this.reset = reset;\n\n  // emitter\n  this.on = on;\n  this.off = off;\n\n  // extension\n  this.registerBehavior = function(element, behavior) {\n    behaviors[element] = behavior;\n  };\n}\n\nSimulator.$inject = [\n  'injector',\n  'eventBus',\n  'elementRegistry'\n];\n\n\n// helpers /////////////////\n\nfunction NoopBehavior() {\n\n  this.signal = function(context) {\n    console.log('ignored #exit', context.element);\n  };\n\n  this.exit = function(context) {\n    console.log('ignored #exit', context.element);\n  };\n\n  this.enter = function(context) {\n    console.log('ignored #enter', context.element);\n  };\n\n}\n\nfunction isRethrow(event, interrupt) {\n  return (\n    event.type === interrupt.type &&\n    event.boundary && !interrupt.boundary\n  );\n}\n\nfunction isImplicitMessageCatch(element) {\n  return is(element, 'bpmn:ReceiveTask') || element.incoming.some(element => is(element, 'bpmn:MessageFlow'));\n}\n\nfunction isSpecialBoundaryEvent(element) {\n  if (!isBoundaryEvent(element)) {\n    return false;\n  }\n\n  const eventDefinitions = getEventDefinitions(element);\n\n  return !eventDefinitions[0] || isAny(eventDefinitions[0], [\n    'bpmn:ConditionalEventDefinition', 'bpmn:TimerEventDefinition'\n  ]);\n}\n\nfunction getEventDefinitions(element) {\n  return element.businessObject.get('eventDefinitions') || [];\n}\n", "export default function StartEventBehavior(\n    simulator,\n    activityBehavior) {\n\n  this._simulator = simulator;\n  this._activityBehavior = activityBehavior;\n\n  simulator.registerBehavior('bpmn:StartEvent', this);\n}\n\nStartEventBehavior.prototype.signal = function(context) {\n  this._simulator.exit(context);\n};\n\nStartEventBehavior.prototype.exit = function(context) {\n  this._activityBehavior.exit(context);\n};\n\nStartEventBehavior.$inject = [\n  'simulator',\n  'activityBehavior'\n];", "export default function EndEventBehavior(\n    simulator,\n    scopeBehavior,\n    intermediateThrowEventBehavior) {\n\n  this._intermediateThrowEventBehavior = intermediateThrowEventBehavior;\n  this._scopeBehavior = scopeBehavior;\n\n  simulator.registerBehavior('bpmn:EndEvent', this);\n}\n\nEndEventBehavior.$inject = [\n  'simulator',\n  'scopeBehavior',\n  'intermediateThrowEventBehavior'\n];\n\nEndEventBehavior.prototype.enter = function(context) {\n  this._intermediateThrowEventBehavior.enter(context);\n};\n\nEndEventBehavior.prototype.signal = function(context) {\n  this._intermediateThrowEventBehavior.signal(context);\n};\n\nEndEventBehavior.prototype.exit = function(context) {\n\n  const {\n    scope\n  } = context;\n\n  this._scopeBehavior.tryExit(scope.parent, scope);\n};", "import {\n  getBusinessObject\n} from '../util/ModelUtil';\n\n\nexport default function BoundaryEventBehavior(\n    simulator,\n    activityBehavior,\n    scopeBehavior) {\n\n  this._simulator = simulator;\n  this._activityBehavior = activityBehavior;\n  this._scopeBehavior = scopeBehavior;\n\n  simulator.registerBehavior('bpmn:BoundaryEvent', this);\n}\n\nBoundaryEventBehavior.prototype.signal = function(context) {\n\n  const {\n    element,\n    scope,\n    hostScope = this._simulator.findScope({\n      parent: scope.parent,\n      element: element.host\n    })\n  } = context;\n\n  if (!hostScope) {\n    throw new Error('host scope not found');\n  }\n\n  const cancelActivity = getBusinessObject(element).cancelActivity;\n\n  if (cancelActivity) {\n    this._scopeBehavior.interrupt(hostScope, scope);\n\n    // activities are pending completion before actual exit\n    const event = this._scopeBehavior.tryExit(hostScope, scope);\n\n    if (event) {\n      const subscription = this._simulator.subscribe(hostScope, event, initiator => {\n        subscription.remove();\n\n        return this._simulator.exit(context);\n      });\n\n      return;\n    }\n  }\n\n  this._simulator.exit(context);\n};\n\nBoundaryEventBehavior.prototype.exit = function(context) {\n  this._activityBehavior.exit(context);\n};\n\nBoundaryEventBehavior.$inject = [\n  'simulator',\n  'activityBehavior',\n  'scopeBehavior'\n];", "export default function IntermediateCatchEventBehavior(\r\n    simulator,\r\n    activityBehavior) {\r\n\r\n  this._activityBehavior = activityBehavior;\r\n  this._simulator = simulator;\r\n\r\n  simulator.registerBehavior('bpmn:IntermediateCatchEvent', this);\r\n  simulator.registerBehavior('bpmn:ReceiveTask', this);\r\n}\r\n\r\nIntermediateCatchEventBehavior.$inject = [\r\n  'simulator',\r\n  'activityBehavior'\r\n];\r\n\r\nIntermediateCatchEventBehavior.prototype.signal = function(context) {\r\n  return this._simulator.exit(context);\r\n};\r\n\r\nIntermediateCatchEventBehavior.prototype.enter = function(context) {\r\n  const {\r\n    element\r\n  } = context;\r\n\r\n  // adapt special wait semantics; user must manually\r\n  // trigger to indicate message received\r\n  return this._activityBehavior.signalOnEvent(context, element);\r\n};\r\n\r\nIntermediateCatchEventBehavior.prototype.exit = function(context) {\r\n  this._activityBehavior.exit(context);\r\n};", "export default function IntermediateThrowEventBehavior(\r\n    simulator,\r\n    activityBehavior,\r\n    eventBehaviors) {\r\n\r\n  this._simulator = simulator;\r\n  this._activityBehavior = activityBehavior;\r\n  this._eventBehaviors = eventBehaviors;\r\n\r\n  simulator.registerBehavior('bpmn:IntermediateThrowEvent', this);\r\n  simulator.registerBehavior('bpmn:SendTask', this);\r\n}\r\n\r\nIntermediateThrowEventBehavior.prototype.enter = function(context) {\r\n  const {\r\n    element\r\n  } = context;\r\n\r\n  const eventBehavior = this._eventBehaviors.get(element);\r\n\r\n  if (eventBehavior) {\r\n    const event = eventBehavior(context);\r\n\r\n    if (event) {\r\n      return this._activityBehavior.signalOnEvent(context, event);\r\n    }\r\n  }\r\n\r\n  this._activityBehavior.enter(context);\r\n};\r\n\r\nIntermediateThrowEventBehavior.prototype.signal = function(context) {\r\n  this._activityBehavior.signal(context);\r\n};\r\n\r\nIntermediateThrowEventBehavior.prototype.exit = function(context) {\r\n  this._activityBehavior.exit(context);\r\n};\r\n\r\nIntermediateThrowEventBehavior.$inject = [\r\n  'simulator',\r\n  'activityBehavior',\r\n  'eventBehaviors'\r\n];", "import {\n  filterSequenceFlows\n} from '../util/ModelUtil';\n\n\nexport default function ExclusiveGatewayBehavior(simulator, scopeBehavior) {\n  this._scopeBehavior = scopeBehavior;\n  this._simulator = simulator;\n\n  simulator.registerBehavior('bpmn:ExclusiveGateway', this);\n}\n\nExclusiveGatewayBehavior.prototype.enter = function(context) {\n  this._simulator.exit(context);\n};\n\nExclusiveGatewayBehavior.prototype.exit = function(context) {\n\n  const {\n    element,\n    scope\n  } = context;\n\n  // depends on UI to properly configure activeOutgoing for\n  // each exclusive gateway\n\n  const outgoings = filterSequenceFlows(element.outgoing);\n\n  if (outgoings.length === 1) {\n    return this._simulator.enter({\n      element: outgoings[0],\n      scope: scope.parent\n    });\n  }\n\n  const {\n    activeOutgoing\n  } = this._simulator.getConfig(element);\n\n  const outgoing = outgoings.find(o => o === activeOutgoing);\n\n  if (!outgoing) {\n    return this._scopeBehavior.tryExit(scope.parent, scope);\n  }\n\n  return this._simulator.enter({\n    element: outgoing,\n    scope: scope.parent\n  });\n};\n\nExclusiveGatewayBehavior.$inject = [\n  'simulator',\n  'scopeBehavior'\n];", "import {\n  filterSequenceFlows\n} from '../util/ModelUtil';\n\n\nexport default function ParallelGatewayBehavior(\n    simulator,\n    activityBehavior) {\n\n  this._simulator = simulator;\n  this._activityBehavior = activityBehavior;\n\n  simulator.registerBehavior('bpmn:ParallelGateway', this);\n}\n\nParallelGatewayBehavior.prototype.enter = function(context) {\n\n  const {\n    scope\n  } = context;\n\n  const joiningScopes = this._findJoiningScopes(context);\n\n  if (joiningScopes.length) {\n\n    for (const childScope of joiningScopes) {\n\n      if (childScope !== scope) {\n\n        // complete joining child scope\n        this._simulator.destroyScope(childScope.complete(), scope);\n      }\n    }\n\n    this._simulator.exit(context);\n  }\n};\n\n/**\n * Find scopes that will be joined by this transition.\n *\n * @param {Object} enterContext\n * @return {Scope[]} scopes joined by this transition\n */\nParallelGatewayBehavior.prototype._findJoiningScopes = function(enterContext) {\n\n  const {\n    scope,\n    element\n  } = enterContext;\n\n  const sequenceFlows = filterSequenceFlows(element.incoming);\n\n  const {\n    parent: parentScope\n  } = scope;\n\n  const elementScopes = this._simulator.findScopes({\n    parent: parentScope,\n    element: element\n  });\n\n  const matchingScopes = sequenceFlows\n    .map(\n      flow => elementScopes\n        .find(scope => scope.initiator.element === flow)\n    )\n    .filter(scope => scope);\n\n  if (matchingScopes.length === sequenceFlows.length) {\n    return matchingScopes;\n  } else {\n    return [];\n  }\n};\n\nParallelGatewayBehavior.prototype.exit = function(context) {\n  this._activityBehavior.exit(context);\n};\n\nParallelGatewayBehavior.$inject = [\n  'simulator',\n  'activityBehavior'\n];", "import { isAny } from '../util/ModelUtil';\n\n\nexport default function EventBasedGatewayBehavior(simulator) {\n  this._simulator = simulator;\n\n  simulator.registerBehavior('bpmn:EventBasedGateway', this);\n}\n\nEventBasedGatewayBehavior.$inject = [\n  'simulator'\n];\n\nEventBasedGatewayBehavior.prototype.enter = function(context) {\n\n  const {\n    element,\n    scope\n  } = context;\n\n  const parentScope = scope.parent;\n\n  const triggerElements = getTriggers(element);\n\n  // create subscriptions for outgoing event triggers\n  // do nothing else beyond that\n  const subscriptions = triggerElements.map(\n    triggerElement => this._simulator.subscribe(parentScope, triggerElement, initiator => {\n\n      // cancel all subscriptions\n      subscriptions.forEach(subscription => subscription.remove());\n\n      // destroy this scope\n      this._simulator.destroyScope(scope, initiator);\n\n      // signal triggered event\n      return this._simulator.signal({\n        element: triggerElement,\n        parentScope,\n        initiator\n      });\n    })\n  );\n\n};\n\n\n// helpers ////////////////\n\nfunction getTriggers(element) {\n  return element.outgoing.map(\n    outgoing => outgoing.target\n  ).filter(activity => isAny(activity, [\n    'bpmn:IntermediateCatchEvent',\n    'bpmn:ReceiveTask'\n  ]));\n}", "import {\n  filterSequenceFlows, isSequenceFlow\n} from '../util/ModelUtil';\n\n\nexport default function InclusiveGatewayBehavior(\n    simulator,\n    activityBehavior) {\n\n  this._simulator = simulator;\n  this._activityBehavior = activityBehavior;\n\n  simulator.registerBehavior('bpmn:InclusiveGateway', this);\n}\n\nInclusiveGatewayBehavior.prototype.enter = function(context) {\n  this._tryJoin(context);\n};\n\nInclusiveGatewayBehavior.prototype.exit = function(context) {\n\n  const {\n    element,\n    scope\n  } = context;\n\n  // depends on UI to properly configure activeOutgoing for\n  // each inclusive gateway\n\n  const outgoings = filterSequenceFlows(element.outgoing);\n\n  // fork based on configured active outgoings\n  if (outgoings.length > 1) {\n\n    const {\n      activeOutgoing = []\n    } = this._simulator.getConfig(element);\n\n    if (!activeOutgoing.length) {\n      throw new Error('no outgoing configured');\n    }\n\n    for (const outgoing of activeOutgoing) {\n      this._simulator.enter({\n        element: outgoing,\n        scope: scope.parent\n      });\n    }\n\n  } else {\n\n    // exit like any activity\n    this._activityBehavior.exit(context);\n  }\n\n};\n\nInclusiveGatewayBehavior.prototype._tryJoin = function(context) {\n\n  const remainingScopes = this._getRemainingScopes(context);\n\n  const remainingElements = remainingScopes.map(scope => scope.element);\n\n  // join right away if possible\n  // this implies that there are no remaining scopes\n  // or non of the remaining scopes are reachable\n  if (!this._canReachAnyElement(remainingElements, context.element)) {\n    return this._join(context);\n  }\n\n  const elementScopes = this._getElementScopes(context);\n\n  const {\n    scope\n  } = context;\n\n  // only subscribe to changes with the first\n  // element scope; prevent unneeded computation\n  if (elementScopes[0] !== scope) {\n    return;\n  }\n\n  const event = this._simulator.waitForScopes(scope, remainingScopes);\n\n  const subscription = this._simulator.subscribe(scope, event, () => {\n    subscription.remove();\n\n    this._tryJoin(context);\n  });\n};\n\n/**\n * Get scopes that may potentially be waited for,\n * in the context of an inclusive gateway.\n *\n * @param {object} context\n * @return {object[]}\n */\nInclusiveGatewayBehavior.prototype._getRemainingScopes = function(context) {\n  const {\n    scope,\n    element\n  } = context;\n\n  const {\n    parent: parentScope\n  } = scope;\n\n  return this._simulator.findScopes(\n    scope => scope.parent === parentScope && scope.element !== element\n  );\n};\n\nInclusiveGatewayBehavior.prototype._join = function(context) {\n  const elementScopes = this._getElementScopes(context);\n\n  for (const childScope of elementScopes) {\n\n    if (childScope !== context.scope) {\n\n      // complete joining child scope\n      this._simulator.destroyScope(childScope.complete(), context.scope);\n    }\n  }\n\n  this._simulator.exit(context);\n};\n\n/**\n * Get scopes on the element for the given context.\n *\n * @param {object} context\n *\n * @return {object[]} scopes\n */\nInclusiveGatewayBehavior.prototype._getElementScopes = function(context) {\n  const {\n    element,\n    scope\n  } = context;\n\n  return this._simulator.findScopes({\n    parent: scope.parent,\n    element\n  });\n};\n\n/**\n * Return true if any elements can be reached\n * from the current element, searching the execution\n * graph backwards.\n *\n * @param {object[]} elements\n * @param {object} currentElement\n * @param {Set<object>} traversed\n *\n * @return {boolean}\n */\nInclusiveGatewayBehavior.prototype._canReachAnyElement = function(elements, currentElement, traversed = new Set()) {\n\n  if (!elements.length) {\n    return false;\n  }\n\n  // avoid infinite recursion\n  if (traversed.has(currentElement)) {\n    return false;\n  }\n\n  traversed.add(currentElement);\n\n  if (elements.some(e => e === currentElement)) {\n    return true;\n  }\n\n  if (isSequenceFlow(currentElement)) {\n    return this._canReachAnyElement(elements, currentElement.source, traversed);\n  }\n\n  const incomingFlows = filterSequenceFlows(currentElement.incoming);\n\n  for (const flow of incomingFlows) {\n    if (this._canReachAnyElement(elements, flow, traversed)) {\n      return true;\n    }\n  }\n\n  return false;\n};\n\nInclusiveGatewayBehavior.$inject = [\n  'simulator',\n  'activityBehavior'\n];", "import {\n  isEventSubProcess,\n  isMessageFlow,\n  isSequenceFlow\n} from '../util/ModelUtil';\n\n\nexport default function ActivityBehavior(\n    simulator,\n    scopeBehavior,\n    transactionBehavior\n) {\n  this._simulator = simulator;\n  this._scopeBehavior = scopeBehavior;\n  this._transactionBehavior = transactionBehavior;\n\n  const elements = [\n    'bpmn:BusinessRuleTask',\n    'bpmn:CallActivity',\n    'bpmn:ManualTask',\n    'bpmn:ScriptTask',\n    'bpmn:ServiceTask',\n    'bpmn:Task',\n    'bpmn:UserTask'\n  ];\n\n  for (const element of elements) {\n    simulator.registerBehavior(element, this);\n  }\n}\n\nActivityBehavior.$inject = [\n  'simulator',\n  'scopeBehavior',\n  'transactionBehavior'\n];\n\nActivityBehavior.prototype.signal = function(context) {\n\n  // trigger messages that are pending send\n  const event = this._triggerMessages(context);\n\n  if (event) {\n    return this.signalOnEvent(context, event);\n  }\n\n  this._simulator.exit(context);\n};\n\nActivityBehavior.prototype.enter = function(context) {\n\n  const {\n    element\n  } = context;\n\n  const continueEvent = this.waitAtElement(element);\n\n  if (continueEvent) {\n    return this.signalOnEvent(context, continueEvent);\n  }\n\n  // trigger messages that are pending send\n  const event = this._triggerMessages(context);\n\n  if (event) {\n    return this.signalOnEvent(context, event);\n  }\n\n  this._simulator.exit(context);\n};\n\nActivityBehavior.prototype.exit = function(context) {\n\n  const {\n    element,\n    scope\n  } = context;\n\n  const parentScope = scope.parent;\n\n  // TODO(nikku): if a outgoing flow is conditional,\n  //              task has exclusive gateway semantics,\n  //              else, task has parallel gateway semantics\n\n  const complete = !scope.failed;\n\n  // compensation is registered AFTER successful completion\n  // of normal scope activities (non event sub-processes).\n  //\n  // we must register it now, not earlier\n  if (complete && !isEventSubProcess(element)) {\n    this._transactionBehavior.registerCompensation(scope);\n  }\n\n  // if exception flow is active,\n  // do not activate any outgoing flows\n  const activatedFlows = complete\n    ? element.outgoing.filter(isSequenceFlow)\n    : [];\n\n  activatedFlows.forEach(\n    element => this._simulator.enter({\n      element,\n      scope: parentScope\n    })\n  );\n\n  // element has token sink semantics\n  if (activatedFlows.length === 0) {\n    this._scopeBehavior.tryExit(parentScope, scope);\n  }\n};\n\nActivityBehavior.prototype.signalOnEvent = function(context, event) {\n\n  const {\n    scope,\n    element\n  } = context;\n\n  const subscription = this._simulator.subscribe(scope, event, initiator => {\n\n    subscription.remove();\n\n    return this._simulator.signal({\n      scope,\n      element,\n      initiator\n    });\n  });\n};\n\n/**\n * Returns an event to subscribe to if wait on element is configured.\n *\n * @param {Element} element\n *\n * @return {Object|null} event\n */\nActivityBehavior.prototype.waitAtElement = function(element) {\n  const wait = this._simulator.getConfig(element).wait;\n\n  return wait && {\n    element,\n    type: 'continue',\n    interrupting: false,\n    boundary: false\n  };\n};\n\nActivityBehavior.prototype._getMessageContexts = function(element, after = null) {\n\n  const filterAfter = after ? ctx => ctx.referencePoint.x > after.x : () => true;\n  const sortByReference = (a, b) => a.referencePoint.x - b.referencePoint.x;\n\n  return [\n    ...element.incoming.filter(isMessageFlow).map(flow => ({\n      incoming: flow,\n      referencePoint: last(flow.waypoints)\n    })),\n    ...element.outgoing.filter(isMessageFlow).map(flow => ({\n      outgoing: flow,\n      referencePoint: first(flow.waypoints)\n    }))\n  ].sort(sortByReference).filter(filterAfter);\n};\n\n/**\n * @param {any} context\n *\n * @return {Object} event to subscribe to proceed\n */\nActivityBehavior.prototype._triggerMessages = function(context) {\n\n  // check for the next message flows to either\n  // trigger or wait for; this implements intuitive,\n  // as-you-would expect message flow execution in modeling\n  // direction (left-to-right).\n\n  const {\n    element,\n    initiator,\n    scope\n  } = context;\n\n  let messageContexts = scope.messageContexts;\n\n  if (!messageContexts) {\n    messageContexts = scope.messageContexts = this._getMessageContexts(element);\n  }\n\n  const initiatingFlow = initiator && initiator.element;\n\n  if (isMessageFlow(initiatingFlow)) {\n\n    // ignore out of bounds messages received;\n    // user may manually advance and force send all outgoing\n    // messages\n    if (scope.expectedIncoming !== initiatingFlow) {\n      console.debug('Simulator :: ActivityBehavior :: ignoring out-of-bounds message');\n\n      return;\n    }\n  }\n\n  while (messageContexts.length) {\n    const {\n      incoming,\n      outgoing\n    } = messageContexts.shift();\n\n    if (incoming) {\n\n      // force sending of all remaining messages,\n      // as the user triggered the task manually (for demonstration\n      // purposes\n      if (!initiator) {\n        continue;\n      }\n\n      // remember expected incoming for future use\n      scope.expectedIncoming = incoming;\n\n      return {\n        element,\n        type: 'message',\n        name: incoming.id,\n        interrupting: false,\n        boundary: false\n      };\n    }\n\n    this._simulator.signal({\n      element: outgoing\n    });\n  }\n\n};\n\n\n// helpers //////////////////\n\nfunction first(arr) {\n  return arr && arr[0];\n}\n\nfunction last(arr) {\n  return arr && arr[arr.length - 1];\n}", "import {\n  getChildren,\n  is,\n  isEventSubProcess,\n  isInterrupting,\n  isStartEvent,\n  isNoneStartEvent,\n  isImplicitStartEvent\n} from '../util/ModelUtil';\n\n\nexport default function SubProcessBehavior(\n    simulator,\n    activityBehavior,\n    scopeBehavior,\n    transactionBehavior,\n    elementRegistry) {\n\n  this._simulator = simulator;\n  this._activityBehavior = activityBehavior;\n  this._scopeBehavior = scopeBehavior;\n  this._transactionBehavior = transactionBehavior;\n  this._elementRegistry = elementRegistry;\n\n  simulator.registerBehavior('bpmn:SubProcess', this);\n  simulator.registerBehavior('bpmn:Transaction', this);\n  simulator.registerBehavior('bpmn:AdHocSubProcess', this);\n}\n\nSubProcessBehavior.$inject = [\n  'simulator',\n  'activityBehavior',\n  'scopeBehavior',\n  'transactionBehavior',\n  'elementRegistry'\n];\n\nSubProcessBehavior.prototype.signal = function(context) {\n  this._start(context);\n};\n\nSubProcessBehavior.prototype.enter = function(context) {\n\n  const {\n    element\n  } = context;\n\n  const continueEvent = this._activityBehavior.waitAtElement(element);\n\n  if (continueEvent) {\n    return this._activityBehavior.signalOnEvent(context, continueEvent);\n  }\n\n  this._start(context);\n};\n\nSubProcessBehavior.prototype.exit = function(context) {\n\n  const {\n    scope\n  } = context;\n\n  const parentScope = scope.parent;\n\n  // successful completion of the fail initiator (event sub-process)\n  // recovers the parent, so that the normal flow is being executed\n  if (parentScope.failInitiator === scope) {\n    parentScope.complete();\n  }\n\n  this._activityBehavior.exit(context);\n};\n\nSubProcessBehavior.prototype._start = function(context) {\n  const {\n    element,\n    startEvent,\n    scope\n  } = context;\n\n  const targetScope = scope.parent;\n\n  if (isEventSubProcess(element)) {\n\n    if (!startEvent) {\n      throw new Error('missing <startEvent>: required for event sub-process');\n    }\n  } else {\n    if (startEvent) {\n      throw new Error('unexpected <startEvent>: not allowed for sub-process');\n    }\n  }\n\n  if (targetScope.destroyed) {\n    throw new Error(`target scope <${targetScope.id}> destroyed`);\n  }\n\n  if (isTransaction(element)) {\n    this._transactionBehavior.setup(context);\n  }\n\n  if (startEvent && isInterrupting(startEvent)) {\n    this._scopeBehavior.interrupt(targetScope, scope);\n  }\n\n  const startNodes = this._findStarts(element, startEvent);\n\n  for (const element of startNodes) {\n\n    if (isStartEvent(element)) {\n      this._simulator.signal({\n        element,\n        parentScope: scope,\n        initiator: scope\n      });\n    } else {\n      this._simulator.enter({\n        element,\n        scope,\n        initiator: scope\n      });\n    }\n  }\n};\n\nSubProcessBehavior.prototype._findStarts = function(element, startEvent) {\n  const isStartEvent = startEvent\n    ? (node) => startEvent === node\n    : (node) => isNoneStartEvent(node);\n\n  return getChildren(element, this._elementRegistry).filter(\n    node => (\n      isStartEvent(node) || isImplicitStartEvent(node)\n    )\n  );\n};\n\nfunction isTransaction(element) {\n  return is(element, 'bpmn:Transaction');\n}\n", "import {\n  ScopeTraits\n} from '../ScopeTraits';\n\nimport {\n  getChildren,\n  isAny,\n  isCompensationEvent,\n  isCompensationActivity,\n  isEventSubProcess,\n  isStartEvent,\n  is\n} from '../util/ModelUtil';\n\nimport {\n  eventsMatch\n} from '../util/EventsUtil';\n\nimport {\n  filterSet\n} from '../util/SetUtil';\n\n\nconst CANCEL_EVENT = {\n  type: 'cancel',\n  interrupting: true,\n  boundary: false,\n  persistent: true\n};\n\n\nexport default function TransactionBehavior(simulator, scopeBehavior, elementRegistry) {\n  this._simulator = simulator;\n  this._scopeBehavior = scopeBehavior;\n  this._elementRegistry = elementRegistry;\n}\n\nTransactionBehavior.$inject = [\n  'simulator',\n  'scopeBehavior',\n  'elementRegistry'\n];\n\nTransactionBehavior.prototype.setup = function(context) {\n\n  const {\n    scope\n  } = context;\n\n  const cancelSubscription = this._simulator.subscribe(scope, CANCEL_EVENT, (initiator) => {\n\n    cancelSubscription.remove();\n\n    return this.cancel({\n      scope,\n      initiator\n    });\n  });\n\n  const compensateEvent = {\n    type: 'compensate',\n    ref: scope.element,\n    persistent: true,\n    traits: ScopeTraits.NOT_DEAD\n  };\n\n  const compensateSubscription = this._simulator.subscribe(scope, compensateEvent, (initiator) => {\n\n    // need to trigger ordinary\n    // transaction cancelation\n    if (!scope.canceled) {\n      return this._simulator.trigger({\n        event: CANCEL_EVENT,\n        scope\n      });\n    }\n\n    compensateSubscription.remove();\n\n    return this.compensate({\n      scope,\n      element: scope.element,\n      initiator\n    });\n  });\n};\n\nTransactionBehavior.prototype.cancel = function(context) {\n\n  const {\n    scope,\n    initiator\n  } = context;\n\n  // bail out on double cancel\n  if (scope.destroyed) {\n    return;\n  }\n\n  // mark scope as canceled\n  scope.cancel(initiator);\n\n  // trigger compensation on element\n  this._simulator.trigger({\n    event: {\n      type: 'compensate',\n      ref: scope.element\n    },\n    initiator,\n    scope\n  });\n\n  // re-trigger cancel (to trigger boundary cancel events)\n  return this._simulator.trigger({\n    scope,\n    initiator,\n    event: CANCEL_EVENT\n  });\n};\n\nTransactionBehavior.prototype.registerCompensation = function(scope) {\n\n  const {\n    element\n  } = scope;\n\n  // check for compensation triggers\n  //\n  // * embedded compensation event sub-processes\n  // * compensation boundary events\n\n  const children = getChildren(element, this._elementRegistry);\n\n  const compensateStartEvents = children.filter(\n    isEventSubProcess\n  ).map(\n    element => getChildren(element, this._elementRegistry).find(\n      element => isStartEvent(element) && isCompensationEvent(element)\n    )\n  ).filter(s => s);\n\n  const compensateBoundaryEvents = element.attachers.filter(isCompensationEvent);\n\n  if (!compensateStartEvents.length && !compensateBoundaryEvents.length) {\n    return;\n  }\n\n  // always register on parent scope\n  const transactionScope = this.findTransactionScope(scope.parent);\n\n  // sub processes may enter a <compensable> state\n  // in that state they are kept alive on exit\n  // until the parent gets destroyed; as long as they are kept alive\n  // compensation can happen on them\n  //\n  if (!is(transactionScope.element, 'bpmn:Transaction')) {\n    this.makeCompensable(transactionScope);\n  }\n\n  for (const startEvent of compensateStartEvents) {\n\n    const compensationEvent = {\n      element: startEvent,\n      type: 'compensate',\n      persistent: true,\n      interrupting: true,\n      ref: element,\n      traits: ScopeTraits.NOT_DEAD\n    };\n\n    const compensateEventSub = startEvent.parent;\n\n    const subscription = this._simulator.subscribe(scope, compensationEvent, initiator => {\n\n      subscription.remove();\n\n      return this._simulator.signal({\n        initiator,\n        element: compensateEventSub,\n        startEvent,\n        parentScope: scope\n      });\n    });\n  }\n\n  for (const boundaryEvent of compensateBoundaryEvents) {\n\n    const compensationEvent = {\n      element: boundaryEvent,\n      type: 'compensate',\n      persistent: true,\n      ref: element,\n      traits: ScopeTraits.NOT_DEAD\n    };\n\n    const compensateActivity = boundaryEvent.outgoing.map(\n      outgoing => outgoing.target\n    ).find(\n      isCompensationActivity\n    );\n\n    if (!compensateActivity) {\n      continue;\n    }\n\n    const subscription = this._simulator.subscribe(transactionScope, compensationEvent, initiator => {\n\n      subscription.remove();\n\n      // enter compensate activity like normal task\n      return this._simulator.enter({\n        initiator,\n        element: compensateActivity,\n        scope: transactionScope\n      });\n    });\n  }\n};\n\nTransactionBehavior.prototype.makeCompensable = function(scope) {\n\n  if (scope.hasTrait(ScopeTraits.COMPENSABLE) || !scope.parent) {\n    return;\n  }\n\n  const compensateEvent = {\n    type: 'compensate',\n    ref: scope.element,\n    interrupting: true,\n    persistent: true,\n    traits: ScopeTraits.NOT_DEAD\n  };\n\n  scope.compensable();\n\n  const scopeSub = this._simulator.subscribe(scope, compensateEvent, (initiator) => {\n\n    scopeSub.remove();\n\n    scope.fail(initiator);\n\n    this.compensate({\n      scope,\n      element: scope.element,\n      initiator\n    });\n\n    this._scopeBehavior.tryExit(scope, initiator);\n\n    return scope;\n  });\n\n  const parentScope = scope.parent;\n\n  if (!parentScope) {\n    return;\n  }\n\n  const parentSub = this._simulator.subscribe(parentScope, compensateEvent, initiator => {\n\n    parentSub.remove();\n\n    return this._simulator.trigger({\n      scope,\n      event: compensateEvent,\n      initiator\n    });\n\n  });\n\n  this.makeCompensable(parentScope);\n};\n\n\nTransactionBehavior.prototype.findTransactionScope = function(scope) {\n\n  let parentScope = scope;\n\n  while (parentScope) {\n    const element = parentScope.element;\n\n    if (is(element, 'bpmn:SubProcess') && !isEventSubProcess(element)) {\n      return parentScope;\n    }\n\n    if (isAny(element, [\n      'bpmn:Transaction',\n      'bpmn:Process',\n      'bpmn:Participant'\n    ])) {\n      return parentScope;\n    }\n\n    parentScope = parentScope.parent;\n  }\n\n  throw noTransactionContext(scope);\n};\n\nTransactionBehavior.prototype.compensate = function(context) {\n\n  const {\n    scope,\n    element\n  } = context;\n\n  // compensate all\n  const compensateSubscriptions = filterSet(\n    scope.subscriptions,\n    subscription => eventsMatch({ type: 'compensate' }, subscription.event)\n  );\n\n  const localSubscriptions = compensateSubscriptions.filter(subscription => subscription.event.ref === element);\n\n  const otherSubscriptions = compensateSubscriptions.filter(subscription => subscription.event.ref !== element);\n\n  for (const subscription of localSubscriptions) {\n    this._scopeBehavior.preExit(scope, initiator => {\n      return this._simulator.trigger(subscription);\n    });\n  }\n\n  for (const subscription of otherSubscriptions.reverse()) {\n    this._scopeBehavior.preExit(scope, initiator => {\n      return this._simulator.trigger(subscription);\n    });\n  }\n};\n\n\n// helpers ///////////////\n\nfunction noTransactionContext(scope) {\n  throw new Error(`no transaction context for <${scope.id}>`);\n}", "export default function SequenceFlowBehavior(\n    simulator,\n    scopeBehavior) {\n\n  this._simulator = simulator;\n  this._scopeBehavior = scopeBehavior;\n\n  simulator.registerBehavior('bpmn:SequenceFlow', this);\n}\n\nSequenceFlowBehavior.prototype.enter = function(context) {\n  this._simulator.exit(context);\n};\n\nSequenceFlowBehavior.prototype.exit = function(context) {\n  const {\n    element,\n    scope\n  } = context;\n\n  this._simulator.enter({\n    initiator: scope,\n    element: element.target,\n    scope: scope.parent\n  });\n};\n\nSequenceFlowBehavior.$inject = [\n  'simulator',\n  'scopeBehavior'\n];", "import {\n  isCatchEvent\n} from '../util/ModelUtil';\n\n\nexport default function MessageFlowBehavior(simulator) {\n  this._simulator = simulator;\n\n  simulator.registerBehavior('bpmn:MessageFlow', this);\n}\n\nMessageFlowBehavior.$inject = [ 'simulator' ];\n\nMessageFlowBehavior.prototype.signal = function(context) {\n  this._simulator.exit(context);\n};\n\nMessageFlowBehavior.prototype.exit = function(context) {\n  const {\n    element,\n    scope: initiator\n  } = context;\n\n  const target = element.target;\n\n  // the event triggered is either the message event\n  // represented by the target message start or catch event _or_\n  // an event that uses { name: messageFlow.id } as an identifier\n  const event = isCatchEvent(target) ? target : {\n    type: 'message',\n    element,\n    name: element.id\n  };\n\n  const subscription = this._simulator.findSubscription({\n    event,\n    elements: [ target, target.parent ]\n  });\n\n  if (subscription) {\n    this._simulator.trigger({\n      event,\n      initiator,\n      scope: subscription.scope\n    });\n  }\n};", "import {\n  find,\n  some\n} from 'min-dash';\n\nimport {\n  is as __is,\n  getBusinessObject\n} from 'bpmn-js/lib/util/ModelUtil';\n\nexport function is(element, types) {\n  if (element.type === 'label') {\n    return false;\n  }\n\n  if (!Array.isArray(types)) {\n    types = [ types ];\n  }\n\n  return types.some(function(type) {\n    return __is(element, type);\n  });\n}\n\nexport function getEventDefinition(event, eventDefinitionType) {\n  return find(getBusinessObject(event).eventDefinitions, definition => {\n    return is(definition, eventDefinitionType);\n  });\n}\n\nexport function isTypedEvent(event, eventDefinitionType) {\n  return some(getBusinessObject(event).eventDefinitions, definition => {\n    return is(definition, eventDefinitionType);\n  });\n}\n\nexport {\n  getBusinessObject\n};", "import {\n  getEventDefinition,\n  isTypedEvent\n} from '../../util/ElementHelper';\n\nimport {\n  ScopeTraits\n} from '../ScopeTraits';\n\nimport {\n  getChildren,\n  isEventSubProcess,\n  isLinkCatch\n} from '../util/ModelUtil';\n\n\nexport default function EventBehaviors(\n    simulator,\n    elementRegistry,\n    scopeBehavior) {\n\n  this._simulator = simulator;\n  this._elementRegistry = elementRegistry;\n  this._scopeBehavior = scopeBehavior;\n}\n\nEventBehaviors.$inject = [\n  'simulator',\n  'elementRegistry',\n  'scopeBehavior'\n];\n\n\nEventBehaviors.prototype.get = function(element) {\n\n  const behaviors = {\n    'bpmn:LinkEventDefinition': (context) => {\n\n      const {\n        element,\n        scope\n      } = context;\n\n      const link = getLinkDefinition(element);\n\n      const parentScope = scope.parent;\n      const parentElement = parentScope.element;\n      const children = getChildren(parentElement, this._elementRegistry);\n\n      const linkTargets = children.filter(element =>\n        isLinkCatch(element) &&\n        getLinkDefinition(element).name === link.name\n      );\n\n      for (const linkTarget of linkTargets) {\n        this._simulator.signal({\n          element: linkTarget,\n          parentScope,\n          initiator: scope\n        });\n      }\n    },\n\n    'bpmn:SignalEventDefinition': (context) => {\n\n      // HINT: signals work only within the whole diagram,\n      //       triggers start events, boundary events and\n      //       intermediate catch events\n\n      const {\n        element,\n        scope\n      } = context;\n\n      const subscriptions = this._simulator.findSubscriptions({\n        event: element\n      });\n\n      const signaledScopes = new Set();\n\n      for (const subscription of subscriptions) {\n\n        const signaledScope = subscription.scope;\n\n        if (signaledScopes.has(signaledScope)) {\n          continue;\n        }\n\n        signaledScopes.add(signaledScope);\n\n        this._simulator.trigger({\n          event: element,\n          scope: signaledScope,\n          initiator: scope\n        });\n      }\n    },\n\n    'bpmn:EscalationEventDefinition': (context) => {\n\n      // HINT: escalations are propagated up the scope\n      //       chain and caught by the first matching boundary event\n      //       or event sub-process\n\n      const {\n        element,\n        scope\n      } = context;\n\n      const scopes = this._simulator.findScopes({\n        subscribedTo: {\n          event: element\n        },\n        trait: ScopeTraits.ACTIVE\n      });\n\n      let triggerScope = scope;\n\n      while ((triggerScope = triggerScope.parent)) {\n\n        if (scopes.includes(triggerScope)) {\n          this._simulator.trigger({\n            event: element,\n            scope: triggerScope,\n            initiator: scope\n          });\n\n          break;\n        }\n      }\n\n    },\n\n    'bpmn:ErrorEventDefinition': (context) => {\n\n      // HINT: errors are propagated up the scope\n      //       chain and caught by the first matching boundary event\n      //       or event sub-process\n\n      const {\n        element,\n        scope\n      } = context;\n\n      const scopes = this._simulator.findScopes({\n        subscribedTo: {\n          event: element\n        },\n        trait: ScopeTraits.ACTIVE\n      });\n\n      let triggerScope = scope;\n\n      // TODO(nikku): ensure error always interrupts, also if no error\n      //              catch is present\n      while ((triggerScope = triggerScope.parent)) {\n\n        if (scopes.includes(triggerScope)) {\n          this._simulator.trigger({\n            event: element,\n            scope: triggerScope,\n            initiator: scope\n          });\n\n          break;\n        }\n      }\n    },\n\n    'bpmn:TerminateEventDefinition': (context) => {\n      const {\n        scope\n      } = context;\n\n      this._scopeBehavior.terminate(scope.parent, scope);\n    },\n\n    'bpmn:CancelEventDefinition': (context) => {\n\n      // HINT: cancels the surrounding transaction scope (does not bubble)\n\n      const {\n        scope,\n        element\n      } = context;\n\n      this._simulator.trigger({\n        event: element,\n        initiator: scope,\n        scope: findSubscriptionScope(scope)\n      });\n    },\n\n    'bpmn:CompensateEventDefinition': (context) => {\n\n      const {\n        scope,\n        element\n      } = context;\n\n      return this._simulator.waitForScopes(\n        scope,\n        this._simulator.trigger({\n          event: element,\n          scope: findSubscriptionScope(scope)\n        })\n      );\n    }\n  };\n\n  const entry = Object.entries(behaviors).find(\n    entry => isTypedEvent(element, entry[0])\n  );\n\n  return entry && entry[1];\n};\n\n\n// helpers ///////////////\n\nfunction getLinkDefinition(element) {\n  return getEventDefinition(element, 'bpmn:LinkEventDefinition');\n}\n\nfunction findSubscriptionScope(scope) {\n\n  // the scope is the first non event sub-process\n  while (isEventSubProcess(scope.parent.element)) {\n    scope = scope.parent;\n  }\n\n  return scope.parent;\n}", "const PRE_EXIT_EVENT = {\n  type: 'pre-exit',\n  persistent: true,\n  interrupting: true,\n  boundary: false\n};\n\nconst EXIT_EVENT = {\n  type: 'exit',\n  interrupting: true,\n  boundary: false,\n  persistent: true\n};\n\n\nexport default function ScopeBehavior(simulator) {\n  this._simulator = simulator;\n}\n\nScopeBehavior.$inject = [\n  'simulator'\n];\n\n/**\n * Is the given scope finished?\n *\n * @param {Scope}  scope\n * @param {Scope|Function} [excludeScope=null]\n *\n * @return {boolean}\n */\nScopeBehavior.prototype.isFinished = function(scope, excludeScope = null) {\n\n  excludeScope = matchScope(excludeScope);\n\n  return scope.children.every(c => c.destroyed || c.completed || excludeScope(c));\n};\n\n/**\n * Destroy all scope children.\n *\n * @param {Scope} scope\n * @param {Scope} initiator\n * @param {Scope|Function} [excludeScope=null]\n */\nScopeBehavior.prototype.destroyChildren = function(scope, initiator, excludeScope = null) {\n\n  excludeScope = matchScope(excludeScope);\n\n  scope.children.filter(c => !c.destroyed && !excludeScope(c)).map(c => {\n    this._simulator.destroyScope(c, initiator);\n  });\n};\n\nScopeBehavior.prototype.terminate = function(scope, initiator) {\n\n  // kill all child scopes\n  this.destroyChildren(scope, initiator);\n\n  // mark as terminated\n  scope.terminate(initiator);\n\n  // exit immediately\n  this.tryExit(scope, initiator);\n};\n\nScopeBehavior.prototype.interrupt = function(scope, initiator) {\n\n  // kill children but initiator\n  this.destroyChildren(scope, initiator, initiator);\n\n  // mark as failed\n  scope.fail(initiator);\n};\n\nScopeBehavior.prototype.tryExit = function(scope, initiator) {\n  if (!scope) {\n    throw new Error('missing <scope>');\n  }\n\n  if (!initiator) {\n    initiator = scope;\n  }\n\n  if (!this.isFinished(scope, initiator)) {\n    return EXIT_EVENT;\n  }\n\n  const preExitSubscriptions = this._simulator.findSubscriptions({\n    event: PRE_EXIT_EVENT,\n    scope\n  });\n\n  for (const subscription of preExitSubscriptions) {\n\n    const {\n      event,\n      scope\n    } = subscription;\n\n    const scopes = this._simulator.trigger({\n      event,\n      scope,\n      initiator\n    });\n\n    if (scopes.length) {\n      return EXIT_EVENT;\n    }\n  }\n\n  this._simulator.trigger({\n    event: EXIT_EVENT,\n    scope,\n    initiator\n  });\n\n  this.exit({\n    scope,\n    initiator\n  });\n};\n\nScopeBehavior.prototype.exit = function(context) {\n\n  const {\n    scope,\n    initiator\n  } = context;\n\n  if (!initiator) {\n    throw new Error('missing <initiator>');\n  }\n\n  this._simulator.exit({\n    element: scope.element,\n    scope: scope,\n    initiator\n  });\n};\n\nScopeBehavior.prototype.preExit = function(scope, triggerFn) {\n  const subscription = this._simulator.subscribe(scope, PRE_EXIT_EVENT, (initiator) => {\n\n    subscription.remove();\n\n    return triggerFn(initiator);\n  });\n\n  return subscription;\n};\n\n\n// helpers ////////////////\n\n/**\n * Create a scope matcher.\n *\n * @param {Scope|Function} fnOrScope\n *\n * @return { (Scope) => boolean }\n */\nfunction matchScope(fnOrScope) {\n\n  if (typeof fnOrScope === 'function') {\n    return fnOrScope;\n  }\n\n  return (scope) => scope === fnOrScope;\n}", "import {\n  isImplicitStartEvent,\n  isNoneStartEvent,\n  isStartEvent\n} from '../util/ModelUtil';\n\n\nexport default function ProcessBehavior(\n    simulator,\n    scopeBehavior) {\n\n  this._simulator = simulator;\n  this._scopeBehavior = scopeBehavior;\n\n  simulator.registerBehavior('bpmn:Process', this);\n  simulator.registerBehavior('bpmn:Participant', this);\n}\n\nProcessBehavior.prototype.signal = function(context) {\n\n  const {\n    element,\n    startEvent,\n    startNodes = this._findStarts(element, startEvent),\n    scope\n  } = context;\n\n  if (!startNodes.length) {\n    throw new Error('missing <startNodes> or <startEvent>');\n  }\n\n  for (const startNode of startNodes) {\n\n    if (isStartEvent(startNode)) {\n      this._simulator.signal({\n        element: startNode,\n        parentScope: scope\n      });\n    } else {\n      this._simulator.enter({\n        element: startNode,\n        scope\n      });\n    }\n  }\n\n};\n\nProcessBehavior.prototype.exit = function(context) {\n\n  const {\n    scope,\n    initiator\n  } = context;\n\n  // ensure that all sub-scopes are destroyed\n\n  this._scopeBehavior.destroyChildren(scope, initiator);\n};\n\nProcessBehavior.prototype._findStarts = function(element, startEvent) {\n\n  const isStartEvent = startEvent\n    ? (node) => startEvent === node\n    : (node) => isNoneStartEvent(node);\n\n  return element.children.filter(\n    node => (\n      isStartEvent(node) || isImplicitStartEvent(node)\n    )\n  );\n};\n\nProcessBehavior.$inject = [\n  'simulator',\n  'scopeBehavior'\n];", "import StartEventBehavior from './StartEventBehavior';\nimport EndEventBehavior from './EndEventBehavior';\nimport BoundaryEventBehavior from './BoundaryEventBehavior';\nimport IntermediateCatchEventBehavior from './IntermediateCatchEventBehavior';\nimport IntermediateThrowEventBehavior from './IntermediateThrowEventBehavior';\n\nimport ExclusiveGatewayBehavior from './ExclusiveGatewayBehavior';\nimport ParallelGatewayBehavior from './ParallelGatewayBehavior';\nimport EventBasedGatewayBehavior from './EventBasedGatewayBehavior';\nimport InclusiveGatewayBehavior from './InclusiveGatewayBehavior';\n\nimport ActivityBehavior from './ActivityBehavior';\nimport SubProcessBehavior from './SubProcessBehavior';\nimport TransactionBehavior from './TransactionBehavior';\n\nimport SequenceFlowBehavior from './SequenceFlowBehavior';\nimport MessageFlowBehavior from './MessageFlowBehavior';\n\nimport EventBehaviors from './EventBehaviors';\nimport ScopeBehavior from './ScopeBehavior';\n\nimport ProcessBehavior from './ProcessBehavior';\n\n\nexport default {\n  __init__: [\n    'startEventBehavior',\n    'endEventBehavior',\n    'boundaryEventBehavior',\n    'intermediateCatchEventBehavior',\n    'intermediateThrowEventBehavior',\n    'exclusiveGatewayBehavior',\n    'parallelGatewayBehavior',\n    'eventBasedGatewayBehavior',\n    'inclusiveGatewayBehavior',\n    'subProcessBehavior',\n    'sequenceFlowBehavior',\n    'messageFlowBehavior',\n    'processBehavior'\n  ],\n  startEventBehavior: [ 'type', StartEventBehavior ],\n  endEventBehavior: [ 'type', EndEventBehavior ],\n  boundaryEventBehavior: [ 'type', BoundaryEventBehavior ],\n  intermediateCatchEventBehavior: [ 'type', IntermediateCatchEventBehavior ],\n  intermediateThrowEventBehavior: [ 'type', IntermediateThrowEventBehavior ],\n  exclusiveGatewayBehavior: [ 'type', ExclusiveGatewayBehavior ],\n  parallelGatewayBehavior: [ 'type', ParallelGatewayBehavior ],\n  eventBasedGatewayBehavior: [ 'type', EventBasedGatewayBehavior ],\n  inclusiveGatewayBehavior: [ 'type', InclusiveGatewayBehavior ],\n  activityBehavior: [ 'type', ActivityBehavior ],\n  subProcessBehavior: [ 'type', SubProcessBehavior ],\n  sequenceFlowBehavior: [ 'type', SequenceFlowBehavior ],\n  messageFlowBehavior: [ 'type', MessageFlowBehavior ],\n  eventBehaviors: [ 'type', EventBehaviors ],\n  scopeBehavior: [ 'type', ScopeBehavior ],\n  processBehavior: [ 'type', ProcessBehavior ],\n  transactionBehavior: [ 'type', TransactionBehavior ]\n};", "import Simulator from './Simulator';\nimport SimulationBehaviorModule from './behaviors';\n\nconst HIGH_PRIORITY = 5000;\n\nexport default {\n  __depends__: [\n    SimulationBehaviorModule\n  ],\n  __init__: [\n    [ 'eventBus', 'simulator', function(eventBus, simulator) {\n      eventBus.on([\n        'tokenSimulation.toggleMode',\n        'tokenSimulation.resetSimulation'\n      ], HIGH_PRIORITY, event => {\n        simulator.reset();\n      });\n    } ]\n  ],\n  simulator: [ 'type', Simulator ]\n};", "import MessageFlowBehavior from '../../simulator/behaviors/MessageFlowBehavior';\n\nimport inherits from 'inherits-browser';\n\n\nexport default function AnimatedMessageFlowBehavior(injector, animation) {\n  injector.invoke(MessageFlowBehavior, this);\n\n  this._animation = animation;\n}\n\ninherits(AnimatedMessageFlowBehavior, MessageFlowBehavior);\n\nAnimatedMessageFlowBehavior.$inject = [\n  'injector',\n  'animation'\n];\n\nAnimatedMessageFlowBehavior.prototype.signal = function(context) {\n\n  const {\n    element,\n    scope\n  } = context;\n\n  this._animation.animate(element, scope, () => {\n    MessageFlowBehavior.prototype.signal.call(this, context);\n  });\n};\n", "import SequenceFlowBehavior from '../../simulator/behaviors/SequenceFlowBehavior';\n\nimport inherits from 'inherits-browser';\n\n\nexport default function AnimatedSequenceFlowBehavior(injector, animation) {\n  injector.invoke(SequenceFlowBehavior, this);\n\n  this._animation = animation;\n}\n\ninherits(AnimatedSequenceFlowBehavior, SequenceFlowBehavior);\n\nAnimatedSequenceFlowBehavior.$inject = [\n  'injector',\n  'animation'\n];\n\nAnimatedSequenceFlowBehavior.prototype.enter = function(context) {\n\n  const {\n    element,\n    scope\n  } = context;\n\n  this._animation.animate(element, scope, () => {\n    SequenceFlowBehavior.prototype.enter.call(this, context);\n  });\n};", "import AnimatedMessageFlowBehavior from './AnimatedMessageFlowBehavior';\nimport AnimatedSequence<PERSON>lowBehavior from './AnimatedSequenceFlowBehavior';\n\nexport default {\n  sequenceFlowBehavior: [ 'type', AnimatedSequenceFlowBehavior ],\n  messageFlowBehavior: [ 'type', AnimatedMessageFlowBehavior ]\n};", "const TOGGLE_MODE_EVENT = 'tokenSimulation.toggleMode';\r\nconst PLAY_SIMULATION_EVENT = 'tokenSimulation.playSimulation';\r\nconst PAUSE_SIMULATION_EVENT = 'tokenSimulation.pauseSimulation';\r\nconst RESET_SIMULATION_EVENT = 'tokenSimulation.resetSimulation';\r\nconst ANIMATION_CREATED_EVENT = 'tokenSimulation.animationCreated';\r\nconst ANIMATION_SPEED_CHANGED_EVENT = 'tokenSimulation.animationSpeedChanged';\r\nconst ELEMENT_CHANGED_EVENT = 'tokenSimulation.simulator.elementChanged';\r\nconst SCOPE_DESTROYED_EVENT = 'tokenSimulation.simulator.destroyScope';\r\nconst SCOPE_CHANGED_EVENT = 'tokenSimulation.simulator.scopeChanged';\r\nconst SCOPE_CREATE_EVENT = 'tokenSimulation.simulator.createScope';\r\nconst SCOPE_FILTER_CHANGED_EVENT = 'tokenSimulation.scopeFilterChanged';\r\nconst TRACE_EVENT = 'tokenSimulation.simulator.trace';\r\n\r\nexport {\r\n  TOGGLE_MODE_EVENT,\r\n  PLAY_SIMULATION_EVENT,\r\n  PAUSE_SIMULATION_EVENT,\r\n  RESET_SIMULATION_EVENT,\r\n  ANIMATION_CREATED_EVENT,\r\n  ANIMATION_SPEED_CHANGED_EVENT,\r\n  ELEMENT_CHANGED_EVENT,\r\n  SCOPE_DESTROYED_EVENT,\r\n  SCOPE_CHANGED_EVENT,\r\n  SCOPE_CREATE_EVENT,\r\n  SCOPE_FILTER_CHANGED_EVENT,\r\n  TRACE_EVENT\r\n};", "import {\n  SCOPE_FILTER_CHANGED_EVENT,\n  TOGGLE_MODE_EVENT,\n  RESET_SIMULATION_EVENT,\n  SCOPE_CREATE_EVENT,\n  SCOPE_DESTROYED_EVENT\n} from '../../util/EventHelper';\n\nconst DEFAULT_SCOPE_FILTER = (s) => true;\n\n\nexport default function ScopeFilter(eventBus, simulator) {\n  this._eventBus = eventBus;\n  this._simulator = simulator;\n\n  this._filter = DEFAULT_SCOPE_FILTER;\n\n  eventBus.on([\n    TOGGLE_MODE_EVENT,\n    RESET_SIMULATION_EVENT\n  ], () => {\n    this._filter = DEFAULT_SCOPE_FILTER;\n  });\n\n  eventBus.on(SCOPE_DESTROYED_EVENT, event => {\n\n    const {\n      scope\n    } = event;\n\n    // if we're currently filtering, ensure newly\n    // created instance is shown\n\n    if (this._scope === scope && scope.parent) {\n      this.toggle(scope.parent);\n    }\n  });\n\n\n  eventBus.on(SCOPE_CREATE_EVENT, event => {\n\n    const {\n      scope\n    } = event;\n\n    // if we're currently filtering, ensure newly\n    // created instance is shown\n\n    if (!scope.parent && this._scope && !isAncestor(this._scope, scope)) {\n      this.toggle(null);\n    }\n  });\n}\n\nScopeFilter.prototype.toggle = function(scope) {\n\n  const setFilter = this._scope !== scope;\n\n  this._scope = setFilter ? scope : null;\n\n  this._filter =\n    this._scope\n      ? s => isAncestor(this._scope, s)\n      : s => true;\n\n  this._eventBus.fire(SCOPE_FILTER_CHANGED_EVENT, {\n    filter: this._filter,\n    scope: this._scope\n  });\n};\n\nScopeFilter.prototype.isShown = function(scope) {\n\n  if (typeof scope === 'string') {\n    scope = this._simulator.findScope(s => s.id === scope);\n  }\n\n  return scope && this._filter(scope);\n};\n\nScopeFilter.prototype.findScope = function(options) {\n  return this._simulator.findScopes(options).filter(s => this.isShown(s))[0];\n};\n\nScopeFilter.$inject = [\n  'eventBus',\n  'simulator'\n];\n\nfunction isAncestor(parent, scope) {\n  do {\n    if (parent === scope) {\n      return true;\n    }\n  } while ((scope = scope.parent));\n\n  return false;\n}", "import ScopeFilter from './ScopeFilter';\n\nexport default {\n  scopeFilter: [ 'type', ScopeFilter ]\n};", "import {\n  query as domQuery\n} from 'min-dom';\n\nimport {\n  appendTo as svgAppendTo,\n  create as svgCreate,\n  attr as svgAttr,\n  remove as svgRemove\n} from 'tiny-svg';\n\nimport {\n  RESET_SIMULATION_EVENT,\n  PLAY_SIMULATION_EVENT,\n  PAUSE_SIMULATION_EVENT,\n  ANIMATION_CREATED_EVENT,\n  ANIMATION_SPEED_CHANGED_EVENT,\n  SCOPE_DESTROYED_EVENT,\n  SCOPE_FILTER_CHANGED_EVENT\n} from '../util/EventHelper';\n\nconst STYLE = getComputedStyle(document.documentElement);\n\nconst DEFAULT_PRIMARY_COLOR = STYLE.getPropertyValue('--token-simulation-green-base-44');\nconst DEFAULT_AUXILIARY_COLOR = STYLE.getPropertyValue('--token-simulation-white');\n\nfunction noop() {}\n\nfunction getSegmentEasing(index, waypoints) {\n\n  // only a single segment\n  if (waypoints.length === 2) {\n    return EASE_IN_OUT;\n  }\n\n  // first segment\n  if (index === 1) {\n    return EASE_IN;\n  }\n\n  // last segment\n  if (index === waypoints.length - 1) {\n    return EASE_OUT;\n  }\n\n  return EASE_LINEAR;\n}\n\nconst EASE_LINEAR = function(pos) {\n  return pos;\n};\nconst EASE_IN = function(pos) {\n  return -Math.cos(pos * Math.PI / 2) + 1;\n};\nconst EASE_OUT = function(pos) {\n  return Math.sin(pos * Math.PI / 2);\n};\nconst EASE_IN_OUT = function(pos) {\n  return -Math.cos(pos * Math.PI) / 2 + 0.5;\n};\n\nconst TOKEN_SIZE = 20;\n\n\n/**\n * @param { { randomize?: boolean } | null } [config]\n * @param { import('diagram-js/lib/core/Canvas').default } canvas\n * @param { import('diagram-js/lib/core/EventBus').default } eventBus\n * @param { import('../features/scope-filter/ScopeFilter').default } scopeFilter\n */\nexport default function Animation(config, canvas, eventBus, scopeFilter) {\n  this._eventBus = eventBus;\n  this._scopeFilter = scopeFilter;\n  this._canvas = canvas;\n\n  this._randomize = config && config.randomize !== false;\n\n  this._animations = new Set();\n  this._speed = 1;\n\n  eventBus.on([\n    'diagram.destroy',\n    RESET_SIMULATION_EVENT\n  ], () => {\n    this.clearAnimations();\n  });\n\n  eventBus.on(PAUSE_SIMULATION_EVENT, () => {\n    this.pause();\n  });\n\n  eventBus.on(PLAY_SIMULATION_EVENT, () => {\n    this.play();\n  });\n\n  eventBus.on(SCOPE_FILTER_CHANGED_EVENT, event => {\n\n    this.each(animation => {\n      if (this._scopeFilter.isShown(animation.scope)) {\n        animation.show();\n      } else {\n        animation.hide();\n      }\n    });\n  });\n\n  eventBus.on(SCOPE_DESTROYED_EVENT, event => {\n    const {\n      scope\n    } = event;\n\n    this.clearAnimations(scope);\n  });\n}\n\nAnimation.prototype.animate = function(connection, scope, done) {\n  this.createAnimation(connection, scope, done);\n};\n\nAnimation.prototype.pause = function() {\n  this.each(animation => animation.pause());\n};\n\nAnimation.prototype.play = function() {\n  this.each(animation => animation.play());\n};\n\nAnimation.prototype.each = function(fn) {\n  this._animations.forEach(fn);\n};\n\nAnimation.prototype.createAnimation = function(connection, scope, done = noop) {\n  const group = this._getGroup(scope);\n\n  if (!group) {\n    return;\n  }\n\n  const tokenGfx = this._createTokenGfx(group, scope);\n\n  const animation = new TokenAnimation(tokenGfx, connection.waypoints, this._randomize, () => {\n    this._clearAnimation(animation);\n\n    done();\n  });\n\n  animation.setSpeed(this.getAnimationSpeed());\n\n  if (!this._scopeFilter.isShown(scope)) {\n    animation.hide();\n  }\n\n  animation.scope = scope;\n  animation.element = connection;\n\n  this._animations.add(animation);\n\n  this._eventBus.fire(ANIMATION_CREATED_EVENT, {\n    animation\n  });\n\n  animation.play();\n\n  return animation;\n};\n\nAnimation.prototype.setAnimationSpeed = function(speed) {\n  this._speed = speed;\n\n  this.each(animation => animation.setSpeed(speed));\n\n  this._eventBus.fire(ANIMATION_SPEED_CHANGED_EVENT, {\n    speed\n  });\n};\n\nAnimation.prototype.getAnimationSpeed = function() {\n  return this._speed;\n};\n\nAnimation.prototype.clearAnimations = function(scope) {\n  this.each(animation => {\n    if (!scope || animation.scope === scope) {\n      this._clearAnimation(animation);\n    }\n  });\n};\n\nAnimation.prototype._clearAnimation = function(animation) {\n  animation.remove();\n\n  this._animations.delete(animation);\n};\n\nAnimation.prototype._createTokenGfx = function(group, scope) {\n  const parent = svgCreate(this._getTokenSVG(scope).trim());\n\n  return svgAppendTo(parent, group);\n};\n\nAnimation.prototype._getTokenSVG = function(scope) {\n\n  const colors = scope.colors || {\n    primary: DEFAULT_PRIMARY_COLOR,\n    auxiliary: DEFAULT_AUXILIARY_COLOR\n  };\n\n  return `\n    <g class=\"bts-token\">\n      <circle\n        class=\"bts-circle\"\n        r=\"${TOKEN_SIZE / 2}\"\n        cx=\"${TOKEN_SIZE / 2}\"\n        cy=\"${TOKEN_SIZE / 2}\"\n        fill=\"${ colors.primary }\"\n      />\n      <text\n        class=\"bts-text\"\n        transform=\"translate(10, 14)\"\n        text-anchor=\"middle\"\n        fill=\"${ colors.auxiliary }\"\n      >1</text>\n    </g>\n  `;\n};\n\nAnimation.prototype._getGroup = function(scope) {\n\n  var canvas = this._canvas;\n\n  var layer, root;\n\n  // bpmn-js@9 compatibility:\n  // show animation tokens on plane layers\n  if ('findRoot' in canvas) {\n    root = canvas.findRoot(scope.element);\n    layer = canvas._findPlaneForRoot(root).layer;\n  } else {\n    layer = domQuery('.viewport', canvas._svg);\n  }\n\n  var group = domQuery('.bts-animation-tokens', layer);\n\n  if (!group) {\n    group = svgCreate('<g class=\"bts-animation-tokens\" />');\n\n    svgAppendTo(\n      group,\n      layer\n    );\n  }\n\n  return group;\n};\n\nAnimation.$inject = [\n  'config.animation',\n  'canvas',\n  'eventBus',\n  'scopeFilter'\n];\n\n\nfunction TokenAnimation(gfx, waypoints, randomize, done) {\n  this.gfx = gfx;\n  this.waypoints = waypoints;\n  this.done = done;\n  this.randomize = randomize;\n\n  this._paused = true;\n  this._t = 0;\n  this._parts = [];\n\n  this.create();\n}\n\nTokenAnimation.prototype.pause = function() {\n  this._paused = true;\n};\n\nTokenAnimation.prototype.play = function() {\n\n  if (this._paused) {\n    this._paused = false;\n\n    this.tick(0);\n  }\n\n  this.schedule();\n};\n\nTokenAnimation.prototype.schedule = function() {\n\n  if (this._paused) {\n    return;\n  }\n\n  if (this._scheduled) {\n    return;\n  }\n\n  const last = Date.now();\n\n  this._scheduled = true;\n\n  requestAnimationFrame(() => {\n    this._scheduled = false;\n\n    if (this._paused) {\n      return;\n    }\n\n    this.tick((Date.now() - last) * this._speed);\n    this.schedule();\n  });\n};\n\n\nTokenAnimation.prototype.tick = function(tElapsed) {\n\n  const t = this._t = this._t + tElapsed;\n\n  const part = this._parts.find(\n    p => p.startTime <= t && p.endTime > t\n  );\n\n  // completed\n  if (!part) {\n    return this.completed();\n  }\n\n  const segmentTime = t - part.startTime;\n  const segmentLength = part.length * part.easing(segmentTime / part.duration);\n\n  const currentLength = part.startLength + segmentLength;\n\n  const point = this._path.getPointAtLength(currentLength);\n\n  this.move(point.x, point.y);\n};\n\nTokenAnimation.prototype.move = function(x, y) {\n  svgAttr(this.gfx, 'transform', `translate(${x}, ${y})`);\n};\n\nTokenAnimation.prototype.create = function() {\n  const waypoints = this.waypoints;\n\n  const parts = waypoints.reduce((parts, point, index) => {\n\n    const lastPoint = waypoints[index - 1];\n\n    if (lastPoint) {\n      const lastPart = parts[parts.length - 1];\n\n      const startLength = lastPart && lastPart.endLength || 0;\n      const length = distance(lastPoint, point);\n\n      parts.push({\n        startLength,\n        endLength: startLength + length,\n        length,\n        easing: getSegmentEasing(index, waypoints)\n      });\n    }\n\n    return parts;\n  }, []);\n\n  const totalLength = parts.reduce(function(length, part) {\n    return length + part.length;\n  }, 0);\n\n  const d = waypoints.reduce((d, waypoint, index) => {\n\n    const x = waypoint.x - TOKEN_SIZE / 2,\n          y = waypoint.y - TOKEN_SIZE / 2;\n\n    d.push([ index > 0 ? 'L' : 'M', x, y ]);\n\n    return d;\n  }, []).flat().join(' ');\n\n  const totalDuration = getAnimationDuration(totalLength, this._randomize);\n\n  this._parts = parts.reduce((parts, part, index) => {\n    const duration = totalDuration / totalLength * part.length;\n    const startTime = index > 0 ? parts[index - 1].endTime : 0;\n    const endTime = startTime + duration;\n\n    return [\n      ...parts,\n      {\n        ...part,\n        startTime,\n        endTime,\n        duration\n      }\n    ];\n  }, []);\n\n  this._path = svgCreate(`<path d=\"${d}\" />`);\n  this._t = 0;\n};\n\nTokenAnimation.prototype.show = function() {\n  svgAttr(this.gfx, 'display', '');\n};\n\nTokenAnimation.prototype.hide = function() {\n  svgAttr(this.gfx, 'display', 'none');\n};\n\nTokenAnimation.prototype.completed = function() {\n  this.done();\n};\n\nTokenAnimation.prototype.remove = function() {\n  this.pause();\n\n  svgRemove(this.gfx);\n};\n\nTokenAnimation.prototype.setSpeed = function(speed) {\n  this._speed = speed;\n};\n\nfunction getAnimationDuration(length, randomize = false) {\n  return Math.log(length) * (randomize ? randomBetween(250, 300) : 250);\n}\n\nfunction randomBetween(min, max) {\n  return min + Math.floor(Math.random() * (max - min));\n}\n\nfunction distance(a, b) {\n  return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2));\n}", "import AnimatedBehaviorsModule from './behaviors';\nimport ScopeFilterModule from '../features/scope-filter';\nimport SimulatorModule from '../simulator';\n\nimport Animation from './Animation';\n\nexport default {\n  __depends__: [\n    SimulatorModule,\n    AnimatedBehaviorsModule,\n    ScopeFilterModule\n  ],\n  animation: [ 'type', Animation ]\n};", "import randomColor from 'randomcolor';\n\nimport {\n  SCOPE_CREATE_EVENT\n} from '../../util/EventHelper';\n\nconst HIGH_PRIORITY = 1500;\n\n\nexport default function ColoredScopes(eventBus) {\n\n  const colors = randomColor({\n    count: 60\n  }).filter(c => getContrastYIQ(c.substring(1)) < 200);\n\n  function getContrastYIQ(hexcolor) {\n    var r = parseInt(hexcolor.substr(0,2),16);\n    var g = parseInt(hexcolor.substr(2,2),16);\n    var b = parseInt(hexcolor.substr(4,2),16);\n    var yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;\n    return yiq;\n  }\n\n  let colorsIdx = 0;\n\n  function getColors(scope) {\n    const {\n      element\n    } = scope;\n\n    if (element && element.type === 'bpmn:MessageFlow') {\n      return {\n        primary: '#999',\n        auxiliary: '#FFF'\n      };\n    }\n\n    if (scope.parent) {\n      return scope.parent.colors;\n    }\n\n    const primary = colors[ (colorsIdx++) % colors.length ];\n\n    return {\n      primary,\n      auxiliary: getContrastYIQ(primary) >= 128 ? '#111' : '#fff'\n    };\n  }\n\n  eventBus.on(SCOPE_CREATE_EVENT, HIGH_PRIORITY, event => {\n\n    const {\n      scope\n    } = event;\n\n    scope.colors = getColors(scope);\n  });\n}\n\nColoredScopes.$inject = [\n  'eventBus'\n];", "import ColoredScopes from './ColoredScopes';\n\nexport default {\n  __init__: [\n    'coloredScopes'\n  ],\n  coloredScopes: [ 'type', ColoredScopes ]\n};", "var LogSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 448 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M12.83 352h262.34A12.82 12.82 0 0 0 288 339.17v-38.34A12.82 12.82 0 0 0 275.17 288H12.83A12.82 12.82 0 0 0 0 300.83v38.34A12.82 12.82 0 0 0 12.83 352zm0-256h262.34A12.82 12.82 0 0 0 288 83.17V44.83A12.82 12.82 0 0 0 275.17 32H12.83A12.82 12.82 0 0 0 0 44.83v38.34A12.82 12.82 0 0 0 12.83 96zM432 160H16a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16zm0 256H16a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16z\\\"/></svg>\";\n\nvar AngleRightSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 256 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34z\\\"/></svg>\";\n\nvar CheckCircleSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 512 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z\\\"/></svg>\";\n\nvar ForkSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 384 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M384 144c0-44.2-35.8-80-80-80s-80 35.8-80 80c0 36.4 24.3 67.1 57.5 76.8-.6 16.1-4.2 28.5-11 36.9-15.4 19.2-49.3 22.4-85.2 25.7-28.2 2.6-57.4 5.4-81.3 16.9v-144c32.5-10.2 56-40.5 56-76.3 0-44.2-35.8-80-80-80S0 35.8 0 80c0 35.8 23.5 66.1 56 76.3v199.3C23.5 365.9 0 396.2 0 432c0 44.2 35.8 80 80 80s80-35.8 80-80c0-34-21.2-63.1-51.2-74.6 3.1-5.2 7.8-9.8 14.9-13.4 16.2-8.2 40.4-10.4 66.1-12.8 42.2-3.9 90-8.4 118.2-43.4 14-17.4 21.1-39.8 21.6-67.9 31.6-10.8 54.4-40.7 54.4-75.9zM80 64c8.8 0 16 7.2 16 16s-7.2 16-16 16-16-7.2-16-16 7.2-16 16-16zm0 384c-8.8 0-16-7.2-16-16s7.2-16 16-16 16 7.2 16 16-7.2 16-16 16zm224-320c8.8 0 16 7.2 16 16s-7.2 16-16 16-16-7.2-16-16 7.2-16 16-16z\\\"/></svg>\";\n\nvar ExclamationTriangleSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 576 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M569.517 440.013C587.975 472.007 564.806 512 527.94 512H48.054c-36.937 0-59.999-40.055-41.577-71.987L246.423 23.985c18.467-32.009 64.72-31.951 83.154 0l239.94 416.028zM288 354c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z\\\"/></svg>\";\n\nvar InfoSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 192 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M20 424.229h20V279.771H20c-11.046 0-20-8.954-20-20V212c0-11.046 8.954-20 20-20h112c11.046 0 20 8.954 20 20v212.229h20c11.046 0 20 8.954 20 20V492c0 11.046-8.954 20-20 20H20c-11.046 0-20-8.954-20-20v-47.771c0-11.046 8.954-20 20-20zM96 0C56.235 0 24 32.235 24 72s32.235 72 72 72 72-32.235 72-72S135.764 0 96 0z\\\"/></svg>\";\n\nvar PauseSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 448 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M144 479H48c-26.5 0-48-21.5-48-48V79c0-26.5 21.5-48 48-48h96c26.5 0 48 21.5 48 48v352c0 26.5-21.5 48-48 48zm304-48V79c0-26.5-21.5-48-48-48h-96c-26.5 0-48 21.5-48 48v352c0 26.5 21.5 48 48 48h96c26.5 0 48-21.5 48-48z\\\"/></svg>\";\n\nvar RemovePauseSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 580.5 448\\\">\\n  <path fill=\\\"currentColor\\\" d=\\\"M112 0C85 0 64 22 64 48v196l192-89V48c0-26-22-48-48-48zm256 0c-27 0-48 22-48 48v77l190-89c-5-21-24-36-46-36Zm144 105-192 89v70l192-89zM256 224 64 314v70l192-90zm256 21-192 89v66c0 27 21 48 48 48h96c26 0 48-21 48-48zM256 364 89 442c7 4 14 6 23 6h96c26 0 48-21 48-48z\\\"/>\\n  <rect fill=\\\"currentColor\\\" width=\\\"63.3\\\" height=\\\"618.2\\\" x=\\\"311.5\\\" y=\\\"-469.4\\\" transform=\\\"rotate(65)\\\" rx=\\\"10\\\"/>\\n</svg>\\n\";\n\nvar PlaySVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 448 512\\\"><!-- Adapted from Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z\\\"/></svg>\";\n\nvar ResetSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 512 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M440.65 12.57l4 82.77A247.16 247.16 0 0 0 255.83 8C134.73 8 33.91 94.92 12.29 209.82A12 12 0 0 0 24.09 224h49.05a12 12 0 0 0 11.67-9.26 175.91 175.91 0 0 1 317-56.94l-101.46-4.86a12 12 0 0 0-12.57 12v47.41a12 12 0 0 0 12 12H500a12 12 0 0 0 12-12V12a12 12 0 0 0-12-12h-47.37a12 12 0 0 0-11.98 12.57zM255.83 432a175.61 175.61 0 0 1-146-77.8l101.8 4.87a12 12 0 0 0 12.57-12v-47.4a12 12 0 0 0-12-12H12a12 12 0 0 0-12 12V500a12 12 0 0 0 12 12h47.35a12 12 0 0 0 12-12.6l-4.15-82.57A247.17 247.17 0 0 0 255.83 504c121.11 0 221.93-86.92 243.55-201.82a12 12 0 0 0-11.8-14.18h-49.05a12 12 0 0 0-11.67 9.26A175.86 175.86 0 0 1 255.83 432z\\\"/></svg>\";\n\nvar TachometerSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 576 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M288 32C128.94 32 0 160.94 0 320c0 52.8 14.25 102.26 39.06 144.8 5.61 9.62 16.3 15.2 27.44 15.2h443c11.14 0 21.83-5.58 27.44-15.2C561.75 422.26 576 372.8 576 320c0-159.06-128.94-288-288-288zm0 64c14.71 0 26.58 10.13 30.32 23.65-1.11 2.26-2.64 4.23-3.45 6.67l-9.22 27.67c-5.13 3.49-10.97 6.01-17.64 6.01-17.67 0-32-14.33-32-32S270.33 96 288 96zM96 384c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32 32-14.33 32-32 32zm48-160c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32 32-14.33 32-32 32zm246.77-72.41l-61.33 184C343.13 347.33 352 364.54 352 384c0 11.72-3.38 22.55-8.88 32H232.88c-5.5-9.45-8.88-20.28-8.88-32 0-33.94 26.5-61.43 59.9-63.59l61.34-184.01c4.17-12.56 17.73-19.45 30.36-15.17 12.57 4.19 19.35 17.79 15.17 30.36zm14.66 57.2l15.52-46.55c3.47-1.29 7.13-2.23 11.05-2.23 17.67 0 32 14.33 32 32s-14.33 32-32 32c-11.38-.01-20.89-6.28-26.57-15.22zM480 384c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32 32-14.33 32-32 32z\\\"/></svg>\";\n\nvar TimesCircleSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 512 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm121.6 313.1c4.7 4.7 4.7 12.3 0 17L338 377.6c-4.7 4.7-12.3 4.7-17 0L256 312l-65.1 65.6c-4.7 4.7-12.3 4.7-17 0L134.4 338c-4.7-4.7-4.7-12.3 0-17l65.6-65-65.6-65.1c-4.7-4.7-4.7-12.3 0-17l39.6-39.6c4.7-4.7 12.3-4.7 17 0l65 65.7 65.1-65.6c4.7-4.7 12.3-4.7 17 0l39.6 39.6c4.7 4.7 4.7 12.3 0 17L312 256l65.6 65.1z\\\"/></svg>\";\n\nvar TimesSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 352 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z\\\"/></svg>\";\n\nvar ToggleOffSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 576 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M384 64H192C85.961 64 0 149.961 0 256s85.961 192 192 192h192c106.039 0 192-85.961 192-192S490.039 64 384 64zM64 256c0-70.741 57.249-128 128-128 70.741 0 128 57.249 128 128 0 70.741-57.249 128-128 128-70.741 0-128-57.249-128-128zm320 128h-48.905c65.217-72.858 65.236-183.12 0-256H384c70.741 0 128 57.249 128 128 0 70.74-57.249 128-128 128z\\\"/></svg>\";\n\nvar ToggleOnSVG = \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 576 512\\\"><!-- Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) --><path fill=\\\"currentColor\\\" d=\\\"M384 64H192C86 64 0 150 0 256s86 192 192 192h192c106 0 192-86 192-192S490 64 384 64zm0 320c-70.8 0-128-57.3-128-128 0-70.8 57.3-128 128-128 70.8 0 128 57.3 128 128 0 70.8-57.3 128-128 128z\\\"/></svg>\";\n\nfunction createIcon(svg) {\n  return function Icon(className = '') {\n    return `<span class=\"bts-icon ${ className }\">${svg}</span>`;\n  };\n}\n\nconst LogIcon = createIcon(LogSVG);\nconst AngleRightIcon = createIcon(AngleRightSVG);\nconst CheckCircleIcon = createIcon(CheckCircleSVG);\nconst RemovePauseIcon = createIcon(RemovePauseSVG);\nconst ForkIcon = createIcon(ForkSVG);\nconst ExclamationTriangleIcon = createIcon(ExclamationTriangleSVG);\nconst InfoIcon = createIcon(InfoSVG);\nconst PauseIcon = createIcon(PauseSVG);\nconst PlayIcon = createIcon(PlaySVG);\nconst ResetIcon = createIcon(ResetSVG);\nconst TachometerIcon = createIcon(TachometerSVG);\nconst TimesCircleIcon = createIcon(TimesCircleSVG);\nconst TimesIcon = createIcon(TimesSVG);\nconst ToggleOffIcon = createIcon(ToggleOffSVG);\nconst ToggleOnIcon = createIcon(ToggleOnSVG);\n\nexport { AngleRightIcon, CheckCircleIcon, ExclamationTriangleIcon, ForkIcon, InfoIcon, LogIcon, PauseIcon, PlayIcon, RemovePauseIcon, ResetIcon, TachometerIcon, TimesCircleIcon, TimesIcon, ToggleOffIcon, ToggleOnIcon };\n", "import {\r\n  is\r\n} from '../../../util/ElementHelper';\r\n\r\nimport {\r\n  ForkIcon\r\n} from '../../../icons';\r\n\r\n\r\nexport default function ExclusiveGatewayHandler(exclusiveGatewaySettings) {\r\n  this._exclusiveGatewaySettings = exclusiveGatewaySettings;\r\n}\r\n\r\nExclusiveGatewayHandler.prototype.createContextPads = function(element) {\r\n\r\n  const outgoingFlows = element.outgoing.filter(function(outgoing) {\r\n    return is(outgoing, 'bpmn:SequenceFlow');\r\n  });\r\n\r\n  if (outgoingFlows.length < 2) {\r\n    return;\r\n  }\r\n\r\n  const html = `\r\n    <div class=\"bts-context-pad\" title=\"Set Sequence Flow\">\r\n      ${ForkIcon()}\r\n    </div>\r\n  `;\r\n\r\n  const action = () => {\r\n    this._exclusiveGatewaySettings.setSequenceFlow(element);\r\n  };\r\n\r\n  return [\r\n    {\r\n      action,\r\n      element,\r\n      html\r\n    }\r\n  ];\r\n};\r\n\r\nExclusiveGatewayHandler.$inject = [\r\n  'exclusiveGatewaySettings'\r\n];", "import {\r\n  ForkIcon\r\n} from '../../../icons';\r\n\r\nimport { getBusinessObject } from '../../../util/ElementHelper';\r\nimport { isSequenceFlow } from '../../../simulator/util/ModelUtil';\r\n\r\nexport default function InclusiveGatewayHandler(inclusiveGatewaySettings) {\r\n  this._inclusiveGatewaySettings = inclusiveGatewaySettings;\r\n}\r\n\r\nInclusiveGatewayHandler.prototype.createContextPads = function(element) {\r\n  const outgoingFlows = element.outgoing.filter(isSequenceFlow);\r\n\r\n  if (outgoingFlows.length < 2) {\r\n    return;\r\n  }\r\n\r\n  const nonDefaultFlows = outgoingFlows.filter(outgoing => {\r\n    const flowBo = getBusinessObject(outgoing),\r\n          gatewayBo = getBusinessObject(element);\r\n\r\n    return gatewayBo.default !== flowBo;\r\n  });\r\n\r\n  const html = `\r\n    <div class=\"bts-context-pad\" title=\"Set Sequence Flow\">\r\n      ${ForkIcon()}\r\n    </div>\r\n  `;\r\n\r\n  return nonDefaultFlows.map(sequenceFlow => {\r\n    const action = () => {\r\n      this._inclusiveGatewaySettings.toggleSequenceFlow(element, sequenceFlow);\r\n    };\r\n\r\n    return {\r\n      action,\r\n      element: sequenceFlow,\r\n      html\r\n    };\r\n  });\r\n};\r\n\r\nInclusiveGatewayHandler.$inject = [\r\n  'inclusiveGatewaySettings'\r\n];", "import {\n  PauseIcon,\n  RemovePauseIcon\n} from '../../../icons';\n\n\nimport {\n  is,\n  getBusinessObject\n} from '../../../util/ElementHelper';\n\n\nexport default function PauseHandler(simulator) {\n  this._simulator = simulator;\n}\n\nPauseHandler.prototype.createContextPads = function(element) {\n\n  if (\n    is(element, 'bpmn:ReceiveTask') || (\n      is(element, 'bpmn:SubProcess') && getBusinessObject(element).triggeredByEvent\n    )\n  ) {\n    return [];\n  }\n\n  return [\n    this.createPauseContextPad(element)\n  ];\n};\n\nPauseHandler.prototype.createPauseContextPad = function(element) {\n\n  const contexts = () => this._findSubscriptions({\n    element\n  });\n\n  const wait = this._isPaused(element);\n\n  const html = `\n    <div class=\"bts-context-pad ${ wait ? '' : 'show-hover' }\" title=\"${ wait ? 'Remove' : 'Add' } pause point\">\n      ${ (wait ? RemovePauseIcon : PauseIcon)('show-hover') }\n      ${ PauseIcon('hide-hover') }\n    </div>\n  `;\n\n  const action = () => {\n    this._togglePaused(element);\n  };\n\n  return {\n    action,\n    element,\n    hideContexts: contexts,\n    html\n  };\n};\n\nPauseHandler.prototype._isPaused = function(element) {\n\n  const {\n    wait\n  } = this._simulator.getConfig(element);\n\n  return wait;\n};\n\nPauseHandler.prototype._togglePaused = function(element) {\n  const wait = !this._isPaused(element);\n\n  this._simulator.waitAtElement(element, wait);\n};\n\nPauseHandler.prototype._findSubscriptions = function(options) {\n  return this._simulator.findSubscriptions(options);\n};\n\nPauseHandler.$inject = [\n  'simulator'\n];", "import {\n  PlayIcon\n} from '../../../icons';\n\n\nexport default function Tri<PERSON><PERSON><PERSON>ler(simulator) {\n  this._simulator = simulator;\n}\n\nTriggerHandler.$inject = [\n  'simulator'\n];\n\nTriggerHandler.prototype.createContextPads = function(element) {\n  return [\n    this.createTriggerContextPad(element)\n  ];\n};\n\nTriggerHandler.prototype.createTriggerContextPad = function(element) {\n\n  const contexts = () => {\n    const subscriptions = this._findSubscriptions({\n      element\n    });\n\n    const sortedSubscriptions = subscriptions.slice().sort((a, b) => {\n      return a.event.type === 'none' ? 1 : -1;\n    });\n\n    return sortedSubscriptions;\n  };\n\n  const html = `\n    <div class=\"bts-context-pad\" title=\"Trigger Event\">\n      ${PlayIcon()}\n    </div>\n  `;\n\n  const action = (subscriptions) => {\n\n    const {\n      event,\n      scope\n    } = subscriptions[0];\n\n    return this._simulator.trigger({\n      event,\n      scope\n    });\n  };\n\n  return {\n    action,\n    element,\n    html,\n    contexts\n  };\n};\n\nTriggerHandler.prototype._findSubscriptions = function(options) {\n  return this._simulator.findSubscriptions(options);\n};", "import {\r\n  is\r\n} from '../../util/ElementHelper';\r\n\r\nimport {\r\n  TOGGLE_MODE_EVENT,\r\n  RESET_SIMULATION_EVENT,\r\n  ELEMENT_CHANGED_EVENT,\r\n  SCOPE_FILTER_CHANGED_EVENT\r\n} from '../../util/EventHelper';\r\n\r\nimport {\r\n  event as domEvent,\r\n  classes as domClasses,\r\n  queryAll as domQueryAll,\r\n  domify\r\n} from 'min-dom';\r\n\r\nimport ExclusiveGatewayHandler from './handler/ExclusiveGatewayHandler';\r\nimport InclusiveGatewayHandler from './handler/InclusiveGatewayHandler';\r\nimport PauseHandler from './handler/PauseHandler';\r\nimport TriggerHandler from './handler/TriggerHandler';\r\n\r\n\r\nconst LOW_PRIORITY = 500;\r\n\r\nconst OFFSET_TOP = -15;\r\nconst OFFSET_LEFT = -15;\r\n\r\n\r\nexport default function ContextPads(\r\n    eventBus, elementRegistry,\r\n    overlays, injector,\r\n    canvas, scopeFilter) {\r\n\r\n  this._elementRegistry = elementRegistry;\r\n  this._overlays = overlays;\r\n  this._injector = injector;\r\n  this._canvas = canvas;\r\n  this._scopeFilter = scopeFilter;\r\n\r\n  this._overlayCache = new Map();\r\n\r\n  this._handlerIdx = 0;\r\n\r\n  this._handlers = [];\r\n\r\n  this.registerHandler('bpmn:ExclusiveGateway', ExclusiveGatewayHandler);\r\n  this.registerHandler('bpmn:InclusiveGateway', InclusiveGatewayHandler);\r\n\r\n  this.registerHandler('bpmn:Activity', PauseHandler);\r\n\r\n  this.registerHandler('bpmn:Event', TriggerHandler);\r\n  this.registerHandler('bpmn:Gateway', TriggerHandler);\r\n  this.registerHandler('bpmn:Activity', TriggerHandler);\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, LOW_PRIORITY, context => {\r\n    const active = context.active;\r\n\r\n    if (active) {\r\n      this.openContextPads();\r\n    } else {\r\n      this.closeContextPads();\r\n    }\r\n  });\r\n\r\n  eventBus.on(RESET_SIMULATION_EVENT, LOW_PRIORITY, () => {\r\n    this.closeContextPads();\r\n    this.openContextPads();\r\n  });\r\n\r\n  eventBus.on(SCOPE_FILTER_CHANGED_EVENT, event => {\r\n\r\n    const showElements = domQueryAll(\r\n      '.djs-overlay-ts-context-menu [data-scope-ids]',\r\n      overlays._overlayRoot\r\n    );\r\n\r\n    for (const element of showElements) {\r\n\r\n      const scopeIds = element.dataset.scopeIds.split(',');\r\n\r\n      const shown = scopeIds.some(id => scopeFilter.isShown(id));\r\n\r\n      domClasses(element).toggle('hidden', !shown);\r\n    }\r\n\r\n    const hideElements = domQueryAll(\r\n      '.djs-overlay-ts-context-menu [data-hide-scope-ids]',\r\n      overlays._overlayRoot\r\n    );\r\n\r\n    for (const element of hideElements) {\r\n\r\n      const scopeIds = element.dataset.hideScopeIds.split(',');\r\n\r\n      const shown = scopeIds.some(id => scopeFilter.isShown(id));\r\n\r\n      domClasses(element).toggle('hidden', shown);\r\n    }\r\n  });\r\n\r\n  eventBus.on(ELEMENT_CHANGED_EVENT, LOW_PRIORITY, event => {\r\n    const {\r\n      element\r\n    } = event;\r\n\r\n    this.updateElementContextPads(element);\r\n  });\r\n}\r\n\r\n/**\r\n * Register a handler for an element type.\r\n * An element type can have multiple handlers.\r\n *\r\n * @param {String} type\r\n * @param {Object} handlerCls\r\n */\r\nContextPads.prototype.registerHandler = function(type, handlerCls) {\r\n  const handler = this._injector.instantiate(handlerCls);\r\n\r\n  handler.hash = String(this._handlerIdx++);\r\n\r\n  this._handlers.push({ handler, type });\r\n};\r\n\r\nContextPads.prototype.getHandlers = function(element) {\r\n\r\n  return (\r\n    this._handlers.filter(\r\n      ({ type }) => is(element, type)\r\n    ).map(\r\n      ({ handler }) => handler\r\n    )\r\n  );\r\n};\r\n\r\nContextPads.prototype.openContextPads = function(parent) {\r\n\r\n  if (!parent) {\r\n    parent = this._canvas.getRootElement();\r\n  }\r\n\r\n  this._elementRegistry.forEach((element) => {\r\n    if (isAncestor(parent, element)) {\r\n      this.updateElementContextPads(element);\r\n    }\r\n  });\r\n};\r\n\r\nContextPads.prototype._getOverlays = function(hash) {\r\n  return this._overlayCache.get(hash) || [];\r\n};\r\n\r\nContextPads.prototype._addOverlay = function(element, options) {\r\n\r\n  const {\r\n    handlerHash\r\n  } = options;\r\n\r\n  if (!handlerHash) {\r\n    throw new Error('<handlerHash> required');\r\n  }\r\n\r\n  const overlayId = this._overlays.add(element, 'bts-context-menu', {\r\n    ...options,\r\n    position: {\r\n      top: OFFSET_TOP,\r\n      left: OFFSET_LEFT\r\n    },\r\n    show: {\r\n      minZoom: 0.5\r\n    }\r\n  });\r\n\r\n  const overlay = this._overlays.get(overlayId);\r\n\r\n  const overlayCache = this._overlayCache;\r\n\r\n  if (!overlayCache.has(handlerHash)) {\r\n    overlayCache.set(handlerHash, []);\r\n  }\r\n\r\n  overlayCache.get(handlerHash).push(overlay);\r\n};\r\n\r\nContextPads.prototype._removeOverlay = function(overlay) {\r\n\r\n  const {\r\n    id,\r\n    handlerHash\r\n  } = overlay;\r\n\r\n  // remove overlay\r\n  this._overlays.remove(id);\r\n\r\n  // remove from overlay cache\r\n  const overlays = this._overlayCache.get(handlerHash) || [];\r\n\r\n  const idx = overlays.indexOf(overlay);\r\n\r\n  if (idx !== -1) {\r\n    overlays.splice(idx, 1);\r\n  }\r\n};\r\n\r\nContextPads.prototype.updateElementContextPads = function(element) {\r\n  for (const handler of this.getHandlers(element)) {\r\n    this._updateElementContextPads(element, handler);\r\n  }\r\n};\r\n\r\nContextPads.prototype._updateElementContextPads = function(element, handler) {\r\n\r\n  const canvas = this._canvas;\r\n\r\n  const contextPads = (handler.createContextPads(element) || []).filter(p => p);\r\n\r\n  const handlerHash = `${element.id}------${handler.hash}`;\r\n\r\n  const existingOverlays = this._getOverlays(handlerHash);\r\n\r\n  const updatedOverlays = [];\r\n\r\n  for (const contextPad of contextPads) {\r\n\r\n    const {\r\n      element,\r\n      contexts: _contexts,\r\n      hideContexts: _hideContexts,\r\n      action: _action,\r\n      html: _html\r\n    } = contextPad;\r\n\r\n\r\n    const hash = `${contextPad.element.id}-------${_html}`;\r\n\r\n    let existingOverlay = existingOverlays.find(\r\n      o => o.hash === hash\r\n    );\r\n\r\n    const html = existingOverlay && existingOverlay.html || domify(_html);\r\n\r\n    if (_contexts) {\r\n      const contexts = _contexts();\r\n\r\n      html.dataset.scopeIds = contexts.map(c => c.scope.id).join(',');\r\n\r\n      const shownScopes = contexts.filter(c => this._scopeFilter.isShown(c.scope));\r\n\r\n      domClasses(html).toggle('hidden', shownScopes.length === 0);\r\n    }\r\n\r\n    if (_hideContexts) {\r\n      const contexts = _hideContexts();\r\n\r\n      html.dataset.hideScopeIds = contexts.map(c => c.scope.id).join(',');\r\n\r\n      const shownScopes = contexts.filter(c => this._scopeFilter.isShown(c.scope));\r\n\r\n      domClasses(html).toggle('hidden', shownScopes.length > 0);\r\n    }\r\n\r\n    if (existingOverlay) {\r\n      updatedOverlays.push(existingOverlay);\r\n\r\n      continue;\r\n    }\r\n\r\n    if (_action) {\r\n\r\n      domEvent.bind(html, 'click', event => {\r\n        event.preventDefault();\r\n\r\n        const contexts = _contexts\r\n          ? _contexts().filter(c => this._scopeFilter.isShown(c.scope))\r\n          : null;\r\n\r\n        _action(contexts);\r\n\r\n        if ('restoreFocus' in canvas) {\r\n          canvas.restoreFocus();\r\n        }\r\n      });\r\n    }\r\n\r\n    this._addOverlay(element, {\r\n      hash,\r\n      handlerHash,\r\n      html\r\n    });\r\n  }\r\n\r\n  for (const existingOverlay of existingOverlays) {\r\n    if (!updatedOverlays.includes(existingOverlay)) {\r\n      this._removeOverlay(existingOverlay);\r\n    }\r\n  }\r\n};\r\n\r\nContextPads.prototype.closeContextPads = function() {\r\n  for (const overlays of this._overlayCache.values()) {\r\n\r\n    for (const overlay of overlays) {\r\n      this._closeOverlay(overlay);\r\n    }\r\n  }\r\n\r\n  this._overlayCache.clear();\r\n};\r\n\r\nContextPads.prototype._closeOverlay = function(overlay) {\r\n  this._overlays.remove(overlay.id);\r\n};\r\n\r\nContextPads.$inject = [\r\n  'eventBus',\r\n  'elementRegistry',\r\n  'overlays',\r\n  'injector',\r\n  'canvas',\r\n  'scopeFilter'\r\n];\r\n\r\n\r\n// helpers ///////////////\r\n\r\nexport function isAncestor(ancestor, descendant) {\r\n\r\n  do {\r\n    if (ancestor === descendant) {\r\n      return true;\r\n    }\r\n\r\n    descendant = descendant.parent;\r\n  } while (descendant);\r\n\r\n  return false;\r\n}", "import ContextPads from './ContextPads';\n\nimport ScopeFilterModule from '../scope-filter';\n\nexport default {\n  __depends__: [\n    ScopeFilterModule\n  ],\n  __init__: [\n    'contextPads'\n  ],\n  contextPads: [ 'type', ContextPads ]\n};", "import {\n  SCOPE_DESTROYED_EVENT\n} from '../../util/EventHelper';\n\nimport {\n  CheckCircleIcon\n} from '../../icons';\n\n\nexport default function SimulationState(\n    eventBus,\n    simulator,\n    elementNotifications) {\n\n  eventBus.on(SCOPE_DESTROYED_EVENT, event => {\n    const {\n      scope\n    } = event;\n\n    const {\n      destroyInitiator,\n      element: scopeElement\n    } = scope;\n\n    if (!scope.completed || !destroyInitiator) {\n      return;\n    }\n\n    const processScopes = [\n      'bpmn:Process',\n      'bpmn:Participant'\n    ];\n\n    if (!processScopes.includes(scopeElement.type)) {\n      return;\n    }\n\n    elementNotifications.addElementNotification(destroyInitiator.element, {\n      type: 'success',\n      icon: CheckCircleIcon(),\n      text: 'Finished',\n      scope\n    });\n  });\n}\n\nSimulationState.$inject = [\n  'eventBus',\n  'simulator',\n  'elementNotifications'\n];", "import {\r\n  domify\r\n} from 'min-dom';\r\n\r\nimport {\r\n  TOGGLE_MODE_EVENT,\r\n  RESET_SIMULATION_EVENT,\r\n  SCOPE_CREATE_EVENT\r\n} from '../../util/EventHelper';\r\n\r\nconst OFFSET_TOP = -15;\r\nconst OFFSET_RIGHT = 15;\r\n\r\n\r\nexport default function ElementNotifications(overlays, eventBus) {\r\n  this._overlays = overlays;\r\n\r\n  eventBus.on([\r\n    RESET_SIMULATION_EVENT,\r\n    SCOPE_CREATE_EVENT,\r\n    TOGGLE_MODE_EVENT\r\n  ], () => {\r\n    this.clear();\r\n  });\r\n}\r\n\r\nElementNotifications.prototype.addElementNotification = function(element, options) {\r\n  const position = {\r\n    top: OFFSET_TOP,\r\n    right: OFFSET_RIGHT\r\n  };\r\n\r\n  const {\r\n    type,\r\n    icon,\r\n    text,\r\n    scope = {}\r\n  } = options;\r\n\r\n  const colors = scope.colors;\r\n\r\n  const colorMarkup = colors\r\n    ? `style=\"color: ${colors.auxiliary}; background: ${colors.primary}\"`\r\n    : '';\r\n\r\n  const html = domify(`\r\n    <div class=\"bts-element-notification ${ type || '' }\" ${colorMarkup}>\r\n      ${ icon || '' }\r\n      <span class=\"bts-text\">${ text }</span>\r\n    </div>\r\n  `);\r\n\r\n  this._overlays.add(element, 'bts-element-notification', {\r\n    position,\r\n    html: html,\r\n    show: {\r\n      minZoom: 0.5\r\n    }\r\n  });\r\n};\r\n\r\nElementNotifications.prototype.clear = function() {\r\n  this._overlays.remove({ type: 'bts-element-notification' });\r\n};\r\n\r\nElementNotifications.prototype.removeElementNotification = function(element) {\r\n  this._overlays.remove({ element: element });\r\n};\r\n\r\nElementNotifications.$inject = [ 'overlays', 'eventBus' ];", "import ElementNotifications from './ElementNotifications';\n\nexport default {\n  elementNotifications: [ 'type', ElementNotifications ]\n};", "import {\n  domify\n} from 'min-dom';\n\nimport {\n  TOGGLE_MODE_EVENT,\n  RESET_SIMULATION_EVENT\n} from '../../util/EventHelper';\n\nimport {\n  InfoIcon\n} from '../../icons';\n\n\nconst NOTIFICATION_TIME_TO_LIVE = 2000; // ms\n\nconst INFO_ICON = InfoIcon();\n\n\nexport default function Notifications(eventBus, canvas, scopeFilter) {\n  this._eventBus = eventBus;\n  this._canvas = canvas;\n  this._scopeFilter = scopeFilter;\n\n  this._init();\n\n  eventBus.on([\n    TOGGLE_MODE_EVENT,\n    RESET_SIMULATION_EVENT\n  ], event => {\n    this.clear();\n  });\n}\n\nNotifications.prototype._init = function() {\n  this.container = domify('<div class=\"bts-notifications\"></div>');\n\n  this._canvas.getContainer().appendChild(this.container);\n};\n\nNotifications.prototype.showNotification = function(options) {\n\n  const {\n    text,\n    type = 'info',\n    icon = INFO_ICON,\n    scope,\n    ttl = NOTIFICATION_TIME_TO_LIVE\n  } = options;\n\n  if (scope && !this._scopeFilter.isShown(scope)) {\n    return;\n  }\n\n  const iconMarkup = icon.startsWith('<')\n    ? icon\n    : `<i class=\"${ icon }\"></i>`;\n\n  const colors = scope && scope.colors;\n\n  const colorMarkup = colors ? `style=\"color: ${colors.auxiliary}; background: ${colors.primary}\"` : '';\n\n  const notification = domify(`\n    <div class=\"bts-notification ${type}\">\n      <span class=\"bts-icon\">${iconMarkup}</span>\n      <span class=\"bts-text\" title=\"${ text }\">${text}</span>\n      ${ scope ? `<span class=\"bts-scope\" ${colorMarkup}>${scope.id}</span>` : '' }\n    </div>\n  `);\n\n  this.container.appendChild(notification);\n\n  // prevent more than 5 notifications at once\n  while (this.container.children.length > 5) {\n    this.container.children[0].remove();\n  }\n\n  setTimeout(function() {\n    notification.remove();\n  }, ttl);\n};\n\nNotifications.prototype.clear = function() {\n  while (this.container.children.length) {\n    this.container.children[0].remove();\n  }\n};\n\nNotifications.$inject = [\n  'eventBus',\n  'canvas',\n  'scopeFilter'\n];", "import ScopeFilterModule from '../scope-filter';\n\nimport Notifications from './Notifications';\n\nexport default {\n  __depends__: [\n    ScopeFilterModule\n  ],\n  notifications: [ 'type', Notifications ]\n};", "import SimulationState from './SimulationState';\n\nimport ElementNotificationsModule from '../element-notifications';\nimport NotificationsModule from '../notifications';\n\nexport default {\n  __depends__: [\n    ElementNotificationsModule,\n    NotificationsModule\n  ],\n  __init__: [\n    'simulationState'\n  ],\n  simulationState: [ 'type', SimulationState ]\n};", "import {\n  domify,\n  classes as domClasses,\n  event as domEvent,\n  query as domQuery,\n  queryAll as domQueryAll,\n  clear as domClear\n} from 'min-dom';\n\nimport {\n  TOGGLE_MODE_EVENT,\n  SCOPE_CREATE_EVENT,\n  SCOPE_CHANGED_EVENT,\n  SCOPE_FILTER_CHANGED_EVENT,\n  SCOPE_DESTROYED_EVENT,\n  RESET_SIMULATION_EVENT,\n} from '../../util/EventHelper';\n\nconst FILL_COLOR = '--token-simulation-silver-base-97';\nconst STROKE_COLOR = '--token-simulation-green-base-44';\n\nconst ID = 'show-scopes';\n\nconst VERY_HIGH_PRIORITY = 3000;\n\n\nexport default function ShowScopes(\n    eventBus,\n    canvas,\n    scopeFilter,\n    elementColors,\n    simulationStyles) {\n\n  this._eventBus = eventBus;\n  this._canvas = canvas;\n  this._scopeFilter = scopeFilter;\n  this._elementColors = elementColors;\n  this._simulationStyles = simulationStyles;\n\n  this._highlight = null;\n\n  this._init();\n\n  eventBus.on(TOGGLE_MODE_EVENT, event => {\n    const active = event.active;\n\n    if (active) {\n      domClasses(this._container).remove('hidden');\n    } else {\n      domClasses(this._container).add('hidden');\n      domClear(this._container);\n\n      this.unhighlightScope();\n    }\n  });\n\n  eventBus.on(SCOPE_FILTER_CHANGED_EVENT, event => {\n\n    const allElements = this.getScopeElements();\n\n    for (const element of allElements) {\n      const scopeId = element.dataset.scopeId;\n\n      domClasses(element).toggle('inactive', !this._scopeFilter.isShown(scopeId));\n    }\n  });\n\n  eventBus.on(SCOPE_CREATE_EVENT, event => {\n    this.addScope(event.scope);\n  });\n\n  eventBus.on(SCOPE_DESTROYED_EVENT, event => {\n    this.removeScope(event.scope);\n  });\n\n  eventBus.on(SCOPE_CHANGED_EVENT, event => {\n    this.updateScope(event.scope);\n  });\n\n  eventBus.on(RESET_SIMULATION_EVENT, () => {\n    this.removeAllInstances();\n  });\n}\n\nShowScopes.prototype._init = function() {\n  this._container = domify('<div class=\"bts-scopes hidden\"></div>');\n\n  this._canvas.getContainer().appendChild(this._container);\n};\n\nShowScopes.prototype.addScope = function(scope) {\n\n  const processElements = [\n    'bpmn:Process',\n    'bpmn:SubProcess',\n    'bpmn:Participant'\n  ];\n\n  const {\n    element: scopeElement\n  } = scope;\n\n  if (!processElements.includes(scopeElement.type)) {\n    return;\n  }\n\n  const colors = scope.colors;\n\n  const colorMarkup = colors ? `style=\"color: ${colors.auxiliary}; background: ${colors.primary}\"` : '';\n\n  const html = domify(`\n    <div data-scope-id=\"${scope.id}\" class=\"bts-scope\"\n         title=\"View Process Instance ${scope.id}\" ${colorMarkup}>\n      ${scope.getTokens()}\n    </div>\n  `);\n\n  domEvent.bind(html, 'click', () => {\n    this._scopeFilter.toggle(scope);\n  });\n\n  domEvent.bind(html, 'mouseenter', () => {\n    this.highlightScope(scopeElement);\n  });\n\n  domEvent.bind(html, 'mouseleave', () => {\n    this.unhighlightScope();\n  });\n\n  if (!this._scopeFilter.isShown(scope)) {\n    domClasses(html).add('inactive');\n  }\n\n  this._container.appendChild(html);\n};\n\nShowScopes.prototype.getScopeElements = function() {\n  return domQueryAll('[data-scope-id]', this._container);\n};\n\nShowScopes.prototype.getScopeElement = function(scope) {\n  return domQuery(`[data-scope-id=\"${scope.id}\"]`, this._container);\n};\n\nShowScopes.prototype.updateScope = function(scope) {\n  const element = this.getScopeElement(scope);\n\n  if (element) {\n    element.textContent = scope.getTokens();\n  }\n};\n\nShowScopes.prototype.removeScope = function(scope) {\n  const element = this.getScopeElement(scope);\n\n  if (element) {\n    element.remove();\n  }\n};\n\nShowScopes.prototype.removeAllInstances = function() {\n  this._container.innerHTML = '';\n};\n\nShowScopes.prototype.highlightScope = function(element) {\n\n  this.unhighlightScope();\n\n  this._highlight = element;\n\n  this._elementColors.add(element, ID, this._getHighlightColors(), VERY_HIGH_PRIORITY);\n\n  if (!element.parent) {\n    domClasses(this._canvas.getContainer()).add('highlight');\n  }\n};\n\nShowScopes.prototype.unhighlightScope = function() {\n\n  if (!this._highlight) {\n    return;\n  }\n\n  const element = this._highlight;\n\n  this._elementColors.remove(element, ID);\n\n  if (!element.parent) {\n    domClasses(this._canvas.getContainer()).remove('highlight');\n  }\n\n  this._highlight = null;\n};\n\nShowScopes.prototype._getHighlightColors = function() {\n  return {\n    fill: this._simulationStyles.get(FILL_COLOR),\n    stroke: this._simulationStyles.get(STROKE_COLOR)\n  };\n};\n\nShowScopes.$inject = [\n  'eventBus',\n  'canvas',\n  'scopeFilter',\n  'elementColors',\n  'simulationStyles'\n];", "export default function SimulationStyles() {\n  this._cache = {};\n}\n\nSimulationStyles.$inject = [];\n\n\nSimulationStyles.prototype.get = function(prop) {\n\n  const cachedValue = this._cache[prop];\n\n  if (cachedValue) {\n    return cachedValue;\n  }\n\n  if (!this._computedStyle) {\n    this._computedStyle = this._getComputedStyle();\n  }\n\n  return this._cache[prop] = this._computedStyle.getPropertyValue(prop).trim();\n};\n\nSimulationStyles.prototype._getComputedStyle = function() {\n\n  const get = typeof getComputedStyle === 'function'\n    ? getComputedStyle\n    : getComputedStyleMock;\n\n  const element = typeof document !== 'undefined'\n    ? document.documentElement\n    : {};\n\n  return get(element);\n};\n\n\n// helpers //////////////////\n\nfunction getComputedStyleMock() {\n  return {\n    getPropertyValue() {\n      return '';\n    }\n  };\n}", "import SimulationStyles from './SimulationStyles';\n\nexport default {\n  simulationStyles: [ 'type', SimulationStyles ]\n};", "import ShowScopes from './ShowScopes';\n\nimport ScopeFilterModule from '../scope-filter';\nimport SimulationStylesModule from '../simulation-styles';\n\nexport default {\n  __depends__: [\n    ScopeFilterModule,\n    SimulationStylesModule\n  ],\n  __init__: [\n    'showScopes'\n  ],\n  showScopes: [ 'type', ShowScopes ]\n};", "/**\n * @param {string} str\n *\n * @return {string}\n */\nexport function escapeCSS(str) {\n  return CSS.escape(str);\n}\n\nvar HTML_ESCAPE_MAP = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  '\\'': '&#39;'\n};\n\n/**\n * @param {string} str\n *\n * @return {string}\n */\nexport function escapeHTML(str) {\n  str = '' + str;\n\n  return str && str.replace(/[&<>\"']/g, function(match) {\n    return HTML_ESCAPE_MAP[match];\n  });\n}\n", "import {\r\n  domify,\r\n  classes as domClasses,\r\n  event as domEvent,\r\n  query as domQ<PERSON>y,\r\n  queryAll as dom<PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  delegate as domDelegate\r\n} from 'min-dom';\r\n\r\nimport {\r\n  getBusinessObject,\r\n  is,\r\n} from '../../util/ElementHelper';\r\n\r\nimport {\r\n  escapeHTML\r\n} from 'diagram-js/lib/util/EscapeUtil';\r\n\r\nimport {\r\n  TOGGLE_MODE_EVENT,\r\n  RESET_SIMULATION_EVENT,\r\n  SCOPE_DESTROYED_EVENT,\r\n  SCOPE_CREATE_EVENT,\r\n  SCOPE_FILTER_CHANGED_EVENT,\r\n  TRACE_EVENT\r\n} from '../../util/EventHelper';\r\n\r\nimport {\r\n  LogIcon,\r\n  TimesIcon,\r\n  TimesCircleIcon,\r\n  CheckCircleIcon,\r\n  InfoIcon\r\n} from '../../icons';\r\n\r\n\r\nconst ICON_INFO = InfoIcon();\r\n\r\nfunction getElementName(element) {\r\n  const name = element && element.businessObject.name;\r\n\r\n  return name && escapeHTML(name);\r\n}\r\n\r\nfunction getIconForIntermediateEvent(element, throwOrCatch) {\r\n  const eventTypeString = getEventTypeString(element);\r\n  if (eventTypeString === 'none') {\r\n    return 'bpmn-icon-intermediate-event-none';\r\n  }\r\n  return `bpmn-icon-intermediate-event-${throwOrCatch}-${eventTypeString}`;\r\n}\r\n\r\nfunction getEventTypeString(element) {\r\n  const bo = getBusinessObject(element);\r\n  if (bo.get('eventDefinitions').length === 0) {\r\n    return 'none';\r\n  }\r\n  const eventDefinition = bo.eventDefinitions[0];\r\n\r\n  if (is(eventDefinition, 'bpmn:MessageEventDefinition')) {\r\n    return 'message';\r\n  }\r\n  if (is(eventDefinition, 'bpmn:TimerEventDefinition')) {\r\n    return 'timer';\r\n  }\r\n  if (is(eventDefinition, 'bpmn:SignalEventDefinition')) {\r\n    return 'signal';\r\n  }\r\n  if (is(eventDefinition, 'bpmn:ErrorEventDefinition')) {\r\n    return 'error';\r\n  }\r\n  if (is(eventDefinition, 'bpmn:EscalationEventDefinition')) {\r\n    return 'escalation';\r\n  }\r\n  if (is(eventDefinition, 'bpmn:CompensateEventDefinition')) {\r\n    return 'compensation';\r\n  }\r\n  if (is(eventDefinition, 'bpmn:ConditionalEventDefinition')) {\r\n    return 'condition';\r\n  }\r\n  if (is(eventDefinition, 'bpmn:LinkEventDefinition')) {\r\n    return 'link';\r\n  }\r\n  if (is(eventDefinition, 'bpmn:CancelEventDefinition')) {\r\n    return 'cancel';\r\n  }\r\n  if (is(eventDefinition, 'bpmn:TerminateEventDefinition')) {\r\n    return 'terminate';\r\n  }\r\n  return 'none';\r\n}\r\n\r\n\r\nexport default function Log(\r\n    eventBus, notifications,\r\n    tokenSimulationPalette, canvas,\r\n    scopeFilter, simulator) {\r\n\r\n  this._notifications = notifications;\r\n  this._tokenSimulationPalette = tokenSimulationPalette;\r\n  this._canvas = canvas;\r\n  this._scopeFilter = scopeFilter;\r\n\r\n  this._init();\r\n\r\n  eventBus.on(SCOPE_FILTER_CHANGED_EVENT, event => {\r\n    const allElements = domQueryAll('.bts-entry[data-scope-id]', this._container);\r\n\r\n    for (const element of allElements) {\r\n      const scopeId = element.dataset.scopeId;\r\n\r\n      domClasses(element).toggle('inactive', !this._scopeFilter.isShown(scopeId));\r\n    }\r\n  });\r\n\r\n  eventBus.on(SCOPE_DESTROYED_EVENT, event => {\r\n    const {\r\n      scope\r\n    } = event;\r\n\r\n    const {\r\n      element: scopeElement\r\n    } = scope;\r\n\r\n    const completed = scope.completed;\r\n\r\n    const processScopes = [\r\n      'bpmn:Process',\r\n      'bpmn:Participant',\r\n      'bpmn:SubProcess'\r\n    ];\r\n\r\n    if (!processScopes.includes(scopeElement.type)) {\r\n      return;\r\n    }\r\n\r\n    const isSubProcess = is(scopeElement, 'bpmn:SubProcess');\r\n\r\n    const text = `${\r\n      isSubProcess ? (getElementName(scopeElement) || 'SubProcess') : 'Process'\r\n    } ${\r\n      completed ? 'finished' : 'canceled'\r\n    }`;\r\n\r\n    this.log({\r\n      text,\r\n      icon: completed ? CheckCircleIcon() : TimesCircleIcon(),\r\n      scope\r\n    });\r\n  });\r\n\r\n  eventBus.on(SCOPE_CREATE_EVENT, event => {\r\n    const {\r\n      scope\r\n    } = event;\r\n\r\n    const {\r\n      element: scopeElement\r\n    } = scope;\r\n\r\n    const processScopes = [\r\n      'bpmn:Process',\r\n      'bpmn:Participant',\r\n      'bpmn:SubProcess'\r\n    ];\r\n\r\n    if (!processScopes.includes(scopeElement.type)) {\r\n      return;\r\n    }\r\n\r\n    const isSubProcess = is(scopeElement, 'bpmn:SubProcess');\r\n\r\n    const text = `${\r\n      isSubProcess ? (getElementName(scopeElement) || 'SubProcess') : 'Process'\r\n    } started`;\r\n\r\n    this.log({\r\n      text,\r\n      icon: CheckCircleIcon(),\r\n      scope\r\n    });\r\n  });\r\n\r\n  eventBus.on(TRACE_EVENT, event => {\r\n\r\n    const {\r\n      action,\r\n      scope: elementScope,\r\n      element\r\n    } = event;\r\n\r\n    if (action !== 'exit') {\r\n      return;\r\n    }\r\n\r\n    const scope = elementScope.parent;\r\n\r\n    const elementName = getElementName(element);\r\n\r\n    // log tasks ////////////\r\n\r\n    if (is(element, 'bpmn:ServiceTask')) {\r\n      return this.log({\r\n        text: elementName || 'Service Task',\r\n        icon: 'bpmn-icon-service',\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:UserTask')) {\r\n      return this.log({\r\n        text: elementName || 'User Task',\r\n        icon: 'bpmn-icon-user',\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:CallActivity')) {\r\n      return this.log({\r\n        text: elementName || 'Call Activity',\r\n        icon: 'bpmn-icon-call-activity',\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:ScriptTask')) {\r\n      return this.log({\r\n        text: elementName || 'Script Task',\r\n        icon: 'bpmn-icon-script',\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:BusinessRuleTask')) {\r\n      return this.log({\r\n        text: elementName || 'Business Rule Task',\r\n        icon: 'bpmn-icon-business-rule',\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:ManualTask')) {\r\n      return this.log({\r\n        text: elementName || 'Manual Task',\r\n        icon: 'bpmn-icon-manual-task',\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:ReceiveTask')) {\r\n      return this.log({\r\n        text: elementName || 'Receive Task',\r\n        icon: 'bpmn-icon-receive',\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:SendTask')) {\r\n      return this.log({\r\n        text: elementName || 'Send Task',\r\n        icon: 'bpmn-icon-send',\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:Task')) {\r\n      return this.log({\r\n        text: elementName || 'Task',\r\n        icon: 'bpmn-icon-task',\r\n        scope\r\n      });\r\n    }\r\n\r\n    // log gateways ////////////\r\n\r\n    if (is(element, 'bpmn:ExclusiveGateway')) {\r\n      return this.log({\r\n        text: elementName || 'Exclusive Gateway',\r\n        icon: 'bpmn-icon-gateway-xor',\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:ParallelGateway')) {\r\n      return this.log({\r\n        text: elementName || 'Parallel Gateway',\r\n        icon: 'bpmn-icon-gateway-parallel',\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:InclusiveGateway')) {\r\n      return this.log({\r\n        text: elementName || 'Inclusive Gateway',\r\n        icon: 'bpmn-icon-gateway-or',\r\n        scope\r\n      });\r\n    }\r\n\r\n    // log events /////////////\r\n\r\n    if (is(element, 'bpmn:StartEvent')) {\r\n      return this.log({\r\n        text: elementName || 'Start Event',\r\n        icon: `bpmn-icon-start-event-${getEventTypeString(element)}`,\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:IntermediateCatchEvent')) {\r\n      return this.log({\r\n        text: elementName || 'Intermediate Event',\r\n        icon: getIconForIntermediateEvent(element, 'catch'),\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:IntermediateThrowEvent')) {\r\n      return this.log({\r\n        text: elementName || 'Intermediate Event',\r\n        icon: getIconForIntermediateEvent(element, 'throw'),\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:BoundaryEvent')) {\r\n      return this.log({\r\n        text: elementName || 'Boundary Event',\r\n        icon: getIconForIntermediateEvent(element, 'catch'),\r\n        scope\r\n      });\r\n    }\r\n\r\n    if (is(element, 'bpmn:EndEvent')) {\r\n\r\n      // TODO: No trace event for terminate end events is emitted\r\n      return this.log({\r\n        text: elementName || 'End Event',\r\n        icon: `bpmn-icon-end-event-${getEventTypeString(element)}`,\r\n        scope\r\n      });\r\n    }\r\n  });\r\n\r\n\r\n  eventBus.on([\r\n    TOGGLE_MODE_EVENT,\r\n    RESET_SIMULATION_EVENT\r\n  ], event => {\r\n    this.clear();\r\n    this.toggle(false);\r\n  });\r\n}\r\n\r\nLog.prototype._init = function() {\r\n  this._container = domify(`\r\n    <div class=\"bts-log hidden djs-scrollable\">\r\n      <div class=\"bts-header\">\r\n        ${ LogIcon('bts-log-icon') }\r\n        Simulation Log\r\n        <button class=\"bts-close\" aria-label=\"Close\">\r\n          ${ TimesIcon() }\r\n        </button>\r\n      </div>\r\n      <div class=\"bts-content\">\r\n        <p class=\"bts-entry placeholder\">No Entries</p>\r\n      </div>\r\n    </div>\r\n  `);\r\n\r\n  this._placeholder = domQuery('.bts-placeholder', this._container);\r\n\r\n  this._content = domQuery('.bts-content', this._container);\r\n\r\n  domEvent.bind(this._content, 'mousedown', event => {\r\n    event.stopPropagation();\r\n  });\r\n\r\n  this._close = domQuery('.bts-close', this._container);\r\n\r\n  domEvent.bind(this._close, 'click', () => {\r\n    this.toggle(false);\r\n  });\r\n\r\n  this._icon = domQuery('.bts-log-icon', this._container);\r\n\r\n  domEvent.bind(this._icon, 'click', () => {\r\n    this.toggle();\r\n  });\r\n\r\n  this._canvas.getContainer().appendChild(this._container);\r\n\r\n  this.paletteEntry = domify(`\r\n    <div class=\"bts-entry\" title=\"Toggle Simulation Log\">\r\n      ${ LogIcon() }\r\n    </div>\r\n  `);\r\n\r\n  domEvent.bind(this.paletteEntry, 'click', () => {\r\n    this.toggle();\r\n  });\r\n\r\n  this._tokenSimulationPalette.addEntry(this.paletteEntry, 3);\r\n};\r\n\r\nLog.prototype.isShown = function() {\r\n  const container = this._container;\r\n\r\n  return !domClasses(container).has('hidden');\r\n};\r\n\r\nLog.prototype.toggle = function(shown = !this.isShown()) {\r\n  const container = this._container;\r\n\r\n  if (shown) {\r\n    domClasses(container).remove('hidden');\r\n  } else {\r\n    domClasses(container).add('hidden');\r\n  }\r\n};\r\n\r\nLog.prototype.log = function(options) {\r\n\r\n  const {\r\n    text,\r\n    type = 'info',\r\n    icon = ICON_INFO,\r\n    scope\r\n  } = options;\r\n\r\n  const content = this._content;\r\n\r\n  domClasses(this._placeholder).add('hidden');\r\n\r\n  if (!this.isShown()) {\r\n    this._notifications.showNotification(options);\r\n  }\r\n\r\n  const iconMarkup = icon.startsWith('<') ? icon : `<i class=\"${icon}\"></i>`;\r\n\r\n  const colors = scope && scope.colors;\r\n\r\n  const colorMarkup = colors ? `style=\"background: ${colors.primary}; color: ${colors.auxiliary}\"` : '';\r\n\r\n  const logEntry = domify(`\r\n    <p class=\"bts-entry ${ type } ${\r\n      scope && this._scopeFilter.isShown(scope) ? '' : 'inactive'\r\n    }\" ${\r\n      scope ? `data-scope-id=\"${scope.id}\"` : ''\r\n    }>\r\n      <span class=\"bts-icon\">${iconMarkup}</span>\r\n      <span class=\"bts-text\" title=\"${ text }\">${text}</span>\r\n      ${\r\n        scope\r\n          ? `<span class=\"bts-scope\" data-scope-id=\"${scope.id}\" ${colorMarkup}>${scope.id}</span>`\r\n          : ''\r\n      }\r\n    </p>\r\n  `);\r\n\r\n  domDelegate.bind(logEntry, '.bts-scope[data-scope-id]', 'click', event => {\r\n    this._scopeFilter.toggle(scope);\r\n  });\r\n\r\n  // determine if the container should scroll,\r\n  // because it is currently scrolled to the very bottom\r\n  const shouldScroll = Math.abs(content.clientHeight + content.scrollTop - content.scrollHeight) < 2;\r\n\r\n  content.appendChild(logEntry);\r\n\r\n  if (shouldScroll) {\r\n    content.scrollTop = content.scrollHeight;\r\n  }\r\n};\r\n\r\nLog.prototype.clear = function() {\r\n  while (this._content.firstChild) {\r\n    this._content.removeChild(this._content.firstChild);\r\n  }\r\n\r\n  this._placeholder = domify('<p class=\"bts-entry placeholder\">No Entries</p>');\r\n\r\n  this._content.appendChild(this._placeholder);\r\n};\r\n\r\nLog.$inject = [\r\n  'eventBus',\r\n  'notifications',\r\n  'tokenSimulationPalette',\r\n  'canvas',\r\n  'scopeFilter',\r\n  'simulator'\r\n];", "import Log from './Log';\n\nimport ScopeFilterModule from '../scope-filter';\nimport NotificationsModule from '../notifications';\n\nexport default {\n  __depends__: [\n    NotificationsModule,\n    ScopeFilterModule\n  ],\n  __init__: [\n    'log'\n  ],\n  log: [ 'type', Log ]\n};", "import {\r\n  classes as domClasses\r\n} from 'min-dom';\r\n\r\nimport {\r\n  is\r\n} from '../../util/ElementHelper';\r\n\r\nimport {\r\n  TOGGLE_MODE_EVENT\r\n} from '../../util/EventHelper';\r\n\r\nimport {\r\n  ExclamationTriangleIcon\r\n} from '../../icons';\r\n\r\n\r\nconst UNSUPPORTED_ELEMENTS = [\r\n  'bpmn:ComplexGateway'\r\n];\r\n\r\nfunction isLabel(element) {\r\n  return element.labelTarget;\r\n}\r\n\r\n\r\nexport default function ElementSupport(\r\n    eventBus, elementRegistry, canvas,\r\n    notifications, elementNotifications) {\r\n\r\n  this._eventBus = eventBus;\r\n  this._elementRegistry = elementRegistry;\r\n  this._elementNotifications = elementNotifications;\r\n  this._notifications = notifications;\r\n\r\n  this._canvasParent = canvas.getContainer().parentNode;\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, event => {\r\n\r\n    if (event.active) {\r\n      this.enable();\r\n    } else {\r\n      this.clear();\r\n    }\r\n  });\r\n}\r\n\r\nElementSupport.prototype.getUnsupportedElements = function() {\r\n  return this._unsupportedElements;\r\n};\r\n\r\nElementSupport.prototype.enable = function() {\r\n\r\n  const unsupportedElements = [];\r\n\r\n  this._elementRegistry.forEach(element => {\r\n\r\n    if (isLabel(element)) {\r\n      return;\r\n    }\r\n\r\n    if (!is(element, UNSUPPORTED_ELEMENTS)) {\r\n      return;\r\n    }\r\n\r\n    this.showWarning(element);\r\n\r\n    unsupportedElements.push(element);\r\n  });\r\n\r\n  if (unsupportedElements.length) {\r\n\r\n    this._notifications.showNotification({\r\n      text: 'Found unsupported elements',\r\n      icon: ExclamationTriangleIcon(),\r\n      type: 'warning',\r\n      ttl: 5000\r\n    });\r\n  }\r\n\r\n  this._unsupportedElements = unsupportedElements;\r\n};\r\n\r\nElementSupport.prototype.clear = function() {\r\n  domClasses(this._canvasParent).remove('warning');\r\n};\r\n\r\nElementSupport.prototype.showWarning = function(element) {\r\n  this._elementNotifications.addElementNotification(element, {\r\n    type: 'warning',\r\n    icon: ExclamationTriangleIcon(),\r\n    text: 'Not supported'\r\n  });\r\n};\r\n\r\nElementSupport.$inject = [\r\n  'eventBus',\r\n  'elementRegistry',\r\n  'canvas',\r\n  'notifications',\r\n  'elementNotifications'\r\n];", "import ElementSupport from './ElementSupport';\nimport ElementNotificationsModule from '../element-notifications';\nimport NotificationsModule from '../notifications';\n\nexport default {\n  __depends__: [\n    ElementNotificationsModule,\n    NotificationsModule\n  ],\n  __init__: [ 'elementSupport' ],\n  elementSupport: [ 'type', ElementSupport ]\n};\n", "import {\r\n  domify,\r\n  classes as domClasses,\r\n  event as domEvent\r\n} from 'min-dom';\r\n\r\nimport {\r\n  TOGGLE_MODE_EVENT,\r\n  PLAY_SIMULATION_EVENT,\r\n  PAUSE_SIMULATION_EVENT,\r\n  RESET_SIMULATION_EVENT,\r\n  SCOPE_CREATE_EVENT,\r\n  TRACE_EVENT\r\n} from '../../util/EventHelper';\r\n\r\nimport {\r\n  PlayIcon,\r\n  PauseIcon\r\n} from '../../icons';\r\n\r\n\r\nconst PLAY_MARKUP = PlayIcon();\r\nconst PAUSE_MARKUP = PauseIcon();\r\n\r\nconst HIGH_PRIORITY = 1500;\r\n\r\n\r\nexport default function PauseSimulation(\r\n    eventBus, tokenSimulationPalette,\r\n    notifications, canvas) {\r\n\r\n  this._eventBus = eventBus;\r\n  this._tokenSimulationPalette = tokenSimulationPalette;\r\n  this._notifications = notifications;\r\n\r\n  this.canvasParent = canvas.getContainer().parentNode;\r\n\r\n  this.isActive = false;\r\n  this.isPaused = true;\r\n\r\n  this._init();\r\n\r\n  // unpause on simulation start\r\n  eventBus.on(SCOPE_CREATE_EVENT, HIGH_PRIORITY, event => {\r\n    this.activate();\r\n    this.unpause();\r\n  });\r\n\r\n  eventBus.on([\r\n    RESET_SIMULATION_EVENT,\r\n    TOGGLE_MODE_EVENT\r\n  ], () => {\r\n    this.deactivate();\r\n    this.pause();\r\n  });\r\n\r\n  eventBus.on(TRACE_EVENT, HIGH_PRIORITY, event => {\r\n    this.unpause();\r\n  });\r\n}\r\n\r\nPauseSimulation.prototype._init = function() {\r\n  this.paletteEntry = domify(`\r\n    <div class=\"bts-entry disabled\" title=\"Play/Pause Simulation\">\r\n      ${ PLAY_MARKUP }\r\n    </div>\r\n  `);\r\n\r\n  domEvent.bind(this.paletteEntry, 'click', this.toggle.bind(this));\r\n\r\n  this._tokenSimulationPalette.addEntry(this.paletteEntry, 1);\r\n};\r\n\r\nPauseSimulation.prototype.toggle = function() {\r\n  if (this.isPaused) {\r\n    this.unpause();\r\n  } else {\r\n    this.pause();\r\n  }\r\n};\r\n\r\nPauseSimulation.prototype.pause = function() {\r\n  if (!this.isActive) {\r\n    return;\r\n  }\r\n\r\n  domClasses(this.paletteEntry).remove('active');\r\n  domClasses(this.canvasParent).add('paused');\r\n\r\n  this.paletteEntry.innerHTML = PLAY_MARKUP;\r\n\r\n  this._eventBus.fire(PAUSE_SIMULATION_EVENT);\r\n\r\n  this._notifications.showNotification({\r\n    text: 'Pause Simulation'\r\n  });\r\n\r\n  this.isPaused = true;\r\n};\r\n\r\nPauseSimulation.prototype.unpause = function() {\r\n\r\n  if (!this.isActive || !this.isPaused) {\r\n    return;\r\n  }\r\n\r\n  domClasses(this.paletteEntry).add('active');\r\n  domClasses(this.canvasParent).remove('paused');\r\n\r\n  this.paletteEntry.innerHTML = PAUSE_MARKUP;\r\n\r\n  this._eventBus.fire(PLAY_SIMULATION_EVENT);\r\n\r\n  this._notifications.showNotification({\r\n    text: 'Play Simulation'\r\n  });\r\n\r\n  this.isPaused = false;\r\n};\r\n\r\nPauseSimulation.prototype.activate = function() {\r\n  this.isActive = true;\r\n\r\n  domClasses(this.paletteEntry).remove('disabled');\r\n};\r\n\r\nPauseSimulation.prototype.deactivate = function() {\r\n  this.isActive = false;\r\n\r\n  domClasses(this.paletteEntry).remove('active');\r\n  domClasses(this.paletteEntry).add('disabled');\r\n};\r\n\r\nPauseSimulation.$inject = [\r\n  'eventBus',\r\n  'tokenSimulationPalette',\r\n  'notifications',\r\n  'canvas'\r\n];", "import PauseSimulation from './PauseSimulation';\n\nimport NotificationsModule from '../notifications';\n\nexport default {\n  __depends__: [\n    NotificationsModule\n  ],\n  __init__: [\n    'pauseSimulation'\n  ],\n  pauseSimulation: [ 'type', PauseSimulation ]\n};", "import {\r\n  domify,\r\n  classes as domClasses,\r\n  event as domEvent\r\n} from 'min-dom';\r\n\r\nimport {\r\n  TOGGLE_MODE_EVENT,\r\n  RESET_SIMULATION_EVENT,\r\n  SCOPE_CREATE_EVENT\r\n} from '../../util/EventHelper';\r\n\r\nimport {\r\n  ResetIcon\r\n} from '../../icons';\r\n\r\n\r\nexport default function ResetSimulation(eventBus, tokenSimulationPalette, notifications) {\r\n  this._eventBus = eventBus;\r\n  this._tokenSimulationPalette = tokenSimulationPalette;\r\n  this._notifications = notifications;\r\n\r\n  this._init();\r\n\r\n  eventBus.on(SCOPE_CREATE_EVENT, () => {\r\n    domClasses(this._paletteEntry).remove('disabled');\r\n  });\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, (event) => {\r\n    const active = this._active = event.active;\r\n\r\n    if (!active) {\r\n      this.resetSimulation();\r\n    }\r\n  });\r\n}\r\n\r\nResetSimulation.prototype._init = function() {\r\n  this._paletteEntry = domify(`\r\n    <div class=\"bts-entry disabled\" title=\"Reset Simulation\">\r\n      ${ ResetIcon() }\r\n    </div>\r\n  `);\r\n\r\n  domEvent.bind(this._paletteEntry, 'click', () => {\r\n    this.resetSimulation();\r\n\r\n    this._notifications.showNotification({\r\n      text: 'Reset Simulation',\r\n      type: 'info'\r\n    });\r\n  });\r\n\r\n  this._tokenSimulationPalette.addEntry(this._paletteEntry, 2);\r\n};\r\n\r\nResetSimulation.prototype.resetSimulation = function() {\r\n  domClasses(this._paletteEntry).add('disabled');\r\n\r\n  this._eventBus.fire(RESET_SIMULATION_EVENT);\r\n};\r\n\r\nResetSimulation.$inject = [\r\n  'eventBus',\r\n  'tokenSimulationPalette',\r\n  'notifications'\r\n];", "import ResetSimulation from './ResetSimulation';\n\nimport NotificationsModule from '../notifications';\n\nexport default {\n  __depends__: [\n    NotificationsModule\n  ],\n  __init__: [\n    'resetSimulation'\n  ],\n  resetSimulation: [ 'type', ResetSimulation ]\n};", "import {\r\n  domify,\r\n  queryAll as domQueryAll,\r\n  classes as domClasses\r\n} from 'min-dom';\r\n\r\nimport {\r\n  is\r\n} from '../../util/ElementHelper';\r\n\r\nimport {\r\n  ELEMENT_CHANGED_EVENT,\r\n  SCOPE_FILTER_CHANGED_EVENT\r\n} from '../../util/EventHelper';\r\n\r\n\r\nconst OFFSET_BOTTOM = 10;\r\nconst OFFSET_LEFT = -15;\r\n\r\nconst LOW_PRIORITY = 500;\r\n\r\nconst DEFAULT_PRIMARY_COLOR = '--token-simulation-green-base-44';\r\nconst DEFAULT_AUXILIARY_COLOR = '--token-simulation-white';\r\n\r\n\r\nexport default function TokenCount(\r\n    eventBus, overlays,\r\n    simulator, scopeFilter,\r\n    simulationStyles) {\r\n\r\n  this._overlays = overlays;\r\n  this._scopeFilter = scopeFilter;\r\n  this._simulator = simulator;\r\n  this._simulationStyles = simulationStyles;\r\n\r\n  this.overlayIds = {};\r\n\r\n  eventBus.on(ELEMENT_CHANGED_EVENT, LOW_PRIORITY, event => {\r\n\r\n    const {\r\n      element\r\n    } = event;\r\n\r\n    this.removeTokenCounts(element);\r\n    this.addTokenCounts(element);\r\n  });\r\n\r\n  eventBus.on(SCOPE_FILTER_CHANGED_EVENT, event => {\r\n\r\n    const allElements = domQueryAll('.bts-token-count[data-scope-id]', overlays._overlayRoot);\r\n\r\n    for (const element of allElements) {\r\n      const scopeId = element.dataset.scopeId;\r\n\r\n      domClasses(element).toggle('inactive', !this._scopeFilter.isShown(scopeId));\r\n    }\r\n  });\r\n}\r\n\r\nTokenCount.prototype.addTokenCounts = function(element) {\r\n\r\n  if (is(element, 'bpmn:MessageFlow') || is(element, 'bpmn:SequenceFlow')) {\r\n    return;\r\n  }\r\n\r\n  const scopes = this._simulator.findScopes(scope => {\r\n    return (\r\n      !scope.destroyed &&\r\n      scope.children.some(c => !c.destroyed && c.element === element && !c.children.length)\r\n    );\r\n  });\r\n\r\n  this.addTokenCount(element, scopes);\r\n};\r\n\r\nTokenCount.prototype.addTokenCount = function(element, scopes) {\r\n  if (!scopes.length) {\r\n    return;\r\n  }\r\n\r\n  const tokenMarkup = scopes.map(scope => {\r\n    return this._getTokenHTML(element, scope);\r\n  }).join('');\r\n\r\n  const html = domify(`\r\n    <div class=\"bts-token-count-parent\">\r\n      ${tokenMarkup}\r\n    </div>\r\n  `);\r\n\r\n  const position = { bottom: OFFSET_BOTTOM, left: OFFSET_LEFT };\r\n\r\n  const overlayId = this._overlays.add(element, 'bts-token-count', {\r\n    position: position,\r\n    html: html,\r\n    show: {\r\n      minZoom: 0.5\r\n    }\r\n  });\r\n\r\n  this.overlayIds[element.id] = overlayId;\r\n};\r\n\r\nTokenCount.prototype.removeTokenCounts = function(element) {\r\n  this.removeTokenCount(element);\r\n};\r\n\r\nTokenCount.prototype.removeTokenCount = function(element) {\r\n  const overlayId = this.overlayIds[element.id];\r\n\r\n  if (!overlayId) {\r\n    return;\r\n  }\r\n\r\n  this._overlays.remove(overlayId);\r\n\r\n  delete this.overlayIds[element.id];\r\n};\r\n\r\nTokenCount.prototype._getTokenHTML = function(element, scope) {\r\n\r\n  const colors = scope.colors || this._getDefaultColors();\r\n\r\n  return `\r\n    <div data-scope-id=\"${scope.id}\" class=\"bts-token-count waiting ${this._scopeFilter.isShown(scope) ? '' : 'inactive' }\"\r\n         style=\"color: ${colors.auxiliary}; background: ${ colors.primary }\">\r\n      ${scope.getTokensByElement(element)}\r\n    </div>\r\n  `;\r\n};\r\n\r\nTokenCount.prototype._getDefaultColors = function() {\r\n  return {\r\n    primary: this._simulationStyles.get(DEFAULT_PRIMARY_COLOR),\r\n    auxiliary: this._simuationStyles.get(DEFAULT_AUXILIARY_COLOR)\r\n  };\r\n};\r\n\r\nTokenCount.$inject = [\r\n  'eventBus',\r\n  'overlays',\r\n  'simulator',\r\n  'scopeFilter',\r\n  'simulationStyles'\r\n];", "import TokenCount from './TokenCount';\n\nimport ScopeFilterModule from '../scope-filter';\nimport SimulationStylesModule from '../simulation-styles';\n\nexport default {\n  __depends__: [\n    ScopeFilterModule,\n    SimulationStylesModule\n  ],\n  __init__: [\n    'tokenCount'\n  ],\n  tokenCount: [ 'type', TokenCount ]\n};", "import {\n  domify,\n  classes as domClasses,\n  delegate as domDelegate,\n  queryAll as domQueryAll\n} from 'min-dom';\n\nimport {\n  TOGGLE_MODE_EVENT,\n  ANIMATION_SPEED_CHANGED_EVENT\n} from '../../util/EventHelper';\n\nconst SPEEDS = [\n  [ 'Slow', 0.5 ],\n  [ 'Normal', 1 ],\n  [ 'Fast', 2 ]\n];\n\nimport {\n  TachometerIcon,\n  AngleRightIcon\n} from '../../icons';\n\n\nexport default function SetAnimationSpeed(canvas, animation, eventBus) {\n  this._canvas = canvas;\n  this._animation = animation;\n  this._eventBus = eventBus;\n\n  this._init(animation.getAnimationSpeed());\n\n  eventBus.on(TOGGLE_MODE_EVENT, event => {\n    const active = event.active;\n\n    if (!active) {\n      domClasses(this._container).add('hidden');\n    } else {\n      domClasses(this._container).remove('hidden');\n    }\n  });\n\n  eventBus.on(ANIMATION_SPEED_CHANGED_EVENT, event => {\n    this.setActive(event.speed);\n  });\n}\n\nSetAnimationSpeed.prototype.getToggleSpeed = function(element) {\n  return parseFloat(element.dataset.speed);\n};\n\nSetAnimationSpeed.prototype._init = function(animationSpeed) {\n  this._container = domify(`\n    <div class=\"bts-set-animation-speed hidden\">\n      ${ TachometerIcon() }\n      <div class=\"bts-animation-speed-buttons\">\n        ${\n          SPEEDS.map(([ label, speed ], idx) => `\n            <button title=\"Set animation speed = ${ label }\" data-speed=\"${ speed }\" class=\"bts-animation-speed-button ${speed === animationSpeed ? 'active' : ''}\">\n              ${\n                Array.from({ length: idx + 1 }).map(\n                  () => AngleRightIcon()\n                ).join('')\n              }\n            </button>\n          `).join('')\n        }\n      </div>\n    </div>\n  `);\n\n  domDelegate.bind(this._container, '[data-speed]', 'click', event => {\n\n    const toggle = event.delegateTarget;\n\n    const speed = this.getToggleSpeed(toggle);\n\n    this._animation.setAnimationSpeed(speed);\n  });\n\n  this._canvas.getContainer().appendChild(this._container);\n};\n\nSetAnimationSpeed.prototype.setActive = function(speed) {\n  domQueryAll('[data-speed]', this._container).forEach(toggle => {\n\n    const active = this.getToggleSpeed(toggle) === speed;\n\n    domClasses(toggle)[active ? 'add' : 'remove']('active');\n  });\n};\n\nSetAnimationSpeed.$inject = [\n  'canvas',\n  'animation',\n  'eventBus'\n];\n", "import SetAnimationSpeed from './SetAnimationSpeed';\n\nexport default {\n  __init__: [\n    'setAnimationSpeed'\n  ],\n  setAnimationSpeed: [ 'type', SetAnimationSpeed ]\n};", "import {\r\n  is\r\n} from '../../util/ElementHelper';\r\n\r\nimport {\r\n  TOGGLE_MODE_EVENT\r\n} from '../../util/EventHelper';\r\n\r\n\r\nconst SELECTED_COLOR = '--token-simulation-grey-darken-30';\r\nconst NOT_SELECTED_COLOR = '--token-simulation-grey-lighten-56';\r\n\r\nfunction getNext(gateway, sequenceFlow) {\r\n  var outgoing = gateway.outgoing.filter(isSequenceFlow);\r\n\r\n  var index = outgoing.indexOf(sequenceFlow || gateway.sequenceFlow);\r\n\r\n  if (outgoing[index + 1]) {\r\n    return outgoing[index + 1];\r\n  } else {\r\n    return outgoing[0];\r\n  }\r\n}\r\n\r\nfunction isSequenceFlow(connection) {\r\n  return is(connection, 'bpmn:SequenceFlow');\r\n}\r\n\r\nconst ID = 'exclusive-gateway-settings';\r\n\r\nconst HIGH_PRIORITY = 2000;\r\n\r\n\r\nexport default function ExclusiveGatewaySettings(\r\n    eventBus, elementRegistry,\r\n    elementColors, simulator, simulationStyles) {\r\n\r\n  this._elementRegistry = elementRegistry;\r\n  this._elementColors = elementColors;\r\n  this._simulator = simulator;\r\n  this._simulationStyles = simulationStyles;\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, event => {\r\n    if (event.active) {\r\n      this.setSequenceFlowsDefault();\r\n    } else {\r\n      this.resetSequenceFlows();\r\n    }\r\n  });\r\n}\r\n\r\nExclusiveGatewaySettings.prototype.setSequenceFlowsDefault = function() {\r\n  const exclusiveGateways = this._elementRegistry.filter(element => {\r\n    return is(element, 'bpmn:ExclusiveGateway');\r\n  });\r\n\r\n  for (const gateway of exclusiveGateways) {\r\n    this.setSequenceFlow(gateway);\r\n  }\r\n};\r\n\r\nExclusiveGatewaySettings.prototype.resetSequenceFlows = function() {\r\n\r\n  const exclusiveGateways = this._elementRegistry.filter(element => {\r\n    return is(element, 'bpmn:ExclusiveGateway');\r\n  });\r\n\r\n  exclusiveGateways.forEach(exclusiveGateway => {\r\n    if (exclusiveGateway.outgoing.filter(isSequenceFlow).length) {\r\n      this.resetSequenceFlow(exclusiveGateway);\r\n    }\r\n  });\r\n};\r\n\r\nExclusiveGatewaySettings.prototype.resetSequenceFlow = function(gateway) {\r\n  this._simulator.setConfig(gateway, { activeOutgoing: undefined });\r\n};\r\n\r\nExclusiveGatewaySettings.prototype.setSequenceFlow = function(gateway) {\r\n\r\n  const outgoing = gateway.outgoing.filter(isSequenceFlow);\r\n\r\n  // not forking\r\n  if (outgoing.length < 2) {\r\n    return;\r\n  }\r\n\r\n  const {\r\n    activeOutgoing\r\n  } = this._simulator.getConfig(gateway);\r\n\r\n  let newActiveOutgoing;\r\n\r\n  if (activeOutgoing) {\r\n\r\n    // set next sequence flow\r\n    newActiveOutgoing = getNext(gateway, activeOutgoing);\r\n  } else {\r\n\r\n    // set first sequence flow\r\n    newActiveOutgoing = outgoing[ 0 ];\r\n  }\r\n\r\n  this._simulator.setConfig(gateway, { activeOutgoing: newActiveOutgoing });\r\n\r\n  // set colors\r\n  gateway.outgoing.forEach(outgoing => {\r\n\r\n    const style = outgoing === newActiveOutgoing ? SELECTED_COLOR : NOT_SELECTED_COLOR;\r\n    const stroke = this._simulationStyles.get(style);\r\n\r\n    this._elementColors.add(outgoing, ID, {\r\n      stroke\r\n    }, HIGH_PRIORITY);\r\n  });\r\n};\r\n\r\nExclusiveGatewaySettings.$inject = [\r\n  'eventBus',\r\n  'elementRegistry',\r\n  'elementColors',\r\n  'simulator',\r\n  'simulationStyles'\r\n];", "import {\n  getDi,\n  isAny\n} from 'bpmn-js/lib/util/ModelUtil';\n\nimport {\n  TOGGLE_MODE_EVENT\n} from '../../util/EventHelper';\n\nconst VERY_HIGH_PRIORITY = 50000;\n\n/**\n * @typedef Colors\n * @prop {string} fill\n * @prop {string} stroke\n */\n\n/**\n * @typedef CustomColors\n * @prop {string} fill\n * @prop {string} stroke\n * @prop {number} priority\n */\n\nexport default function ElementColors(elementRegistry, eventBus, graphicsFactory) {\n  this._elementRegistry = elementRegistry;\n  this._eventBus = eventBus;\n  this._graphicsFactory = graphicsFactory;\n\n  this._originalColors = {};\n  this._customColors = {};\n\n  eventBus.on(TOGGLE_MODE_EVENT, VERY_HIGH_PRIORITY, event => {\n    const active = event.active;\n\n    if (active) {\n      this._saveOriginalColors();\n    } else {\n      this._applyOriginalColors();\n\n      this._originalColors = {};\n      this._customColors = {};\n    }\n  });\n\n  eventBus.on('saveXML.start', VERY_HIGH_PRIORITY, () => {\n    this._applyOriginalColors();\n\n    eventBus.once('saveXML.done', () => this._applyCustomColors());\n  });\n}\n\nElementColors.$inject = [\n  'elementRegistry',\n  'eventBus',\n  'graphicsFactory'\n];\n\n/**\n * Add colors to an element. Element will be redrawn with highest priority\n * colors.\n *\n * @param {Object} element\n * @param {string} id\n * @param {Colors} colors\n * @param {number} [priority=1000]\n */\nElementColors.prototype.add = function(element, id, colors, priority = 1000) {\n  let elementColors = this._customColors[ element.id ];\n\n  if (!elementColors) {\n    elementColors = this._customColors[ element.id ] = {};\n  }\n\n  elementColors[ id ] = {\n    ...colors,\n    priority\n  };\n\n  this._applyHighestPriorityColor(element);\n};\n\n\n/**\n * Remove colors from an element. Element will be redrawn with highest priority\n * colors.\n *\n * @param {Object} element\n * @param {string} id\n */\nElementColors.prototype.remove = function(element, id) {\n  const elementColors = this._customColors[ element.id ];\n\n  if (elementColors) {\n    delete elementColors[ id ];\n\n    if (!Object.keys(elementColors)) {\n      delete this._customColors[ element.id ];\n    }\n  }\n\n  this._applyHighestPriorityColor(element);\n};\n\nElementColors.prototype._get = function(element) {\n  const di = getDi(element);\n\n  if (!di) {\n    return undefined;\n  }\n\n  // reading in accordance with bpmn-js@8.7+,\n  // BPMN-in-Color specification\n  if (isLabel(element)) {\n    return {\n      stroke: di.label && di.label.get('color')\n    };\n  } else if (isAny(di, [ 'bpmndi:BPMNEdge', 'bpmndi:BPMNShape' ])) {\n    return {\n      fill: di.get('background-color'),\n      stroke: di.get('border-color')\n    };\n  }\n};\n\nElementColors.prototype._set = function(element, colors = {}) {\n  const {\n    fill,\n    stroke\n  } = colors;\n\n  const di = getDi(element);\n\n  if (!di) {\n    return;\n  }\n\n  // writing in accordance with bpmn-js@8.7+,\n  // BPMN-in-Color specification\n  if (isLabel(element)) {\n    di.label && di.label.set('color', stroke);\n  } else if (isAny(di, [ 'bpmndi:BPMNEdge', 'bpmndi:BPMNShape' ])) {\n    di.set('background-color', fill);\n    di.set('border-color', stroke);\n  }\n\n  this._forceRedraw(element);\n};\n\nElementColors.prototype._saveOriginalColors = function() {\n  this._originalColors = {};\n\n  this._elementRegistry.forEach(element => {\n    this._originalColors[ element.id ] = this._get(element);\n  });\n};\n\nElementColors.prototype._applyOriginalColors = function() {\n  this._elementRegistry.forEach(element => {\n    const colors = this._originalColors[ element.id ];\n\n    if (colors) {\n      this._set(element, colors);\n    }\n  });\n};\n\nElementColors.prototype._applyCustomColors = function() {\n  this._elementRegistry.forEach(element => {\n    const elementColors = this._customColors[ element.id ];\n\n    if (elementColors) {\n      this._set(element, getColorsWithHighestPriority(elementColors));\n    }\n  });\n};\n\nElementColors.prototype._applyHighestPriorityColor = function(element) {\n  const elementColors = this._customColors[ element.id ];\n\n  if (!elementColors) {\n    this._set(element, this._originalColors[ element.id ]);\n\n    return;\n  }\n\n  this._set(element, getColorsWithHighestPriority(elementColors));\n};\n\nElementColors.prototype._forceRedraw = function(element) {\n  const gfx = this._elementRegistry.getGraphics(element);\n\n  const type = element.waypoints ? 'connection' : 'shape';\n\n  this._graphicsFactory.update(type, element, gfx);\n};\n\n\n// helpers /////////////////\n\nfunction isLabel(element) {\n  return 'labelTarget' in element;\n}\n\n/**\n * Get colors with highest priority.\n *\n * @param {Map<string, CustomColors>|undefined} colors\n *\n * @returns {Colors|undefined}\n */\nfunction getColorsWithHighestPriority(colors = {}) {\n  const colorsWithHighestPriority = Object.values(colors).reduce((colorsWithHighestPriority, colors) => {\n    const { priority = 1000 } = colors;\n\n    if (!colorsWithHighestPriority || priority > colorsWithHighestPriority.priority) {\n      return colors;\n    }\n\n    return colorsWithHighestPriority;\n  }, undefined);\n\n  if (colorsWithHighestPriority) {\n    const { priority, ...fillAndStroke } = colorsWithHighestPriority;\n\n    return fillAndStroke;\n  }\n}", "import ElementColors from './ElementColors';\n\nexport default {\n  elementColors: [ 'type', ElementColors ]\n};", "import ExclusiveGatewaySettings from './ExclusiveGatewaySettings';\nimport ElementColorsModule from '../element-colors';\nimport SimulationStylesModule from '../simulation-styles';\n\nexport default {\n  __depends__: [\n    ElementColorsModule,\n    SimulationStylesModule\n  ],\n  exclusiveGatewaySettings: [ 'type', ExclusiveGatewaySettings ]\n};", "import {\n  TOGGLE_MODE_EVENT\n} from '../../util/EventHelper';\n\nconst ID = 'neutral-element-colors';\n\nexport default function NeutralElementColors(\n    eventBus, elementRegistry, elementColors) {\n\n  this._elementRegistry = elementRegistry;\n  this._elementColors = elementColors;\n\n  eventBus.on(TOGGLE_MODE_EVENT, event => {\n    const { active } = event;\n\n    if (active) {\n      this._setNeutralColors();\n    }\n  });\n}\n\nNeutralElementColors.prototype._setNeutralColors = function() {\n  this._elementRegistry.forEach(element => {\n    this._elementColors.add(element, ID, {\n      stroke: '#212121',\n      fill: '#fff'\n    });\n  });\n};\n\nNeutralElementColors.$inject = [\n  'eventBus',\n  'elementRegistry',\n  'elementColors'\n];", "import NeutralElementColors from './NeutralElementColors';\nimport ElementColorsModule from '../element-colors';\n\nexport default {\n  __depends__: [ ElementColorsModule ],\n  __init__: [\n    'neutralElementColors'\n  ],\n  neutralElementColors: [ 'type', NeutralElementColors ]\n};", "import {\n  TOGGLE_MODE_EVENT\n} from '../../util/EventHelper';\n\n\nconst SELECTED_COLOR = '--token-simulation-grey-darken-30';\nconst NOT_SELECTED_COLOR = '--token-simulation-grey-lighten-56';\n\nimport {\n  getBusinessObject,\n  is,\n  isSequenceFlow\n} from '../../simulator/util/ModelUtil';\n\nconst COLOR_ID = 'inclusive-gateway-settings';\n\n\nexport default function InclusiveGatewaySettings(\n    eventBus, elementRegistry,\n    elementColors, simulator, simulationStyles) {\n\n  this._elementRegistry = elementRegistry;\n  this._elementColors = elementColors;\n  this._simulator = simulator;\n  this._simulationStyles = simulationStyles;\n\n  eventBus.on(TOGGLE_MODE_EVENT, event => {\n    if (event.active) {\n      this.setDefaults();\n    } else {\n      this.reset();\n    }\n  });\n}\n\nInclusiveGatewaySettings.prototype.setDefaults = function() {\n  const inclusiveGateways = this._elementRegistry.filter(element => {\n    return is(element, 'bpmn:InclusiveGateway');\n  });\n\n  inclusiveGateways.forEach(inclusiveGateway => {\n    if (inclusiveGateway.outgoing.filter(isSequenceFlow).length > 1) {\n      this._setGatewayDefaults(inclusiveGateway);\n    }\n  });\n};\n\nInclusiveGatewaySettings.prototype.reset = function() {\n  const inclusiveGateways = this._elementRegistry.filter(element => {\n    return is(element, 'bpmn:InclusiveGateway');\n  });\n\n  inclusiveGateways.forEach(inclusiveGateway => {\n    if (inclusiveGateway.outgoing.filter(isSequenceFlow).length > 1) {\n      this._resetGateway(inclusiveGateway);\n    }\n  });\n};\n\nInclusiveGatewaySettings.prototype.toggleSequenceFlow = function(gateway, sequenceFlow) {\n  const activeOutgoing = this._getActiveOutgoing(gateway),\n        defaultFlow = getDefaultFlow(gateway),\n        nonDefaultFlows = getNonDefaultFlows(gateway);\n\n  let newActiveOutgoing;\n  if (activeOutgoing.includes(sequenceFlow)) {\n    newActiveOutgoing = without(activeOutgoing, sequenceFlow);\n  } else {\n    newActiveOutgoing = without(activeOutgoing, defaultFlow).concat(sequenceFlow);\n  }\n\n  // make sure at least one flow is active\n  if (!newActiveOutgoing.length) {\n\n    // default flow if available\n    if (defaultFlow) {\n      newActiveOutgoing = [ defaultFlow ];\n    } else {\n\n      // or another flow which is not the one toggled\n      newActiveOutgoing = [ nonDefaultFlows.find(flow => flow !== sequenceFlow) ];\n    }\n  }\n\n  this._setActiveOutgoing(gateway, newActiveOutgoing);\n};\n\nInclusiveGatewaySettings.prototype._getActiveOutgoing = function(gateway) {\n  const {\n    activeOutgoing\n  } = this._simulator.getConfig(gateway);\n\n  return activeOutgoing;\n};\n\nInclusiveGatewaySettings.prototype._setActiveOutgoing = function(gateway, activeOutgoing) {\n  this._simulator.setConfig(gateway, { activeOutgoing });\n\n  const sequenceFlows = gateway.outgoing.filter(isSequenceFlow);\n\n  // set colors\n  sequenceFlows.forEach(outgoing => {\n\n    const style = (!activeOutgoing || activeOutgoing.includes(outgoing)) ?\n      SELECTED_COLOR : NOT_SELECTED_COLOR;\n    const stroke = this._simulationStyles.get(style);\n\n    this._elementColors.add(outgoing, COLOR_ID, {\n      stroke\n    });\n  });\n};\n\nInclusiveGatewaySettings.prototype._setGatewayDefaults = function(gateway) {\n  const sequenceFlows = gateway.outgoing.filter(isSequenceFlow);\n\n  const defaultFlow = getDefaultFlow(gateway);\n  const nonDefaultFlows = without(sequenceFlows, defaultFlow);\n\n  this._setActiveOutgoing(gateway, nonDefaultFlows);\n};\n\nInclusiveGatewaySettings.prototype._resetGateway = function(gateway) {\n  this._setActiveOutgoing(gateway, undefined);\n};\n\nInclusiveGatewaySettings.$inject = [\n  'eventBus',\n  'elementRegistry',\n  'elementColors',\n  'simulator',\n  'simulationStyles'\n];\n\nfunction getDefaultFlow(gateway) {\n  const defaultFlow = getBusinessObject(gateway).default;\n\n  if (!defaultFlow) {\n    return;\n  }\n\n  return gateway.outgoing.find(flow => {\n    const flowBo = getBusinessObject(flow);\n\n    return flowBo === defaultFlow;\n  });\n}\n\nfunction getNonDefaultFlows(gateway) {\n  const defaultFlow = getDefaultFlow(gateway);\n\n  return gateway.outgoing.filter(flow => {\n    const flowBo = getBusinessObject(flow);\n\n    return flowBo !== defaultFlow;\n  });\n}\n\nfunction without(array, element) {\n  return array.filter(arrayElement => arrayElement !== element);\n}\n", "import InclusiveGatewaySettings from './InclusiveGatewaySettings';\nimport ElementColorsModule from '../element-colors';\nimport SimulationStylesModule from '../simulation-styles';\n\nexport default {\n  __depends__: [\n    ElementColorsModule,\n    SimulationStylesModule\n  ],\n  inclusiveGatewaySettings: [ 'type', InclusiveGatewaySettings ]\n};", "import {\r\n  domify,\r\n  classes as domClasses\r\n} from 'min-dom';\r\n\r\nimport {\r\n  TOGGLE_MODE_EVENT\r\n} from '../../util/EventHelper';\r\n\r\n\r\nexport default function Palette(eventBus, canvas) {\r\n  var self = this;\r\n\r\n  this._canvas = canvas;\r\n\r\n  this.entries = [];\r\n\r\n  this._init();\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\r\n    var active = context.active;\r\n\r\n    if (active) {\r\n      domClasses(self.container).remove('hidden');\r\n    } else {\r\n      domClasses(self.container).add('hidden');\r\n    }\r\n  });\r\n}\r\n\r\nPalette.prototype._init = function() {\r\n  this.container = domify('<div class=\"bts-palette hidden\"></div>');\r\n\r\n  this._canvas.getContainer().appendChild(this.container);\r\n};\r\n\r\nPalette.prototype.addEntry = function(entry, index) {\r\n  var childIndex = 0;\r\n\r\n  this.entries.forEach(function(entry) {\r\n    if (index >= entry.index) {\r\n      childIndex++;\r\n    }\r\n  });\r\n\r\n  this.container.insertBefore(entry, this.container.childNodes[childIndex]);\r\n\r\n  this.entries.push({\r\n    entry: entry,\r\n    index: index\r\n  });\r\n};\r\n\r\nPalette.$inject = [ 'eventBus', 'canvas' ];", "import Palette from './Palette';\n\nexport default {\n  __init__: [\n    'tokenSimulationPalette'\n  ],\n  tokenSimulationPalette: [ 'type', Palette ]\n};", "import SimulatorModule from './simulator';\nimport AnimationModule from './animation';\nimport ColoredScopesModule from './features/colored-scopes';\nimport ContextPadsModule from './features/context-pads';\nimport SimulationStateModule from './features/simulation-state';\nimport ShowScopesModule from './features/show-scopes';\nimport LogModule from './features/log';\nimport ElementSupportModule from './features/element-support';\nimport PauseSimulationModule from './features/pause-simulation';\nimport ResetSimulationModule from './features/reset-simulation';\nimport TokenCountModule from './features/token-count';\nimport SetAnimationSpeedModule from './features/set-animation-speed';\n\nimport ExclusiveGatewaySettingsModule from './features/exclusive-gateway-settings';\nimport NeutralElementColors from './features/neutral-element-colors';\nimport InclusiveGatewaySettingsModule from './features/inclusive-gateway-settings';\nimport TokenSimulationPaletteModule from './features/palette';\n\nexport default {\n  __depends__: [\n    SimulatorModule,\n    AnimationModule,\n    ColoredScopesModule,\n    ContextPadsModule,\n    SimulationStateModule,\n    ShowScopesModule,\n    LogModule,\n    ElementSupportModule,\n    PauseSimulationModule,\n    ResetSimulationModule,\n    TokenCountModule,\n    SetAnimationSpeedModule,\n    ExclusiveGatewaySettingsModule,\n    NeutralElementColors,\n    InclusiveGatewaySettingsModule,\n    TokenSimulationPaletteModule\n  ]\n};", "import {\r\n  TOGGLE_MODE_EVENT\r\n} from '../../util/EventHelper';\r\n\r\nconst HIGH_PRIORITY = 10001;\r\n\r\n\r\nexport default function DisableModeling(\r\n    eventBus,\r\n    contextPad,\r\n    dragging,\r\n    directEditing,\r\n    editorActions,\r\n    modeling,\r\n    palette) {\r\n\r\n  let modelingDisabled = false;\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, HIGH_PRIORITY, event => {\r\n\r\n    modelingDisabled = event.active;\r\n\r\n    if (modelingDisabled) {\r\n      directEditing.cancel();\r\n      dragging.cancel();\r\n    }\r\n\r\n    palette._update();\r\n  });\r\n\r\n  function intercept(obj, fnName, cb) {\r\n    const fn = obj[fnName];\r\n    obj[fnName] = function() {\r\n      return cb.call(this, fn, arguments);\r\n    };\r\n  }\r\n\r\n  function ignoreIfModelingDisabled(obj, fnName) {\r\n    intercept(obj, fnName, function(fn, args) {\r\n      if (modelingDisabled) {\r\n        return;\r\n      }\r\n\r\n      return fn.apply(this, args);\r\n    });\r\n  }\r\n\r\n  function throwIfModelingDisabled(obj, fnName) {\r\n    intercept(obj, fnName, function(fn, args) {\r\n      if (modelingDisabled) {\r\n        throw new Error('model is read-only');\r\n      }\r\n\r\n      return fn.apply(this, args);\r\n    });\r\n  }\r\n\r\n  ignoreIfModelingDisabled(dragging, 'init');\r\n\r\n  ignoreIfModelingDisabled(directEditing, 'activate');\r\n\r\n  ignoreIfModelingDisabled(dragging, 'init');\r\n\r\n  ignoreIfModelingDisabled(directEditing, 'activate');\r\n\r\n  throwIfModelingDisabled(modeling, 'moveShape');\r\n  throwIfModelingDisabled(modeling, 'updateAttachment');\r\n  throwIfModelingDisabled(modeling, 'moveElements');\r\n  throwIfModelingDisabled(modeling, 'moveConnection');\r\n  throwIfModelingDisabled(modeling, 'layoutConnection');\r\n  throwIfModelingDisabled(modeling, 'createConnection');\r\n  throwIfModelingDisabled(modeling, 'createShape');\r\n  throwIfModelingDisabled(modeling, 'createLabel');\r\n  throwIfModelingDisabled(modeling, 'appendShape');\r\n  throwIfModelingDisabled(modeling, 'removeElements');\r\n  throwIfModelingDisabled(modeling, 'distributeElements');\r\n  throwIfModelingDisabled(modeling, 'removeShape');\r\n  throwIfModelingDisabled(modeling, 'removeConnection');\r\n  throwIfModelingDisabled(modeling, 'replaceShape');\r\n  throwIfModelingDisabled(modeling, 'pasteElements');\r\n  throwIfModelingDisabled(modeling, 'alignElements');\r\n  throwIfModelingDisabled(modeling, 'resizeShape');\r\n  throwIfModelingDisabled(modeling, 'createSpace');\r\n  throwIfModelingDisabled(modeling, 'updateWaypoints');\r\n  throwIfModelingDisabled(modeling, 'reconnectStart');\r\n  throwIfModelingDisabled(modeling, 'reconnectEnd');\r\n\r\n  intercept(editorActions, 'trigger', function(fn, args) {\r\n    const action = args[0];\r\n\r\n    if (modelingDisabled && isAnyAction([\r\n      'undo',\r\n      'redo',\r\n      'copy',\r\n      'paste',\r\n      'removeSelection',\r\n      'spaceTool',\r\n      'lassoTool',\r\n      'globalConnectTool',\r\n      'distributeElements',\r\n      'alignElements',\r\n      'directEditing',\r\n    ], action)) {\r\n      return;\r\n    }\r\n\r\n    return fn.apply(this, args);\r\n  });\r\n}\r\n\r\nDisableModeling.$inject = [\r\n  'eventBus',\r\n  'contextPad',\r\n  'dragging',\r\n  'directEditing',\r\n  'editorActions',\r\n  'modeling',\r\n  'palette'\r\n];\r\n\r\n\r\n// helpers //////////\r\n\r\nfunction isAnyAction(actions, action) {\r\n  return actions.indexOf(action) > -1;\r\n}", "import DisableModeling from './DisableModeling';\n\nexport default {\n  __init__: [\n    'disableModeling'\n  ],\n  disableModeling: [ 'type', DisableModeling ]\n};", "import {\r\n  domify,\r\n  classes as domClasses,\r\n  event as domEvent,\r\n  query as domQuery\r\n} from 'min-dom';\r\n\r\nimport {\r\n  TOGGLE_MODE_EVENT\r\n} from '../../../util/EventHelper';\r\n\r\nimport {\r\n  ToggleOffIcon,\r\n  ToggleOnIcon\r\n} from '../../../icons';\r\n\r\n\r\nexport default function ToggleMode(\r\n    eventBus, canvas, selection,\r\n    contextPad) {\r\n\r\n  this._eventBus = eventBus;\r\n  this._canvas = canvas;\r\n  this._selection = selection;\r\n  this._contextPad = contextPad;\r\n\r\n  this._active = false;\r\n\r\n  eventBus.on('import.parse.start', () => {\r\n\r\n    if (this._active) {\r\n      this.toggleMode(false);\r\n\r\n      eventBus.once('import.done', () => {\r\n        this.toggleMode(true);\r\n      });\r\n    }\r\n  });\r\n\r\n  eventBus.on('diagram.init', () => {\r\n    this._canvasParent = this._canvas.getContainer().parentNode;\r\n    this._palette = domQuery('.djs-palette', this._canvas.getContainer());\r\n\r\n    this._init();\r\n  });\r\n}\r\n\r\nToggleMode.prototype._init = function() {\r\n  this._container = domify(`\r\n    <div class=\"bts-toggle-mode\">\r\n      Token Simulation <span class=\"bts-toggle\">${ ToggleOffIcon() }</span>\r\n    </div>\r\n  `);\r\n\r\n  domEvent.bind(this._container, 'click', () => this.toggleMode());\r\n\r\n  this._canvas.getContainer().appendChild(this._container);\r\n};\r\n\r\nToggleMode.prototype.toggleMode = function(active = !this._active) {\r\n\r\n  if (active === this._active) {\r\n    return;\r\n  }\r\n\r\n  if (active) {\r\n    this._container.innerHTML = `Token Simulation <span class=\"bts-toggle\">${ ToggleOnIcon() }</span>`;\r\n\r\n    domClasses(this._canvasParent).add('simulation');\r\n    domClasses(this._palette).add('hidden');\r\n  } else {\r\n    this._container.innerHTML = `Token Simulation <span class=\"bts-toggle\">${ ToggleOffIcon() }</span>`;\r\n\r\n    domClasses(this._canvasParent).remove('simulation');\r\n    domClasses(this._palette).remove('hidden');\r\n\r\n    const elements = this._selection.get();\r\n\r\n    if (elements.length === 1) {\r\n      this._contextPad.open(elements[0]);\r\n    }\r\n  }\r\n\r\n  this._eventBus.fire(TOGGLE_MODE_EVENT, {\r\n    active\r\n  });\r\n\r\n  this._active = active;\r\n};\r\n\r\nToggleMode.$inject = [\r\n  'eventBus',\r\n  'canvas',\r\n  'selection',\r\n  'contextPad'\r\n];", "import ToggleMode from './ToggleMode';\n\nexport default {\n  __init__: [\n    'toggleMode'\n  ],\n  toggleMode: [ 'type', ToggleMode ]\n};", "import { TOGGLE_MODE_EVENT } from '../../util/EventHelper';\n\nexport default function EditorActions(\n    eventBus,\n    toggleMode,\n    pauseSimulation,\n    resetSimulation,\n    editorActions,\n    injector\n) {\n  var active = false;\n\n  editorActions.register({\n    toggleTokenSimulation: function() {\n      toggleMode.toggleMode();\n    }\n  });\n\n  editorActions.register({\n    togglePauseTokenSimulation: function() {\n      active && pauseSimulation.toggle();\n    }\n  });\n\n  editorActions.register({\n    resetTokenSimulation: function() {\n      active && resetSimulation.resetSimulation();\n    }\n  });\n\n  const log = injector.get('log', false);\n\n  log && editorActions.register({\n    toggleTokenSimulationLog: function() {\n      log.toggle();\n    }\n  });\n\n  eventBus.on(TOGGLE_MODE_EVENT, (event) => {\n    active = event.active;\n  });\n}\n\nEditorActions.$inject = [\n  'eventBus',\n  'toggleMode',\n  'pauseSimulation',\n  'resetSimulation',\n  'editorActions',\n  'injector'\n];", "import EditorActions from './EditorActions';\n\nexport default {\n  __init__: [\n    'tokenSimulationEditorActions'\n  ],\n  tokenSimulationEditorActions: [ 'type', EditorActions ]\n};", "import {\n  TOGGLE_MODE_EVENT\n} from '../../util/EventHelper';\n\nconst VERY_HIGH_PRIORITY = 10000;\n\n\nexport default function KeyboardBindings(eventBus, injector) {\n\n  var editorActions = injector.get('editorActions', false),\n      keyboard = injector.get('keyboard', false);\n\n  if (!keyboard || !editorActions) {\n    return;\n  }\n\n\n  var isActive = false;\n\n\n  function handleKeyEvent(keyEvent) {\n    if (isKey([ 't', 'T' ], keyEvent)) {\n      editorActions.trigger('toggleTokenSimulation');\n\n      return true;\n    }\n\n    if (!isActive) {\n      return;\n    }\n\n    if (isKey([ 'l', 'L' ], keyEvent)) {\n      editorActions.trigger('toggleTokenSimulationLog');\n\n      return true;\n    }\n\n    // see https://developer.mozilla.org/de/docs/Web/API/KeyboardEvent/key/Key_Values#Whitespace_keys\n    if (isKey([ ' ', 'Spacebar' ], keyEvent)) {\n      editorActions.trigger('togglePauseTokenSimulation');\n\n      return true;\n    }\n\n    if (isKey([ 'r', 'R' ], keyEvent)) {\n      editorActions.trigger('resetTokenSimulation');\n\n      return true;\n    }\n  }\n\n\n  eventBus.on('keyboard.init', function() {\n\n    keyboard.addListener(VERY_HIGH_PRIORITY, function(event) {\n      var keyEvent = event.keyEvent;\n\n      handleKeyEvent(keyEvent);\n    });\n\n  });\n\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\n    var active = context.active;\n\n    if (active) {\n      isActive = true;\n    } else {\n      isActive = false;\n    }\n  });\n\n}\n\nKeyboardBindings.$inject = [ 'eventBus', 'injector' ];\n\n\n// helpers //////////\n\nfunction isKey(keys, event) {\n  return keys.indexOf(event.key) > -1;\n}", "import KeyboardBindings from './KeyboardBindings';\n\nexport default {\n  __init__: [\n    'tokenSimulationKeyboardBindings'\n  ],\n  tokenSimulationKeyboardBindings: [ 'type', KeyboardBindings ]\n};", "import BaseModule from './base';\nimport DisableModelingModule from './features/disable-modeling';\n\nimport ToggleModeModule from './features/toggle-mode/modeler';\nimport TokenSimulationEditorActionsModule from './features/editor-actions';\nimport TokenSimulationKeyboardBindingsModule from './features/keyboard-bindings';\n\nexport default {\n  __depends__: [\n    BaseModule,\n    DisableModelingModule,\n    ToggleModeModule,\n    TokenSimulationEditorActionsModule,\n    TokenSimulationKeyboardBindingsModule\n  ]\n};"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAGC,KAAC,SAAS,MAAM,SAAS;AAGxB,UAAI,OAAO,YAAY,UAAU;AAC/B,YAAIA,eAAc,QAAQ;AAG1B,YAAI,OAAO,WAAW,YAAY,UAAU,OAAO,SAAS;AAC1D,oBAAU,OAAO,UAAUA;AAAA,QAC7B;AAGA,gBAAQ,cAAcA;AAAA,MAGxB,WAAW,OAAO,WAAW,cAAc,OAAO,KAAK;AACrD,eAAO,CAAC,GAAG,OAAO;AAAA,MAGpB,OAAO;AACL,aAAK,cAAc,QAAQ;AAAA,MAC7B;AAAA,IAEF,GAAE,SAAM,WAAW;AAGjB,UAAI,OAAO;AAGX,UAAI,kBAAkB,CAAC;AAGvB,sBAAgB;AAGhB,UAAI,cAAc,CAAC;AAEnB,UAAIA,eAAc,SAAU,SAAS;AAEnC,kBAAU,WAAW,CAAC;AAItB,YAAI,QAAQ,SAAS,UAAa,QAAQ,SAAS,QAAQ,QAAQ,SAAS,SAAS,QAAQ,MAAM,EAAE,GAAG;AACtG,iBAAO,QAAQ;AAAA,QAGjB,WAAW,OAAO,QAAQ,SAAS,UAAU;AAC3C,iBAAO,gBAAgB,QAAQ,IAAI;AAAA,QAGrC,WAAW,QAAQ,SAAS,UAAa,QAAQ,SAAS,MAAM;AAC9D,gBAAM,IAAI,UAAU,6CAA6C;AAAA,QAGnE,OAAO;AACL,iBAAO;AAAA,QACT;AAEA,YAAI,GAAE,GAAE;AAGR,YAAI,QAAQ,UAAU,QAAQ,QAAQ,UAAU,QAAW;AAEzD,cAAI,cAAc,QAAQ,OACtB,SAAS,CAAC;AAEd,mBAAS,IAAI,GAAG,IAAI,QAAQ,OAAO,KAAK;AACtC,wBAAY,KAAK,KAAK;AAAA,UACtB;AACF,kBAAQ,QAAQ;AAEhB,iBAAO,cAAc,OAAO,QAAQ;AAElC,gBAAI,QAAQA,aAAY,OAAO;AAE/B,gBAAI,SAAS,MAAM;AACjB,sBAAQ,OAAO;AAAA,YACjB;AAEA,mBAAO,KAAK,KAAK;AAAA,UACnB;AAEA,kBAAQ,QAAQ;AAEhB,iBAAO;AAAA,QACT;AAGA,YAAI,QAAQ,OAAO;AAGnB,YAAI,eAAe,GAAG,OAAO;AAG7B,YAAI,eAAe,GAAG,GAAG,OAAO;AAGhC,eAAO,UAAU,CAAC,GAAE,GAAE,CAAC,GAAG,OAAO;AAAA,MACnC;AAEA,eAAS,QAAQ,SAAS;AACxB,YAAI,YAAY,SAAS,GAAG;AAC1B,cAAI,WAAW,gBAAgB,QAAQ,GAAG;AAE1C,cAAI,MAAM,aAAa,QAAQ;AAG/B,cAAI,QAAQ,SAAS,CAAC,IAAI,SAAS,CAAC,KAAK,YAAY;AAErD,cAAI,IAAI,UAAU,MAAM,SAAS,CAAC,KAAK,IAAI;AAG3C,cAAI,YAAY,CAAC,MAAM,MAAM;AAC3B,iBAAK,IAAI,KAAK,YAAY;AAAA,UAC5B,OACK;AACH,wBAAY,CAAC,IAAI;AAAA,UACd;AAEL,cAAI,OAAO,SAAS,CAAC,IAAI,IAAI,QAAQ,KACjC,OAAO,SAAS,CAAC,KAAK,IAAI,KAAK,QAAQ;AAE3C,qBAAW,CAAC,KAAK,GAAG;AAEpB,gBAAM,aAAa,QAAQ;AAE3B,cAAI,MAAM,GAAG;AAAC,kBAAM,MAAM;AAAA,UAAI;AAC9B,iBAAO;AAAA,QACT,OACK;AACH,cAAI,WAAW,YAAY,QAAQ,GAAG;AAEtC,gBAAM,aAAa,QAAQ;AAG3B,cAAI,MAAM,GAAG;AACX,kBAAM,MAAM;AAAA,UACd;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,eAAgB,KAAK,SAAS;AAErC,YAAI,QAAQ,QAAQ,cAAc;AAChC,iBAAO;AAAA,QACT;AAEA,YAAI,QAAQ,eAAe,UAAU;AACnC,iBAAO,aAAa,CAAC,GAAE,GAAG,CAAC;AAAA,QAC7B;AAEA,YAAI,kBAAkB,mBAAmB,GAAG;AAE5C,YAAI,OAAO,gBAAgB,CAAC,GACxB,OAAO,gBAAgB,CAAC;AAE5B,gBAAQ,QAAQ,YAAY;AAAA,UAE1B,KAAK;AACH,mBAAO;AACP;AAAA,UAEF,KAAK;AACH,mBAAO,OAAO;AACd;AAAA,UAEF,KAAK;AACH,mBAAO;AACP;AAAA,QACL;AAEC,eAAO,aAAa,CAAC,MAAM,IAAI,CAAC;AAAA,MAElC;AAEA,eAAS,eAAgB,GAAG,GAAG,SAAS;AAEtC,YAAI,OAAO,qBAAqB,GAAG,CAAC,GAChC,OAAO;AAEX,gBAAQ,QAAQ,YAAY;AAAA,UAE1B,KAAK;AACH,mBAAO,OAAO;AACd;AAAA,UAEF,KAAK;AACH,oBAAQ,OAAO,QAAM;AACrB;AAAA,UAEF,KAAK;AACH,mBAAO;AACP,mBAAO;AACP;AAAA,QACJ;AAEA,eAAO,aAAa,CAAC,MAAM,IAAI,CAAC;AAAA,MAClC;AAEA,eAAS,UAAW,KAAK,SAAS;AAEhC,gBAAQ,QAAQ,QAAQ;AAAA,UAEtB,KAAK;AACH,mBAAO;AAAA,UAET,KAAK;AACH,mBAAO,SAAS,GAAG;AAAA,UAErB,KAAK;AACH,gBAAI,MAAM,SAAS,GAAG;AACtB,mBAAO,SAAO,IAAI,CAAC,IAAE,OAAK,IAAI,CAAC,IAAE,QAAM,IAAI,CAAC,IAAE;AAAA,UAEhD,KAAK;AACH,gBAAI,WAAW,SAAS,GAAG;AAC3B,gBAAI,QAAQ,QAAQ,SAAS,KAAK,OAAO;AACzC,mBAAO,UAAQ,SAAS,CAAC,IAAE,OAAK,SAAS,CAAC,IAAE,QAAM,SAAS,CAAC,IAAE,QAAQ,QAAQ;AAAA,UAEhF,KAAK;AACH,mBAAO,SAAS,GAAG;AAAA,UAErB,KAAK;AACH,gBAAI,MAAM,SAAS,GAAG;AACtB,mBAAO,SAAS,IAAI,KAAK,IAAI,IAAI;AAAA,UAEnC,KAAK;AACH,gBAAI,WAAW,SAAS,GAAG;AAC3B,gBAAI,QAAQ,QAAQ,SAAS,KAAK,OAAO;AACzC,mBAAO,UAAU,SAAS,KAAK,IAAI,IAAI,OAAO,QAAQ;AAAA,UAExD;AACE,mBAAO,SAAS,GAAG;AAAA,QACvB;AAAA,MAEF;AAEA,eAAS,qBAAqB,GAAG,GAAG;AAElC,YAAI,cAAc,aAAa,CAAC,EAAE;AAElC,iBAAS,IAAI,GAAG,IAAI,YAAY,SAAS,GAAG,KAAK;AAE/C,cAAI,KAAK,YAAY,CAAC,EAAE,CAAC,GACrB,KAAK,YAAY,CAAC,EAAE,CAAC;AAEzB,cAAI,KAAK,YAAY,IAAE,CAAC,EAAE,CAAC,GACvB,KAAK,YAAY,IAAE,CAAC,EAAE,CAAC;AAE3B,cAAI,KAAK,MAAM,KAAK,IAAI;AAErB,gBAAI,KAAK,KAAK,OAAK,KAAK,KACpB,IAAI,KAAK,IAAE;AAEf,mBAAO,IAAE,IAAI;AAAA,UAChB;AAAA,QAEF;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,YAAa,YAAY;AAEhC,YAAI,OAAO,SAAS,UAAU,MAAM,UAAU;AAE5C,cAAI,SAAS,SAAS,UAAU;AAEhC,cAAI,SAAS,OAAO,SAAS,GAAG;AAC9B,mBAAO,CAAC,QAAQ,MAAM;AAAA,UACxB;AAAA,QAEF;AAEA,YAAI,OAAO,eAAe,UAAU;AAElC,cAAI,gBAAgB,UAAU,GAAG;AAC/B,gBAAI,QAAQ,gBAAgB,UAAU;AACtC,gBAAI,MAAM,UAAU;AAAC,qBAAO,MAAM;AAAA,YAAS;AAAA,UAC7C,WAAW,WAAW,MAAM,gCAAgC,GAAG;AAC7D,gBAAI,MAAM,SAAS,UAAU,EAAE,CAAC;AAChC,mBAAO,CAAE,KAAK,GAAI;AAAA,UACpB;AAAA,QACF;AAEA,eAAO,CAAC,GAAE,GAAG;AAAA,MAEf;AAEA,eAAS,mBAAoB,KAAK;AAChC,eAAO,aAAa,GAAG,EAAE;AAAA,MAC3B;AAEA,eAAS,aAAc,KAAK;AAG1B,YAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,iBAAM;AAAA,QACR;AAEA,iBAAS,aAAa,iBAAiB;AACpC,cAAI,QAAQ,gBAAgB,SAAS;AACrC,cAAI,MAAM,YACN,OAAO,MAAM,SAAS,CAAC,KACvB,OAAO,MAAM,SAAS,CAAC,GAAG;AAC3B,mBAAO,gBAAgB,SAAS;AAAA,UACnC;AAAA,QACH;AAAE,eAAO;AAAA,MACX;AAEA,eAAS,aAAc,OAAO;AAC5B,YAAI,SAAS,MAAM;AAEjB,cAAI,eAAe;AACnB,cAAI,IAAE,KAAK,OAAO;AAClB,eAAK;AACL,eAAK;AACL,iBAAO,KAAK,MAAM,MAAM,CAAC,IAAI,KAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,EAAE;AAAA,QAC1D,OAAO;AAEL,cAAI,MAAM,MAAM,CAAC,KAAK;AACtB,cAAI,MAAM,MAAM,CAAC,KAAK;AACtB,kBAAQ,OAAO,OAAO,SAAS;AAC/B,cAAI,MAAM,OAAO;AACjB,iBAAO,KAAK,MAAM,MAAM,OAAO,MAAM,IAAI;AAAA,QAC/C;AAAA,MACE;AAEA,eAAS,SAAU,KAAI;AAErB,YAAI,MAAM,SAAS,GAAG;AAEtB,iBAAS,eAAe,GAAG;AACvB,cAAIC,OAAM,EAAE,SAAS,EAAE;AACvB,iBAAOA,KAAI,UAAU,IAAI,MAAMA,OAAMA;AAAA,QACzC;AAEA,YAAI,MAAM,MAAM,eAAe,IAAI,CAAC,CAAC,IAAI,eAAe,IAAI,CAAC,CAAC,IAAI,eAAe,IAAI,CAAC,CAAC;AAEvF,eAAO;AAAA,MAET;AAEA,eAAS,YAAa,MAAM,UAAU,aAAa;AAEjD,YAAI,OAAO,YAAY,CAAC,EAAE,CAAC,GACvB,OAAO,YAAY,YAAY,SAAS,CAAC,EAAE,CAAC,GAE5C,OAAO,YAAY,YAAY,SAAS,CAAC,EAAE,CAAC,GAC5C,OAAO,YAAY,CAAC,EAAE,CAAC;AAE3B,wBAAgB,IAAI,IAAI;AAAA,UACtB;AAAA,UACA;AAAA,UACA,iBAAiB,CAAC,MAAM,IAAI;AAAA,UAC5B,iBAAiB,CAAC,MAAM,IAAI;AAAA,QAC9B;AAAA,MAEF;AAEA,eAAS,kBAAmB;AAE1B;AAAA,UACE;AAAA,UACA;AAAA,UACA,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,CAAC,CAAC;AAAA,QAChB;AAEA;AAAA,UACE;AAAA,UACA,CAAC,KAAI,EAAE;AAAA,UACP,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,CAAC;AAAA,QAC5E;AAEA;AAAA,UACE;AAAA,UACA,CAAC,IAAG,EAAE;AAAA,UACN,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,CAAC;AAAA,QAC5D;AAEA;AAAA,UACE;AAAA,UACA,CAAC,IAAG,EAAE;AAAA,UACN,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,CAAC;AAAA,QACpE;AAEA;AAAA,UACE;AAAA,UACA,CAAC,IAAG,GAAG;AAAA,UACP,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,CAAC;AAAA,QACpE;AAEA;AAAA,UACE;AAAA,UACA,CAAC,KAAK,GAAG;AAAA,UACT,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,CAAC;AAAA,QAC5E;AAEA;AAAA,UACE;AAAA,UACA,CAAC,KAAK,GAAG;AAAA,UACT,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,CAAC;AAAA,QAC5E;AAEA;AAAA,UACE;AAAA,UACA,CAAC,KAAK,GAAG;AAAA,UACT,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,CAAC;AAAA,QAC5D;AAAA,MAEF;AAEA,eAAS,SAAU,KAAK;AAItB,YAAI,IAAI,IAAI,CAAC;AACb,YAAI,MAAM,GAAG;AAAC,cAAI;AAAA,QAAE;AACpB,YAAI,MAAM,KAAK;AAAC,cAAI;AAAA,QAAI;AAGxB,YAAI,IAAE;AACN,YAAI,IAAI,IAAI,CAAC,IAAE,KACX,IAAI,IAAI,CAAC,IAAE;AAEf,YAAI,MAAM,KAAK,MAAM,IAAE,CAAC,GACtB,IAAI,IAAI,IAAI,KACZ,IAAI,KAAK,IAAI,IACb,IAAI,KAAK,IAAI,IAAE,IACf,IAAI,KAAK,KAAK,IAAI,KAAG,IACrB,IAAI,KACJ,IAAI,KACJ,IAAI;AAEN,gBAAO,KAAK;AAAA,UACV,KAAK;AAAG,gBAAI;AAAG,gBAAI;AAAG,gBAAI;AAAI;AAAA,UAC9B,KAAK;AAAG,gBAAI;AAAG,gBAAI;AAAG,gBAAI;AAAI;AAAA,UAC9B,KAAK;AAAG,gBAAI;AAAG,gBAAI;AAAG,gBAAI;AAAI;AAAA,UAC9B,KAAK;AAAG,gBAAI;AAAG,gBAAI;AAAG,gBAAI;AAAI;AAAA,UAC9B,KAAK;AAAG,gBAAI;AAAG,gBAAI;AAAG,gBAAI;AAAI;AAAA,UAC9B,KAAK;AAAG,gBAAI;AAAG,gBAAI;AAAG,gBAAI;AAAI;AAAA,QAChC;AAEA,YAAI,SAAS,CAAC,KAAK,MAAM,IAAE,GAAG,GAAG,KAAK,MAAM,IAAE,GAAG,GAAG,KAAK,MAAM,IAAE,GAAG,CAAC;AACrE,eAAO;AAAA,MACT;AAEA,eAAS,SAAU,KAAK;AACtB,cAAM,IAAI,QAAQ,MAAM,EAAE;AAC1B,cAAM,IAAI,WAAW,IAAI,IAAI,QAAQ,QAAQ,MAAM,IAAI;AAEvD,YAAI,MAAM,SAAS,IAAI,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI,KACrC,QAAQ,SAAS,IAAI,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI,KACzC,OAAO,SAAS,IAAI,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI;AAE9C,YAAI,OAAO,KAAK,IAAI,KAAK,OAAO,IAAI,GAC9B,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,IAAI,GACxC,aAAa,OAAQ,QAAQ,OAAQ;AAE3C,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAK,mBAAO,CAAE,OAAQ,QAAQ,QAAQ,QAAS,MAAM,GAAG,YAAY,IAAK;AAAA,UAC9E,KAAK;AAAO,mBAAO,CAAE,OAAQ,OAAO,OAAO,QAAS,MAAM,GAAG,YAAY,IAAK;AAAA,UAC9E,KAAK;AAAM,mBAAO,CAAE,OAAQ,MAAM,SAAS,QAAS,MAAM,GAAG,YAAY,IAAK;AAAA,QAChF;AAAA,MACF;AAEA,eAAS,SAAU,KAAK;AACtB,YAAI,IAAI,IAAI,CAAC,GACX,IAAI,IAAI,CAAC,IAAE,KACX,IAAI,IAAI,CAAC,IAAE,KACX,KAAK,IAAE,KAAG;AAEZ,eAAO;AAAA,UACL;AAAA,UACA,KAAK,MAAM,IAAE,KAAK,IAAE,IAAI,IAAI,IAAE,KAAK,GAAK,IAAI;AAAA,UAC5C,IAAE,IAAI;AAAA,QACR;AAAA,MACF;AAEA,eAAS,gBAAiB,QAAQ;AAChC,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,KAAK;AACxC,cAAI,SAAS,OAAO;AAAkB;AACtC,mBAAS,OAAO,WAAW,CAAC;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AAGA,eAAS,gBAAgB,UACzB;AAAE,YAAI,CAAC,MAAM,QAAQ,GAAG;AACtB,cAAI,SAAS,SAAS,QAAQ;AAE9B,cAAI,SAAS,OAAO,SAAS,GAAG;AAC9B,mBAAO,aAAa,QAAQ,EAAE;AAAA,UAChC;AAAA,QACF,WACW,OAAO,aAAa,UAAU;AAErC,cAAI,gBAAgB,QAAQ,GAAG;AAC7B,gBAAI,QAAQ,gBAAgB,QAAQ;AAEpC,gBAAI,MAAM,UAAU;AAClB,qBAAO,MAAM;AAAA,YAChB;AAAA,UACH,WAAW,SAAS,MAAM,gCAAgC,GAAG;AACzD,gBAAI,MAAM,SAAS,QAAQ,EAAE,CAAC;AAC9B,mBAAO,aAAa,GAAG,EAAE;AAAA,UAC7B;AAAA,QACF;AAEE,eAAO,CAAC,GAAE,GAAG;AAAA,MACjB;AACE,aAAOD;AAAA,IACT,CAAC;AAAA;AAAA;;;ACrgBD,IAAM,YAAY;AAClB,IAAM,UAAU,KAAK;AACrB,IAAM,SAAS,KAAK;AACpB,IAAM,QAAQ,KAAK;AACnB,IAAM,YAAY,KAAK;AACvB,IAAM,SAAS,KAAK;AACpB,IAAM,aAAa,KAAK;AACxB,IAAM,WAAW,KAAK;AACtB,IAAM,YAAY,KAAK;AACvB,IAAM,cAAc,KAAK;AAEzB,IAAM,SAAS,YAAY,UAAU;AACrC,IAAM,WAAW,YAAY;AAEtB,IAAM,cAAc,OAAO,OAAO;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;ACvBD,IAAM,OAAO,CAAC;AAEd,SAAS,kBAAkB,OAAO,QAAQ;AACxC,QAAM,IAAI,MAAM,uBAAuB,MAAM,IAAI,OAAO,MAAM,EAAE;AAClE;AAEA,SAAS,OAAO,OAAO,MAAM;AAC3B,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EActB,YAAY,MAAM,QAAQ;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,GAAG;AACN,SAAK,OAAO;AAQZ,SAAK,SAAS;AAEd,SAAK,SAAS,OAAO,OAAO,IAAI;AAChC,SAAK,eAAe,OAAO,aAAa,IAAI;AAC5C,SAAK,UAAU,OAAO,QAAQ,IAAI;AAClC,SAAK,YAAY,OAAO,UAAU,IAAI;AACtC,SAAK,WAAW,OAAO,SAAS,IAAI;AACpC,SAAK,QAAQ,OAAO,MAAM,IAAI;AAC9B,SAAK,aAAa,OAAO,WAAW,IAAI;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACd,YAAQ,KAAK,SAAS,WAAW;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK,aAAa,kBAAkB,MAAM,UAAU;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK,YAAY,kBAAkB,MAAM,SAAS;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,WAAO,KAAK,WAAW,kBAAkB,MAAM,QAAQ;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,WAAO,KAAK,SAAS,kBAAkB,MAAM,MAAM;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK,cAAc,kBAAkB,MAAM,WAAW;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK,gBAAgB,kBAAkB,MAAM,aAAa;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,WAAO,KAAK,UAAU,kBAAkB,MAAM,OAAO;AAAA,EACvD;AACF;AAEA,IAAME,UAAS,IAAI,WAAW,UAAU,YAAY,YAAY,YAAY,MAAM;AAElF,IAAMC,cAAa,IAAI,WAAW,cAAc,YAAY,YAAY,YAAY,UAAU;AAE9F,IAAMC,aAAY,IAAI,WAAW,aAAa,YAAY,YAAY,YAAY,SAAS;AAE3F,IAAM,cAAc,IAAI,WAAW,eAAe,YAAY,SAAS,YAAY,YAAY;AAAA,EAC7F,SAASD;AACX,CAAC;AAED,IAAM,YAAY,IAAI,WAAW,aAAa,YAAY,SAAS,YAAY,SAAS,YAAY,UAAU;AAAA,EAC5G,SAASD;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AACb,CAAC;AAED,IAAM,aAAa,IAAI,WAAW,cAAc,YAAY,SAAS,YAAY,WAAW;AAAA,EAC1F,SAASE;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AACb,CAAC;AAED,IAAM,UAAU,IAAI,WAAW,WAAW,YAAY,SAAS,YAAY,QAAQ;AAAA,EACjF,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAASF;AAAA,EACT,WAAW;AACb,CAAC;AAED,IAAM,sBAAsB,IAAI,WAAW,uBAAuB,YAAY,SAAS,YAAY,QAAQ;AAAA,EACzG,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAASA;AACX,CAAC;AAED,IAAM,wBAAwB,IAAI,WAAW,yBAAyB,YAAY,QAAQ,YAAY,WAAW;AAAA,EAC/G,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAASE;AAAA,EACT,aAAa;AACf,CAAC;AAED,IAAM,yBAAyB,IAAI,WAAW,0BAA0B,YAAY,SAAS,YAAY,WAAW;AAAA,EAClH,SAAS;AAAA,EACT,WAAW;AAAA,EACX,aAAa;AACf,CAAC;AAED,IAAM,sBAAsB,IAAI,WAAW,uBAAuB,YAAY,UAAU,YAAY,aAAa;AAAA,EAC/G,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,WAAW;AACb,CAAC;AAED,IAAMC,WAAU,IAAI,WAAW,WAAW,YAAY,SAAS;AAAA,EAC7D,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAASF;AAAA,EACT,MAAM;AAAA,EACN,WAAW;AACb,CAAC;AAED,IAAMG,aAAY,IAAI,WAAW,aAAa,YAAY,WAAW;AAAA,EACnE,OAAOD;AAAA,EACP,SAASF;AACX,CAAC;AAEM,IAAM,cAAc,OAAO,OAAO;AAAA,EACvC,WAAAG;AAAA,EACA,SAAAD;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAAD;AAAA,EACA;AAAA,EACA,QAAAF;AAAA,EACA;AAAA,EACA,YAAAC;AACF,CAAC;;;ACtMD,IAAqB,QAArB,MAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUzB,YAAY,IAAI,SAAS,SAAS,MAAM,YAAY,MAAM;AACxD,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,YAAY;AAEjB,SAAK,gBAAgB,oBAAI,IAAI;AAE7B,SAAK,WAAW,CAAC;AACjB,SAAK,QAAQ,YAAY;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK,SAAS,YAAY,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,KAAK,SAAS,YAAY,SAAS;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,KAAK,SAAS,YAAY,SAAS;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK,SAAS,YAAY,QAAQ;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,KAAK,SAAS,YAAY,MAAM;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACd,WAAO,KAAK,MAAM,SAAS,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,SAAK,QAAQ,KAAK,MAAM,MAAM;AAE9B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,SAAK,QAAQ,KAAK,MAAM,YAAY;AAEpC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,WAAW;AACd,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,QAAQ,KAAK,MAAM,KAAK;AAE7B,WAAK,gBAAgB;AAAA,IACvB;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,WAAW;AAEhB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,QAAQ,KAAK,MAAM,OAAO;AAE/B,WAAK,kBAAkB;AAAA,IACzB;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,WAAW;AACnB,SAAK,QAAQ,KAAK,MAAM,UAAU;AAElC,SAAK,qBAAqB;AAE1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,SAAK,QAAQ,KAAK,MAAM,SAAS;AAEjC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,WAAW;AACjB,SAAK,QAAQ,KAAK,MAAM,QAAQ;AAEhC,SAAK,mBAAmB;AAExB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK,SAAS,OAAO,OAAK,CAAC,EAAE,SAAS,EAAE;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,SAAS;AAC1B,WAAO,KAAK,SAAS,OAAO,OAAK,CAAC,EAAE,aAAa,EAAE,YAAY,OAAO,EAAE;AAAA,EAC1E;AAEF;;;AC7KO,SAAS,UAAU,KAAK,SAAS;AAEtC,QAAM,UAAU,CAAC;AAEjB,aAAW,MAAM,KAAK;AACpB,QAAI,QAAQ,EAAE,GAAG;AACf,cAAQ,KAAK,EAAE;AAAA,IACjB;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,QAAQ,KAAK,SAAS;AAEpC,aAAW,MAAM,KAAK;AACpB,QAAI,QAAQ,EAAE,GAAG;AACf,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACtBO,SAAS,YAAY,GAAG,GAAG;AAChC,QAAM,YAAY,CAAE,QAAQ,QAAQ,MAAO,EAAE,MAAM,CAAAI,UAAQ,EAAEA,SAAQ,MAAM,EAAEA,KAAI,MAAM,EAAEA,KAAI,CAAC;AAC9F,QAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,SAAS,WAAW,EAAE,SAAS;AAElE,SAAO,cAAc,iBAAiB,UAAU,GAAG,CAAC;AACtD;AAEO,SAAS,UAAU,GAAG,GAAG;AAC9B,QAAMA,QAAO;AACb,SAAO,EAAEA,SAAQ,MAAM,EAAEA,KAAI,MAAM,EAAEA,KAAI;AAC3C;;;ACMO,SAAS,oBAAoB,OAAO;AACzC,SAAO,MAAM,OAAO,OAAK,GAAG,GAAG,mBAAmB,CAAC;AACrD;AAEO,SAAS,cAAc,SAAS;AACrC,SAAO,GAAG,SAAS,kBAAkB;AACvC;AAEO,SAAS,eAAe,SAAS;AACtC,SAAO,GAAG,SAAS,mBAAmB;AACxC;AAMO,SAAS,YAAY,SAAS;AACnC,SAAO,aAAa,OAAO,KAAK,aAAa,SAAS,0BAA0B;AAClF;AAEO,SAAS,oBAAoB,SAAS;AAC3C,SAAO,aAAa,OAAO,KAAK,aAAa,SAAS,gCAAgC;AACxF;AAEO,SAAS,uBAAuB,SAAS;AAC9C,SAAO,GAAG,SAAS,eAAe,KAAK,QAAQ,eAAe;AAChE;AAEO,SAAS,aAAa,SAAS;AACpC,UACE,GAAG,SAAS,iBAAiB,KAC7B,GAAG,SAAS,kBAAkB,MAC3B,CAAC,QAAQ,OAAO;AACvB;AAEO,SAAS,gBAAgB,SAAS;AACvC,SAAO,GAAG,SAAS,oBAAoB,KAAK,CAAC,QAAQ,OAAO;AAC9D;AAEO,SAAS,iBAAiB,SAAS;AACxC,SAAO,aAAa,OAAO,KAAK,CAAC,aAAa,OAAO;AACvD;AAEO,SAAS,qBAAqB,SAAS;AAC5C,MAAI,QAAQ,OAAO,GAAG;AACpB,WAAO;AAAA,EACT;AAEA,MAAI,CAACC,OAAM,SAAS;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,GAAG;AACF,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,OAAO,GAAG;AACxB,WAAO;AAAA,EACT;AAEA,QAAM,WAAW,QAAQ,SAAS,KAAK,cAAc;AAErD,MAAI,UAAU;AACZ,WAAO;AAAA,EACT;AAEA,MAAI,uBAAuB,OAAO,GAAG;AACnC,WAAO;AAAA,EACT;AAEA,MAAI,kBAAkB,OAAO,GAAG;AAC9B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEO,SAAS,aAAa,SAAS;AACpC,SAAO,GAAG,SAAS,iBAAiB,KAAK,CAAC,QAAQ,OAAO;AAC3D;AAEO,SAAS,QAAQ,SAAS;AAC/B,SAAO,CAAC,CAAC,QAAQ;AACnB;AAEO,SAAS,kBAAkB,SAAS;AACzC,SAAO,kBAAkB,OAAO,EAAE;AACpC;AAEO,SAAS,eAAe,SAAS;AACtC,SACE,GAAG,SAAS,iBAAiB,KAAK,kBAAkB,OAAO,EAAE,kBAE7D,GAAG,SAAS,oBAAoB,KAAK,kBAAkB,OAAO,EAAE;AAEpE;AAEO,SAASA,OAAM,SAAS,OAAO;AACpC,SAAO,MAAM,KAAK,UAAQ,GAAG,SAAS,IAAI,CAAC;AAC7C;AAQO,SAAS,aAAaC,QAAO,qBAAqB;AACvD,SAAO,KAAK,kBAAkBA,MAAK,EAAE,kBAAkB,gBAAc;AACnE,WAAO,sBAAsB,GAAG,YAAY,mBAAmB,IAAI;AAAA,EACrE,CAAC;AACH;AAEO,SAAS,YAAY,SAAS,iBAAiB;AACpD,MAAI,QAAQ,YAAY,QAAQ,SAAS,WAAW,GAAG;AACrD,WAAO,QAAQ;AAAA,EACjB;AAEA,MAAI,GAAG,SAAS,iBAAiB,KAAK,CAAC,QAAQ,GAAG,YAAY;AAK5D,WAAO,gBAAgB,IAAI,oBAAoB,OAAO,CAAC,EAAE;AAAA,EAC3D;AAEA,SAAO,CAAC;AACV;;;ACvGe,SAAR,UAA2B,UAAU,UAAU,iBAAiB;AAErE,QAAM,MAAM,SAAS,IAAI,YAAY,KAAK,KAAK,IAAI,kBAAI,CAAE,IAAI,EAAG,CAAC;AAGjE,QAAM,gBAAgB,CAAC;AAEvB,QAAM,YAAY,CAAC;AAEnB,QAAM,eAAe,IAAI,aAAa;AAEtC,QAAM,kBAAkB,oBAAI,IAAI;AAEhC,QAAM,OAAO,CAAC;AAEd,QAAM,SAAS,oBAAI,IAAI;AACvB,QAAM,gBAAgB,oBAAI,IAAI;AAE9B,KAAG,QAAQ,WAAW;AACpB,eAAW,WAAW,iBAAiB;AACrC,WAAK,kBAAkB;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,oBAAgB,MAAM;AAAA,EACxB,CAAC;AAED,WAAS,MAAM,OAAO,MAAM;AAG1B,SAAK,KAAK,CAAE,MAAM,KAAM,CAAC;AAEzB,QAAI,KAAK,WAAW,GAAG;AACrB;AAAA,IACF;AAEA,QAAI;AAEJ,WAAQ,OAAO,KAAK,CAAC,GAAI;AAEvB,YAAM,CAAEC,OAAMC,MAAM,IAAI;AAExB,UAAI,CAACA,OAAM,WAAW;AACpB,QAAAD,MAAK;AAAA,MACP;AAGA,WAAK,MAAM;AAAA,IACb;AAEA,SAAK,MAAM;AAAA,EACb;AAEA,WAAS,YAAY,SAAS;AAC5B,WAAO,UAAU,QAAQ,IAAI,KAAK;AAAA,EACpC;AAEA,WAAS,OAAO,SAAS;AAEvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ,gBAAgB;AAAA,QACtB;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH,IAAI;AAEJ,UAAM,OAAO,WAAW;AAEtB,UAAI,CAAC,MAAM,SAAS;AAClB,cAAM,MAAM;AAAA,MACd;AAEA,YAAM,UAAU;AAAA,QACd,GAAG;AAAA,QACH;AAAA,MACF,CAAC;AAED,kBAAY,OAAO,EAAE,OAAO;AAAA,QAC1B,GAAG;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,MAAM,QAAQ;AAChB,qBAAa,MAAM,MAAM;AAAA,MAC3B;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,WAAS,MAAM,SAAS;AAEtB,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,YAAY;AAAA,IACd,IAAI;AAEJ,UAAM,QAAQ,gBAAgB;AAAA,MAC5B;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAED,UAAM,OAAO,WAAW;AAEtB,UAAI,CAAC,MAAM,SAAS;AAClB,cAAM,MAAM;AAAA,MACd;AAEA,YAAM,SAAS,OAAO;AAEtB,kBAAY,OAAO,EAAE,MAAM;AAAA,QACzB,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACF,CAAC;AAED,UAAI,MAAM,QAAQ;AAChB,qBAAa,MAAM,MAAM;AAAA,MAC3B;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,WAAS,KAAK,SAAS;AAErB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd,IAAI;AAEJ,UAAM,OAAO,WAAW;AAEtB,YAAM,QAAQ,OAAO;AAErB,kBAAY,OAAO,EAAE,KAAK;AAAA,QACxB,GAAG;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,MAAM,SAAS;AACjB,cAAM,SAAS;AAAA,MACjB;AAEA,mBAAa,OAAO,SAAS;AAE7B,YAAM,UAAU,aAAa,MAAM,MAAM;AAAA,IAC3C,CAAC;AAAA,EACH;AAEA,WAAS,QAAQ,SAAS;AACxB,UAAM;AAAA,MACJ,OAAO;AAAA,MACP;AAAA,MACA;AAAA,IACF,IAAI;AAWJ,UAAME,SAAQ,SAAS,MAAM;AAE7B,UAAMC,iBAAgB,MAAM;AAE5B,QAAI,wBAAwB;AAAA,MAC1BA;AAAA,MAAe,kBAAgB,YAAYD,QAAO,aAAa,KAAK;AAAA,IACtE;AAEA,QAAIA,OAAM,SAAS,WAAWA,OAAM,SAAS,cAAc;AACzD,YAAM,yBAAyB;AAAA,QAC7B;AAAA,QAAuB,kBAAgB,UAAUA,QAAO,aAAa,KAAK;AAAA,MAC5E;AAEA,UAAI,sBAAsB,MAAM,kBAAgB,aAAa,MAAM,QAAQ,KACpE,uBAAuB,KAAK,kBAAgB,aAAa,MAAM,QAAQ,KACvE,uBAAuB,KAAK,kBAAgB,CAAC,aAAa,MAAM,QAAQ,GAAG;AAChF,gCAAwB;AAAA,MAC1B;AAAA,IACF;AAEA,UAAM,kBAAkB,sBAAsB;AAAA,MAC5C,kBAAgB,CAAC,aAAa,MAAM;AAAA,IACtC;AAEA,UAAM,eAAe,sBAAsB;AAAA,MACzC,kBAAgB,aAAa,MAAM;AAAA,IACrC;AAEA,QAAI,CAAC,aAAa,QAAQ;AACxB,aAAO,gBAAgB;AAAA,QACrB,kBAAgB,aAAa,UAAU,SAAS;AAAA,MAClD,EAAE,KAAK;AAAA,IACT;AAEA,UAAM,YAAY,aAAa,KAAK,kBAAgB,CAAC,aAAa,MAAM,QAAQ,KAAK,aAAa,CAAC;AAEnG,UAAM,yBAAyB;AAAA,MAC7BC;AAAA,MACA,kBAAgB,aAAa,MAAM,cAAc,UAAU,aAAa,OAAO,UAAU,KAAK;AAAA,IAChG;AAEA,IAAAA,eAAc,QAAQ,kBAAgB;AACpC,UAAI,CAAC,uBAAuB,SAAS,YAAY,GAAG;AAClD,qBAAa,OAAO;AAAA,MACtB;AAAA,IACF,CAAC;AAED,WAAO,CAAE,UAAU,UAAU,SAAS,CAAE,EAAE,KAAK,EAAE,OAAO,OAAK,CAAC;AAAA,EAChE;AAEA,WAAS,UAAU,OAAOD,QAAO,WAAW;AAE1C,IAAAA,SAAQ,SAASA,MAAK;AAEtB,UAAM,UAAUA,OAAM;AAEtB,UAAM,eAAe;AAAA,MACnB;AAAA,MACA,OAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AACP,oBAAY,YAAY;AAAA,MAC1B;AAAA,IACF;AAEA,kBAAc,IAAI,YAAY;AAE9B,UAAM,cAAc,IAAI,YAAY;AAEpC,QAAI,SAAS;AACX,qBAAe,OAAO;AAAA,IACxB;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,YAAY,cAAc;AACjC,UAAM;AAAA,MACJ;AAAA,MACA,OAAAA;AAAA,IACF,IAAI;AAEJ,kBAAc,OAAO,YAAY;AAEjC,UAAM,cAAc,OAAO,YAAY;AAEvC,QAAIA,OAAM,SAAS;AACjB,qBAAeA,OAAM,OAAO;AAAA,IAC9B;AAAA,EACF;AAEA,WAAS,kBAAkB,SAAS;AAClC,QACE,GAAG,SAAS,iBAAiB,KAC7B,GAAG,SAAS,6BAA6B,KACzC,GAAG,SAAS,kBAAkB,KAC9B,uBAAuB,OAAO,GAC9B;AACA,aAAO,kBAAkB,OAAO,EAAE,QAAQ,QAAQ;AAAA,IACpD;AAEA,WAAO;AAAA,EACT;AAOA,WAAS,aAAa,SAAS;AAC7B,WAAO;AAAA,MACL;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,MAAM,QAAQ;AAAA,MACd,MAAM;AAAA,IACR;AAAA,EACF;AAOA,WAAS,SAAS,SAAS;AAGzB,QAAI,CAAC,QAAQ,gBAAgB;AAC3B,aAAO;AAAA,IACT;AAEA,UAAM,eAAe,eAAe,OAAO;AAC3C,UAAM,WAAW,gBAAgB,OAAO;AAKxC,UAAM,OAAO,kBAAkB,OAAO;AAEtC,UAAM,YAAY;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAI,OAAO,EAAE,KAAK,IAAI,CAAC;AAAA,IACzB;AAEA,UAAM,kBAAkB,oBAAoB,OAAO,EAAE,CAAC;AAEtD,QAAI,CAAC,iBAAiB;AAEpB,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM,uBAAuB,OAAO,IAAI,YAAY;AAAA,MACtD;AAAA,IACF;AAEA,QAAI,GAAG,iBAAiB,0BAA0B,GAAG;AACnD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,QACN,MAAM,gBAAgB;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,GAAG,iBAAiB,4BAA4B,GAAG;AACrD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,QACN,KAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,QAAI,GAAG,iBAAiB,2BAA2B,GAAG;AACpD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,MACR;AAAA,IACF;AAEA,QAAI,GAAG,iBAAiB,iCAAiC,GAAG;AAC1D,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,MACR;AAAA,IACF;AAEA,QAAI,GAAG,iBAAiB,gCAAgC,GAAG;AACzD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,QACN,KAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,QAAI,GAAG,iBAAiB,4BAA4B,GAAG;AACrD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,MACR;AAAA,IACF;AAEA,QAAI,GAAG,iBAAiB,2BAA2B,GAAG;AACpD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,QACN,KAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,QAAI,GAAG,iBAAiB,6BAA6B,GAAG;AACtD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,QACN,KAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,QAAI,GAAG,iBAAiB,gCAAgC,GAAG;AAEzD,UAAI,MAAM,gBAAgB,eAAe,gBAAgB,IAAI,gBAAgB,YAAY,EAAE;AAE3F,UAAI,CAAC,KAAK;AAER,YAAI,aAAa,OAAO,KAAK,kBAAkB,QAAQ,MAAM,GAAG;AAI9D,gBAAM,QAAQ,OAAO;AAAA,QACvB,WAAW,gBAAgB,OAAO,GAAG;AAGnC,gBAAM,QAAQ;AAAA,QAChB,OAAO;AAGL,gBAAM,QAAQ;AAAA,QAChB;AAAA,MACF;AAEA,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,QACN;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF;AAEA,UAAM,IAAI,MAAM,4BAA4B,eAAe;AAAA,EAC7D;AAEA,WAAS,YAAY,SAAS,YAAY,MAAM;AAE9C,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF,IAAI;AAEJ,iBAAa,MAAM,eAAe;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT,CAAC;AAED,UAAM,QAAQ,IAAI,MAAM,IAAI,KAAK,GAAG,SAAS,aAAa,SAAS;AAEnE,QAAI,aAAa;AACf,kBAAY,SAAS,KAAK,KAAK;AAAA,IACjC;AAEA,WAAO,IAAI,KAAK;AAEhB,iBAAa,KAAK,eAAe;AAAA,MAC/B;AAAA,IACF,CAAC;AAED,mBAAe,OAAO;AAEtB,QAAI,aAAa;AACf,qBAAe,YAAY,OAAO;AAAA,IACpC;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,QAAQ;AAElC,QAAI,OAAO,WAAW,YAAY;AAChC,aAAO;AAAA,IACT;AAEA,UAAM;AAAA,MACJ,OAAO;AAAA,MACP;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM,WAAW,OAAO,YAAa,WAAW,CAAE,OAAQ;AAC1D,UAAMA,SAAQ,UAAU,SAAS,MAAM;AAEvC,WACE,CAAC,kBACE,CAACA,UAAS,YAAYA,QAAO,aAAa,KAAK,OAC/C,CAAC,YAAY,SAAS,SAAS,aAAa,OAAO,OACnD,CAAC,SAAS,UAAU,aAAa;AAAA,EAExC;AAEA,WAAS,wBAAwBA,QAAO;AACtC,UAAM,sBAAsBA,WAAU,aAAaA,SAAQ,mBAAmBA,MAAK;AAEnF,WACE,WAAS,MAAM,KAAK,MAAM,aAAa,EAAE,KAAK,mBAAmB;AAAA,EAErE;AAEA,WAAS,YAAY,QAAQ;AAE3B,QAAI,OAAO,WAAW,YAAY;AAChC,aAAO;AAAA,IACT;AAEA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,YAAY;AAAA,MACpB;AAAA,IACF,IAAI;AAEJ,UAAM,eAAe,eAAe,wBAAwB,YAAY,IAAI,MAAM;AAElF,WACE,YACG,CAAC,WAAW,MAAM,YAAY,aAC9B,CAAC,UAAU,MAAM,WAAW,YAC5B,CAAC,kBAAkB,MAAM,mBAAmB,cAAc,IAAI,MAC/D,MAAM,SAAS,KAAK,KACpB,aAAa,KAAK;AAAA,EAExB;AAEA,WAAS,kBAAkB,QAAQ;AACjC,WAAO,UAAU,eAAe,mBAAmB,MAAM,CAAC;AAAA,EAC5D;AAEA,WAAS,iBAAiB,QAAQ;AAChC,WAAO,QAAQ,eAAe,mBAAmB,MAAM,CAAC;AAAA,EAC1D;AAEA,WAAS,WAAW,QAAQ;AAC1B,WAAO,UAAU,QAAQ,YAAY,MAAM,CAAC;AAAA,EAC9C;AAEA,WAAS,UAAU,QAAQ;AACzB,WAAO,QAAQ,QAAQ,YAAY,MAAM,CAAC;AAAA,EAC5C;AAEA,WAAS,aAAa,OAAO,YAAY,MAAM;AAE7C,QAAI,MAAM,WAAW;AACnB;AAAA,IACF;AAEA,UAAM,QAAQ,SAAS;AAGvB,eAAW,gBAAgB,MAAM,eAAe;AAC9C,YAAM,QAAQ,aAAa,MAAM,UAAU,YAAY;AAEvD,UAAI,CAAC,MAAM,SAAS,KAAK,GAAG;AAC1B,oBAAY,YAAY;AAAA,MAC1B;AAAA,IACF;AAOA,QAAI,MAAM,WAAW;AAGnB,iBAAW,cAAc,MAAM,UAAU;AACvC,YAAI,CAAC,WAAW,WAAW;AACzB,uBAAa,YAAY,SAAS;AAAA,QACpC;AAAA,MACF;AAEA,YAAM,gBAAgB;AAAA,QACpB,SAAS,MAAM;AAAA,QACf;AAAA,MACF,CAAC;AAGD,aAAO,OAAO,KAAK;AAEnB,WAAK,gBAAgB;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,mBAAe,MAAM,OAAO;AAE5B,QAAI,MAAM,QAAQ;AAChB,qBAAe,MAAM,OAAO,OAAO;AAAA,IACrC;AAAA,EACF;AAEA,WAAS,MAAM,QAAQ,SAAS;AAE9B,SAAK,SAAS;AAAA,MACZ,GAAG;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,eAAe,SAAS;AAC/B,oBAAgB,IAAI,OAAO;AAI3B,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAEA,WAAS,aAAa,OAAO;AAC3B,SAAK,gBAAgB;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,KAAKA,QAAO,UAAU,CAAC,GAAG;AACjC,WAAO,SAAS,KAAK,6BAA6BA,MAAK,IAAI,OAAO;AAAA,EACpE;AAEA,WAAS,GAAGA,QAAO,UAAU;AAC3B,aAAS,GAAG,+BAA+BA,QAAO,QAAQ;AAAA,EAC5D;AAEA,WAAS,IAAIA,QAAO,UAAU;AAC5B,aAAS,IAAI,+BAA+BA,QAAO,QAAQ;AAAA,EAC7D;AAEA,WAAS,UAAU,SAAS,eAAe;AAEzC,UAAM,iBAAiB,UAAU,OAAO;AAExC,kBAAc,QAAQ,MAAM,OAAO,IAAI;AAAA,MACrC,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,mBAAe,OAAO;AAAA,EACxB;AAEA,WAAS,uBAAuB;AAE9B,UAAM,aAAa,CAAC;AAEpB,oBAAgB,QAAQ,aAAW;AAEjC,UAAI,CAACE,OAAM,SAAS,CAAE,gBAAgB,kBAAmB,CAAC,GAAG;AAC3D;AAAA,MACF;AAEA,YAAM,QAAQ,YAAY;AAAA,QACxB;AAAA,MACF,GAAG,KAAK;AAER,iBAAW,KAAK,KAAK;AAErB,YAAM,cAAc,QAAQ,SAAS,OAAO,YAAY;AAExD,YAAM,sBAAsB,QAAQ,SAAS,OAAO,oBAAoB;AAExE,iBAAW,cAAc,aAAa;AAEpC,cAAMF,SAAQ;AAAA,UACZ,GAAG,SAAS,UAAU;AAAA,UACtB,cAAc;AAAA,QAChB;AAGA,kBAAU,OAAOA,QAAO,eAAa,OAAO;AAAA,UAC1C;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAEA,UAAI,CAAC,YAAY,QAAQ;AAEvB,mBAAW,sBAAsB,qBAAqB;AAEpD,gBAAMA,SAAQ,aAAa,kBAAkB;AAG7C,oBAAU,OAAOA,QAAO,eAAa,OAAO;AAAA,YAC1C;AAAA,YACA;AAAA,UACF,CAAC,CAAC;AAAA,QACJ;AAAA,MACF;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,WAAS,gBAAgB,SAAS;AAEhC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAEJ,UAAM,QAAQ,YAAY,OAAO;AAEjC,UAAM;AAAA,MACJ,YAAY,CAAC;AAAA,IACf,IAAI;AAEJ,UAAM,WAAW,YAAY,SAAS,eAAe;AAErD,eAAW,gBAAgB,UAAU;AAGnC,UAAI,kBAAkB,YAAY,GAAG;AACnC,cAAM,cAAc,YAAY,cAAc,eAAe,EAAE;AAAA,UAC7D,CAAAG,aAAW,aAAaA,QAAO,KAAK,CAAC,oBAAoBA,QAAO;AAAA,QAClE;AAEA,mBAAW,cAAc,aAAa;AACpC,oBAAU,OAAO,YAAY,eAAa;AAExC,mBAAO,OAAO;AAAA,cACZ,SAAS;AAAA,cACT,aAAa;AAAA,cACb;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,eAAW,YAAY,WAAW;AAGhC,UAAI,gBAAgB,QAAQ,KAAK,CAAC,oBAAoB,QAAQ,GAAG;AAE/D,kBAAU,OAAO,UAAU,eAAa;AACtC,iBAAO,OAAO;AAAA,YACZ,SAAS;AAAA,YACT,aAAa,MAAM;AAAA,YACnB,WAAW;AAAA,YACX;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,UAAU,SAAS;AAC1B,WAAO,cAAc,QAAQ,MAAM,OAAO,KAAK,CAAC;AAAA,EAClD;AAEA,WAAS,cAAc,OAAOC,SAAQ;AAEpC,QAAI,CAACA,QAAO,QAAQ;AAClB;AAAA,IACF;AAEA,UAAMJ,SAAQ;AAAA,MACZ,MAAM;AAAA,MACN,YAAY;AAAA,IACd;AAEA,UAAM,kBAAkB,IAAI,IAAII,OAAM;AAEtC,UAAM,kBAAkB,CAAC,iBAAiB;AACxC,sBAAgB,OAAO,aAAa,KAAK;AAEzC,UAAI,gBAAgB,SAAS,GAAG;AAC9B,YAAI,gBAAgB,eAAe;AAEnC,gBAAQ;AAAA,UACN;AAAA,UACA,OAAAJ;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,OAAG,gBAAgB,eAAe;AAElC,WAAOA;AAAA,EACT;AAEA,WAAS,cAAc,SAAS,OAAO,MAAM;AAC3C,cAAU,SAAS;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,QAAQ;AACf,eAAW,SAAS,QAAQ;AAC1B,mBAAa,KAAK;AAAA,IACpB;AAEA,eAAW,aAAa,qBAAqB,GAAG;AAC9C,aAAO,IAAI,SAAS;AAAA,IACtB;AAIA,SAAK,MAAM;AACX,SAAK,OAAO;AAAA,EACd;AAGA,OAAK,cAAc;AACnB,OAAK,eAAe;AAGpB,OAAK,YAAY;AACjB,OAAK,aAAa;AAElB,OAAK,mBAAmB;AACxB,OAAK,oBAAoB;AAGzB,OAAK,gBAAgB;AAErB,OAAK,gBAAgB;AAErB,OAAK,YAAY;AACjB,OAAK,YAAY;AAGjB,OAAK,SAAS;AACd,OAAK,QAAQ;AACb,OAAK,OAAO;AAGZ,OAAK,YAAY;AACjB,OAAK,UAAU;AAGf,OAAK,QAAQ;AAGb,OAAK,KAAK;AACV,OAAK,MAAM;AAGX,OAAK,mBAAmB,SAAS,SAAS,UAAU;AAClD,cAAU,OAAO,IAAI;AAAA,EACvB;AACF;AAEA,UAAU,UAAU;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AACF;AAKA,SAAS,eAAe;AAEtB,OAAK,SAAS,SAAS,SAAS;AAC9B,YAAQ,IAAI,iBAAiB,QAAQ,OAAO;AAAA,EAC9C;AAEA,OAAK,OAAO,SAAS,SAAS;AAC5B,YAAQ,IAAI,iBAAiB,QAAQ,OAAO;AAAA,EAC9C;AAEA,OAAK,QAAQ,SAAS,SAAS;AAC7B,YAAQ,IAAI,kBAAkB,QAAQ,OAAO;AAAA,EAC/C;AAEF;AAEA,SAAS,UAAUA,QAAO,WAAW;AACnC,SACEA,OAAM,SAAS,UAAU,QACzBA,OAAM,YAAY,CAAC,UAAU;AAEjC;AAEA,SAAS,uBAAuB,SAAS;AACvC,SAAO,GAAG,SAAS,kBAAkB,KAAK,QAAQ,SAAS,KAAK,CAAAG,aAAW,GAAGA,UAAS,kBAAkB,CAAC;AAC5G;AAEA,SAAS,uBAAuB,SAAS;AACvC,MAAI,CAAC,gBAAgB,OAAO,GAAG;AAC7B,WAAO;AAAA,EACT;AAEA,QAAM,mBAAmB,oBAAoB,OAAO;AAEpD,SAAO,CAAC,iBAAiB,CAAC,KAAKD,OAAM,iBAAiB,CAAC,GAAG;AAAA,IACxD;AAAA,IAAmC;AAAA,EACrC,CAAC;AACH;AAEA,SAAS,oBAAoB,SAAS;AACpC,SAAO,QAAQ,eAAe,IAAI,kBAAkB,KAAK,CAAC;AAC5D;;;ACh6Be,SAAR,mBACH,WACA,kBAAkB;AAEpB,OAAK,aAAa;AAClB,OAAK,oBAAoB;AAEzB,YAAU,iBAAiB,mBAAmB,IAAI;AACpD;AAEA,mBAAmB,UAAU,SAAS,SAAS,SAAS;AACtD,OAAK,WAAW,KAAK,OAAO;AAC9B;AAEA,mBAAmB,UAAU,OAAO,SAAS,SAAS;AACpD,OAAK,kBAAkB,KAAK,OAAO;AACrC;AAEA,mBAAmB,UAAU;AAAA,EAC3B;AAAA,EACA;AACF;;;ACrBe,SAAR,iBACH,WACA,eACA,gCAAgC;AAElC,OAAK,kCAAkC;AACvC,OAAK,iBAAiB;AAEtB,YAAU,iBAAiB,iBAAiB,IAAI;AAClD;AAEA,iBAAiB,UAAU;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AACF;AAEA,iBAAiB,UAAU,QAAQ,SAAS,SAAS;AACnD,OAAK,gCAAgC,MAAM,OAAO;AACpD;AAEA,iBAAiB,UAAU,SAAS,SAAS,SAAS;AACpD,OAAK,gCAAgC,OAAO,OAAO;AACrD;AAEA,iBAAiB,UAAU,OAAO,SAAS,SAAS;AAElD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAEJ,OAAK,eAAe,QAAQ,MAAM,QAAQ,KAAK;AACjD;;;AC3Be,SAAR,sBACH,WACA,kBACA,eAAe;AAEjB,OAAK,aAAa;AAClB,OAAK,oBAAoB;AACzB,OAAK,iBAAiB;AAEtB,YAAU,iBAAiB,sBAAsB,IAAI;AACvD;AAEA,sBAAsB,UAAU,SAAS,SAAS,SAAS;AAEzD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,YAAY,KAAK,WAAW,UAAU;AAAA,MACpC,QAAQ,MAAM;AAAA,MACd,SAAS,QAAQ;AAAA,IACnB,CAAC;AAAA,EACH,IAAI;AAEJ,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAEA,QAAM,iBAAiB,kBAAkB,OAAO,EAAE;AAElD,MAAI,gBAAgB;AAClB,SAAK,eAAe,UAAU,WAAW,KAAK;AAG9C,UAAMG,SAAQ,KAAK,eAAe,QAAQ,WAAW,KAAK;AAE1D,QAAIA,QAAO;AACT,YAAM,eAAe,KAAK,WAAW,UAAU,WAAWA,QAAO,eAAa;AAC5E,qBAAa,OAAO;AAEpB,eAAO,KAAK,WAAW,KAAK,OAAO;AAAA,MACrC,CAAC;AAED;AAAA,IACF;AAAA,EACF;AAEA,OAAK,WAAW,KAAK,OAAO;AAC9B;AAEA,sBAAsB,UAAU,OAAO,SAAS,SAAS;AACvD,OAAK,kBAAkB,KAAK,OAAO;AACrC;AAEA,sBAAsB,UAAU;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AACF;;;AC9De,SAAR,+BACH,WACA,kBAAkB;AAEpB,OAAK,oBAAoB;AACzB,OAAK,aAAa;AAElB,YAAU,iBAAiB,+BAA+B,IAAI;AAC9D,YAAU,iBAAiB,oBAAoB,IAAI;AACrD;AAEA,+BAA+B,UAAU;AAAA,EACvC;AAAA,EACA;AACF;AAEA,+BAA+B,UAAU,SAAS,SAAS,SAAS;AAClE,SAAO,KAAK,WAAW,KAAK,OAAO;AACrC;AAEA,+BAA+B,UAAU,QAAQ,SAAS,SAAS;AACjE,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAIJ,SAAO,KAAK,kBAAkB,cAAc,SAAS,OAAO;AAC9D;AAEA,+BAA+B,UAAU,OAAO,SAAS,SAAS;AAChE,OAAK,kBAAkB,KAAK,OAAO;AACrC;;;AChCe,SAAR,+BACH,WACA,kBACA,gBAAgB;AAElB,OAAK,aAAa;AAClB,OAAK,oBAAoB;AACzB,OAAK,kBAAkB;AAEvB,YAAU,iBAAiB,+BAA+B,IAAI;AAC9D,YAAU,iBAAiB,iBAAiB,IAAI;AAClD;AAEA,+BAA+B,UAAU,QAAQ,SAAS,SAAS;AACjE,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAEJ,QAAM,gBAAgB,KAAK,gBAAgB,IAAI,OAAO;AAEtD,MAAI,eAAe;AACjB,UAAMC,SAAQ,cAAc,OAAO;AAEnC,QAAIA,QAAO;AACT,aAAO,KAAK,kBAAkB,cAAc,SAASA,MAAK;AAAA,IAC5D;AAAA,EACF;AAEA,OAAK,kBAAkB,MAAM,OAAO;AACtC;AAEA,+BAA+B,UAAU,SAAS,SAAS,SAAS;AAClE,OAAK,kBAAkB,OAAO,OAAO;AACvC;AAEA,+BAA+B,UAAU,OAAO,SAAS,SAAS;AAChE,OAAK,kBAAkB,KAAK,OAAO;AACrC;AAEA,+BAA+B,UAAU;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AACF;;;ACtCe,SAAR,yBAA0C,WAAW,eAAe;AACzE,OAAK,iBAAiB;AACtB,OAAK,aAAa;AAElB,YAAU,iBAAiB,yBAAyB,IAAI;AAC1D;AAEA,yBAAyB,UAAU,QAAQ,SAAS,SAAS;AAC3D,OAAK,WAAW,KAAK,OAAO;AAC9B;AAEA,yBAAyB,UAAU,OAAO,SAAS,SAAS;AAE1D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAKJ,QAAM,YAAY,oBAAoB,QAAQ,QAAQ;AAEtD,MAAI,UAAU,WAAW,GAAG;AAC1B,WAAO,KAAK,WAAW,MAAM;AAAA,MAC3B,SAAS,UAAU,CAAC;AAAA,MACpB,OAAO,MAAM;AAAA,IACf,CAAC;AAAA,EACH;AAEA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,KAAK,WAAW,UAAU,OAAO;AAErC,QAAM,WAAW,UAAU,KAAK,OAAK,MAAM,cAAc;AAEzD,MAAI,CAAC,UAAU;AACb,WAAO,KAAK,eAAe,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACxD;AAEA,SAAO,KAAK,WAAW,MAAM;AAAA,IAC3B,SAAS;AAAA,IACT,OAAO,MAAM;AAAA,EACf,CAAC;AACH;AAEA,yBAAyB,UAAU;AAAA,EACjC;AAAA,EACA;AACF;;;ACjDe,SAAR,wBACH,WACA,kBAAkB;AAEpB,OAAK,aAAa;AAClB,OAAK,oBAAoB;AAEzB,YAAU,iBAAiB,wBAAwB,IAAI;AACzD;AAEA,wBAAwB,UAAU,QAAQ,SAAS,SAAS;AAE1D,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAEJ,QAAM,gBAAgB,KAAK,mBAAmB,OAAO;AAErD,MAAI,cAAc,QAAQ;AAExB,eAAW,cAAc,eAAe;AAEtC,UAAI,eAAe,OAAO;AAGxB,aAAK,WAAW,aAAa,WAAW,SAAS,GAAG,KAAK;AAAA,MAC3D;AAAA,IACF;AAEA,SAAK,WAAW,KAAK,OAAO;AAAA,EAC9B;AACF;AAQA,wBAAwB,UAAU,qBAAqB,SAAS,cAAc;AAE5E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,gBAAgB,oBAAoB,QAAQ,QAAQ;AAE1D,QAAM;AAAA,IACJ,QAAQ;AAAA,EACV,IAAI;AAEJ,QAAM,gBAAgB,KAAK,WAAW,WAAW;AAAA,IAC/C,QAAQ;AAAA,IACR;AAAA,EACF,CAAC;AAED,QAAM,iBAAiB,cACpB;AAAA,IACC,UAAQ,cACL,KAAK,CAAAC,WAASA,OAAM,UAAU,YAAY,IAAI;AAAA,EACnD,EACC,OAAO,CAAAA,WAASA,MAAK;AAExB,MAAI,eAAe,WAAW,cAAc,QAAQ;AAClD,WAAO;AAAA,EACT,OAAO;AACL,WAAO,CAAC;AAAA,EACV;AACF;AAEA,wBAAwB,UAAU,OAAO,SAAS,SAAS;AACzD,OAAK,kBAAkB,KAAK,OAAO;AACrC;AAEA,wBAAwB,UAAU;AAAA,EAChC;AAAA,EACA;AACF;;;AChFe,SAAR,0BAA2C,WAAW;AAC3D,OAAK,aAAa;AAElB,YAAU,iBAAiB,0BAA0B,IAAI;AAC3D;AAEA,0BAA0B,UAAU;AAAA,EAClC;AACF;AAEA,0BAA0B,UAAU,QAAQ,SAAS,SAAS;AAE5D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,cAAc,MAAM;AAE1B,QAAM,kBAAkB,YAAY,OAAO;AAI3C,QAAM,gBAAgB,gBAAgB;AAAA,IACpC,oBAAkB,KAAK,WAAW,UAAU,aAAa,gBAAgB,eAAa;AAGpF,oBAAc,QAAQ,kBAAgB,aAAa,OAAO,CAAC;AAG3D,WAAK,WAAW,aAAa,OAAO,SAAS;AAG7C,aAAO,KAAK,WAAW,OAAO;AAAA,QAC5B,SAAS;AAAA,QACT;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEF;AAKA,SAAS,YAAY,SAAS;AAC5B,SAAO,QAAQ,SAAS;AAAA,IACtB,cAAY,SAAS;AAAA,EACvB,EAAE,OAAO,cAAYC,OAAM,UAAU;AAAA,IACnC;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACJ;;;ACnDe,SAAR,yBACH,WACA,kBAAkB;AAEpB,OAAK,aAAa;AAClB,OAAK,oBAAoB;AAEzB,YAAU,iBAAiB,yBAAyB,IAAI;AAC1D;AAEA,yBAAyB,UAAU,QAAQ,SAAS,SAAS;AAC3D,OAAK,SAAS,OAAO;AACvB;AAEA,yBAAyB,UAAU,OAAO,SAAS,SAAS;AAE1D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAKJ,QAAM,YAAY,oBAAoB,QAAQ,QAAQ;AAGtD,MAAI,UAAU,SAAS,GAAG;AAExB,UAAM;AAAA,MACJ,iBAAiB,CAAC;AAAA,IACpB,IAAI,KAAK,WAAW,UAAU,OAAO;AAErC,QAAI,CAAC,eAAe,QAAQ;AAC1B,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AAEA,eAAW,YAAY,gBAAgB;AACrC,WAAK,WAAW,MAAM;AAAA,QACpB,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EAEF,OAAO;AAGL,SAAK,kBAAkB,KAAK,OAAO;AAAA,EACrC;AAEF;AAEA,yBAAyB,UAAU,WAAW,SAAS,SAAS;AAE9D,QAAM,kBAAkB,KAAK,oBAAoB,OAAO;AAExD,QAAM,oBAAoB,gBAAgB,IAAI,CAAAC,WAASA,OAAM,OAAO;AAKpE,MAAI,CAAC,KAAK,oBAAoB,mBAAmB,QAAQ,OAAO,GAAG;AACjE,WAAO,KAAK,MAAM,OAAO;AAAA,EAC3B;AAEA,QAAM,gBAAgB,KAAK,kBAAkB,OAAO;AAEpD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAIJ,MAAI,cAAc,CAAC,MAAM,OAAO;AAC9B;AAAA,EACF;AAEA,QAAMC,SAAQ,KAAK,WAAW,cAAc,OAAO,eAAe;AAElE,QAAM,eAAe,KAAK,WAAW,UAAU,OAAOA,QAAO,MAAM;AACjE,iBAAa,OAAO;AAEpB,SAAK,SAAS,OAAO;AAAA,EACvB,CAAC;AACH;AASA,yBAAyB,UAAU,sBAAsB,SAAS,SAAS;AACzE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM;AAAA,IACJ,QAAQ;AAAA,EACV,IAAI;AAEJ,SAAO,KAAK,WAAW;AAAA,IACrB,CAAAD,WAASA,OAAM,WAAW,eAAeA,OAAM,YAAY;AAAA,EAC7D;AACF;AAEA,yBAAyB,UAAU,QAAQ,SAAS,SAAS;AAC3D,QAAM,gBAAgB,KAAK,kBAAkB,OAAO;AAEpD,aAAW,cAAc,eAAe;AAEtC,QAAI,eAAe,QAAQ,OAAO;AAGhC,WAAK,WAAW,aAAa,WAAW,SAAS,GAAG,QAAQ,KAAK;AAAA,IACnE;AAAA,EACF;AAEA,OAAK,WAAW,KAAK,OAAO;AAC9B;AASA,yBAAyB,UAAU,oBAAoB,SAAS,SAAS;AACvE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,SAAO,KAAK,WAAW,WAAW;AAAA,IAChC,QAAQ,MAAM;AAAA,IACd;AAAA,EACF,CAAC;AACH;AAaA,yBAAyB,UAAU,sBAAsB,SAAS,UAAU,gBAAgB,YAAY,oBAAI,IAAI,GAAG;AAEjH,MAAI,CAAC,SAAS,QAAQ;AACpB,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,IAAI,cAAc,GAAG;AACjC,WAAO;AAAA,EACT;AAEA,YAAU,IAAI,cAAc;AAE5B,MAAI,SAAS,KAAK,CAAAE,OAAKA,OAAM,cAAc,GAAG;AAC5C,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,cAAc,GAAG;AAClC,WAAO,KAAK,oBAAoB,UAAU,eAAe,QAAQ,SAAS;AAAA,EAC5E;AAEA,QAAM,gBAAgB,oBAAoB,eAAe,QAAQ;AAEjE,aAAW,QAAQ,eAAe;AAChC,QAAI,KAAK,oBAAoB,UAAU,MAAM,SAAS,GAAG;AACvD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,yBAAyB,UAAU;AAAA,EACjC;AAAA,EACA;AACF;;;AC1Le,SAAR,iBACH,WACA,eACA,qBACF;AACA,OAAK,aAAa;AAClB,OAAK,iBAAiB;AACtB,OAAK,uBAAuB;AAE5B,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,aAAW,WAAW,UAAU;AAC9B,cAAU,iBAAiB,SAAS,IAAI;AAAA,EAC1C;AACF;AAEA,iBAAiB,UAAU;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AACF;AAEA,iBAAiB,UAAU,SAAS,SAAS,SAAS;AAGpD,QAAMC,SAAQ,KAAK,iBAAiB,OAAO;AAE3C,MAAIA,QAAO;AACT,WAAO,KAAK,cAAc,SAASA,MAAK;AAAA,EAC1C;AAEA,OAAK,WAAW,KAAK,OAAO;AAC9B;AAEA,iBAAiB,UAAU,QAAQ,SAAS,SAAS;AAEnD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAEJ,QAAM,gBAAgB,KAAK,cAAc,OAAO;AAEhD,MAAI,eAAe;AACjB,WAAO,KAAK,cAAc,SAAS,aAAa;AAAA,EAClD;AAGA,QAAMA,SAAQ,KAAK,iBAAiB,OAAO;AAE3C,MAAIA,QAAO;AACT,WAAO,KAAK,cAAc,SAASA,MAAK;AAAA,EAC1C;AAEA,OAAK,WAAW,KAAK,OAAO;AAC9B;AAEA,iBAAiB,UAAU,OAAO,SAAS,SAAS;AAElD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,cAAc,MAAM;AAM1B,QAAM,WAAW,CAAC,MAAM;AAMxB,MAAI,YAAY,CAAC,kBAAkB,OAAO,GAAG;AAC3C,SAAK,qBAAqB,qBAAqB,KAAK;AAAA,EACtD;AAIA,QAAM,iBAAiB,WACnB,QAAQ,SAAS,OAAO,cAAc,IACtC,CAAC;AAEL,iBAAe;AAAA,IACb,CAAAC,aAAW,KAAK,WAAW,MAAM;AAAA,MAC/B,SAAAA;AAAA,MACA,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAGA,MAAI,eAAe,WAAW,GAAG;AAC/B,SAAK,eAAe,QAAQ,aAAa,KAAK;AAAA,EAChD;AACF;AAEA,iBAAiB,UAAU,gBAAgB,SAAS,SAASD,QAAO;AAElE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,eAAe,KAAK,WAAW,UAAU,OAAOA,QAAO,eAAa;AAExE,iBAAa,OAAO;AAEpB,WAAO,KAAK,WAAW,OAAO;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AASA,iBAAiB,UAAU,gBAAgB,SAAS,SAAS;AAC3D,QAAM,OAAO,KAAK,WAAW,UAAU,OAAO,EAAE;AAEhD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,IACd,UAAU;AAAA,EACZ;AACF;AAEA,iBAAiB,UAAU,sBAAsB,SAAS,SAAS,QAAQ,MAAM;AAE/E,QAAM,cAAc,QAAQ,SAAO,IAAI,eAAe,IAAI,MAAM,IAAI,MAAM;AAC1E,QAAM,kBAAkB,CAAC,GAAG,MAAM,EAAE,eAAe,IAAI,EAAE,eAAe;AAExE,SAAO;AAAA,IACL,GAAG,QAAQ,SAAS,OAAO,aAAa,EAAE,IAAI,WAAS;AAAA,MACrD,UAAU;AAAA,MACV,gBAAgB,KAAK,KAAK,SAAS;AAAA,IACrC,EAAE;AAAA,IACF,GAAG,QAAQ,SAAS,OAAO,aAAa,EAAE,IAAI,WAAS;AAAA,MACrD,UAAU;AAAA,MACV,gBAAgB,MAAM,KAAK,SAAS;AAAA,IACtC,EAAE;AAAA,EACJ,EAAE,KAAK,eAAe,EAAE,OAAO,WAAW;AAC5C;AAOA,iBAAiB,UAAU,mBAAmB,SAAS,SAAS;AAO9D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,MAAI,kBAAkB,MAAM;AAE5B,MAAI,CAAC,iBAAiB;AACpB,sBAAkB,MAAM,kBAAkB,KAAK,oBAAoB,OAAO;AAAA,EAC5E;AAEA,QAAM,iBAAiB,aAAa,UAAU;AAE9C,MAAI,cAAc,cAAc,GAAG;AAKjC,QAAI,MAAM,qBAAqB,gBAAgB;AAC7C,cAAQ,MAAM,iEAAiE;AAE/E;AAAA,IACF;AAAA,EACF;AAEA,SAAO,gBAAgB,QAAQ;AAC7B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,MAAM;AAE1B,QAAI,UAAU;AAKZ,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AAGA,YAAM,mBAAmB;AAEzB,aAAO;AAAA,QACL;AAAA,QACA,MAAM;AAAA,QACN,MAAM,SAAS;AAAA,QACf,cAAc;AAAA,QACd,UAAU;AAAA,MACZ;AAAA,IACF;AAEA,SAAK,WAAW,OAAO;AAAA,MACrB,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEF;AAKA,SAAS,MAAM,KAAK;AAClB,SAAO,OAAO,IAAI,CAAC;AACrB;AAEA,SAAS,KAAK,KAAK;AACjB,SAAO,OAAO,IAAI,IAAI,SAAS,CAAC;AAClC;;;AC7Oe,SAAR,mBACH,WACA,kBACA,eACA,qBACA,iBAAiB;AAEnB,OAAK,aAAa;AAClB,OAAK,oBAAoB;AACzB,OAAK,iBAAiB;AACtB,OAAK,uBAAuB;AAC5B,OAAK,mBAAmB;AAExB,YAAU,iBAAiB,mBAAmB,IAAI;AAClD,YAAU,iBAAiB,oBAAoB,IAAI;AACnD,YAAU,iBAAiB,wBAAwB,IAAI;AACzD;AAEA,mBAAmB,UAAU;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,mBAAmB,UAAU,SAAS,SAAS,SAAS;AACtD,OAAK,OAAO,OAAO;AACrB;AAEA,mBAAmB,UAAU,QAAQ,SAAS,SAAS;AAErD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAEJ,QAAM,gBAAgB,KAAK,kBAAkB,cAAc,OAAO;AAElE,MAAI,eAAe;AACjB,WAAO,KAAK,kBAAkB,cAAc,SAAS,aAAa;AAAA,EACpE;AAEA,OAAK,OAAO,OAAO;AACrB;AAEA,mBAAmB,UAAU,OAAO,SAAS,SAAS;AAEpD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAEJ,QAAM,cAAc,MAAM;AAI1B,MAAI,YAAY,kBAAkB,OAAO;AACvC,gBAAY,SAAS;AAAA,EACvB;AAEA,OAAK,kBAAkB,KAAK,OAAO;AACrC;AAEA,mBAAmB,UAAU,SAAS,SAAS,SAAS;AACtD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,cAAc,MAAM;AAE1B,MAAI,kBAAkB,OAAO,GAAG;AAE9B,QAAI,CAAC,YAAY;AACf,YAAM,IAAI,MAAM,sDAAsD;AAAA,IACxE;AAAA,EACF,OAAO;AACL,QAAI,YAAY;AACd,YAAM,IAAI,MAAM,sDAAsD;AAAA,IACxE;AAAA,EACF;AAEA,MAAI,YAAY,WAAW;AACzB,UAAM,IAAI,MAAM,iBAAiB,YAAY,EAAE,aAAa;AAAA,EAC9D;AAEA,MAAI,cAAc,OAAO,GAAG;AAC1B,SAAK,qBAAqB,MAAM,OAAO;AAAA,EACzC;AAEA,MAAI,cAAc,eAAe,UAAU,GAAG;AAC5C,SAAK,eAAe,UAAU,aAAa,KAAK;AAAA,EAClD;AAEA,QAAM,aAAa,KAAK,YAAY,SAAS,UAAU;AAEvD,aAAWE,YAAW,YAAY;AAEhC,QAAI,aAAaA,QAAO,GAAG;AACzB,WAAK,WAAW,OAAO;AAAA,QACrB,SAAAA;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA,MACb,CAAC;AAAA,IACH,OAAO;AACL,WAAK,WAAW,MAAM;AAAA,QACpB,SAAAA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,mBAAmB,UAAU,cAAc,SAAS,SAAS,YAAY;AACvE,QAAMC,gBAAe,aACjB,CAAC,SAAS,eAAe,OACzB,CAAC,SAAS,iBAAiB,IAAI;AAEnC,SAAO,YAAY,SAAS,KAAK,gBAAgB,EAAE;AAAA,IACjD,UACEA,cAAa,IAAI,KAAK,qBAAqB,IAAI;AAAA,EAEnD;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,GAAG,SAAS,kBAAkB;AACvC;;;ACpHA,IAAM,eAAe;AAAA,EACnB,MAAM;AAAA,EACN,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AACd;AAGe,SAAR,oBAAqC,WAAW,eAAe,iBAAiB;AACrF,OAAK,aAAa;AAClB,OAAK,iBAAiB;AACtB,OAAK,mBAAmB;AAC1B;AAEA,oBAAoB,UAAU;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AACF;AAEA,oBAAoB,UAAU,QAAQ,SAAS,SAAS;AAEtD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAEJ,QAAM,qBAAqB,KAAK,WAAW,UAAU,OAAO,cAAc,CAAC,cAAc;AAEvF,uBAAmB,OAAO;AAE1B,WAAO,KAAK,OAAO;AAAA,MACjB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,QAAM,kBAAkB;AAAA,IACtB,MAAM;AAAA,IACN,KAAK,MAAM;AAAA,IACX,YAAY;AAAA,IACZ,QAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,yBAAyB,KAAK,WAAW,UAAU,OAAO,iBAAiB,CAAC,cAAc;AAI9F,QAAI,CAAC,MAAM,UAAU;AACnB,aAAO,KAAK,WAAW,QAAQ;AAAA,QAC7B,OAAO;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH;AAEA,2BAAuB,OAAO;AAE9B,WAAO,KAAK,WAAW;AAAA,MACrB;AAAA,MACA,SAAS,MAAM;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,oBAAoB,UAAU,SAAS,SAAS,SAAS;AAEvD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAGJ,MAAI,MAAM,WAAW;AACnB;AAAA,EACF;AAGA,QAAM,OAAO,SAAS;AAGtB,OAAK,WAAW,QAAQ;AAAA,IACtB,OAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAK,MAAM;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAGD,SAAO,KAAK,WAAW,QAAQ;AAAA,IAC7B;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAEA,oBAAoB,UAAU,uBAAuB,SAAS,OAAO;AAEnE,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAOJ,QAAM,WAAW,YAAY,SAAS,KAAK,gBAAgB;AAE3D,QAAM,wBAAwB,SAAS;AAAA,IACrC;AAAA,EACF,EAAE;AAAA,IACA,CAAAC,aAAW,YAAYA,UAAS,KAAK,gBAAgB,EAAE;AAAA,MACrD,CAAAA,aAAW,aAAaA,QAAO,KAAK,oBAAoBA,QAAO;AAAA,IACjE;AAAA,EACF,EAAE,OAAO,OAAK,CAAC;AAEf,QAAM,2BAA2B,QAAQ,UAAU,OAAO,mBAAmB;AAE7E,MAAI,CAAC,sBAAsB,UAAU,CAAC,yBAAyB,QAAQ;AACrE;AAAA,EACF;AAGA,QAAM,mBAAmB,KAAK,qBAAqB,MAAM,MAAM;AAO/D,MAAI,CAAC,GAAG,iBAAiB,SAAS,kBAAkB,GAAG;AACrD,SAAK,gBAAgB,gBAAgB;AAAA,EACvC;AAEA,aAAW,cAAc,uBAAuB;AAE9C,UAAM,oBAAoB;AAAA,MACxB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,KAAK;AAAA,MACL,QAAQ,YAAY;AAAA,IACtB;AAEA,UAAM,qBAAqB,WAAW;AAEtC,UAAM,eAAe,KAAK,WAAW,UAAU,OAAO,mBAAmB,eAAa;AAEpF,mBAAa,OAAO;AAEpB,aAAO,KAAK,WAAW,OAAO;AAAA,QAC5B;AAAA,QACA,SAAS;AAAA,QACT;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,aAAW,iBAAiB,0BAA0B;AAEpD,UAAM,oBAAoB;AAAA,MACxB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,KAAK;AAAA,MACL,QAAQ,YAAY;AAAA,IACtB;AAEA,UAAM,qBAAqB,cAAc,SAAS;AAAA,MAChD,cAAY,SAAS;AAAA,IACvB,EAAE;AAAA,MACA;AAAA,IACF;AAEA,QAAI,CAAC,oBAAoB;AACvB;AAAA,IACF;AAEA,UAAM,eAAe,KAAK,WAAW,UAAU,kBAAkB,mBAAmB,eAAa;AAE/F,mBAAa,OAAO;AAGpB,aAAO,KAAK,WAAW,MAAM;AAAA,QAC3B;AAAA,QACA,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAEA,oBAAoB,UAAU,kBAAkB,SAAS,OAAO;AAE9D,MAAI,MAAM,SAAS,YAAY,WAAW,KAAK,CAAC,MAAM,QAAQ;AAC5D;AAAA,EACF;AAEA,QAAM,kBAAkB;AAAA,IACtB,MAAM;AAAA,IACN,KAAK,MAAM;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,QAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,YAAY;AAElB,QAAM,WAAW,KAAK,WAAW,UAAU,OAAO,iBAAiB,CAAC,cAAc;AAEhF,aAAS,OAAO;AAEhB,UAAM,KAAK,SAAS;AAEpB,SAAK,WAAW;AAAA,MACd;AAAA,MACA,SAAS,MAAM;AAAA,MACf;AAAA,IACF,CAAC;AAED,SAAK,eAAe,QAAQ,OAAO,SAAS;AAE5C,WAAO;AAAA,EACT,CAAC;AAED,QAAM,cAAc,MAAM;AAE1B,MAAI,CAAC,aAAa;AAChB;AAAA,EACF;AAEA,QAAM,YAAY,KAAK,WAAW,UAAU,aAAa,iBAAiB,eAAa;AAErF,cAAU,OAAO;AAEjB,WAAO,KAAK,WAAW,QAAQ;AAAA,MAC7B;AAAA,MACA,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EAEH,CAAC;AAED,OAAK,gBAAgB,WAAW;AAClC;AAGA,oBAAoB,UAAU,uBAAuB,SAAS,OAAO;AAEnE,MAAI,cAAc;AAElB,SAAO,aAAa;AAClB,UAAM,UAAU,YAAY;AAE5B,QAAI,GAAG,SAAS,iBAAiB,KAAK,CAAC,kBAAkB,OAAO,GAAG;AACjE,aAAO;AAAA,IACT;AAEA,QAAIC,OAAM,SAAS;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,GAAG;AACF,aAAO;AAAA,IACT;AAEA,kBAAc,YAAY;AAAA,EAC5B;AAEA,QAAM,qBAAqB,KAAK;AAClC;AAEA,oBAAoB,UAAU,aAAa,SAAS,SAAS;AAE3D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAGJ,QAAM,0BAA0B;AAAA,IAC9B,MAAM;AAAA,IACN,kBAAgB,YAAY,EAAE,MAAM,aAAa,GAAG,aAAa,KAAK;AAAA,EACxE;AAEA,QAAM,qBAAqB,wBAAwB,OAAO,kBAAgB,aAAa,MAAM,QAAQ,OAAO;AAE5G,QAAM,qBAAqB,wBAAwB,OAAO,kBAAgB,aAAa,MAAM,QAAQ,OAAO;AAE5G,aAAW,gBAAgB,oBAAoB;AAC7C,SAAK,eAAe,QAAQ,OAAO,eAAa;AAC9C,aAAO,KAAK,WAAW,QAAQ,YAAY;AAAA,IAC7C,CAAC;AAAA,EACH;AAEA,aAAW,gBAAgB,mBAAmB,QAAQ,GAAG;AACvD,SAAK,eAAe,QAAQ,OAAO,eAAa;AAC9C,aAAO,KAAK,WAAW,QAAQ,YAAY;AAAA,IAC7C,CAAC;AAAA,EACH;AACF;AAKA,SAAS,qBAAqB,OAAO;AACnC,QAAM,IAAI,MAAM,+BAA+B,MAAM,EAAE,GAAG;AAC5D;;;AC9Ue,SAAR,qBACH,WACA,eAAe;AAEjB,OAAK,aAAa;AAClB,OAAK,iBAAiB;AAEtB,YAAU,iBAAiB,qBAAqB,IAAI;AACtD;AAEA,qBAAqB,UAAU,QAAQ,SAAS,SAAS;AACvD,OAAK,WAAW,KAAK,OAAO;AAC9B;AAEA,qBAAqB,UAAU,OAAO,SAAS,SAAS;AACtD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,OAAK,WAAW,MAAM;AAAA,IACpB,WAAW;AAAA,IACX,SAAS,QAAQ;AAAA,IACjB,OAAO,MAAM;AAAA,EACf,CAAC;AACH;AAEA,qBAAqB,UAAU;AAAA,EAC7B;AAAA,EACA;AACF;;;ACzBe,SAAR,oBAAqC,WAAW;AACrD,OAAK,aAAa;AAElB,YAAU,iBAAiB,oBAAoB,IAAI;AACrD;AAEA,oBAAoB,UAAU,CAAE,WAAY;AAE5C,oBAAoB,UAAU,SAAS,SAAS,SAAS;AACvD,OAAK,WAAW,KAAK,OAAO;AAC9B;AAEA,oBAAoB,UAAU,OAAO,SAAS,SAAS;AACrD,QAAM;AAAA,IACJ;AAAA,IACA,OAAO;AAAA,EACT,IAAI;AAEJ,QAAM,SAAS,QAAQ;AAKvB,QAAMC,SAAQ,aAAa,MAAM,IAAI,SAAS;AAAA,IAC5C,MAAM;AAAA,IACN;AAAA,IACA,MAAM,QAAQ;AAAA,EAChB;AAEA,QAAM,eAAe,KAAK,WAAW,iBAAiB;AAAA,IACpD,OAAAA;AAAA,IACA,UAAU,CAAE,QAAQ,OAAO,MAAO;AAAA,EACpC,CAAC;AAED,MAAI,cAAc;AAChB,SAAK,WAAW,QAAQ;AAAA,MACtB,OAAAA;AAAA,MACA;AAAA,MACA,OAAO,aAAa;AAAA,IACtB,CAAC;AAAA,EACH;AACF;;;ACpCO,SAASC,IAAG,SAAS,OAAO;AACjC,MAAI,QAAQ,SAAS,SAAS;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAQ,CAAE,KAAM;AAAA,EAClB;AAEA,SAAO,MAAM,KAAK,SAAS,MAAM;AAC/B,WAAO,GAAK,SAAS,IAAI;AAAA,EAC3B,CAAC;AACH;AAEO,SAAS,mBAAmBC,QAAO,qBAAqB;AAC7D,SAAO,KAAK,kBAAkBA,MAAK,EAAE,kBAAkB,gBAAc;AACnE,WAAOD,IAAG,YAAY,mBAAmB;AAAA,EAC3C,CAAC;AACH;AAEO,SAASE,cAAaD,QAAO,qBAAqB;AACvD,SAAO,KAAK,kBAAkBA,MAAK,EAAE,kBAAkB,gBAAc;AACnE,WAAOD,IAAG,YAAY,mBAAmB;AAAA,EAC3C,CAAC;AACH;;;AClBe,SAAR,eACH,WACA,iBACA,eAAe;AAEjB,OAAK,aAAa;AAClB,OAAK,mBAAmB;AACxB,OAAK,iBAAiB;AACxB;AAEA,eAAe,UAAU;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AACF;AAGA,eAAe,UAAU,MAAM,SAAS,SAAS;AAE/C,QAAM,YAAY;AAAA,IAChB,4BAA4B,CAAC,YAAY;AAEvC,YAAM;AAAA,QACJ,SAAAG;AAAA,QACA;AAAA,MACF,IAAI;AAEJ,YAAM,OAAO,kBAAkBA,QAAO;AAEtC,YAAM,cAAc,MAAM;AAC1B,YAAM,gBAAgB,YAAY;AAClC,YAAM,WAAW,YAAY,eAAe,KAAK,gBAAgB;AAEjE,YAAM,cAAc,SAAS;AAAA,QAAO,CAAAA,aAClC,YAAYA,QAAO,KACnB,kBAAkBA,QAAO,EAAE,SAAS,KAAK;AAAA,MAC3C;AAEA,iBAAW,cAAc,aAAa;AACpC,aAAK,WAAW,OAAO;AAAA,UACrB,SAAS;AAAA,UACT;AAAA,UACA,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IAEA,8BAA8B,CAAC,YAAY;AAMzC,YAAM;AAAA,QACJ,SAAAA;AAAA,QACA;AAAA,MACF,IAAI;AAEJ,YAAM,gBAAgB,KAAK,WAAW,kBAAkB;AAAA,QACtD,OAAOA;AAAA,MACT,CAAC;AAED,YAAM,iBAAiB,oBAAI,IAAI;AAE/B,iBAAW,gBAAgB,eAAe;AAExC,cAAM,gBAAgB,aAAa;AAEnC,YAAI,eAAe,IAAI,aAAa,GAAG;AACrC;AAAA,QACF;AAEA,uBAAe,IAAI,aAAa;AAEhC,aAAK,WAAW,QAAQ;AAAA,UACtB,OAAOA;AAAA,UACP,OAAO;AAAA,UACP,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IAEA,kCAAkC,CAAC,YAAY;AAM7C,YAAM;AAAA,QACJ,SAAAA;AAAA,QACA;AAAA,MACF,IAAI;AAEJ,YAAM,SAAS,KAAK,WAAW,WAAW;AAAA,QACxC,cAAc;AAAA,UACZ,OAAOA;AAAA,QACT;AAAA,QACA,OAAO,YAAY;AAAA,MACrB,CAAC;AAED,UAAI,eAAe;AAEnB,aAAQ,eAAe,aAAa,QAAS;AAE3C,YAAI,OAAO,SAAS,YAAY,GAAG;AACjC,eAAK,WAAW,QAAQ;AAAA,YACtB,OAAOA;AAAA,YACP,OAAO;AAAA,YACP,WAAW;AAAA,UACb,CAAC;AAED;AAAA,QACF;AAAA,MACF;AAAA,IAEF;AAAA,IAEA,6BAA6B,CAAC,YAAY;AAMxC,YAAM;AAAA,QACJ,SAAAA;AAAA,QACA;AAAA,MACF,IAAI;AAEJ,YAAM,SAAS,KAAK,WAAW,WAAW;AAAA,QACxC,cAAc;AAAA,UACZ,OAAOA;AAAA,QACT;AAAA,QACA,OAAO,YAAY;AAAA,MACrB,CAAC;AAED,UAAI,eAAe;AAInB,aAAQ,eAAe,aAAa,QAAS;AAE3C,YAAI,OAAO,SAAS,YAAY,GAAG;AACjC,eAAK,WAAW,QAAQ;AAAA,YACtB,OAAOA;AAAA,YACP,OAAO;AAAA,YACP,WAAW;AAAA,UACb,CAAC;AAED;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,iCAAiC,CAAC,YAAY;AAC5C,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AAEJ,WAAK,eAAe,UAAU,MAAM,QAAQ,KAAK;AAAA,IACnD;AAAA,IAEA,8BAA8B,CAAC,YAAY;AAIzC,YAAM;AAAA,QACJ;AAAA,QACA,SAAAA;AAAA,MACF,IAAI;AAEJ,WAAK,WAAW,QAAQ;AAAA,QACtB,OAAOA;AAAA,QACP,WAAW;AAAA,QACX,OAAO,sBAAsB,KAAK;AAAA,MACpC,CAAC;AAAA,IACH;AAAA,IAEA,kCAAkC,CAAC,YAAY;AAE7C,YAAM;AAAA,QACJ;AAAA,QACA,SAAAA;AAAA,MACF,IAAI;AAEJ,aAAO,KAAK,WAAW;AAAA,QACrB;AAAA,QACA,KAAK,WAAW,QAAQ;AAAA,UACtB,OAAOA;AAAA,UACP,OAAO,sBAAsB,KAAK;AAAA,QACpC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,QAAM,QAAQ,OAAO,QAAQ,SAAS,EAAE;AAAA,IACtC,CAAAC,WAASC,cAAa,SAASD,OAAM,CAAC,CAAC;AAAA,EACzC;AAEA,SAAO,SAAS,MAAM,CAAC;AACzB;AAKA,SAAS,kBAAkB,SAAS;AAClC,SAAO,mBAAmB,SAAS,0BAA0B;AAC/D;AAEA,SAAS,sBAAsB,OAAO;AAGpC,SAAO,kBAAkB,MAAM,OAAO,OAAO,GAAG;AAC9C,YAAQ,MAAM;AAAA,EAChB;AAEA,SAAO,MAAM;AACf;;;ACxOA,IAAM,iBAAiB;AAAA,EACrB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AACZ;AAEA,IAAM,aAAa;AAAA,EACjB,MAAM;AAAA,EACN,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AACd;AAGe,SAAR,cAA+B,WAAW;AAC/C,OAAK,aAAa;AACpB;AAEA,cAAc,UAAU;AAAA,EACtB;AACF;AAUA,cAAc,UAAU,aAAa,SAAS,OAAO,eAAe,MAAM;AAExE,iBAAe,WAAW,YAAY;AAEtC,SAAO,MAAM,SAAS,MAAM,OAAK,EAAE,aAAa,EAAE,aAAa,aAAa,CAAC,CAAC;AAChF;AASA,cAAc,UAAU,kBAAkB,SAAS,OAAO,WAAW,eAAe,MAAM;AAExF,iBAAe,WAAW,YAAY;AAEtC,QAAM,SAAS,OAAO,OAAK,CAAC,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,OAAK;AACpE,SAAK,WAAW,aAAa,GAAG,SAAS;AAAA,EAC3C,CAAC;AACH;AAEA,cAAc,UAAU,YAAY,SAAS,OAAO,WAAW;AAG7D,OAAK,gBAAgB,OAAO,SAAS;AAGrC,QAAM,UAAU,SAAS;AAGzB,OAAK,QAAQ,OAAO,SAAS;AAC/B;AAEA,cAAc,UAAU,YAAY,SAAS,OAAO,WAAW;AAG7D,OAAK,gBAAgB,OAAO,WAAW,SAAS;AAGhD,QAAM,KAAK,SAAS;AACtB;AAEA,cAAc,UAAU,UAAU,SAAS,OAAO,WAAW;AAC3D,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACnC;AAEA,MAAI,CAAC,WAAW;AACd,gBAAY;AAAA,EACd;AAEA,MAAI,CAAC,KAAK,WAAW,OAAO,SAAS,GAAG;AACtC,WAAO;AAAA,EACT;AAEA,QAAM,uBAAuB,KAAK,WAAW,kBAAkB;AAAA,IAC7D,OAAO;AAAA,IACP;AAAA,EACF,CAAC;AAED,aAAW,gBAAgB,sBAAsB;AAE/C,UAAM;AAAA,MACJ,OAAAE;AAAA,MACA,OAAAC;AAAA,IACF,IAAI;AAEJ,UAAM,SAAS,KAAK,WAAW,QAAQ;AAAA,MACrC,OAAAD;AAAA,MACA,OAAAC;AAAA,MACA;AAAA,IACF,CAAC;AAED,QAAI,OAAO,QAAQ;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,OAAK,WAAW,QAAQ;AAAA,IACtB,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACF,CAAC;AAED,OAAK,KAAK;AAAA,IACR;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,cAAc,UAAU,OAAO,SAAS,SAAS;AAE/C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,OAAK,WAAW,KAAK;AAAA,IACnB,SAAS,MAAM;AAAA,IACf;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,cAAc,UAAU,UAAU,SAAS,OAAO,WAAW;AAC3D,QAAM,eAAe,KAAK,WAAW,UAAU,OAAO,gBAAgB,CAAC,cAAc;AAEnF,iBAAa,OAAO;AAEpB,WAAO,UAAU,SAAS;AAAA,EAC5B,CAAC;AAED,SAAO;AACT;AAYA,SAAS,WAAW,WAAW;AAE7B,MAAI,OAAO,cAAc,YAAY;AACnC,WAAO;AAAA,EACT;AAEA,SAAO,CAAC,UAAU,UAAU;AAC9B;;;AClKe,SAAR,gBACH,WACA,eAAe;AAEjB,OAAK,aAAa;AAClB,OAAK,iBAAiB;AAEtB,YAAU,iBAAiB,gBAAgB,IAAI;AAC/C,YAAU,iBAAiB,oBAAoB,IAAI;AACrD;AAEA,gBAAgB,UAAU,SAAS,SAAS,SAAS;AAEnD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,aAAa,KAAK,YAAY,SAAS,UAAU;AAAA,IACjD;AAAA,EACF,IAAI;AAEJ,MAAI,CAAC,WAAW,QAAQ;AACtB,UAAM,IAAI,MAAM,sCAAsC;AAAA,EACxD;AAEA,aAAW,aAAa,YAAY;AAElC,QAAI,aAAa,SAAS,GAAG;AAC3B,WAAK,WAAW,OAAO;AAAA,QACrB,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,OAAO;AACL,WAAK,WAAW,MAAM;AAAA,QACpB,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEF;AAEA,gBAAgB,UAAU,OAAO,SAAS,SAAS;AAEjD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAIJ,OAAK,eAAe,gBAAgB,OAAO,SAAS;AACtD;AAEA,gBAAgB,UAAU,cAAc,SAAS,SAAS,YAAY;AAEpE,QAAMC,gBAAe,aACjB,CAAC,SAAS,eAAe,OACzB,CAAC,SAAS,iBAAiB,IAAI;AAEnC,SAAO,QAAQ,SAAS;AAAA,IACtB,UACEA,cAAa,IAAI,KAAK,qBAAqB,IAAI;AAAA,EAEnD;AACF;AAEA,gBAAgB,UAAU;AAAA,EACxB;AAAA,EACA;AACF;;;ACpDA,IAAO,oBAAQ;AAAA,EACb,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,oBAAoB,CAAE,QAAQ,kBAAmB;AAAA,EACjD,kBAAkB,CAAE,QAAQ,gBAAiB;AAAA,EAC7C,uBAAuB,CAAE,QAAQ,qBAAsB;AAAA,EACvD,gCAAgC,CAAE,QAAQ,8BAA+B;AAAA,EACzE,gCAAgC,CAAE,QAAQ,8BAA+B;AAAA,EACzE,0BAA0B,CAAE,QAAQ,wBAAyB;AAAA,EAC7D,yBAAyB,CAAE,QAAQ,uBAAwB;AAAA,EAC3D,2BAA2B,CAAE,QAAQ,yBAA0B;AAAA,EAC/D,0BAA0B,CAAE,QAAQ,wBAAyB;AAAA,EAC7D,kBAAkB,CAAE,QAAQ,gBAAiB;AAAA,EAC7C,oBAAoB,CAAE,QAAQ,kBAAmB;AAAA,EACjD,sBAAsB,CAAE,QAAQ,oBAAqB;AAAA,EACrD,qBAAqB,CAAE,QAAQ,mBAAoB;AAAA,EACnD,gBAAgB,CAAE,QAAQ,cAAe;AAAA,EACzC,eAAe,CAAE,QAAQ,aAAc;AAAA,EACvC,iBAAiB,CAAE,QAAQ,eAAgB;AAAA,EAC3C,qBAAqB,CAAE,QAAQ,mBAAoB;AACrD;;;ACtDA,IAAM,gBAAgB;AAEtB,IAAO,oBAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,CAAE,YAAY,aAAa,SAAS,UAAU,WAAW;AACvD,eAAS,GAAG;AAAA,QACV;AAAA,QACA;AAAA,MACF,GAAG,eAAe,CAAAC,WAAS;AACzB,kBAAU,MAAM;AAAA,MAClB,CAAC;AAAA,IACH,CAAE;AAAA,EACJ;AAAA,EACA,WAAW,CAAE,QAAQ,SAAU;AACjC;;;ACfe,SAAR,4BAA6C,UAAU,WAAW;AACvE,WAAS,OAAO,qBAAqB,IAAI;AAEzC,OAAK,aAAa;AACpB;AAEA,EAAS,6BAA6B,mBAAmB;AAEzD,4BAA4B,UAAU;AAAA,EACpC;AAAA,EACA;AACF;AAEA,4BAA4B,UAAU,SAAS,SAAS,SAAS;AAE/D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,OAAK,WAAW,QAAQ,SAAS,OAAO,MAAM;AAC5C,wBAAoB,UAAU,OAAO,KAAK,MAAM,OAAO;AAAA,EACzD,CAAC;AACH;;;ACvBe,SAAR,6BAA8C,UAAU,WAAW;AACxE,WAAS,OAAO,sBAAsB,IAAI;AAE1C,OAAK,aAAa;AACpB;AAEA,EAAS,8BAA8B,oBAAoB;AAE3D,6BAA6B,UAAU;AAAA,EACrC;AAAA,EACA;AACF;AAEA,6BAA6B,UAAU,QAAQ,SAAS,SAAS;AAE/D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,OAAK,WAAW,QAAQ,SAAS,OAAO,MAAM;AAC5C,yBAAqB,UAAU,MAAM,KAAK,MAAM,OAAO;AAAA,EACzD,CAAC;AACH;;;ACzBA,IAAOC,qBAAQ;AAAA,EACb,sBAAsB,CAAE,QAAQ,4BAA6B;AAAA,EAC7D,qBAAqB,CAAE,QAAQ,2BAA4B;AAC7D;;;ACNA,IAAM,oBAAoB;AAC1B,IAAM,wBAAwB;AAC9B,IAAM,yBAAyB;AAC/B,IAAM,yBAAyB;AAC/B,IAAM,0BAA0B;AAChC,IAAM,gCAAgC;AACtC,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAC9B,IAAM,sBAAsB;AAC5B,IAAM,qBAAqB;AAC3B,IAAM,6BAA6B;AACnC,IAAM,cAAc;;;ACHpB,IAAM,uBAAuB,CAAC,MAAM;AAGrB,SAAR,YAA6B,UAAU,WAAW;AACvD,OAAK,YAAY;AACjB,OAAK,aAAa;AAElB,OAAK,UAAU;AAEf,WAAS,GAAG;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG,MAAM;AACP,SAAK,UAAU;AAAA,EACjB,CAAC;AAED,WAAS,GAAG,uBAAuB,CAAAC,WAAS;AAE1C,UAAM;AAAA,MACJ;AAAA,IACF,IAAIA;AAKJ,QAAI,KAAK,WAAW,SAAS,MAAM,QAAQ;AACzC,WAAK,OAAO,MAAM,MAAM;AAAA,IAC1B;AAAA,EACF,CAAC;AAGD,WAAS,GAAG,oBAAoB,CAAAA,WAAS;AAEvC,UAAM;AAAA,MACJ;AAAA,IACF,IAAIA;AAKJ,QAAI,CAAC,MAAM,UAAU,KAAK,UAAU,CAAC,WAAW,KAAK,QAAQ,KAAK,GAAG;AACnE,WAAK,OAAO,IAAI;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AAEA,YAAY,UAAU,SAAS,SAAS,OAAO;AAE7C,QAAM,YAAY,KAAK,WAAW;AAElC,OAAK,SAAS,YAAY,QAAQ;AAElC,OAAK,UACH,KAAK,SACD,OAAK,WAAW,KAAK,QAAQ,CAAC,IAC9B,OAAK;AAEX,OAAK,UAAU,KAAK,4BAA4B;AAAA,IAC9C,QAAQ,KAAK;AAAA,IACb,OAAO,KAAK;AAAA,EACd,CAAC;AACH;AAEA,YAAY,UAAU,UAAU,SAAS,OAAO;AAE9C,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAQ,KAAK,WAAW,UAAU,OAAK,EAAE,OAAO,KAAK;AAAA,EACvD;AAEA,SAAO,SAAS,KAAK,QAAQ,KAAK;AACpC;AAEA,YAAY,UAAU,YAAY,SAAS,SAAS;AAClD,SAAO,KAAK,WAAW,WAAW,OAAO,EAAE,OAAO,OAAK,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC;AAC3E;AAEA,YAAY,UAAU;AAAA,EACpB;AAAA,EACA;AACF;AAEA,SAAS,WAAW,QAAQ,OAAO;AACjC,KAAG;AACD,QAAI,WAAW,OAAO;AACpB,aAAO;AAAA,IACT;AAAA,EACF,SAAU,QAAQ,MAAM;AAExB,SAAO;AACT;;;AC/FA,IAAO,uBAAQ;AAAA,EACb,aAAa,CAAE,QAAQ,WAAY;AACrC;;;ACiBA,IAAM,QAAQ,iBAAiB,SAAS,eAAe;AAEvD,IAAM,wBAAwB,MAAM,iBAAiB,kCAAkC;AACvF,IAAM,0BAA0B,MAAM,iBAAiB,0BAA0B;AAEjF,SAAS,OAAO;AAAC;AAEjB,SAAS,iBAAiB,OAAO,WAAW;AAG1C,MAAI,UAAU,WAAW,GAAG;AAC1B,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,UAAU,SAAS,GAAG;AAClC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,IAAM,cAAc,SAAS,KAAK;AAChC,SAAO;AACT;AACA,IAAM,UAAU,SAAS,KAAK;AAC5B,SAAO,CAAC,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI;AACxC;AACA,IAAM,WAAW,SAAS,KAAK;AAC7B,SAAO,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC;AACnC;AACA,IAAM,cAAc,SAAS,KAAK;AAChC,SAAO,CAAC,KAAK,IAAI,MAAM,KAAK,EAAE,IAAI,IAAI;AACxC;AAEA,IAAM,aAAa;AASJ,SAAR,UAA2B,QAAQ,QAAQ,UAAU,aAAa;AACvE,OAAK,YAAY;AACjB,OAAK,eAAe;AACpB,OAAK,UAAU;AAEf,OAAK,aAAa,UAAU,OAAO,cAAc;AAEjD,OAAK,cAAc,oBAAI,IAAI;AAC3B,OAAK,SAAS;AAEd,WAAS,GAAG;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG,MAAM;AACP,SAAK,gBAAgB;AAAA,EACvB,CAAC;AAED,WAAS,GAAG,wBAAwB,MAAM;AACxC,SAAK,MAAM;AAAA,EACb,CAAC;AAED,WAAS,GAAG,uBAAuB,MAAM;AACvC,SAAK,KAAK;AAAA,EACZ,CAAC;AAED,WAAS,GAAG,4BAA4B,CAAAC,WAAS;AAE/C,SAAK,KAAK,eAAa;AACrB,UAAI,KAAK,aAAa,QAAQ,UAAU,KAAK,GAAG;AAC9C,kBAAU,KAAK;AAAA,MACjB,OAAO;AACL,kBAAU,KAAK;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,WAAS,GAAG,uBAAuB,CAAAA,WAAS;AAC1C,UAAM;AAAA,MACJ;AAAA,IACF,IAAIA;AAEJ,SAAK,gBAAgB,KAAK;AAAA,EAC5B,CAAC;AACH;AAEA,UAAU,UAAU,UAAU,SAAS,YAAY,OAAO,MAAM;AAC9D,OAAK,gBAAgB,YAAY,OAAO,IAAI;AAC9C;AAEA,UAAU,UAAU,QAAQ,WAAW;AACrC,OAAK,KAAK,eAAa,UAAU,MAAM,CAAC;AAC1C;AAEA,UAAU,UAAU,OAAO,WAAW;AACpC,OAAK,KAAK,eAAa,UAAU,KAAK,CAAC;AACzC;AAEA,UAAU,UAAU,OAAO,SAAS,IAAI;AACtC,OAAK,YAAY,QAAQ,EAAE;AAC7B;AAEA,UAAU,UAAU,kBAAkB,SAAS,YAAY,OAAO,OAAO,MAAM;AAC7E,QAAM,QAAQ,KAAK,UAAU,KAAK;AAElC,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AAEA,QAAM,WAAW,KAAK,gBAAgB,OAAO,KAAK;AAElD,QAAM,YAAY,IAAI,eAAe,UAAU,WAAW,WAAW,KAAK,YAAY,MAAM;AAC1F,SAAK,gBAAgB,SAAS;AAE9B,SAAK;AAAA,EACP,CAAC;AAED,YAAU,SAAS,KAAK,kBAAkB,CAAC;AAE3C,MAAI,CAAC,KAAK,aAAa,QAAQ,KAAK,GAAG;AACrC,cAAU,KAAK;AAAA,EACjB;AAEA,YAAU,QAAQ;AAClB,YAAU,UAAU;AAEpB,OAAK,YAAY,IAAI,SAAS;AAE9B,OAAK,UAAU,KAAK,yBAAyB;AAAA,IAC3C;AAAA,EACF,CAAC;AAED,YAAU,KAAK;AAEf,SAAO;AACT;AAEA,UAAU,UAAU,oBAAoB,SAAS,OAAO;AACtD,OAAK,SAAS;AAEd,OAAK,KAAK,eAAa,UAAU,SAAS,KAAK,CAAC;AAEhD,OAAK,UAAU,KAAK,+BAA+B;AAAA,IACjD;AAAA,EACF,CAAC;AACH;AAEA,UAAU,UAAU,oBAAoB,WAAW;AACjD,SAAO,KAAK;AACd;AAEA,UAAU,UAAU,kBAAkB,SAAS,OAAO;AACpD,OAAK,KAAK,eAAa;AACrB,QAAI,CAAC,SAAS,UAAU,UAAU,OAAO;AACvC,WAAK,gBAAgB,SAAS;AAAA,IAChC;AAAA,EACF,CAAC;AACH;AAEA,UAAU,UAAU,kBAAkB,SAAS,WAAW;AACxD,YAAU,OAAO;AAEjB,OAAK,YAAY,OAAO,SAAS;AACnC;AAEA,UAAU,UAAU,kBAAkB,SAAS,OAAO,OAAO;AAC3D,QAAM,SAAS,OAAU,KAAK,aAAa,KAAK,EAAE,KAAK,CAAC;AAExD,SAAO,SAAY,QAAQ,KAAK;AAClC;AAEA,UAAU,UAAU,eAAe,SAAS,OAAO;AAEjD,QAAM,SAAS,MAAM,UAAU;AAAA,IAC7B,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAEA,SAAO;AAAA;AAAA;AAAA;AAAA,aAII,aAAa,CAAC;AAAA,cACb,aAAa,CAAC;AAAA,cACd,aAAa,CAAC;AAAA,gBACX,OAAO,OAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAMf,OAAO,SAAU;AAAA;AAAA;AAAA;AAIlC;AAEA,UAAU,UAAU,YAAY,SAAS,OAAO;AAE9C,MAAI,SAAS,KAAK;AAElB,MAAI,OAAO;AAIX,MAAI,cAAc,QAAQ;AACxB,WAAO,OAAO,SAAS,MAAM,OAAO;AACpC,YAAQ,OAAO,kBAAkB,IAAI,EAAE;AAAA,EACzC,OAAO;AACL,YAAQ,MAAS,aAAa,OAAO,IAAI;AAAA,EAC3C;AAEA,MAAI,QAAQ,MAAS,yBAAyB,KAAK;AAEnD,MAAI,CAAC,OAAO;AACV,YAAQ,OAAU,oCAAoC;AAEtD;AAAA,MACE;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,UAAU,UAAU;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,SAAS,eAAe,KAAK,WAAW,WAAW,MAAM;AACvD,OAAK,MAAM;AACX,OAAK,YAAY;AACjB,OAAK,OAAO;AACZ,OAAK,YAAY;AAEjB,OAAK,UAAU;AACf,OAAK,KAAK;AACV,OAAK,SAAS,CAAC;AAEf,OAAK,OAAO;AACd;AAEA,eAAe,UAAU,QAAQ,WAAW;AAC1C,OAAK,UAAU;AACjB;AAEA,eAAe,UAAU,OAAO,WAAW;AAEzC,MAAI,KAAK,SAAS;AAChB,SAAK,UAAU;AAEf,SAAK,KAAK,CAAC;AAAA,EACb;AAEA,OAAK,SAAS;AAChB;AAEA,eAAe,UAAU,WAAW,WAAW;AAE7C,MAAI,KAAK,SAAS;AAChB;AAAA,EACF;AAEA,MAAI,KAAK,YAAY;AACnB;AAAA,EACF;AAEA,QAAMC,QAAO,KAAK,IAAI;AAEtB,OAAK,aAAa;AAElB,wBAAsB,MAAM;AAC1B,SAAK,aAAa;AAElB,QAAI,KAAK,SAAS;AAChB;AAAA,IACF;AAEA,SAAK,MAAM,KAAK,IAAI,IAAIA,SAAQ,KAAK,MAAM;AAC3C,SAAK,SAAS;AAAA,EAChB,CAAC;AACH;AAGA,eAAe,UAAU,OAAO,SAAS,UAAU;AAEjD,QAAM,IAAI,KAAK,KAAK,KAAK,KAAK;AAE9B,QAAM,OAAO,KAAK,OAAO;AAAA,IACvB,OAAK,EAAE,aAAa,KAAK,EAAE,UAAU;AAAA,EACvC;AAGA,MAAI,CAAC,MAAM;AACT,WAAO,KAAK,UAAU;AAAA,EACxB;AAEA,QAAM,cAAc,IAAI,KAAK;AAC7B,QAAM,gBAAgB,KAAK,SAAS,KAAK,OAAO,cAAc,KAAK,QAAQ;AAE3E,QAAM,gBAAgB,KAAK,cAAc;AAEzC,QAAM,QAAQ,KAAK,MAAM,iBAAiB,aAAa;AAEvD,OAAK,KAAK,MAAM,GAAG,MAAM,CAAC;AAC5B;AAEA,eAAe,UAAU,OAAO,SAAS,GAAG,GAAG;AAC7C,OAAQ,KAAK,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;AACxD;AAEA,eAAe,UAAU,SAAS,WAAW;AAC3C,QAAM,YAAY,KAAK;AAEvB,QAAM,QAAQ,UAAU,OAAO,CAACC,QAAO,OAAO,UAAU;AAEtD,UAAM,YAAY,UAAU,QAAQ,CAAC;AAErC,QAAI,WAAW;AACb,YAAM,WAAWA,OAAMA,OAAM,SAAS,CAAC;AAEvC,YAAM,cAAc,YAAY,SAAS,aAAa;AACtD,YAAM,SAAS,SAAS,WAAW,KAAK;AAExC,MAAAA,OAAM,KAAK;AAAA,QACT;AAAA,QACA,WAAW,cAAc;AAAA,QACzB;AAAA,QACA,QAAQ,iBAAiB,OAAO,SAAS;AAAA,MAC3C,CAAC;AAAA,IACH;AAEA,WAAOA;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,QAAM,cAAc,MAAM,OAAO,SAAS,QAAQ,MAAM;AACtD,WAAO,SAAS,KAAK;AAAA,EACvB,GAAG,CAAC;AAEJ,QAAM,IAAI,UAAU,OAAO,CAACC,IAAG,UAAU,UAAU;AAEjD,UAAM,IAAI,SAAS,IAAI,aAAa,GAC9B,IAAI,SAAS,IAAI,aAAa;AAEpC,IAAAA,GAAE,KAAK,CAAE,QAAQ,IAAI,MAAM,KAAK,GAAG,CAAE,CAAC;AAEtC,WAAOA;AAAA,EACT,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,GAAG;AAEtB,QAAM,gBAAgB,qBAAqB,aAAa,KAAK,UAAU;AAEvE,OAAK,SAAS,MAAM,OAAO,CAACD,QAAO,MAAM,UAAU;AACjD,UAAM,WAAW,gBAAgB,cAAc,KAAK;AACpD,UAAM,YAAY,QAAQ,IAAIA,OAAM,QAAQ,CAAC,EAAE,UAAU;AACzD,UAAM,UAAU,YAAY;AAE5B,WAAO;AAAA,MACL,GAAGA;AAAA,MACH;AAAA,QACE,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,OAAK,QAAQ,OAAU,YAAY,CAAC,MAAM;AAC1C,OAAK,KAAK;AACZ;AAEA,eAAe,UAAU,OAAO,WAAW;AACzC,OAAQ,KAAK,KAAK,WAAW,EAAE;AACjC;AAEA,eAAe,UAAU,OAAO,WAAW;AACzC,OAAQ,KAAK,KAAK,WAAW,MAAM;AACrC;AAEA,eAAe,UAAU,YAAY,WAAW;AAC9C,OAAK,KAAK;AACZ;AAEA,eAAe,UAAU,SAAS,WAAW;AAC3C,OAAK,MAAM;AAEX,SAAU,KAAK,GAAG;AACpB;AAEA,eAAe,UAAU,WAAW,SAAS,OAAO;AAClD,OAAK,SAAS;AAChB;AAEA,SAAS,qBAAqB,QAAQ,YAAY,OAAO;AACvD,SAAO,KAAK,IAAI,MAAM,KAAK,YAAY,cAAc,KAAK,GAAG,IAAI;AACnE;AAEA,SAAS,cAAc,KAAK,KAAK;AAC/B,SAAO,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,IAAI;AACrD;AAEA,SAAS,SAAS,GAAG,GAAG;AACtB,SAAO,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAClE;;;AC/aA,IAAO,oBAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,IACAE;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAE,QAAQ,SAAU;AACjC;;;ACbA,yBAAwB;AAMxB,IAAMC,iBAAgB;AAGP,SAAR,cAA+B,UAAU;AAE9C,QAAM,aAAS,mBAAAC,SAAY;AAAA,IACzB,OAAO;AAAA,EACT,CAAC,EAAE,OAAO,OAAK,eAAe,EAAE,UAAU,CAAC,CAAC,IAAI,GAAG;AAEnD,WAAS,eAAe,UAAU;AAChC,QAAI,IAAI,SAAS,SAAS,OAAO,GAAE,CAAC,GAAE,EAAE;AACxC,QAAI,IAAI,SAAS,SAAS,OAAO,GAAE,CAAC,GAAE,EAAE;AACxC,QAAI,IAAI,SAAS,SAAS,OAAO,GAAE,CAAC,GAAE,EAAE;AACxC,QAAI,OAAQ,IAAI,MAAQ,IAAI,MAAQ,IAAI,OAAQ;AAChD,WAAO;AAAA,EACT;AAEA,MAAI,YAAY;AAEhB,WAAS,UAAU,OAAO;AACxB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAEJ,QAAI,WAAW,QAAQ,SAAS,oBAAoB;AAClD,aAAO;AAAA,QACL,SAAS;AAAA,QACT,WAAW;AAAA,MACb;AAAA,IACF;AAEA,QAAI,MAAM,QAAQ;AAChB,aAAO,MAAM,OAAO;AAAA,IACtB;AAEA,UAAM,UAAU,OAAS,cAAe,OAAO,MAAO;AAEtD,WAAO;AAAA,MACL;AAAA,MACA,WAAW,eAAe,OAAO,KAAK,MAAM,SAAS;AAAA,IACvD;AAAA,EACF;AAEA,WAAS,GAAG,oBAAoBD,gBAAe,CAAAE,WAAS;AAEtD,UAAM;AAAA,MACJ;AAAA,IACF,IAAIA;AAEJ,UAAM,SAAS,UAAU,KAAK;AAAA,EAChC,CAAC;AACH;AAEA,cAAc,UAAU;AAAA,EACtB;AACF;;;AC3DA,IAAO,yBAAQ;AAAA,EACb,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,eAAe,CAAE,QAAQ,aAAc;AACzC;;;ACPA,IAAI,SAAS;AAEb,IAAI,gBAAgB;AAEpB,IAAI,iBAAiB;AAErB,IAAI,UAAU;AAEd,IAAI,yBAAyB;AAE7B,IAAI,UAAU;AAEd,IAAI,WAAW;AAEf,IAAI,iBAAiB;AAErB,IAAI,UAAU;AAEd,IAAI,WAAW;AAEf,IAAI,gBAAgB;AAEpB,IAAI,iBAAiB;AAErB,IAAI,WAAW;AAEf,IAAI,eAAe;AAEnB,IAAI,cAAc;AAElB,SAAS,WAAW,KAAK;AACvB,SAAO,SAAS,KAAK,YAAY,IAAI;AACnC,WAAO,yBAA0B,SAAU,KAAK,GAAG;AAAA,EACrD;AACF;AAEA,IAAM,UAAU,WAAW,MAAM;AACjC,IAAM,iBAAiB,WAAW,aAAa;AAC/C,IAAM,kBAAkB,WAAW,cAAc;AACjD,IAAM,kBAAkB,WAAW,cAAc;AACjD,IAAM,WAAW,WAAW,OAAO;AACnC,IAAM,0BAA0B,WAAW,sBAAsB;AACjE,IAAM,WAAW,WAAW,OAAO;AACnC,IAAM,YAAY,WAAW,QAAQ;AACrC,IAAM,WAAW,WAAW,OAAO;AACnC,IAAM,YAAY,WAAW,QAAQ;AACrC,IAAM,iBAAiB,WAAW,aAAa;AAC/C,IAAM,kBAAkB,WAAW,cAAc;AACjD,IAAM,YAAY,WAAW,QAAQ;AACrC,IAAM,gBAAgB,WAAW,YAAY;AAC7C,IAAM,eAAe,WAAW,WAAW;;;ACzC5B,SAAR,wBAAyC,0BAA0B;AACxE,OAAK,4BAA4B;AACnC;AAEA,wBAAwB,UAAU,oBAAoB,SAAS,SAAS;AAEtE,QAAM,gBAAgB,QAAQ,SAAS,OAAO,SAAS,UAAU;AAC/D,WAAOC,IAAG,UAAU,mBAAmB;AAAA,EACzC,CAAC;AAED,MAAI,cAAc,SAAS,GAAG;AAC5B;AAAA,EACF;AAEA,QAAM,OAAO;AAAA;AAAA,QAEP,SAAS,CAAC;AAAA;AAAA;AAIhB,QAAM,SAAS,MAAM;AACnB,SAAK,0BAA0B,gBAAgB,OAAO;AAAA,EACxD;AAEA,SAAO;AAAA,IACL;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,wBAAwB,UAAU;AAAA,EAChC;AACF;;;ACrCe,SAAR,wBAAyC,0BAA0B;AACxE,OAAK,4BAA4B;AACnC;AAEA,wBAAwB,UAAU,oBAAoB,SAAS,SAAS;AACtE,QAAM,gBAAgB,QAAQ,SAAS,OAAO,cAAc;AAE5D,MAAI,cAAc,SAAS,GAAG;AAC5B;AAAA,EACF;AAEA,QAAM,kBAAkB,cAAc,OAAO,cAAY;AACvD,UAAM,SAAS,kBAAkB,QAAQ,GACnC,YAAY,kBAAkB,OAAO;AAE3C,WAAO,UAAU,YAAY;AAAA,EAC/B,CAAC;AAED,QAAM,OAAO;AAAA;AAAA,QAEP,SAAS,CAAC;AAAA;AAAA;AAIhB,SAAO,gBAAgB,IAAI,kBAAgB;AACzC,UAAM,SAAS,MAAM;AACnB,WAAK,0BAA0B,mBAAmB,SAAS,YAAY;AAAA,IACzE;AAEA,WAAO;AAAA,MACL;AAAA,MACA,SAAS;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,wBAAwB,UAAU;AAAA,EAChC;AACF;;;AClCe,SAAR,aAA8B,WAAW;AAC9C,OAAK,aAAa;AACpB;AAEA,aAAa,UAAU,oBAAoB,SAAS,SAAS;AAE3D,MACEC,IAAG,SAAS,kBAAkB,KAC5BA,IAAG,SAAS,iBAAiB,KAAK,kBAAkB,OAAO,EAAE,kBAE/D;AACA,WAAO,CAAC;AAAA,EACV;AAEA,SAAO;AAAA,IACL,KAAK,sBAAsB,OAAO;AAAA,EACpC;AACF;AAEA,aAAa,UAAU,wBAAwB,SAAS,SAAS;AAE/D,QAAM,WAAW,MAAM,KAAK,mBAAmB;AAAA,IAC7C;AAAA,EACF,CAAC;AAED,QAAM,OAAO,KAAK,UAAU,OAAO;AAEnC,QAAM,OAAO;AAAA,kCACoB,OAAO,KAAK,YAAa,YAAa,OAAO,WAAW,KAAM;AAAA,SACvF,OAAO,kBAAkB,WAAW,YAAY,CAAE;AAAA,QACnD,UAAU,YAAY,CAAE;AAAA;AAAA;AAI/B,QAAM,SAAS,MAAM;AACnB,SAAK,cAAc,OAAO;AAAA,EAC5B;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,EACF;AACF;AAEA,aAAa,UAAU,YAAY,SAAS,SAAS;AAEnD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,KAAK,WAAW,UAAU,OAAO;AAErC,SAAO;AACT;AAEA,aAAa,UAAU,gBAAgB,SAAS,SAAS;AACvD,QAAM,OAAO,CAAC,KAAK,UAAU,OAAO;AAEpC,OAAK,WAAW,cAAc,SAAS,IAAI;AAC7C;AAEA,aAAa,UAAU,qBAAqB,SAAS,SAAS;AAC5D,SAAO,KAAK,WAAW,kBAAkB,OAAO;AAClD;AAEA,aAAa,UAAU;AAAA,EACrB;AACF;;;AC1Ee,SAAR,eAAgC,WAAW;AAChD,OAAK,aAAa;AACpB;AAEA,eAAe,UAAU;AAAA,EACvB;AACF;AAEA,eAAe,UAAU,oBAAoB,SAAS,SAAS;AAC7D,SAAO;AAAA,IACL,KAAK,wBAAwB,OAAO;AAAA,EACtC;AACF;AAEA,eAAe,UAAU,0BAA0B,SAAS,SAAS;AAEnE,QAAM,WAAW,MAAM;AACrB,UAAM,gBAAgB,KAAK,mBAAmB;AAAA,MAC5C;AAAA,IACF,CAAC;AAED,UAAM,sBAAsB,cAAc,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM;AAC/D,aAAO,EAAE,MAAM,SAAS,SAAS,IAAI;AAAA,IACvC,CAAC;AAED,WAAO;AAAA,EACT;AAEA,QAAM,OAAO;AAAA;AAAA,QAEP,SAAS,CAAC;AAAA;AAAA;AAIhB,QAAM,SAAS,CAAC,kBAAkB;AAEhC,UAAM;AAAA,MACJ,OAAAC;AAAA,MACA;AAAA,IACF,IAAI,cAAc,CAAC;AAEnB,WAAO,KAAK,WAAW,QAAQ;AAAA,MAC7B,OAAAA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,eAAe,UAAU,qBAAqB,SAAS,SAAS;AAC9D,SAAO,KAAK,WAAW,kBAAkB,OAAO;AAClD;;;ACtCA,IAAM,eAAe;AAErB,IAAM,aAAa;AACnB,IAAM,cAAc;AAGL,SAAR,YACH,UAAU,iBACV,UAAU,UACV,QAAQ,aAAa;AAEvB,OAAK,mBAAmB;AACxB,OAAK,YAAY;AACjB,OAAK,YAAY;AACjB,OAAK,UAAU;AACf,OAAK,eAAe;AAEpB,OAAK,gBAAgB,oBAAI,IAAI;AAE7B,OAAK,cAAc;AAEnB,OAAK,YAAY,CAAC;AAElB,OAAK,gBAAgB,yBAAyB,uBAAuB;AACrE,OAAK,gBAAgB,yBAAyB,uBAAuB;AAErE,OAAK,gBAAgB,iBAAiB,YAAY;AAElD,OAAK,gBAAgB,cAAc,cAAc;AACjD,OAAK,gBAAgB,gBAAgB,cAAc;AACnD,OAAK,gBAAgB,iBAAiB,cAAc;AAEpD,WAAS,GAAG,mBAAmB,cAAc,aAAW;AACtD,UAAM,SAAS,QAAQ;AAEvB,QAAI,QAAQ;AACV,WAAK,gBAAgB;AAAA,IACvB,OAAO;AACL,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF,CAAC;AAED,WAAS,GAAG,wBAAwB,cAAc,MAAM;AACtD,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AAAA,EACvB,CAAC;AAED,WAAS,GAAG,4BAA4B,CAAAC,WAAS;AAE/C,UAAM,eAAe;AAAA,MACnB;AAAA,MACA,SAAS;AAAA,IACX;AAEA,eAAW,WAAW,cAAc;AAElC,YAAM,WAAW,QAAQ,QAAQ,SAAS,MAAM,GAAG;AAEnD,YAAM,QAAQ,SAAS,KAAK,QAAM,YAAY,QAAQ,EAAE,CAAC;AAEzD,cAAW,OAAO,EAAE,OAAO,UAAU,CAAC,KAAK;AAAA,IAC7C;AAEA,UAAM,eAAe;AAAA,MACnB;AAAA,MACA,SAAS;AAAA,IACX;AAEA,eAAW,WAAW,cAAc;AAElC,YAAM,WAAW,QAAQ,QAAQ,aAAa,MAAM,GAAG;AAEvD,YAAM,QAAQ,SAAS,KAAK,QAAM,YAAY,QAAQ,EAAE,CAAC;AAEzD,cAAW,OAAO,EAAE,OAAO,UAAU,KAAK;AAAA,IAC5C;AAAA,EACF,CAAC;AAED,WAAS,GAAG,uBAAuB,cAAc,CAAAA,WAAS;AACxD,UAAM;AAAA,MACJ;AAAA,IACF,IAAIA;AAEJ,SAAK,yBAAyB,OAAO;AAAA,EACvC,CAAC;AACH;AASA,YAAY,UAAU,kBAAkB,SAAS,MAAM,YAAY;AACjE,QAAM,UAAU,KAAK,UAAU,YAAY,UAAU;AAErD,UAAQ,OAAO,OAAO,KAAK,aAAa;AAExC,OAAK,UAAU,KAAK,EAAE,SAAS,KAAK,CAAC;AACvC;AAEA,YAAY,UAAU,cAAc,SAAS,SAAS;AAEpD,SACE,KAAK,UAAU;AAAA,IACb,CAAC,EAAE,KAAK,MAAMC,IAAG,SAAS,IAAI;AAAA,EAChC,EAAE;AAAA,IACA,CAAC,EAAE,QAAQ,MAAM;AAAA,EACnB;AAEJ;AAEA,YAAY,UAAU,kBAAkB,SAAS,QAAQ;AAEvD,MAAI,CAAC,QAAQ;AACX,aAAS,KAAK,QAAQ,eAAe;AAAA,EACvC;AAEA,OAAK,iBAAiB,QAAQ,CAAC,YAAY;AACzC,QAAIC,YAAW,QAAQ,OAAO,GAAG;AAC/B,WAAK,yBAAyB,OAAO;AAAA,IACvC;AAAA,EACF,CAAC;AACH;AAEA,YAAY,UAAU,eAAe,SAAS,MAAM;AAClD,SAAO,KAAK,cAAc,IAAI,IAAI,KAAK,CAAC;AAC1C;AAEA,YAAY,UAAU,cAAc,SAAS,SAAS,SAAS;AAE7D,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AAEJ,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,wBAAwB;AAAA,EAC1C;AAEA,QAAM,YAAY,KAAK,UAAU,IAAI,SAAS,oBAAoB;AAAA,IAChE,GAAG;AAAA,IACH,UAAU;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AAED,QAAM,UAAU,KAAK,UAAU,IAAI,SAAS;AAE5C,QAAM,eAAe,KAAK;AAE1B,MAAI,CAAC,aAAa,IAAI,WAAW,GAAG;AAClC,iBAAa,IAAI,aAAa,CAAC,CAAC;AAAA,EAClC;AAEA,eAAa,IAAI,WAAW,EAAE,KAAK,OAAO;AAC5C;AAEA,YAAY,UAAU,iBAAiB,SAAS,SAAS;AAEvD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAGJ,OAAK,UAAU,OAAO,EAAE;AAGxB,QAAM,WAAW,KAAK,cAAc,IAAI,WAAW,KAAK,CAAC;AAEzD,QAAM,MAAM,SAAS,QAAQ,OAAO;AAEpC,MAAI,QAAQ,IAAI;AACd,aAAS,OAAO,KAAK,CAAC;AAAA,EACxB;AACF;AAEA,YAAY,UAAU,2BAA2B,SAAS,SAAS;AACjE,aAAW,WAAW,KAAK,YAAY,OAAO,GAAG;AAC/C,SAAK,0BAA0B,SAAS,OAAO;AAAA,EACjD;AACF;AAEA,YAAY,UAAU,4BAA4B,SAAS,SAAS,SAAS;AAE3E,QAAM,SAAS,KAAK;AAEpB,QAAM,eAAe,QAAQ,kBAAkB,OAAO,KAAK,CAAC,GAAG,OAAO,OAAK,CAAC;AAE5E,QAAM,cAAc,GAAG,QAAQ,EAAE,SAAS,QAAQ,IAAI;AAEtD,QAAM,mBAAmB,KAAK,aAAa,WAAW;AAEtD,QAAM,kBAAkB,CAAC;AAEzB,aAAW,cAAc,aAAa;AAEpC,UAAM;AAAA,MACJ,SAAAC;AAAA,MACA,UAAU;AAAA,MACV,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,IAAI;AAGJ,UAAM,OAAO,GAAG,WAAW,QAAQ,EAAE,UAAU,KAAK;AAEpD,QAAI,kBAAkB,iBAAiB;AAAA,MACrC,OAAK,EAAE,SAAS;AAAA,IAClB;AAEA,UAAM,OAAO,mBAAmB,gBAAgB,QAAQ,SAAO,KAAK;AAEpE,QAAI,WAAW;AACb,YAAM,WAAW,UAAU;AAE3B,WAAK,QAAQ,WAAW,SAAS,IAAI,OAAK,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG;AAE9D,YAAM,cAAc,SAAS,OAAO,OAAK,KAAK,aAAa,QAAQ,EAAE,KAAK,CAAC;AAE3E,cAAW,IAAI,EAAE,OAAO,UAAU,YAAY,WAAW,CAAC;AAAA,IAC5D;AAEA,QAAI,eAAe;AACjB,YAAM,WAAW,cAAc;AAE/B,WAAK,QAAQ,eAAe,SAAS,IAAI,OAAK,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG;AAElE,YAAM,cAAc,SAAS,OAAO,OAAK,KAAK,aAAa,QAAQ,EAAE,KAAK,CAAC;AAE3E,cAAW,IAAI,EAAE,OAAO,UAAU,YAAY,SAAS,CAAC;AAAA,IAC1D;AAEA,QAAI,iBAAiB;AACnB,sBAAgB,KAAK,eAAe;AAEpC;AAAA,IACF;AAEA,QAAI,SAAS;AAEX,YAAS,KAAK,MAAM,SAAS,CAAAH,WAAS;AACpC,QAAAA,OAAM,eAAe;AAErB,cAAM,WAAW,YACb,UAAU,EAAE,OAAO,OAAK,KAAK,aAAa,QAAQ,EAAE,KAAK,CAAC,IAC1D;AAEJ,gBAAQ,QAAQ;AAEhB,YAAI,kBAAkB,QAAQ;AAC5B,iBAAO,aAAa;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAK,YAAYG,UAAS;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,aAAW,mBAAmB,kBAAkB;AAC9C,QAAI,CAAC,gBAAgB,SAAS,eAAe,GAAG;AAC9C,WAAK,eAAe,eAAe;AAAA,IACrC;AAAA,EACF;AACF;AAEA,YAAY,UAAU,mBAAmB,WAAW;AAClD,aAAW,YAAY,KAAK,cAAc,OAAO,GAAG;AAElD,eAAW,WAAW,UAAU;AAC9B,WAAK,cAAc,OAAO;AAAA,IAC5B;AAAA,EACF;AAEA,OAAK,cAAc,MAAM;AAC3B;AAEA,YAAY,UAAU,gBAAgB,SAAS,SAAS;AACtD,OAAK,UAAU,OAAO,QAAQ,EAAE;AAClC;AAEA,YAAY,UAAU;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAKO,SAASD,YAAW,UAAU,YAAY;AAE/C,KAAG;AACD,QAAI,aAAa,YAAY;AAC3B,aAAO;AAAA,IACT;AAEA,iBAAa,WAAW;AAAA,EAC1B,SAAS;AAET,SAAO;AACT;;;AC9UA,IAAO,uBAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,aAAa,CAAE,QAAQ,WAAY;AACrC;;;ACHe,SAAR,gBACH,UACA,WACA,sBAAsB;AAExB,WAAS,GAAG,uBAAuB,CAAAE,WAAS;AAC1C,UAAM;AAAA,MACJ;AAAA,IACF,IAAIA;AAEJ,UAAM;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,IACX,IAAI;AAEJ,QAAI,CAAC,MAAM,aAAa,CAAC,kBAAkB;AACzC;AAAA,IACF;AAEA,UAAM,gBAAgB;AAAA,MACpB;AAAA,MACA;AAAA,IACF;AAEA,QAAI,CAAC,cAAc,SAAS,aAAa,IAAI,GAAG;AAC9C;AAAA,IACF;AAEA,yBAAqB,uBAAuB,iBAAiB,SAAS;AAAA,MACpE,MAAM;AAAA,MACN,MAAM,gBAAgB;AAAA,MACtB,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,gBAAgB,UAAU;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AACF;;;ACxCA,IAAMC,cAAa;AACnB,IAAM,eAAe;AAGN,SAAR,qBAAsC,UAAU,UAAU;AAC/D,OAAK,YAAY;AAEjB,WAAS,GAAG;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,MAAM;AACP,SAAK,MAAM;AAAA,EACb,CAAC;AACH;AAEA,qBAAqB,UAAU,yBAAyB,SAAS,SAAS,SAAS;AACjF,QAAM,WAAW;AAAA,IACf,KAAKA;AAAA,IACL,OAAO;AAAA,EACT;AAEA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,CAAC;AAAA,EACX,IAAI;AAEJ,QAAM,SAAS,MAAM;AAErB,QAAM,cAAc,SAChB,iBAAiB,OAAO,SAAS,iBAAiB,OAAO,OAAO,MAChE;AAEJ,QAAM,OAAO,SAAO;AAAA,2CACsB,QAAQ,EAAG,KAAK,WAAW;AAAA,QAC9D,QAAQ,EAAG;AAAA,+BACY,IAAK;AAAA;AAAA,GAElC;AAED,OAAK,UAAU,IAAI,SAAS,4BAA4B;AAAA,IACtD;AAAA,IACA;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH;AAEA,qBAAqB,UAAU,QAAQ,WAAW;AAChD,OAAK,UAAU,OAAO,EAAE,MAAM,2BAA2B,CAAC;AAC5D;AAEA,qBAAqB,UAAU,4BAA4B,SAAS,SAAS;AAC3E,OAAK,UAAU,OAAO,EAAE,QAAiB,CAAC;AAC5C;AAEA,qBAAqB,UAAU,CAAE,YAAY,UAAW;;;ACnExD,IAAO,gCAAQ;AAAA,EACb,sBAAsB,CAAE,QAAQ,oBAAqB;AACvD;;;ACUA,IAAM,4BAA4B;AAElC,IAAM,YAAY,SAAS;AAGZ,SAAR,cAA+B,UAAU,QAAQ,aAAa;AACnE,OAAK,YAAY;AACjB,OAAK,UAAU;AACf,OAAK,eAAe;AAEpB,OAAK,MAAM;AAEX,WAAS,GAAG;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG,CAAAC,WAAS;AACV,SAAK,MAAM;AAAA,EACb,CAAC;AACH;AAEA,cAAc,UAAU,QAAQ,WAAW;AACzC,OAAK,YAAY,SAAO,uCAAuC;AAE/D,OAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,SAAS;AACxD;AAEA,cAAc,UAAU,mBAAmB,SAAS,SAAS;AAE3D,QAAM;AAAA,IACJ;AAAA,IACA,OAAO;AAAA,IACP,OAAO;AAAA,IACP;AAAA,IACA,MAAM;AAAA,EACR,IAAI;AAEJ,MAAI,SAAS,CAAC,KAAK,aAAa,QAAQ,KAAK,GAAG;AAC9C;AAAA,EACF;AAEA,QAAM,aAAa,KAAK,WAAW,GAAG,IAClC,OACA,aAAc,IAAK;AAEvB,QAAM,SAAS,SAAS,MAAM;AAE9B,QAAM,cAAc,SAAS,iBAAiB,OAAO,SAAS,iBAAiB,OAAO,OAAO,MAAM;AAEnG,QAAM,eAAe,SAAO;AAAA,mCACK,IAAI;AAAA,+BACR,UAAU;AAAA,sCACF,IAAK,KAAK,IAAI;AAAA,QAC5C,QAAQ,2BAA2B,WAAW,IAAI,MAAM,EAAE,YAAY,EAAG;AAAA;AAAA,GAE/E;AAED,OAAK,UAAU,YAAY,YAAY;AAGvC,SAAO,KAAK,UAAU,SAAS,SAAS,GAAG;AACzC,SAAK,UAAU,SAAS,CAAC,EAAE,OAAO;AAAA,EACpC;AAEA,aAAW,WAAW;AACpB,iBAAa,OAAO;AAAA,EACtB,GAAG,GAAG;AACR;AAEA,cAAc,UAAU,QAAQ,WAAW;AACzC,SAAO,KAAK,UAAU,SAAS,QAAQ;AACrC,SAAK,UAAU,SAAS,CAAC,EAAE,OAAO;AAAA,EACpC;AACF;AAEA,cAAc,UAAU;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACF;;;ACxFA,IAAO,wBAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,EACF;AAAA,EACA,eAAe,CAAE,QAAQ,aAAc;AACzC;;;ACJA,IAAO,2BAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,iBAAiB,CAAE,QAAQ,eAAgB;AAC7C;;;ACIA,IAAM,aAAa;AACnB,IAAM,eAAe;AAErB,IAAM,KAAK;AAEX,IAAM,qBAAqB;AAGZ,SAAR,WACH,UACA,QACA,aACA,eACA,kBAAkB;AAEpB,OAAK,YAAY;AACjB,OAAK,UAAU;AACf,OAAK,eAAe;AACpB,OAAK,iBAAiB;AACtB,OAAK,oBAAoB;AAEzB,OAAK,aAAa;AAElB,OAAK,MAAM;AAEX,WAAS,GAAG,mBAAmB,CAAAC,WAAS;AACtC,UAAM,SAASA,OAAM;AAErB,QAAI,QAAQ;AACV,cAAW,KAAK,UAAU,EAAE,OAAO,QAAQ;AAAA,IAC7C,OAAO;AACL,cAAW,KAAK,UAAU,EAAE,IAAI,QAAQ;AACxC,YAAS,KAAK,UAAU;AAExB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF,CAAC;AAED,WAAS,GAAG,4BAA4B,CAAAA,WAAS;AAE/C,UAAM,cAAc,KAAK,iBAAiB;AAE1C,eAAW,WAAW,aAAa;AACjC,YAAM,UAAU,QAAQ,QAAQ;AAEhC,cAAW,OAAO,EAAE,OAAO,YAAY,CAAC,KAAK,aAAa,QAAQ,OAAO,CAAC;AAAA,IAC5E;AAAA,EACF,CAAC;AAED,WAAS,GAAG,oBAAoB,CAAAA,WAAS;AACvC,SAAK,SAASA,OAAM,KAAK;AAAA,EAC3B,CAAC;AAED,WAAS,GAAG,uBAAuB,CAAAA,WAAS;AAC1C,SAAK,YAAYA,OAAM,KAAK;AAAA,EAC9B,CAAC;AAED,WAAS,GAAG,qBAAqB,CAAAA,WAAS;AACxC,SAAK,YAAYA,OAAM,KAAK;AAAA,EAC9B,CAAC;AAED,WAAS,GAAG,wBAAwB,MAAM;AACxC,SAAK,mBAAmB;AAAA,EAC1B,CAAC;AACH;AAEA,WAAW,UAAU,QAAQ,WAAW;AACtC,OAAK,aAAa,SAAO,uCAAuC;AAEhE,OAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,UAAU;AACzD;AAEA,WAAW,UAAU,WAAW,SAAS,OAAO;AAE9C,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM;AAAA,IACJ,SAAS;AAAA,EACX,IAAI;AAEJ,MAAI,CAAC,gBAAgB,SAAS,aAAa,IAAI,GAAG;AAChD;AAAA,EACF;AAEA,QAAM,SAAS,MAAM;AAErB,QAAM,cAAc,SAAS,iBAAiB,OAAO,SAAS,iBAAiB,OAAO,OAAO,MAAM;AAEnG,QAAM,OAAO,SAAO;AAAA,0BACI,MAAM,EAAE;AAAA,wCACM,MAAM,EAAE,KAAK,WAAW;AAAA,QACxD,MAAM,UAAU,CAAC;AAAA;AAAA,GAEtB;AAED,QAAS,KAAK,MAAM,SAAS,MAAM;AACjC,SAAK,aAAa,OAAO,KAAK;AAAA,EAChC,CAAC;AAED,QAAS,KAAK,MAAM,cAAc,MAAM;AACtC,SAAK,eAAe,YAAY;AAAA,EAClC,CAAC;AAED,QAAS,KAAK,MAAM,cAAc,MAAM;AACtC,SAAK,iBAAiB;AAAA,EACxB,CAAC;AAED,MAAI,CAAC,KAAK,aAAa,QAAQ,KAAK,GAAG;AACrC,YAAW,IAAI,EAAE,IAAI,UAAU;AAAA,EACjC;AAEA,OAAK,WAAW,YAAY,IAAI;AAClC;AAEA,WAAW,UAAU,mBAAmB,WAAW;AACjD,SAAO,IAAY,mBAAmB,KAAK,UAAU;AACvD;AAEA,WAAW,UAAU,kBAAkB,SAAS,OAAO;AACrD,SAAO,MAAS,mBAAmB,MAAM,EAAE,MAAM,KAAK,UAAU;AAClE;AAEA,WAAW,UAAU,cAAc,SAAS,OAAO;AACjD,QAAM,UAAU,KAAK,gBAAgB,KAAK;AAE1C,MAAI,SAAS;AACX,YAAQ,cAAc,MAAM,UAAU;AAAA,EACxC;AACF;AAEA,WAAW,UAAU,cAAc,SAAS,OAAO;AACjD,QAAM,UAAU,KAAK,gBAAgB,KAAK;AAE1C,MAAI,SAAS;AACX,YAAQ,OAAO;AAAA,EACjB;AACF;AAEA,WAAW,UAAU,qBAAqB,WAAW;AACnD,OAAK,WAAW,YAAY;AAC9B;AAEA,WAAW,UAAU,iBAAiB,SAAS,SAAS;AAEtD,OAAK,iBAAiB;AAEtB,OAAK,aAAa;AAElB,OAAK,eAAe,IAAI,SAAS,IAAI,KAAK,oBAAoB,GAAG,kBAAkB;AAEnF,MAAI,CAAC,QAAQ,QAAQ;AACnB,YAAW,KAAK,QAAQ,aAAa,CAAC,EAAE,IAAI,WAAW;AAAA,EACzD;AACF;AAEA,WAAW,UAAU,mBAAmB,WAAW;AAEjD,MAAI,CAAC,KAAK,YAAY;AACpB;AAAA,EACF;AAEA,QAAM,UAAU,KAAK;AAErB,OAAK,eAAe,OAAO,SAAS,EAAE;AAEtC,MAAI,CAAC,QAAQ,QAAQ;AACnB,YAAW,KAAK,QAAQ,aAAa,CAAC,EAAE,OAAO,WAAW;AAAA,EAC5D;AAEA,OAAK,aAAa;AACpB;AAEA,WAAW,UAAU,sBAAsB,WAAW;AACpD,SAAO;AAAA,IACL,MAAM,KAAK,kBAAkB,IAAI,UAAU;AAAA,IAC3C,QAAQ,KAAK,kBAAkB,IAAI,YAAY;AAAA,EACjD;AACF;AAEA,WAAW,UAAU;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AC/Me,SAAR,mBAAoC;AACzC,OAAK,SAAS,CAAC;AACjB;AAEA,iBAAiB,UAAU,CAAC;AAG5B,iBAAiB,UAAU,MAAM,SAAS,MAAM;AAE9C,QAAM,cAAc,KAAK,OAAO,IAAI;AAEpC,MAAI,aAAa;AACf,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,KAAK,gBAAgB;AACxB,SAAK,iBAAiB,KAAK,kBAAkB;AAAA,EAC/C;AAEA,SAAO,KAAK,OAAO,IAAI,IAAI,KAAK,eAAe,iBAAiB,IAAI,EAAE,KAAK;AAC7E;AAEA,iBAAiB,UAAU,oBAAoB,WAAW;AAExD,QAAM,MAAM,OAAO,qBAAqB,aACpC,mBACA;AAEJ,QAAM,UAAU,OAAO,aAAa,cAChC,SAAS,kBACT,CAAC;AAEL,SAAO,IAAI,OAAO;AACpB;AAKA,SAAS,uBAAuB;AAC9B,SAAO;AAAA,IACL,mBAAmB;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;AC1CA,IAAO,4BAAQ;AAAA,EACb,kBAAkB,CAAE,QAAQ,gBAAiB;AAC/C;;;ACCA,IAAO,sBAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,YAAY,CAAE,QAAQ,UAAW;AACnC;;;ACLA,IAAI,kBAAkB;AAAA,EACpB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAM;AACR;AAOO,SAAS,WAAW,KAAK;AAC9B,QAAM,KAAK;AAEX,SAAO,OAAO,IAAI,QAAQ,YAAY,SAAS,OAAO;AACpD,WAAO,gBAAgB,KAAK;AAAA,EAC9B,CAAC;AACH;;;ACQA,IAAM,YAAY,SAAS;AAE3B,SAAS,eAAe,SAAS;AAC/B,QAAM,OAAO,WAAW,QAAQ,eAAe;AAE/C,SAAO,QAAQ,WAAW,IAAI;AAChC;AAEA,SAAS,4BAA4B,SAAS,cAAc;AAC1D,QAAM,kBAAkB,mBAAmB,OAAO;AAClD,MAAI,oBAAoB,QAAQ;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,gCAAgC,YAAY,IAAI,eAAe;AACxE;AAEA,SAAS,mBAAmB,SAAS;AACnC,QAAM,KAAK,kBAAkB,OAAO;AACpC,MAAI,GAAG,IAAI,kBAAkB,EAAE,WAAW,GAAG;AAC3C,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,GAAG,iBAAiB,CAAC;AAE7C,MAAIC,IAAG,iBAAiB,6BAA6B,GAAG;AACtD,WAAO;AAAA,EACT;AACA,MAAIA,IAAG,iBAAiB,2BAA2B,GAAG;AACpD,WAAO;AAAA,EACT;AACA,MAAIA,IAAG,iBAAiB,4BAA4B,GAAG;AACrD,WAAO;AAAA,EACT;AACA,MAAIA,IAAG,iBAAiB,2BAA2B,GAAG;AACpD,WAAO;AAAA,EACT;AACA,MAAIA,IAAG,iBAAiB,gCAAgC,GAAG;AACzD,WAAO;AAAA,EACT;AACA,MAAIA,IAAG,iBAAiB,gCAAgC,GAAG;AACzD,WAAO;AAAA,EACT;AACA,MAAIA,IAAG,iBAAiB,iCAAiC,GAAG;AAC1D,WAAO;AAAA,EACT;AACA,MAAIA,IAAG,iBAAiB,0BAA0B,GAAG;AACnD,WAAO;AAAA,EACT;AACA,MAAIA,IAAG,iBAAiB,4BAA4B,GAAG;AACrD,WAAO;AAAA,EACT;AACA,MAAIA,IAAG,iBAAiB,+BAA+B,GAAG;AACxD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGe,SAAR,IACH,UAAU,eACV,wBAAwB,QACxB,aAAa,WAAW;AAE1B,OAAK,iBAAiB;AACtB,OAAK,0BAA0B;AAC/B,OAAK,UAAU;AACf,OAAK,eAAe;AAEpB,OAAK,MAAM;AAEX,WAAS,GAAG,4BAA4B,CAAAC,WAAS;AAC/C,UAAM,cAAc,IAAY,6BAA6B,KAAK,UAAU;AAE5E,eAAW,WAAW,aAAa;AACjC,YAAM,UAAU,QAAQ,QAAQ;AAEhC,cAAW,OAAO,EAAE,OAAO,YAAY,CAAC,KAAK,aAAa,QAAQ,OAAO,CAAC;AAAA,IAC5E;AAAA,EACF,CAAC;AAED,WAAS,GAAG,uBAAuB,CAAAA,WAAS;AAC1C,UAAM;AAAA,MACJ;AAAA,IACF,IAAIA;AAEJ,UAAM;AAAA,MACJ,SAAS;AAAA,IACX,IAAI;AAEJ,UAAM,YAAY,MAAM;AAExB,UAAM,gBAAgB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,CAAC,cAAc,SAAS,aAAa,IAAI,GAAG;AAC9C;AAAA,IACF;AAEA,UAAM,eAAeD,IAAG,cAAc,iBAAiB;AAEvD,UAAM,OAAO,GACX,eAAgB,eAAe,YAAY,KAAK,eAAgB,SAClE,IACE,YAAY,aAAa,UAC3B;AAEA,SAAK,IAAI;AAAA,MACP;AAAA,MACA,MAAM,YAAY,gBAAgB,IAAI,gBAAgB;AAAA,MACtD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,WAAS,GAAG,oBAAoB,CAAAC,WAAS;AACvC,UAAM;AAAA,MACJ;AAAA,IACF,IAAIA;AAEJ,UAAM;AAAA,MACJ,SAAS;AAAA,IACX,IAAI;AAEJ,UAAM,gBAAgB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,CAAC,cAAc,SAAS,aAAa,IAAI,GAAG;AAC9C;AAAA,IACF;AAEA,UAAM,eAAeD,IAAG,cAAc,iBAAiB;AAEvD,UAAM,OAAO,GACX,eAAgB,eAAe,YAAY,KAAK,eAAgB,SAClE;AAEA,SAAK,IAAI;AAAA,MACP;AAAA,MACA,MAAM,gBAAgB;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,WAAS,GAAG,aAAa,CAAAC,WAAS;AAEhC,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP;AAAA,IACF,IAAIA;AAEJ,QAAI,WAAW,QAAQ;AACrB;AAAA,IACF;AAEA,UAAM,QAAQ,aAAa;AAE3B,UAAM,cAAc,eAAe,OAAO;AAI1C,QAAID,IAAG,SAAS,kBAAkB,GAAG;AACnC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,eAAe,GAAG;AAChC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,mBAAmB,GAAG;AACpC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,iBAAiB,GAAG;AAClC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,uBAAuB,GAAG;AACxC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,iBAAiB,GAAG;AAClC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,kBAAkB,GAAG;AACnC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,eAAe,GAAG;AAChC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,WAAW,GAAG;AAC5B,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAIA,QAAIA,IAAG,SAAS,uBAAuB,GAAG;AACxC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,sBAAsB,GAAG;AACvC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,uBAAuB,GAAG;AACxC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAIA,QAAIA,IAAG,SAAS,iBAAiB,GAAG;AAClC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM,yBAAyB,mBAAmB,OAAO,CAAC;AAAA,QAC1D;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,6BAA6B,GAAG;AAC9C,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM,4BAA4B,SAAS,OAAO;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,6BAA6B,GAAG;AAC9C,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM,4BAA4B,SAAS,OAAO;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,oBAAoB,GAAG;AACrC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM,4BAA4B,SAAS,OAAO;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAIA,IAAG,SAAS,eAAe,GAAG;AAGhC,aAAO,KAAK,IAAI;AAAA,QACd,MAAM,eAAe;AAAA,QACrB,MAAM,uBAAuB,mBAAmB,OAAO,CAAC;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAGD,WAAS,GAAG;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG,CAAAC,WAAS;AACV,SAAK,MAAM;AACX,SAAK,OAAO,KAAK;AAAA,EACnB,CAAC;AACH;AAEA,IAAI,UAAU,QAAQ,WAAW;AAC/B,OAAK,aAAa,SAAO;AAAA;AAAA;AAAA,UAGhB,QAAQ,cAAc,CAAE;AAAA;AAAA;AAAA,YAGtB,UAAU,CAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAOtB;AAED,OAAK,eAAe,MAAS,oBAAoB,KAAK,UAAU;AAEhE,OAAK,WAAW,MAAS,gBAAgB,KAAK,UAAU;AAExD,QAAS,KAAK,KAAK,UAAU,aAAa,CAAAA,WAAS;AACjD,IAAAA,OAAM,gBAAgB;AAAA,EACxB,CAAC;AAED,OAAK,SAAS,MAAS,cAAc,KAAK,UAAU;AAEpD,QAAS,KAAK,KAAK,QAAQ,SAAS,MAAM;AACxC,SAAK,OAAO,KAAK;AAAA,EACnB,CAAC;AAED,OAAK,QAAQ,MAAS,iBAAiB,KAAK,UAAU;AAEtD,QAAS,KAAK,KAAK,OAAO,SAAS,MAAM;AACvC,SAAK,OAAO;AAAA,EACd,CAAC;AAED,OAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,UAAU;AAEvD,OAAK,eAAe,SAAO;AAAA;AAAA,QAEpB,QAAQ,CAAE;AAAA;AAAA,GAEhB;AAED,QAAS,KAAK,KAAK,cAAc,SAAS,MAAM;AAC9C,SAAK,OAAO;AAAA,EACd,CAAC;AAED,OAAK,wBAAwB,SAAS,KAAK,cAAc,CAAC;AAC5D;AAEA,IAAI,UAAU,UAAU,WAAW;AACjC,QAAM,YAAY,KAAK;AAEvB,SAAO,CAAC,QAAW,SAAS,EAAE,IAAI,QAAQ;AAC5C;AAEA,IAAI,UAAU,SAAS,SAAS,QAAQ,CAAC,KAAK,QAAQ,GAAG;AACvD,QAAM,YAAY,KAAK;AAEvB,MAAI,OAAO;AACT,YAAW,SAAS,EAAE,OAAO,QAAQ;AAAA,EACvC,OAAO;AACL,YAAW,SAAS,EAAE,IAAI,QAAQ;AAAA,EACpC;AACF;AAEA,IAAI,UAAU,MAAM,SAAS,SAAS;AAEpC,QAAM;AAAA,IACJ;AAAA,IACA,OAAO;AAAA,IACP,OAAO;AAAA,IACP;AAAA,EACF,IAAI;AAEJ,QAAM,UAAU,KAAK;AAErB,UAAW,KAAK,YAAY,EAAE,IAAI,QAAQ;AAE1C,MAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,SAAK,eAAe,iBAAiB,OAAO;AAAA,EAC9C;AAEA,QAAM,aAAa,KAAK,WAAW,GAAG,IAAI,OAAO,aAAa,IAAI;AAElE,QAAM,SAAS,SAAS,MAAM;AAE9B,QAAM,cAAc,SAAS,sBAAsB,OAAO,OAAO,YAAY,OAAO,SAAS,MAAM;AAEnG,QAAM,WAAW,SAAO;AAAA,0BACC,IAAK,IAC1B,SAAS,KAAK,aAAa,QAAQ,KAAK,IAAI,KAAK,UACnD,KACE,QAAQ,kBAAkB,MAAM,EAAE,MAAM,EAC1C;AAAA,+BAC2B,UAAU;AAAA,sCACF,IAAK,KAAK,IAAI;AAAA,QAE7C,QACI,0CAA0C,MAAM,EAAE,KAAK,WAAW,IAAI,MAAM,EAAE,YAC9E,EACN;AAAA;AAAA,GAEH;AAED,WAAY,KAAK,UAAU,6BAA6B,SAAS,CAAAA,WAAS;AACxE,SAAK,aAAa,OAAO,KAAK;AAAA,EAChC,CAAC;AAID,QAAM,eAAe,KAAK,IAAI,QAAQ,eAAe,QAAQ,YAAY,QAAQ,YAAY,IAAI;AAEjG,UAAQ,YAAY,QAAQ;AAE5B,MAAI,cAAc;AAChB,YAAQ,YAAY,QAAQ;AAAA,EAC9B;AACF;AAEA,IAAI,UAAU,QAAQ,WAAW;AAC/B,SAAO,KAAK,SAAS,YAAY;AAC/B,SAAK,SAAS,YAAY,KAAK,SAAS,UAAU;AAAA,EACpD;AAEA,OAAK,eAAe,SAAO,iDAAiD;AAE5E,OAAK,SAAS,YAAY,KAAK,YAAY;AAC7C;AAEA,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACveA,IAAO,cAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,KAAK,CAAE,QAAQ,GAAI;AACrB;;;ACGA,IAAM,uBAAuB;AAAA,EAC3B;AACF;AAEA,SAASC,SAAQ,SAAS;AACxB,SAAO,QAAQ;AACjB;AAGe,SAAR,eACH,UAAU,iBAAiB,QAC3B,eAAe,sBAAsB;AAEvC,OAAK,YAAY;AACjB,OAAK,mBAAmB;AACxB,OAAK,wBAAwB;AAC7B,OAAK,iBAAiB;AAEtB,OAAK,gBAAgB,OAAO,aAAa,EAAE;AAE3C,WAAS,GAAG,mBAAmB,CAAAC,WAAS;AAEtC,QAAIA,OAAM,QAAQ;AAChB,WAAK,OAAO;AAAA,IACd,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF,CAAC;AACH;AAEA,eAAe,UAAU,yBAAyB,WAAW;AAC3D,SAAO,KAAK;AACd;AAEA,eAAe,UAAU,SAAS,WAAW;AAE3C,QAAM,sBAAsB,CAAC;AAE7B,OAAK,iBAAiB,QAAQ,aAAW;AAEvC,QAAID,SAAQ,OAAO,GAAG;AACpB;AAAA,IACF;AAEA,QAAI,CAACE,IAAG,SAAS,oBAAoB,GAAG;AACtC;AAAA,IACF;AAEA,SAAK,YAAY,OAAO;AAExB,wBAAoB,KAAK,OAAO;AAAA,EAClC,CAAC;AAED,MAAI,oBAAoB,QAAQ;AAE9B,SAAK,eAAe,iBAAiB;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,wBAAwB;AAAA,MAC9B,MAAM;AAAA,MACN,KAAK;AAAA,IACP,CAAC;AAAA,EACH;AAEA,OAAK,uBAAuB;AAC9B;AAEA,eAAe,UAAU,QAAQ,WAAW;AAC1C,UAAW,KAAK,aAAa,EAAE,OAAO,SAAS;AACjD;AAEA,eAAe,UAAU,cAAc,SAAS,SAAS;AACvD,OAAK,sBAAsB,uBAAuB,SAAS;AAAA,IACzD,MAAM;AAAA,IACN,MAAM,wBAAwB;AAAA,IAC9B,MAAM;AAAA,EACR,CAAC;AACH;AAEA,eAAe,UAAU;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACjGA,IAAO,0BAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU,CAAE,gBAAiB;AAAA,EAC7B,gBAAgB,CAAE,QAAQ,cAAe;AAC3C;;;ACUA,IAAM,cAAc,SAAS;AAC7B,IAAM,eAAe,UAAU;AAE/B,IAAMC,iBAAgB;AAGP,SAAR,gBACH,UAAU,wBACV,eAAe,QAAQ;AAEzB,OAAK,YAAY;AACjB,OAAK,0BAA0B;AAC/B,OAAK,iBAAiB;AAEtB,OAAK,eAAe,OAAO,aAAa,EAAE;AAE1C,OAAK,WAAW;AAChB,OAAK,WAAW;AAEhB,OAAK,MAAM;AAGX,WAAS,GAAG,oBAAoBA,gBAAe,CAAAC,WAAS;AACtD,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf,CAAC;AAED,WAAS,GAAG;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG,MAAM;AACP,SAAK,WAAW;AAChB,SAAK,MAAM;AAAA,EACb,CAAC;AAED,WAAS,GAAG,aAAaD,gBAAe,CAAAC,WAAS;AAC/C,SAAK,QAAQ;AAAA,EACf,CAAC;AACH;AAEA,gBAAgB,UAAU,QAAQ,WAAW;AAC3C,OAAK,eAAe,SAAO;AAAA;AAAA,QAEpB,WAAY;AAAA;AAAA,GAElB;AAED,QAAS,KAAK,KAAK,cAAc,SAAS,KAAK,OAAO,KAAK,IAAI,CAAC;AAEhE,OAAK,wBAAwB,SAAS,KAAK,cAAc,CAAC;AAC5D;AAEA,gBAAgB,UAAU,SAAS,WAAW;AAC5C,MAAI,KAAK,UAAU;AACjB,SAAK,QAAQ;AAAA,EACf,OAAO;AACL,SAAK,MAAM;AAAA,EACb;AACF;AAEA,gBAAgB,UAAU,QAAQ,WAAW;AAC3C,MAAI,CAAC,KAAK,UAAU;AAClB;AAAA,EACF;AAEA,UAAW,KAAK,YAAY,EAAE,OAAO,QAAQ;AAC7C,UAAW,KAAK,YAAY,EAAE,IAAI,QAAQ;AAE1C,OAAK,aAAa,YAAY;AAE9B,OAAK,UAAU,KAAK,sBAAsB;AAE1C,OAAK,eAAe,iBAAiB;AAAA,IACnC,MAAM;AAAA,EACR,CAAC;AAED,OAAK,WAAW;AAClB;AAEA,gBAAgB,UAAU,UAAU,WAAW;AAE7C,MAAI,CAAC,KAAK,YAAY,CAAC,KAAK,UAAU;AACpC;AAAA,EACF;AAEA,UAAW,KAAK,YAAY,EAAE,IAAI,QAAQ;AAC1C,UAAW,KAAK,YAAY,EAAE,OAAO,QAAQ;AAE7C,OAAK,aAAa,YAAY;AAE9B,OAAK,UAAU,KAAK,qBAAqB;AAEzC,OAAK,eAAe,iBAAiB;AAAA,IACnC,MAAM;AAAA,EACR,CAAC;AAED,OAAK,WAAW;AAClB;AAEA,gBAAgB,UAAU,WAAW,WAAW;AAC9C,OAAK,WAAW;AAEhB,UAAW,KAAK,YAAY,EAAE,OAAO,UAAU;AACjD;AAEA,gBAAgB,UAAU,aAAa,WAAW;AAChD,OAAK,WAAW;AAEhB,UAAW,KAAK,YAAY,EAAE,OAAO,QAAQ;AAC7C,UAAW,KAAK,YAAY,EAAE,IAAI,UAAU;AAC9C;AAEA,gBAAgB,UAAU;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACtIA,IAAO,2BAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,iBAAiB,CAAE,QAAQ,eAAgB;AAC7C;;;ACKe,SAAR,gBAAiC,UAAU,wBAAwB,eAAe;AACvF,OAAK,YAAY;AACjB,OAAK,0BAA0B;AAC/B,OAAK,iBAAiB;AAEtB,OAAK,MAAM;AAEX,WAAS,GAAG,oBAAoB,MAAM;AACpC,YAAW,KAAK,aAAa,EAAE,OAAO,UAAU;AAAA,EAClD,CAAC;AAED,WAAS,GAAG,mBAAmB,CAACC,WAAU;AACxC,UAAM,SAAS,KAAK,UAAUA,OAAM;AAEpC,QAAI,CAAC,QAAQ;AACX,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF,CAAC;AACH;AAEA,gBAAgB,UAAU,QAAQ,WAAW;AAC3C,OAAK,gBAAgB,SAAO;AAAA;AAAA,QAErB,UAAU,CAAE;AAAA;AAAA,GAElB;AAED,QAAS,KAAK,KAAK,eAAe,SAAS,MAAM;AAC/C,SAAK,gBAAgB;AAErB,SAAK,eAAe,iBAAiB;AAAA,MACnC,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AAED,OAAK,wBAAwB,SAAS,KAAK,eAAe,CAAC;AAC7D;AAEA,gBAAgB,UAAU,kBAAkB,WAAW;AACrD,UAAW,KAAK,aAAa,EAAE,IAAI,UAAU;AAE7C,OAAK,UAAU,KAAK,sBAAsB;AAC5C;AAEA,gBAAgB,UAAU;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AACF;;;AC9DA,IAAO,2BAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,iBAAiB,CAAE,QAAQ,eAAgB;AAC7C;;;ACIA,IAAM,gBAAgB;AACtB,IAAMC,eAAc;AAEpB,IAAMC,gBAAe;AAErB,IAAMC,yBAAwB;AAC9B,IAAMC,2BAA0B;AAGjB,SAAR,WACH,UAAU,UACV,WAAW,aACX,kBAAkB;AAEpB,OAAK,YAAY;AACjB,OAAK,eAAe;AACpB,OAAK,aAAa;AAClB,OAAK,oBAAoB;AAEzB,OAAK,aAAa,CAAC;AAEnB,WAAS,GAAG,uBAAuBF,eAAc,CAAAG,WAAS;AAExD,UAAM;AAAA,MACJ;AAAA,IACF,IAAIA;AAEJ,SAAK,kBAAkB,OAAO;AAC9B,SAAK,eAAe,OAAO;AAAA,EAC7B,CAAC;AAED,WAAS,GAAG,4BAA4B,CAAAA,WAAS;AAE/C,UAAM,cAAc,IAAY,mCAAmC,SAAS,YAAY;AAExF,eAAW,WAAW,aAAa;AACjC,YAAM,UAAU,QAAQ,QAAQ;AAEhC,cAAW,OAAO,EAAE,OAAO,YAAY,CAAC,KAAK,aAAa,QAAQ,OAAO,CAAC;AAAA,IAC5E;AAAA,EACF,CAAC;AACH;AAEA,WAAW,UAAU,iBAAiB,SAAS,SAAS;AAEtD,MAAIC,IAAG,SAAS,kBAAkB,KAAKA,IAAG,SAAS,mBAAmB,GAAG;AACvE;AAAA,EACF;AAEA,QAAM,SAAS,KAAK,WAAW,WAAW,WAAS;AACjD,WACE,CAAC,MAAM,aACP,MAAM,SAAS,KAAK,OAAK,CAAC,EAAE,aAAa,EAAE,YAAY,WAAW,CAAC,EAAE,SAAS,MAAM;AAAA,EAExF,CAAC;AAED,OAAK,cAAc,SAAS,MAAM;AACpC;AAEA,WAAW,UAAU,gBAAgB,SAAS,SAAS,QAAQ;AAC7D,MAAI,CAAC,OAAO,QAAQ;AAClB;AAAA,EACF;AAEA,QAAM,cAAc,OAAO,IAAI,WAAS;AACtC,WAAO,KAAK,cAAc,SAAS,KAAK;AAAA,EAC1C,CAAC,EAAE,KAAK,EAAE;AAEV,QAAM,OAAO,SAAO;AAAA;AAAA,QAEd,WAAW;AAAA;AAAA,GAEhB;AAED,QAAM,WAAW,EAAE,QAAQ,eAAe,MAAML,aAAY;AAE5D,QAAM,YAAY,KAAK,UAAU,IAAI,SAAS,mBAAmB;AAAA,IAC/D;AAAA,IACA;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AAED,OAAK,WAAW,QAAQ,EAAE,IAAI;AAChC;AAEA,WAAW,UAAU,oBAAoB,SAAS,SAAS;AACzD,OAAK,iBAAiB,OAAO;AAC/B;AAEA,WAAW,UAAU,mBAAmB,SAAS,SAAS;AACxD,QAAM,YAAY,KAAK,WAAW,QAAQ,EAAE;AAE5C,MAAI,CAAC,WAAW;AACd;AAAA,EACF;AAEA,OAAK,UAAU,OAAO,SAAS;AAE/B,SAAO,KAAK,WAAW,QAAQ,EAAE;AACnC;AAEA,WAAW,UAAU,gBAAgB,SAAS,SAAS,OAAO;AAE5D,QAAM,SAAS,MAAM,UAAU,KAAK,kBAAkB;AAEtD,SAAO;AAAA,0BACiB,MAAM,EAAE,oCAAoC,KAAK,aAAa,QAAQ,KAAK,IAAI,KAAK,UAAW;AAAA,yBAChG,OAAO,SAAS,iBAAkB,OAAO,OAAQ;AAAA,QAClE,MAAM,mBAAmB,OAAO,CAAC;AAAA;AAAA;AAGzC;AAEA,WAAW,UAAU,oBAAoB,WAAW;AAClD,SAAO;AAAA,IACL,SAAS,KAAK,kBAAkB,IAAIE,sBAAqB;AAAA,IACzD,WAAW,KAAK,iBAAiB,IAAIC,wBAAuB;AAAA,EAC9D;AACF;AAEA,WAAW,UAAU;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AC3IA,IAAO,sBAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,YAAY,CAAE,QAAQ,UAAW;AACnC;;;ACFA,IAAM,SAAS;AAAA,EACb,CAAE,QAAQ,GAAI;AAAA,EACd,CAAE,UAAU,CAAE;AAAA,EACd,CAAE,QAAQ,CAAE;AACd;AAQe,SAAR,kBAAmC,QAAQ,WAAW,UAAU;AACrE,OAAK,UAAU;AACf,OAAK,aAAa;AAClB,OAAK,YAAY;AAEjB,OAAK,MAAM,UAAU,kBAAkB,CAAC;AAExC,WAAS,GAAG,mBAAmB,CAAAG,WAAS;AACtC,UAAM,SAASA,OAAM;AAErB,QAAI,CAAC,QAAQ;AACX,cAAW,KAAK,UAAU,EAAE,IAAI,QAAQ;AAAA,IAC1C,OAAO;AACL,cAAW,KAAK,UAAU,EAAE,OAAO,QAAQ;AAAA,IAC7C;AAAA,EACF,CAAC;AAED,WAAS,GAAG,+BAA+B,CAAAA,WAAS;AAClD,SAAK,UAAUA,OAAM,KAAK;AAAA,EAC5B,CAAC;AACH;AAEA,kBAAkB,UAAU,iBAAiB,SAAS,SAAS;AAC7D,SAAO,WAAW,QAAQ,QAAQ,KAAK;AACzC;AAEA,kBAAkB,UAAU,QAAQ,SAAS,gBAAgB;AAC3D,OAAK,aAAa,SAAO;AAAA;AAAA,QAElB,eAAe,CAAE;AAAA;AAAA,UAGhB,OAAO,IAAI,CAAC,CAAE,OAAO,KAAM,GAAG,QAAQ;AAAA,mDACI,KAAM,iBAAkB,KAAM,uCAAuC,UAAU,iBAAiB,WAAW,EAAE;AAAA,gBAEjJ,MAAM,KAAK,EAAE,QAAQ,MAAM,EAAE,CAAC,EAAE;AAAA,IAC9B,MAAM,eAAe;AAAA,EACvB,EAAE,KAAK,EAAE,CACX;AAAA;AAAA,WAEH,EAAE,KAAK,EAAE,CACZ;AAAA;AAAA;AAAA,GAGL;AAED,WAAY,KAAK,KAAK,YAAY,gBAAgB,SAAS,CAAAA,WAAS;AAElE,UAAM,SAASA,OAAM;AAErB,UAAM,QAAQ,KAAK,eAAe,MAAM;AAExC,SAAK,WAAW,kBAAkB,KAAK;AAAA,EACzC,CAAC;AAED,OAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,UAAU;AACzD;AAEA,kBAAkB,UAAU,YAAY,SAAS,OAAO;AACtD,MAAY,gBAAgB,KAAK,UAAU,EAAE,QAAQ,YAAU;AAE7D,UAAM,SAAS,KAAK,eAAe,MAAM,MAAM;AAE/C,YAAW,MAAM,EAAE,SAAS,QAAQ,QAAQ,EAAE,QAAQ;AAAA,EACxD,CAAC;AACH;AAEA,kBAAkB,UAAU;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AACF;;;AC7FA,IAAO,8BAAQ;AAAA,EACb,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,mBAAmB,CAAE,QAAQ,iBAAkB;AACjD;;;ACEA,IAAM,iBAAiB;AACvB,IAAM,qBAAqB;AAE3B,SAAS,QAAQ,SAAS,cAAc;AACtC,MAAI,WAAW,QAAQ,SAAS,OAAOC,eAAc;AAErD,MAAI,QAAQ,SAAS,QAAQ,gBAAgB,QAAQ,YAAY;AAEjE,MAAI,SAAS,QAAQ,CAAC,GAAG;AACvB,WAAO,SAAS,QAAQ,CAAC;AAAA,EAC3B,OAAO;AACL,WAAO,SAAS,CAAC;AAAA,EACnB;AACF;AAEA,SAASA,gBAAe,YAAY;AAClC,SAAOC,IAAG,YAAY,mBAAmB;AAC3C;AAEA,IAAMC,MAAK;AAEX,IAAMC,iBAAgB;AAGP,SAAR,yBACH,UAAU,iBACV,eAAe,WAAW,kBAAkB;AAE9C,OAAK,mBAAmB;AACxB,OAAK,iBAAiB;AACtB,OAAK,aAAa;AAClB,OAAK,oBAAoB;AAEzB,WAAS,GAAG,mBAAmB,CAAAC,WAAS;AACtC,QAAIA,OAAM,QAAQ;AAChB,WAAK,wBAAwB;AAAA,IAC/B,OAAO;AACL,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF,CAAC;AACH;AAEA,yBAAyB,UAAU,0BAA0B,WAAW;AACtE,QAAM,oBAAoB,KAAK,iBAAiB,OAAO,aAAW;AAChE,WAAOH,IAAG,SAAS,uBAAuB;AAAA,EAC5C,CAAC;AAED,aAAW,WAAW,mBAAmB;AACvC,SAAK,gBAAgB,OAAO;AAAA,EAC9B;AACF;AAEA,yBAAyB,UAAU,qBAAqB,WAAW;AAEjE,QAAM,oBAAoB,KAAK,iBAAiB,OAAO,aAAW;AAChE,WAAOA,IAAG,SAAS,uBAAuB;AAAA,EAC5C,CAAC;AAED,oBAAkB,QAAQ,sBAAoB;AAC5C,QAAI,iBAAiB,SAAS,OAAOD,eAAc,EAAE,QAAQ;AAC3D,WAAK,kBAAkB,gBAAgB;AAAA,IACzC;AAAA,EACF,CAAC;AACH;AAEA,yBAAyB,UAAU,oBAAoB,SAAS,SAAS;AACvE,OAAK,WAAW,UAAU,SAAS,EAAE,gBAAgB,OAAU,CAAC;AAClE;AAEA,yBAAyB,UAAU,kBAAkB,SAAS,SAAS;AAErE,QAAM,WAAW,QAAQ,SAAS,OAAOA,eAAc;AAGvD,MAAI,SAAS,SAAS,GAAG;AACvB;AAAA,EACF;AAEA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,KAAK,WAAW,UAAU,OAAO;AAErC,MAAI;AAEJ,MAAI,gBAAgB;AAGlB,wBAAoB,QAAQ,SAAS,cAAc;AAAA,EACrD,OAAO;AAGL,wBAAoB,SAAU,CAAE;AAAA,EAClC;AAEA,OAAK,WAAW,UAAU,SAAS,EAAE,gBAAgB,kBAAkB,CAAC;AAGxE,UAAQ,SAAS,QAAQ,CAAAK,cAAY;AAEnC,UAAM,QAAQA,cAAa,oBAAoB,iBAAiB;AAChE,UAAM,SAAS,KAAK,kBAAkB,IAAI,KAAK;AAE/C,SAAK,eAAe,IAAIA,WAAUH,KAAI;AAAA,MACpC;AAAA,IACF,GAAGC,cAAa;AAAA,EAClB,CAAC;AACH;AAEA,yBAAyB,UAAU;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AClHA,IAAMG,sBAAqB;AAeZ,SAAR,cAA+B,iBAAiB,UAAU,iBAAiB;AAChF,OAAK,mBAAmB;AACxB,OAAK,YAAY;AACjB,OAAK,mBAAmB;AAExB,OAAK,kBAAkB,CAAC;AACxB,OAAK,gBAAgB,CAAC;AAEtB,WAAS,GAAG,mBAAmBA,qBAAoB,CAAAC,WAAS;AAC1D,UAAM,SAASA,OAAM;AAErB,QAAI,QAAQ;AACV,WAAK,oBAAoB;AAAA,IAC3B,OAAO;AACL,WAAK,qBAAqB;AAE1B,WAAK,kBAAkB,CAAC;AACxB,WAAK,gBAAgB,CAAC;AAAA,IACxB;AAAA,EACF,CAAC;AAED,WAAS,GAAG,iBAAiBD,qBAAoB,MAAM;AACrD,SAAK,qBAAqB;AAE1B,aAAS,KAAK,gBAAgB,MAAM,KAAK,mBAAmB,CAAC;AAAA,EAC/D,CAAC;AACH;AAEA,cAAc,UAAU;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACF;AAWA,cAAc,UAAU,MAAM,SAAS,SAAS,IAAI,QAAQ,WAAW,KAAM;AAC3E,MAAI,gBAAgB,KAAK,cAAe,QAAQ,EAAG;AAEnD,MAAI,CAAC,eAAe;AAClB,oBAAgB,KAAK,cAAe,QAAQ,EAAG,IAAI,CAAC;AAAA,EACtD;AAEA,gBAAe,EAAG,IAAI;AAAA,IACpB,GAAG;AAAA,IACH;AAAA,EACF;AAEA,OAAK,2BAA2B,OAAO;AACzC;AAUA,cAAc,UAAU,SAAS,SAAS,SAAS,IAAI;AACrD,QAAM,gBAAgB,KAAK,cAAe,QAAQ,EAAG;AAErD,MAAI,eAAe;AACjB,WAAO,cAAe,EAAG;AAEzB,QAAI,CAAC,OAAO,KAAK,aAAa,GAAG;AAC/B,aAAO,KAAK,cAAe,QAAQ,EAAG;AAAA,IACxC;AAAA,EACF;AAEA,OAAK,2BAA2B,OAAO;AACzC;AAEA,cAAc,UAAU,OAAO,SAAS,SAAS;AAC/C,QAAM,KAAK,MAAM,OAAO;AAExB,MAAI,CAAC,IAAI;AACP,WAAO;AAAA,EACT;AAIA,MAAIE,SAAQ,OAAO,GAAG;AACpB,WAAO;AAAA,MACL,QAAQ,GAAG,SAAS,GAAG,MAAM,IAAI,OAAO;AAAA,IAC1C;AAAA,EACF,WAAW,MAAM,IAAI,CAAE,mBAAmB,kBAAmB,CAAC,GAAG;AAC/D,WAAO;AAAA,MACL,MAAM,GAAG,IAAI,kBAAkB;AAAA,MAC/B,QAAQ,GAAG,IAAI,cAAc;AAAA,IAC/B;AAAA,EACF;AACF;AAEA,cAAc,UAAU,OAAO,SAAS,SAAS,SAAS,CAAC,GAAG;AAC5D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,KAAK,MAAM,OAAO;AAExB,MAAI,CAAC,IAAI;AACP;AAAA,EACF;AAIA,MAAIA,SAAQ,OAAO,GAAG;AACpB,OAAG,SAAS,GAAG,MAAM,IAAI,SAAS,MAAM;AAAA,EAC1C,WAAW,MAAM,IAAI,CAAE,mBAAmB,kBAAmB,CAAC,GAAG;AAC/D,OAAG,IAAI,oBAAoB,IAAI;AAC/B,OAAG,IAAI,gBAAgB,MAAM;AAAA,EAC/B;AAEA,OAAK,aAAa,OAAO;AAC3B;AAEA,cAAc,UAAU,sBAAsB,WAAW;AACvD,OAAK,kBAAkB,CAAC;AAExB,OAAK,iBAAiB,QAAQ,aAAW;AACvC,SAAK,gBAAiB,QAAQ,EAAG,IAAI,KAAK,KAAK,OAAO;AAAA,EACxD,CAAC;AACH;AAEA,cAAc,UAAU,uBAAuB,WAAW;AACxD,OAAK,iBAAiB,QAAQ,aAAW;AACvC,UAAM,SAAS,KAAK,gBAAiB,QAAQ,EAAG;AAEhD,QAAI,QAAQ;AACV,WAAK,KAAK,SAAS,MAAM;AAAA,IAC3B;AAAA,EACF,CAAC;AACH;AAEA,cAAc,UAAU,qBAAqB,WAAW;AACtD,OAAK,iBAAiB,QAAQ,aAAW;AACvC,UAAM,gBAAgB,KAAK,cAAe,QAAQ,EAAG;AAErD,QAAI,eAAe;AACjB,WAAK,KAAK,SAAS,6BAA6B,aAAa,CAAC;AAAA,IAChE;AAAA,EACF,CAAC;AACH;AAEA,cAAc,UAAU,6BAA6B,SAAS,SAAS;AACrE,QAAM,gBAAgB,KAAK,cAAe,QAAQ,EAAG;AAErD,MAAI,CAAC,eAAe;AAClB,SAAK,KAAK,SAAS,KAAK,gBAAiB,QAAQ,EAAG,CAAC;AAErD;AAAA,EACF;AAEA,OAAK,KAAK,SAAS,6BAA6B,aAAa,CAAC;AAChE;AAEA,cAAc,UAAU,eAAe,SAAS,SAAS;AACvD,QAAM,MAAM,KAAK,iBAAiB,YAAY,OAAO;AAErD,QAAM,OAAO,QAAQ,YAAY,eAAe;AAEhD,OAAK,iBAAiB,OAAO,MAAM,SAAS,GAAG;AACjD;AAKA,SAASA,SAAQ,SAAS;AACxB,SAAO,iBAAiB;AAC1B;AASA,SAAS,6BAA6B,SAAS,CAAC,GAAG;AACjD,QAAM,4BAA4B,OAAO,OAAO,MAAM,EAAE,OAAO,CAACC,4BAA2BC,YAAW;AACpG,UAAM,EAAE,WAAW,IAAK,IAAIA;AAE5B,QAAI,CAACD,8BAA6B,WAAWA,2BAA0B,UAAU;AAC/E,aAAOC;AAAA,IACT;AAEA,WAAOD;AAAA,EACT,GAAG,MAAS;AAEZ,MAAI,2BAA2B;AAC7B,UAAM,EAAE,UAAU,GAAG,cAAc,IAAI;AAEvC,WAAO;AAAA,EACT;AACF;;;ACjOA,IAAO,yBAAQ;AAAA,EACb,eAAe,CAAE,QAAQ,aAAc;AACzC;;;ACAA,IAAO,qCAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF;AAAA,EACA,0BAA0B,CAAE,QAAQ,wBAAyB;AAC/D;;;ACNA,IAAME,MAAK;AAEI,SAAR,qBACH,UAAU,iBAAiB,eAAe;AAE5C,OAAK,mBAAmB;AACxB,OAAK,iBAAiB;AAEtB,WAAS,GAAG,mBAAmB,CAAAC,WAAS;AACtC,UAAM,EAAE,OAAO,IAAIA;AAEnB,QAAI,QAAQ;AACV,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF,CAAC;AACH;AAEA,qBAAqB,UAAU,oBAAoB,WAAW;AAC5D,OAAK,iBAAiB,QAAQ,aAAW;AACvC,SAAK,eAAe,IAAI,SAASD,KAAI;AAAA,MACnC,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH;AAEA,qBAAqB,UAAU;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AACF;;;AC/BA,IAAO,iCAAQ;AAAA,EACb,aAAa,CAAE,sBAAoB;AAAA,EACnC,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,sBAAsB,CAAE,QAAQ,oBAAqB;AACvD;;;ACJA,IAAME,kBAAiB;AACvB,IAAMC,sBAAqB;AAQ3B,IAAM,WAAW;AAGF,SAAR,yBACH,UAAU,iBACV,eAAe,WAAW,kBAAkB;AAE9C,OAAK,mBAAmB;AACxB,OAAK,iBAAiB;AACtB,OAAK,aAAa;AAClB,OAAK,oBAAoB;AAEzB,WAAS,GAAG,mBAAmB,CAAAC,WAAS;AACtC,QAAIA,OAAM,QAAQ;AAChB,WAAK,YAAY;AAAA,IACnB,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF,CAAC;AACH;AAEA,yBAAyB,UAAU,cAAc,WAAW;AAC1D,QAAM,oBAAoB,KAAK,iBAAiB,OAAO,aAAW;AAChE,WAAO,GAAG,SAAS,uBAAuB;AAAA,EAC5C,CAAC;AAED,oBAAkB,QAAQ,sBAAoB;AAC5C,QAAI,iBAAiB,SAAS,OAAO,cAAc,EAAE,SAAS,GAAG;AAC/D,WAAK,oBAAoB,gBAAgB;AAAA,IAC3C;AAAA,EACF,CAAC;AACH;AAEA,yBAAyB,UAAU,QAAQ,WAAW;AACpD,QAAM,oBAAoB,KAAK,iBAAiB,OAAO,aAAW;AAChE,WAAO,GAAG,SAAS,uBAAuB;AAAA,EAC5C,CAAC;AAED,oBAAkB,QAAQ,sBAAoB;AAC5C,QAAI,iBAAiB,SAAS,OAAO,cAAc,EAAE,SAAS,GAAG;AAC/D,WAAK,cAAc,gBAAgB;AAAA,IACrC;AAAA,EACF,CAAC;AACH;AAEA,yBAAyB,UAAU,qBAAqB,SAAS,SAAS,cAAc;AACtF,QAAM,iBAAiB,KAAK,mBAAmB,OAAO,GAChD,cAAc,eAAe,OAAO,GACpC,kBAAkB,mBAAmB,OAAO;AAElD,MAAI;AACJ,MAAI,eAAe,SAAS,YAAY,GAAG;AACzC,wBAAoB,QAAQ,gBAAgB,YAAY;AAAA,EAC1D,OAAO;AACL,wBAAoB,QAAQ,gBAAgB,WAAW,EAAE,OAAO,YAAY;AAAA,EAC9E;AAGA,MAAI,CAAC,kBAAkB,QAAQ;AAG7B,QAAI,aAAa;AACf,0BAAoB,CAAE,WAAY;AAAA,IACpC,OAAO;AAGL,0BAAoB,CAAE,gBAAgB,KAAK,UAAQ,SAAS,YAAY,CAAE;AAAA,IAC5E;AAAA,EACF;AAEA,OAAK,mBAAmB,SAAS,iBAAiB;AACpD;AAEA,yBAAyB,UAAU,qBAAqB,SAAS,SAAS;AACxE,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,KAAK,WAAW,UAAU,OAAO;AAErC,SAAO;AACT;AAEA,yBAAyB,UAAU,qBAAqB,SAAS,SAAS,gBAAgB;AACxF,OAAK,WAAW,UAAU,SAAS,EAAE,eAAe,CAAC;AAErD,QAAM,gBAAgB,QAAQ,SAAS,OAAO,cAAc;AAG5D,gBAAc,QAAQ,cAAY;AAEhC,UAAM,QAAS,CAAC,kBAAkB,eAAe,SAAS,QAAQ,IAChEF,kBAAiBC;AACnB,UAAM,SAAS,KAAK,kBAAkB,IAAI,KAAK;AAE/C,SAAK,eAAe,IAAI,UAAU,UAAU;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,yBAAyB,UAAU,sBAAsB,SAAS,SAAS;AACzE,QAAM,gBAAgB,QAAQ,SAAS,OAAO,cAAc;AAE5D,QAAM,cAAc,eAAe,OAAO;AAC1C,QAAM,kBAAkB,QAAQ,eAAe,WAAW;AAE1D,OAAK,mBAAmB,SAAS,eAAe;AAClD;AAEA,yBAAyB,UAAU,gBAAgB,SAAS,SAAS;AACnE,OAAK,mBAAmB,SAAS,MAAS;AAC5C;AAEA,yBAAyB,UAAU;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,eAAe,SAAS;AAC/B,QAAM,cAAc,kBAAkB,OAAO,EAAE;AAE/C,MAAI,CAAC,aAAa;AAChB;AAAA,EACF;AAEA,SAAO,QAAQ,SAAS,KAAK,UAAQ;AACnC,UAAM,SAAS,kBAAkB,IAAI;AAErC,WAAO,WAAW;AAAA,EACpB,CAAC;AACH;AAEA,SAAS,mBAAmB,SAAS;AACnC,QAAM,cAAc,eAAe,OAAO;AAE1C,SAAO,QAAQ,SAAS,OAAO,UAAQ;AACrC,UAAM,SAAS,kBAAkB,IAAI;AAErC,WAAO,WAAW;AAAA,EACpB,CAAC;AACH;AAEA,SAAS,QAAQ,OAAO,SAAS;AAC/B,SAAO,MAAM,OAAO,kBAAgB,iBAAiB,OAAO;AAC9D;;;AC5JA,IAAO,qCAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF;AAAA,EACA,0BAA0B,CAAE,QAAQ,wBAAyB;AAC/D;;;ACAe,SAAR,QAAyB,UAAU,QAAQ;AAChD,MAAI,OAAO;AAEX,OAAK,UAAU;AAEf,OAAK,UAAU,CAAC;AAEhB,OAAK,MAAM;AAEX,WAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,QAAI,SAAS,QAAQ;AAErB,QAAI,QAAQ;AACV,cAAW,KAAK,SAAS,EAAE,OAAO,QAAQ;AAAA,IAC5C,OAAO;AACL,cAAW,KAAK,SAAS,EAAE,IAAI,QAAQ;AAAA,IACzC;AAAA,EACF,CAAC;AACH;AAEA,QAAQ,UAAU,QAAQ,WAAW;AACnC,OAAK,YAAY,SAAO,wCAAwC;AAEhE,OAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,SAAS;AACxD;AAEA,QAAQ,UAAU,WAAW,SAAS,OAAO,OAAO;AAClD,MAAI,aAAa;AAEjB,OAAK,QAAQ,QAAQ,SAASE,QAAO;AACnC,QAAI,SAASA,OAAM,OAAO;AACxB;AAAA,IACF;AAAA,EACF,CAAC;AAED,OAAK,UAAU,aAAa,OAAO,KAAK,UAAU,WAAW,UAAU,CAAC;AAExE,OAAK,QAAQ,KAAK;AAAA,IAChB;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,QAAQ,UAAU,CAAE,YAAY,QAAS;;;ACnDzC,IAAO,kBAAQ;AAAA,EACb,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,wBAAwB,CAAE,QAAQ,OAAQ;AAC5C;;;ACWA,IAAO,eAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACjCA,IAAMC,iBAAgB;AAGP,SAAR,gBACH,UACA,YACA,UACA,eACA,eACA,UACA,SAAS;AAEX,MAAI,mBAAmB;AAEvB,WAAS,GAAG,mBAAmBA,gBAAe,CAAAC,WAAS;AAErD,uBAAmBA,OAAM;AAEzB,QAAI,kBAAkB;AACpB,oBAAc,OAAO;AACrB,eAAS,OAAO;AAAA,IAClB;AAEA,YAAQ,QAAQ;AAAA,EAClB,CAAC;AAED,WAAS,UAAU,KAAK,QAAQ,IAAI;AAClC,UAAM,KAAK,IAAI,MAAM;AACrB,QAAI,MAAM,IAAI,WAAW;AACvB,aAAO,GAAG,KAAK,MAAM,IAAI,SAAS;AAAA,IACpC;AAAA,EACF;AAEA,WAAS,yBAAyB,KAAK,QAAQ;AAC7C,cAAU,KAAK,QAAQ,SAAS,IAAI,MAAM;AACxC,UAAI,kBAAkB;AACpB;AAAA,MACF;AAEA,aAAO,GAAG,MAAM,MAAM,IAAI;AAAA,IAC5B,CAAC;AAAA,EACH;AAEA,WAAS,wBAAwB,KAAK,QAAQ;AAC5C,cAAU,KAAK,QAAQ,SAAS,IAAI,MAAM;AACxC,UAAI,kBAAkB;AACpB,cAAM,IAAI,MAAM,oBAAoB;AAAA,MACtC;AAEA,aAAO,GAAG,MAAM,MAAM,IAAI;AAAA,IAC5B,CAAC;AAAA,EACH;AAEA,2BAAyB,UAAU,MAAM;AAEzC,2BAAyB,eAAe,UAAU;AAElD,2BAAyB,UAAU,MAAM;AAEzC,2BAAyB,eAAe,UAAU;AAElD,0BAAwB,UAAU,WAAW;AAC7C,0BAAwB,UAAU,kBAAkB;AACpD,0BAAwB,UAAU,cAAc;AAChD,0BAAwB,UAAU,gBAAgB;AAClD,0BAAwB,UAAU,kBAAkB;AACpD,0BAAwB,UAAU,kBAAkB;AACpD,0BAAwB,UAAU,aAAa;AAC/C,0BAAwB,UAAU,aAAa;AAC/C,0BAAwB,UAAU,aAAa;AAC/C,0BAAwB,UAAU,gBAAgB;AAClD,0BAAwB,UAAU,oBAAoB;AACtD,0BAAwB,UAAU,aAAa;AAC/C,0BAAwB,UAAU,kBAAkB;AACpD,0BAAwB,UAAU,cAAc;AAChD,0BAAwB,UAAU,eAAe;AACjD,0BAAwB,UAAU,eAAe;AACjD,0BAAwB,UAAU,aAAa;AAC/C,0BAAwB,UAAU,aAAa;AAC/C,0BAAwB,UAAU,iBAAiB;AACnD,0BAAwB,UAAU,gBAAgB;AAClD,0BAAwB,UAAU,cAAc;AAEhD,YAAU,eAAe,WAAW,SAAS,IAAI,MAAM;AACrD,UAAM,SAAS,KAAK,CAAC;AAErB,QAAI,oBAAoB,YAAY;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,MAAM,GAAG;AACV;AAAA,IACF;AAEA,WAAO,GAAG,MAAM,MAAM,IAAI;AAAA,EAC5B,CAAC;AACH;AAEA,gBAAgB,UAAU;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAKA,SAAS,YAAY,SAAS,QAAQ;AACpC,SAAO,QAAQ,QAAQ,MAAM,IAAI;AACnC;;;AC3HA,IAAO,2BAAQ;AAAA,EACb,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,iBAAiB,CAAE,QAAQ,eAAgB;AAC7C;;;ACUe,SAAR,WACH,UAAU,QAAQ,WAClB,YAAY;AAEd,OAAK,YAAY;AACjB,OAAK,UAAU;AACf,OAAK,aAAa;AAClB,OAAK,cAAc;AAEnB,OAAK,UAAU;AAEf,WAAS,GAAG,sBAAsB,MAAM;AAEtC,QAAI,KAAK,SAAS;AAChB,WAAK,WAAW,KAAK;AAErB,eAAS,KAAK,eAAe,MAAM;AACjC,aAAK,WAAW,IAAI;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAED,WAAS,GAAG,gBAAgB,MAAM;AAChC,SAAK,gBAAgB,KAAK,QAAQ,aAAa,EAAE;AACjD,SAAK,WAAW,MAAS,gBAAgB,KAAK,QAAQ,aAAa,CAAC;AAEpE,SAAK,MAAM;AAAA,EACb,CAAC;AACH;AAEA,WAAW,UAAU,QAAQ,WAAW;AACtC,OAAK,aAAa,SAAO;AAAA;AAAA,kDAEwB,cAAc,CAAE;AAAA;AAAA,GAEhE;AAED,QAAS,KAAK,KAAK,YAAY,SAAS,MAAM,KAAK,WAAW,CAAC;AAE/D,OAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,UAAU;AACzD;AAEA,WAAW,UAAU,aAAa,SAAS,SAAS,CAAC,KAAK,SAAS;AAEjE,MAAI,WAAW,KAAK,SAAS;AAC3B;AAAA,EACF;AAEA,MAAI,QAAQ;AACV,SAAK,WAAW,YAAY,6CAA8C,aAAa,CAAE;AAEzF,YAAW,KAAK,aAAa,EAAE,IAAI,YAAY;AAC/C,YAAW,KAAK,QAAQ,EAAE,IAAI,QAAQ;AAAA,EACxC,OAAO;AACL,SAAK,WAAW,YAAY,6CAA8C,cAAc,CAAE;AAE1F,YAAW,KAAK,aAAa,EAAE,OAAO,YAAY;AAClD,YAAW,KAAK,QAAQ,EAAE,OAAO,QAAQ;AAEzC,UAAM,WAAW,KAAK,WAAW,IAAI;AAErC,QAAI,SAAS,WAAW,GAAG;AACzB,WAAK,YAAY,KAAK,SAAS,CAAC,CAAC;AAAA,IACnC;AAAA,EACF;AAEA,OAAK,UAAU,KAAK,mBAAmB;AAAA,IACrC;AAAA,EACF,CAAC;AAED,OAAK,UAAU;AACjB;AAEA,WAAW,UAAU;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AC7FA,IAAO,kBAAQ;AAAA,EACb,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,YAAY,CAAE,QAAQ,UAAW;AACnC;;;ACLe,SAAR,cACH,UACA,YACA,iBACA,iBACA,eACA,UACF;AACA,MAAI,SAAS;AAEb,gBAAc,SAAS;AAAA,IACrB,uBAAuB,WAAW;AAChC,iBAAW,WAAW;AAAA,IACxB;AAAA,EACF,CAAC;AAED,gBAAc,SAAS;AAAA,IACrB,4BAA4B,WAAW;AACrC,gBAAU,gBAAgB,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AAED,gBAAc,SAAS;AAAA,IACrB,sBAAsB,WAAW;AAC/B,gBAAU,gBAAgB,gBAAgB;AAAA,IAC5C;AAAA,EACF,CAAC;AAED,QAAM,MAAM,SAAS,IAAI,OAAO,KAAK;AAErC,SAAO,cAAc,SAAS;AAAA,IAC5B,0BAA0B,WAAW;AACnC,UAAI,OAAO;AAAA,IACb;AAAA,EACF,CAAC;AAED,WAAS,GAAG,mBAAmB,CAACC,WAAU;AACxC,aAASA,OAAM;AAAA,EACjB,CAAC;AACH;AAEA,cAAc,UAAU;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AChDA,IAAO,yBAAQ;AAAA,EACb,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,8BAA8B,CAAE,QAAQ,aAAc;AACxD;;;ACHA,IAAMC,sBAAqB;AAGZ,SAAR,iBAAkC,UAAU,UAAU;AAE3D,MAAI,gBAAgB,SAAS,IAAI,iBAAiB,KAAK,GACnD,WAAW,SAAS,IAAI,YAAY,KAAK;AAE7C,MAAI,CAAC,YAAY,CAAC,eAAe;AAC/B;AAAA,EACF;AAGA,MAAI,WAAW;AAGf,WAAS,eAAe,UAAU;AAChC,QAAI,MAAM,CAAE,KAAK,GAAI,GAAG,QAAQ,GAAG;AACjC,oBAAc,QAAQ,uBAAuB;AAE7C,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AAEA,QAAI,MAAM,CAAE,KAAK,GAAI,GAAG,QAAQ,GAAG;AACjC,oBAAc,QAAQ,0BAA0B;AAEhD,aAAO;AAAA,IACT;AAGA,QAAI,MAAM,CAAE,KAAK,UAAW,GAAG,QAAQ,GAAG;AACxC,oBAAc,QAAQ,4BAA4B;AAElD,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,CAAE,KAAK,GAAI,GAAG,QAAQ,GAAG;AACjC,oBAAc,QAAQ,sBAAsB;AAE5C,aAAO;AAAA,IACT;AAAA,EACF;AAGA,WAAS,GAAG,iBAAiB,WAAW;AAEtC,aAAS,YAAYA,qBAAoB,SAASC,QAAO;AACvD,UAAI,WAAWA,OAAM;AAErB,qBAAe,QAAQ;AAAA,IACzB,CAAC;AAAA,EAEH,CAAC;AAED,WAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,QAAI,SAAS,QAAQ;AAErB,QAAI,QAAQ;AACV,iBAAW;AAAA,IACb,OAAO;AACL,iBAAW;AAAA,IACb;AAAA,EACF,CAAC;AAEH;AAEA,iBAAiB,UAAU,CAAE,YAAY,UAAW;AAKpD,SAAS,MAAM,MAAMA,QAAO;AAC1B,SAAO,KAAK,QAAQA,OAAM,GAAG,IAAI;AACnC;;;AC/EA,IAAO,4BAAQ;AAAA,EACb,UAAU;AAAA,IACR;AAAA,EACF;AAAA,EACA,iCAAiC,CAAE,QAAQ,gBAAiB;AAC9D;;;ACAA,IAAOC,mBAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": ["randomColor", "hex", "FAILED", "TERMINATED", "COMPLETED", "RUNNING", "ACTIVATED", "attr", "isAny", "event", "task", "scope", "event", "subscriptions", "isAny", "element", "scopes", "event", "event", "scope", "isAny", "scope", "event", "e", "event", "element", "element", "isStartEvent", "element", "isAny", "event", "is", "event", "isTypedEvent", "element", "entry", "isTypedEvent", "event", "scope", "isStartEvent", "event", "behaviors_default", "event", "event", "last", "parts", "d", "behaviors_default", "HIGH_PRIORITY", "randomColor", "event", "is", "is", "event", "event", "is", "isAncestor", "element", "event", "OFFSET_TOP", "event", "event", "is", "event", "isLabel", "event", "is", "HIGH_PRIORITY", "event", "event", "OFFSET_LEFT", "LOW_PRIORITY", "DEFAULT_PRIMARY_COLOR", "DEFAULT_AUXILIARY_COLOR", "event", "is", "event", "isSequenceFlow", "is", "ID", "HIGH_PRIORITY", "event", "outgoing", "VERY_HIGH_PRIORITY", "event", "isLabel", "colorsWithHighestPriority", "colors", "ID", "event", "SELECTED_COLOR", "NOT_SELECTED_COLOR", "event", "entry", "HIGH_PRIORITY", "event", "event", "VERY_HIGH_PRIORITY", "event", "modeler_default"]}